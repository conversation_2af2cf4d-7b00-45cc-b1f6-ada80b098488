---
description: This rule provides best practices and coding standards for MyBatis-Plus development, focusing on code organization, performance, security, and common pitfalls. It aims to improve code quality and maintainability in MyBatis-Plus projects.
globs: **/*.java,**/*.xml
---
# MyBatis-Plus Best Practices and Coding Standards

This document outlines the best practices and coding standards for developing applications using MyBatis-Plus. Following these guidelines will lead to more maintainable, performant, and secure applications.

## 1. Code Organization and Structure

*   **Package Structure:**
    *   Follow a clear and consistent package structure. A common approach is to organize packages by layer (e.g., `controller`, `service`, `mapper`, `entity`).
    *   Example:
        
        com.example.app
        ├── controller
        │   └── UserController.java
        ├── service
        │   ├── UserService.java
        │   └── UserServiceImpl.java
        ├── mapper
        │   └── UserMapper.java
        ├── entity
        │   └── User.java
        └── config
            └── MyBatisPlusConfig.java
        
*   **Configuration:**
    *   Centralize MyBatis-Plus configuration in a dedicated configuration class.
    *   Use `@Configuration` and `@MapperScan` annotations to configure MyBatis-Plus.
    *   Example:
        java
        @Configuration
        @MapperScan("com.example.app.mapper")
        public class MyBatisPlusConfig {
            // Configuration beans (e.g., PaginationInterceptor)
        }
        
*   **Entity Classes:**
    *   Annotate entity classes with `@TableName` to specify the database table name.
    *   Use `@TableId` to define the primary key column.  Specify `type = IdType.AUTO` for auto-incrementing keys, or `IdType.ASSIGN_ID` for snowflake algorithm.
    *   Use `@TableField` to map entity fields to database columns. Specify `exist = false` for fields that are not persisted in the database.
    *   Use `@TableLogic` for logical deletion fields.
    *   Example:
        java
        @TableName("user")
        @Data
        public class User {
            @TableId(value = "id", type = IdType.AUTO)
            private Long id;

            @TableField("username")
            private String username;

            @TableField("email")
            private String email;

            @TableField(exist = false)
            private String fullName;

            @TableLogic
            @TableField("deleted")
            private Integer deleted;
        }
        
*   **Mapper Interfaces:**
    *   Extend `BaseMapper<T>` interface for basic CRUD operations.
    *   Use `@Mapper` annotation or `@MapperScan` in configuration to register mapper interfaces.
    *   Define custom SQL queries in XML files or using `@Select`, `@Insert`, `@Update`, `@Delete` annotations for simple cases.
    *   Example:
        java
        @Mapper
        public interface UserMapper extends BaseMapper<User> {
            @Select("SELECT * FROM user WHERE username = #{username}")
            User findByUsername(@Param("username") String username);
        }
        
*   **Service Layer:**
    *   Implement a service layer to encapsulate business logic and interact with the mapper interfaces.
    *   Use dependency injection to inject mapper interfaces into service classes.
    *   Consider using `@Service` annotation to register service classes as Spring beans.
    *   Example:
        java
        @Service
        public class UserServiceImpl implements UserService {
            @Autowired
            private UserMapper userMapper;

            @Override
            public User findUserById(Long id) {
                return userMapper.selectById(id);
            }
        }
        

## 2. Common Patterns and Anti-patterns

*   **Active Record Pattern:**
    *   MyBatis-Plus supports the Active Record pattern, allowing entities to perform database operations directly.
    *   Use with caution, as it can tightly couple entities to the persistence layer.
    *   Example:
        java
        User user = new User();
        user.setUsername("testUser");
        user.insert(); // Active Record insert
        
*   **LambdaQueryWrapper/LambdaUpdateWrapper:**
    *   Use `LambdaQueryWrapper` and `LambdaUpdateWrapper` for type-safe queries and updates.
    *   Avoid using string-based column names in queries to prevent typos and improve refactoring.
    *   Example:
        java
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(User::getUsername, "testUser").gt(User::getAge, 18);

        LambdaQueryWrapper<User> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.eq(User::getUsername, "testUser").gt(User::getAge, 18);

        List<User> users = userMapper.selectList(lambdaQuery);
        
*   **Code Generation:**
    *   Utilize MyBatis-Plus code generation to automate the creation of entity classes, mapper interfaces, and XML files.
    *   Customize the code generator templates to match project-specific coding standards.
    *   Configure table prefixes, field prefixes, and other options to generate code that aligns with the database schema.
*   **Anti-patterns:**
    *   **Directly manipulating SQL strings:** Avoid constructing SQL queries using string concatenation. Use `QueryWrapper`, `LambdaQueryWrapper`, or XML-based queries with parameter binding to prevent SQL injection vulnerabilities.
    *   **Over-fetching data:** Avoid selecting all columns when only a subset is required. Use `select` method in the `QueryWrapper` to specify the desired columns.
    *   **Ignoring pagination:** When retrieving large datasets, always implement pagination to improve performance and reduce memory consumption. Use `Page` object with `selectPage` method.
    *   **Not handling null values:**  Be mindful of null values when constructing queries. Use `isNotNull` and `isNull` methods in the `QueryWrapper` to handle null values appropriately.  Use `@TableField(strategy = FieldStrategy.NOT_NULL)` to avoid updating null values.
    *   **Excessive database calls:** Avoid making multiple database calls within a single transaction if possible. Optimize queries and consider using batch operations to reduce the number of database interactions.

## 3. Performance Considerations

*   **Indexing:**
    *   Ensure that appropriate indexes are created on database columns used in queries.
    *   Analyze query execution plans to identify missing indexes.
*   **Pagination:**
    *   Implement pagination for large datasets to reduce memory consumption and improve response times.
    *   Use `Page` object with `selectPage` method for paginated queries.
    *   Example:
        java
        Page<User> page = new Page<>(currentPage, pageSize);
        IPage<User> userPage = userMapper.selectPage(page, null);
        List<User> users = userPage.getRecords();
        long total = userPage.getTotal();
        
*   **Batch Operations:**
    *   Use batch insert, update, and delete operations to improve performance when processing multiple records.
    *   MyBatis-Plus provides `insertBatch`, `updateBatchById`, and `deleteBatchIds` methods for batch operations.
    *   Configure `rewriteBatchedStatements=true` in the JDBC connection URL to enable batch statement rewriting.
*   **Caching:**
    *   Implement caching strategies (e.g., using Redis or Caffeine) to reduce database load for frequently accessed data.
    *   Consider using MyBatis second-level cache for caching query results at the mapper level.
*   **Lazy Loading:**
    *   Use lazy loading for related entities to avoid loading unnecessary data.
    *   Configure lazy loading in the MyBatis configuration file.
*   **SQL Optimization:**
    *   Analyze slow-running queries and optimize SQL statements.
    *   Use `EXPLAIN` statement to understand query execution plans.
    *   Avoid using `SELECT *` and specify only the required columns.
    *   Use `JOIN` operations instead of multiple `SELECT` statements.

## 4. Security Best Practices

*   **SQL Injection Prevention:**
    *   Always use parameterized queries or prepared statements to prevent SQL injection vulnerabilities.
    *   Never construct SQL queries using string concatenation with user-supplied input.
    *   Use `QueryWrapper`, `LambdaQueryWrapper`, or XML-based queries with parameter binding.
*   **Input Validation:**
    *   Validate user input to prevent malicious data from being inserted into the database.
    *   Use server-side validation frameworks (e.g., Spring Validation) to validate input data.
*   **Authorization:**
    *   Implement proper authorization mechanisms to restrict access to sensitive data.
    *   Use Spring Security or other authorization frameworks to control access to resources.
*   **Data Masking:**
    *   Mask sensitive data (e.g., passwords, credit card numbers) in the database to protect against data breaches.
    *   Use encryption or hashing algorithms to store sensitive data securely.
*   **Logging:**
    *   Implement comprehensive logging to track user activity and detect security threats.
    *   Log all security-related events, such as login attempts, authorization failures, and data access violations.

## 5. Testing Approaches

*   **Unit Testing:**
    *   Write unit tests for mapper interfaces and service classes.
    *   Use mock objects to isolate components and test them independently.
    *   Use JUnit or other testing frameworks for unit testing.
*   **Integration Testing:**
    *   Write integration tests to verify the interaction between different components, including the database.
    *   Use an in-memory database (e.g., H2 or embedded Derby) for integration testing.
    *   Use Spring Test framework for integration testing.
*   **Data Setup:**
    *   Use database migration tools (e.g., Flyway or Liquibase) to manage database schema changes and data seeding.
    *   Create test data using SQL scripts or Java code.
*   **Test Coverage:**
    *   Aim for high test coverage to ensure that all parts of the code are tested.
    *   Use code coverage tools (e.g., JaCoCo) to measure test coverage.

## 6. Common Pitfalls and Gotchas

*   **Incorrect Table/Column Mapping:**
    *   Ensure that the table and column names in the entity classes match the database schema exactly.
    *   Use `@TableName` and `@TableField` annotations to specify the correct mappings.
*   **NullPointerException (NPE):**
    *   Be mindful of null values when working with MyBatis-Plus.
    *   Use `Objects.requireNonNull` to check for null values and prevent NPEs.
    *   Use Optional for return values where appropriate.
*   **Type Mismatch:**
    *   Ensure that the data types in the entity classes match the data types in the database schema.
    *   Use appropriate type handlers to convert between Java types and database types.
*   **Lazy Loading Issues:**
    *   Be aware of the N+1 select problem when using lazy loading.
    *   Optimize queries to reduce the number of database calls.
    *   Consider using eager loading for frequently accessed related entities.
*   **Transaction Management:**
    *   Use `@Transactional` annotation to manage transactions.
    *   Ensure that transactions are properly committed or rolled back in case of errors.
    *   Understand the different transaction propagation levels.
*   **Logical Deletion:**
    *   When using `@TableLogic`, ensure the corresponding column in the database is properly defined (e.g., as an integer or boolean).
    *   Understand how MyBatis-Plus handles logical deletion in queries (e.g., automatically adding `WHERE deleted = 0`).

## 7. Tooling and Environment

*   **IDE:**
    *   Use a Java IDE such as IntelliJ IDEA or Eclipse.
    *   Install MyBatis-Plus plugins for code completion, validation, and code generation.
*   **Build Tool:**
    *   Use a build tool such as Maven or Gradle.
    *   Configure MyBatis-Plus dependencies in the build file.
*   **Database:**
    *   Use a relational database such as MySQL, PostgreSQL, or Oracle.
    *   Configure the database connection in the Spring configuration file.
*   **Logging Framework:**
    *   Use a logging framework such as Logback or SLF4J.
    *   Configure logging levels and appenders to capture relevant log messages.
*   **Version Control:**
    *   Use a version control system such as Git.
    *   Commit code changes regularly and follow a consistent branching strategy.
*   **MybatisX IDEA Plugin:**
    *   Consider using MybatisX, a free IDEA plugin, for enhanced MyBatis-Plus support, including code completion, SQL generation, and result map navigation.

By adhering to these best practices and coding standards, developers can create robust, maintainable, and performant applications using MyBatis-Plus.
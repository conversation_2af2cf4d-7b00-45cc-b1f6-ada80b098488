---
description: This rule enforces Bootstrap best practices for code organization, performance, and maintainability. It provides guidelines for using Sass, avoiding common pitfalls, and structuring projects effectively.
globs: **/*.{scss,js,html,vue,jsx,ts,tsx}
---
# Bootstrap Best Practices and Coding Standards

This document outlines the recommended practices for developing projects using Bootstrap, focusing on maintainability, performance, and adherence to community standards. It covers various aspects, from project setup to component design and testing.

## 1. Project Setup and Code Organization

### 1.1. Use Sass

- **Rationale:** Leverage the power of Sass for maintainable and scalable CSS. Bootstrap's official support for Sass ensures compatibility and access to advanced features like variables, mixins, and nesting.
- **Implementation:**
  - Use the Sass version of Bootstrap (`bootstrap-sass` gem for Rails or `bootstrap` package for npm).
  - Configure your project to compile Sass files into CSS.

### 1.2. Avoid the CDN for Production

- **Rationale:** Using a CDN for quick demos is acceptable, but for production environments, it's better to manage Bootstrap locally to have full control over versions and customization.
- **Implementation:**
  - Install Bootstrap via npm or Yarn.
  - Import Bootstrap's CSS and JavaScript files directly into your project.

### 1.3. Project Structure

- **Recommendation:** Organize your project with a clear separation of concerns:
  - `assets/`: Contains all assets (CSS, JavaScript, images, fonts).
  - `components/`: Holds reusable UI components.
  - `pages/`: Contains page-specific code.
  - `styles/`: Contains global styles, variables, and mixins.

## 2. Component Design and Development

### 2.1. Graduate to New Components

- **Rationale:** Avoid extensively restyling existing Bootstrap components. Instead, create new components to maintain a clean and modular codebase.
- **Implementation:**
  - When customization needs exceed minor tweaks, create a new component.
  - Follow a consistent naming convention (e.g., BEM or RSCSS).

### 2.2. Be Selective with Bootstrap Modules

- **Rationale:** Bootstrap's modularity allows you to choose only the necessary components, reducing bloat and improving performance.
- **Implementation:**
  - Start with a minimal set of Bootstrap components.
  - Comment out unused components in your Sass file (e.g., `_bootstrap.scss`).
  - Consider alternative grid systems or icon libraries if they better suit your project's needs.

### 2.3. Naming Conventions (BEM or RSCSS)

- **Rationale:** Consistent naming conventions improve code readability and maintainability.
- **Implementation:**
  - Adopt a naming convention like BEM (Block, Element, Modifier) or RSCSS (Reasonable System for CSS Stylesheet Structure).
  - Avoid Bootstrap's default naming conventions when creating new components.

### 2.4. Avoid Helper Classes

- **Rationale:** Overuse of helper classes (e.g., `.pull-left`, `.text-right`) can lead to tightly coupled and inflexible code.
- **Implementation:**
  - Refactor helper classes into new, semantic components.
  - Use components to encapsulate specific layout or styling patterns.

### 2.5. Restyling Bare Elements

- **Rationale:** Avoid styling bare HTML elements (e.g., `h2`, `h3`) with anything other than fonts and margins to prevent unexpected style inheritance and conflicts.
- **Implementation:**
  - If you need control over the styles to display documents (like Markdown descriptions), create a prefixed component.
  - This will prevent creeping of your own rules into components that rely on these elements to have default styling.

## 3. CSS Best Practices

### 3.1. Variable Overrides

- **Rationale:** Customize Bootstrap by overriding its default variables.
- **Implementation:**
  - Override key variables like `$font-family-sans-serif`, `$font-size-base`, and `$brand-primary`.
  - Refer to Bootstrap's `_variables.scss` file for a complete list of customizable variables.

### 3.2. Use Autoprefixer

- **Rationale:** Automate vendor prefixing with Autoprefixer instead of relying on Bootstrap's CSS3 mixins.
- **Implementation:**
  - Integrate Autoprefixer into your build process.
  - Write standard CSS properties without prefixes, and Autoprefixer will add the necessary prefixes based on browser support.

### 3.3 Media Queries

- **Rationale:** Use Bootstrap variables for media queries to maintain consistency with the framework's breakpoints.
- **Implementation:**
  - Utilize variables like `$screen-sm-min`, `$screen-md-min`, and `$screen-lg-min` in your media queries.

## 4. JavaScript Best Practices

### 4.1. Modularity and Customization

- **Recommendation:** Avoid modifying Bootstrap's core JavaScript files directly. Instead, extend or customize functionality by writing your own JavaScript modules.

### 4.2. Event Handling

- **Recommendation:** Use event delegation for improved performance, especially when dealing with dynamically added elements.

## 5. Performance Considerations

### 5.1. Minimize CSS and JavaScript

- **Rationale:** Reduce file sizes to improve page load times.
- **Implementation:**
  - Minify CSS and JavaScript files in your production build.
  - Use tools like UglifyJS or Terser for JavaScript minification.

### 5.2. Optimize Images

- **Rationale:** Optimize images to reduce file sizes without sacrificing quality.
- **Implementation:**
  - Use image optimization tools like ImageOptim or TinyPNG.
  - Choose the appropriate image format (e.g., WebP, JPEG, PNG) based on the image content.

### 5.3. Lazy Loading

- **Rationale:** Improve initial page load time by loading images and other assets only when they are visible in the viewport.
- **Implementation:**
  - Use a lazy loading library like Lozad.js or native browser lazy-loading.

## 6. Security Best Practices

### 6.1. Sanitize User Input

- **Rationale:** Prevent cross-site scripting (XSS) attacks by sanitizing user input before rendering it in the DOM.
- **Implementation:**
  - Use a library like DOMPurify to sanitize HTML content.

### 6.2. Prevent Clickjacking

- **Rationale:** Protect against clickjacking attacks by setting the `X-Frame-Options` HTTP header.

## 7. Testing Approaches

### 7.1. Unit Testing

- **Recommendation:** Write unit tests for your custom JavaScript components to ensure they function correctly.

### 7.2. End-to-End Testing

- **Recommendation:** Use end-to-end testing frameworks like Cypress or Puppeteer to test the entire user flow and ensure that all components work together seamlessly.

## 8. Common Pitfalls and Gotchas

### 8.1. Conflicts with Third-Party Libraries

- **Issue:** Bootstrap's CSS and JavaScript can sometimes conflict with other libraries.
- **Solution:** Use CSS prefixes or namespaces to avoid conflicts. Load Bootstrap's CSS before other libraries.

### 8.2. Overriding Bootstrap Styles

- **Issue:** Incorrectly overriding Bootstrap's styles can lead to unexpected behavior.
- **Solution:** Use CSS specificity carefully. Inspect the original Bootstrap styles before overriding them.

## 9. Tooling and Environment

### 9.1. Package Manager

- **Recommendation:** Use npm or Yarn to manage dependencies.

### 9.2. Build Tool

- **Recommendation:** Use a build tool like Webpack or Parcel to bundle and optimize assets.

### 9.3. Code Editor

- **Recommendation:** Use a code editor with linting and auto-formatting capabilities (e.g., VS Code with ESLint and Prettier).

By following these best practices, you can build robust, maintainable, and performant Bootstrap-based applications.
create table alipay_payment
(
    id                    int auto_increment comment 'ID'
        primary key,
    create_time           datetime               not null comment '下单时间',
    type                  int                    not null comment '类型:1=门诊,2=住院',
    openid                varchar(30) default '' not null comment 'openid',
    patient_id            varchar(32)            null comment '患者ID',
    id_card_no            varchar(18)            null comment '身份证号',
    jz_card_no            varchar(50) default '' not null comment '就诊卡号',
    zhuyuan_no            varchar(32)            null comment '住院号',
    out_trade_no          varchar(32) default '' not null comment '商户订单号',
    total_amount          int                    not null comment '订单金额/分',
    trade_no              varchar(32)            null comment '支付宝订单号',
    trade_status          varchar(200)           null comment '支付宝交易状态描述',
    his_receipt_no        varchar(32)            null comment 'HIS收据号',
    his_trade_status      varchar(500)           null comment 'HIS交易状态描述',
    his_success_time      datetime               null comment '充值到账时间',
    exrefund              int                    null comment '已退费/分',
    unrefund              int                    null comment '未退费/分',
    manual_recharge_state int(1)                 null comment '手动充值状态:0=未发起,1=待放行,2=已放行',
    remark                varchar(200)           null comment '备注',
    constraint alipay_payment_out_trade_no_uindex
        unique (out_trade_no)
)
    comment '支付宝支付';

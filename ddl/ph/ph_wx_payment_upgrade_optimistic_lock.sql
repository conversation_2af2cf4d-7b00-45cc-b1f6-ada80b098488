-- 为ph_wx_payment表添加乐观锁支持
-- 执行时间：2024-01-xx
-- 说明：添加版本号字段和更新时间字段，用于乐观锁并发控制

-- 添加版本号字段
ALTER TABLE ph_wx_payment ADD COLUMN version BIGINT DEFAULT 0 COMMENT '版本号（乐观锁）';

-- 添加更新时间字段
ALTER TABLE ph_wx_payment ADD COLUMN update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间';

-- 为现有数据初始化版本号
UPDATE ph_wx_payment SET version = 0 WHERE version IS NULL;

-- 为现有数据初始化更新时间
UPDATE ph_wx_payment SET update_time = create_time WHERE update_time IS NULL;

-- 添加索引以优化查询性能
CREATE INDEX idx_ph_wx_payment_version ON ph_wx_payment(version);
CREATE INDEX idx_ph_wx_payment_update_time ON ph_wx_payment(update_time);

-- 验证数据完整性
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN version IS NOT NULL THEN 1 END) as version_not_null,
    COUNT(CASE WHEN update_time IS NOT NULL THEN 1 END) as update_time_not_null
FROM ph_wx_payment; 
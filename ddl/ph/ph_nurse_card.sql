create table ph_nurse_card
(
    id                 int auto_increment comment 'ID'
        primary key,
    zhuyuan_no         varchar(16)  default '' not null comment '住院号',
    department         varchar(32)  default '' not null comment '病区',
    bed                varchar(10)  default '' not null comment '床号',
    ruyuan_time        datetime                not null comment '入院时间',
    chuyuan_time       datetime                null comment '出院时间',
    patient_name       varchar(16)  default '' not null comment '患者姓名',
    patient_id         varchar(16)  default '' not null comment '患者ID',
    patient_id_card_no varchar(18)  default '' not null comment '患者身份证号',
    patient_mobile     varchar(11)  default '' not null comment '患者手机号',
    nurse_name         varchar(16)  default '' not null comment '陪护人姓名',
    nurse_id_card_no   varchar(18)  default '' not null comment '陪护人身份证号',
    nurse_mobile       varchar(11)  default '' not null comment '陪护人手机号',
    relationship       smallint                not null comment '是患者的',
    nurse_nat_image    varchar(200)            not null comment '核酸报告',
    nurse_photo        varchar(200)            not null comment '陪护人照片',
    create_time        datetime                not null comment '申请时间',
    cancel_time        datetime                null comment '取消时间',
    audit_time         datetime                null comment '审核时间',
    effective_time     datetime                null comment '生效时间',
    invalid_time       datetime                null comment '作废时间',
    expired_time       datetime                null comment '失效时间',
    status             int          default 0  not null comment '状态',
    audit_remark       varchar(200) default '' not null comment '审核意见'
)
    comment '电子陪护证';


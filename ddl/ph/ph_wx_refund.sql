create table ph_wx_refund
(
    id                  bigint auto_increment comment 'ID'
        primary key,
    create_time         datetime         not null comment '下单时间',
    type                varchar(2)       not null comment '类型:MZ=门诊,ZY=住院',
    openid              varchar(32)      not null comment 'openid',
    patient_no          varchar(20)      null comment '患者索引号',
    jz_card_no          varchar(32)      not null comment '就诊卡号',
    zhuyuan_no          varchar(32)      not null comment '住院号',
    total_amount        bigint           not null comment '总金额',
    amount              bigint           not null comment '订单金额，以分为单位',
    zy_pay_no           varchar(32)      not null comment '掌医充值订单号',
    wx_pay_no           varchar(32)      not null comment '微信充值订单号',
    zy_refund_no        varchar(32)      not null comment '掌医退款订单号',
    wx_refund_no        varchar(32)      null comment '微信退款订单号',
    his_trade_status    varchar(500)     null comment 'HIS交易状态',
    wx_trade_status     varchar(500)     null comment '微信交易状态',
    manual_refund_state int(1) default 0 not null comment '人工退款状态:0=未发起,1=待放行,2=已放行'
)
    comment '退款记录';

create index ph_wx_refund_create_time_index
    on ph_wx_refund (create_time);


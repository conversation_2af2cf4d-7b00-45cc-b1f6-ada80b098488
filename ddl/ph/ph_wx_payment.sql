create table ph_wx_payment
(
    id               bigint auto_increment comment 'ID'
        primary key,
    create_time      datetime       not null comment '下单时间',
    type             varchar(2)     not null comment '类型:MZ=门诊,ZY=住院',
    openid           varchar(50)    not null comment 'openid',
    patient_no       varchar(20)    null comment '患者索引号',
    jz_card_no       varchar(20)    not null comment '就诊卡号',
    zhuyuan_no       varchar(20)    not null comment '住院号',
    zy_pay_no        varchar(32)    not null comment '掌医订单号',
    amount           bigint         not null comment '订单金额，以分为单位',
    exrefund         int            not null comment '已退款金额，以分为单位',
    unrefund         int            null comment '未退款金额',
    wx_pay_no        varchar(32)    null comment '微信订单号',
    wx_trade_status  varchar(500)   null comment '微信交易状态',
    his_trade_status varchar(500)   null comment 'HIS交易状态',
    his_receipt_no   varchar(36)    null comment 'HIS收据号',
    balance          decimal(12, 2) null comment '余额',
    manual_pay_state int            not null comment '人工充值状态:0=未发起,1=待放行,2=已放行',
    remark           varchar(500)   null comment '备注',
    prescription_nos  varchar(500)    null comment '处方号',
    client_type  tinyint default 1 not null comment '客户端类型:0=微信小程序,1=微信公众号'
)
    comment '充值记录';

create index ph_wx_payment_create_time_index
    on ph_wx_payment (create_time);

create index ph_wx_payment_jz_card_no_index
    on ph_wx_payment (jz_card_no);

create index ph_wx_payment_wx_pay_no_index
    on ph_wx_payment (wx_pay_no);

create index ph_wx_payment_zy_pay_no_index
    on ph_wx_payment (zy_pay_no);


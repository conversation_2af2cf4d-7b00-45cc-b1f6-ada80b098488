create table ph.psc_order
(
    id                 int auto_increment comment 'ID',
    admission_number   varchar(16)            not null comment '住院号',
    patient_id         varchar(16)            not null comment '患者ID',
    patient_name       varchar(36)            not null comment '患者姓名',
    patient_sex        varchar(2)             not null comment '患者性别',
    patient_nation     varchar(2)             not null comment '患者民族',
    patient_birthday   date                   null comment '患者出生日期',
    patient_mobile     varchar(20)            not null comment '患者手机号',
    patient_department varchar(36) default '' not null comment '科室',
    patient_bed_number varchar(10) default '' not null comment '床号',
    service_type       varchar(3)             not null comment '服务类型',
    exam_items         varchar(200)           not null comment '检查项目',
    exam_items_count   int         default 1  not null comment '检查项目数量',
    nurse_employee_no  varchar(10)            not null comment '护士工号',
    nurse_name         varchar(36)            not null comment '护士姓名',
    nurse_mobile       varchar(11)            not null comment '护士手机号',
    carer_employee_no  varchar(10) default '' null comment '陪检工号',
    carer_name         varchar(36) default '' not null comment '陪检姓名',
    carer_mobile       varchar(11) default '' not null comment '陪检手机号',
    status             int                    not null comment '状态：0=待分配陪检，1=待陪检接单，2=服务已就绪，3=正在服务，4=待护士验收，99=服务结束',
    create_time        datetime               not null comment '创建时间',
    assign_time        datetime               null comment '派发时间',
    take_time          datetime               null comment '接单时间',
    start_time         datetime               null comment '开始时间',
    end_time           datetime               null comment '结束时间',
    confirm_time       datetime               null comment '验收时间',
    score              int                    null comment '评价',
    constraint psc_order_pk
        primary key (id)
)
    comment '订单';

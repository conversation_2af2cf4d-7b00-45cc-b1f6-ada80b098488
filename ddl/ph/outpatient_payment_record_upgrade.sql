-- 门诊缴费记录表升级脚本
-- 调整表结构：删除patientId，添加患者卡号和姓名字段，增加HIS响应记录字段

-- 检查表是否存在
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'outpatient_payment_record');

-- 添加新字段
ALTER TABLE outpatient_payment_record 
ADD COLUMN IF NOT EXISTS patient_card_no VARCHAR(50) COMMENT '患者卡号' AFTER id,
ADD COLUMN IF NOT EXISTS patient_name VARCHAR(100) COMMENT '患者姓名' AFTER patient_card_no,
ADD COLUMN IF NOT EXISTS his_response_code VARCHAR(50) COMMENT 'HIS响应代码',
ADD COLUMN IF NOT EXISTS his_response_message TEXT COMMENT 'HIS响应消息',
ADD COLUMN IF NOT EXISTS his_response_data JSON COMMENT 'HIS响应完整数据',
ADD COLUMN IF NOT EXISTS error_details TEXT COMMENT '错误详情';

-- 添加新索引
ALTER TABLE outpatient_payment_record 
ADD INDEX IF NOT EXISTS idx_patient_card_no (patient_card_no),
ADD INDEX IF NOT EXISTS idx_patient_name (patient_name);

-- 如果patient_id字段存在，可以选择性删除（需要确认数据迁移完成后执行）
-- ALTER TABLE outpatient_payment_record DROP COLUMN IF EXISTS patient_id;
-- ALTER TABLE outpatient_payment_record DROP INDEX IF EXISTS idx_patient_id; 
create table ph_material
(
    id          int auto_increment comment 'ID'
        primary key,
    no          varchar(10) default '' not null comment '编码',
    name        varchar(64)            not null comment '名称',
    pinyin_code varchar(64)            not null comment '拼音码',
    type_code   varchar(20)            not null comment '类型编码',
    type_name   varchar(20)            not null comment '类型名称',
    unit        varchar(10)            not null comment '单位',
    price       decimal(10, 2)         not null comment '价格'
)
    comment '材料';

create index ph_material_no_index
    on ph_material (no);


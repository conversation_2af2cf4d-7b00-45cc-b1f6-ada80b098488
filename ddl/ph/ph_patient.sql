create table ph_patient
(
    id           bigint unsigned auto_increment
        primary key,
    client_type  smallint(1)  default 0  not null comment '客户端类型:0=微信,1=支付宝',
    id_card_no   varchar(36)  default '' not null comment '身份证号',
    patient_no   varchar(36)             not null comment '患者索引号',
    jz_card_No   varchar(36)  default '' not null comment '就诊卡号',
    menzhen_no   varchar(20)  default '' null comment '门诊号',
    zhuyuan_no   varchar(20)  default '' not null comment '住院号',
    name         varchar(50)  default '' not null comment '姓名',
    gender       int(1)                  not null comment '性别：0=男，1=女',
    mobile       varchar(20)  default '' not null comment '手机号',
    open_id      varchar(36)  default '' not null,
    union_id     varchar(36)  default '' not null,
    create_time  datetime                not null comment '创建时间',
    active       tinyint(1)              not null comment '是否默认卡',
    empi         varchar(100) default '' null comment '电子健康卡主索引号',
    erhc_card_no varchar(100) default '' null comment '电子健康卡号',
    ruyuan_time  datetime                null comment '入院时间',
    chuyuan_time datetime                null comment '出院时间'
)
    comment '就诊人';

create index patient_jz_card_no_index
    on ph_patient (jz_card_No);

create index patient_open_id_index
    on ph_patient (open_id);

create index ph_patient_id_card_no_index
    on ph_patient (id_card_no);


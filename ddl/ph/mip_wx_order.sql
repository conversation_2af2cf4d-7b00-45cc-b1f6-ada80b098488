create table mip_wx_order
(
    id                          int auto_increment comment 'ID'
        primary key,
    user_name                   varchar(30)             not null comment '用户姓名',
    user_card_no                varchar(30)             not null comment '用户证件号码',
    user_card_type              varchar(2)              not null comment '用户证件类型',
    pay_auth_no                 varchar(100)            not null comment '医保授权码',
    longitude_latitude          varchar(50)             not null comment '经纬度',
    pay_order_id                varchar(100)            null comment '支付订单号',
    his_order_id                varchar(100)            null comment 'HIS订单号',
    order_status                varchar(10)             null comment '订单状态',
    fee_sum_amount              decimal(12, 2)          null comment '费用总额',
    own_pay_amount              decimal(12, 2)          null comment '现金支付金额',
    personal_account_pay_amount decimal(12, 2)          null comment '个人账户支出',
    fund_pay_amount             decimal(12, 2)          null comment '医保基金支出',
    hospital_out_trade_no       varchar(100)            null comment '第三方服务商订单号',
    med_transaction_id          varchar(100)            null comment '诊疗单id',
    hosp_out_refund_no          varchar(120) default '' null comment '医院退款订单号',
    med_refund_id               varchar(120) default '' null comment '微信生成的退款医疗订单号',
    cash_refund_id              varchar(64)  default '' null comment '微信支付退款订单号',
    insurance_refund_id         varchar(120) default '' null comment '微信医保支付生成的医保退款单号'
)
    comment '微信医保订单';

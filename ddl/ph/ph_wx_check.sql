create table ph_wx_check
(
    id                 int            not null comment 'ID'
        primary key,
    wx_pay_amount      decimal(12, 2) null comment '微信支付总金额',
    his_pay_amount     decimal(12, 2) null comment 'HIS充值总金额',
    diff_pay_amount    decimal(12, 2) null comment '支付差额',
    pay_balanced       tinyint(1)     null comment '支付平账？',
    wx_refund_amount   decimal(12, 2) null comment '微信退款总金额',
    his_refund_amount  decimal(12, 2) null comment 'HIS退款总金额',
    diff_refund_amount decimal(12, 2) null comment '退款差额',
    refund_balanced    tinyint(1)     null comment '退款平账？',
    wx_net_in          decimal(12, 2) null comment '微信净流入',
    his_net_in         decimal(12, 2) null comment 'HIS净流入',
    remark             text           null comment '备注'
)
    comment '对账';


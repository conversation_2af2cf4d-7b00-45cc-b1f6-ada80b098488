CREATE TABLE `card_refund` (
    `id` VARCHAR(64) NOT NULL COMMENT '主键',
    `open_id` VARCHAR(128) NOT NULL COMMENT '用户ID',
    `patient_id_card_attachment_id` VARCHAR(64) NOT NULL COMMENT '申报患者身份证附件ID',
    `patient_id_card_no` CHAR(18) NOT NULL COMMENT '申报患者身份证号',
    `patient_name` VARCHAR(50) NOT NULL COMMENT '申报患者姓名',
    `patient_gender` VARCHAR(10) DEFAULT NULL COMMENT '申报患者性别',
    `patient_birth_date` DATE DEFAULT NULL COMMENT '申报患者出生日期',
    `patient_mobile` CHAR(11) NOT NULL COMMENT '申报患者手机号',
    `agent_flag` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否代办 0-否 1-是',
    `agent_name` VARCHAR(50) DEFAULT NULL COMMENT '代办人姓名',
    `agent_id_card_no` CHAR(18) DEFAULT NULL COMMENT '代办人身份证号',
    `agent_id_card_attachment_id` VARCHAR(64) DEFAULT NULL COMMENT '代办人身份证附件ID',
    `agent_mobile` CHAR(11) DEFAULT NULL COMMENT '代办人手机号',
    `agent_relation` VARCHAR(20) DEFAULT NULL COMMENT '代办人与患者关系',
    `bank_card_attachment_id` VARCHAR(64) NOT NULL COMMENT '银行卡附件ID',
    `bank_card_no` VARCHAR(32) NOT NULL COMMENT '银行卡号',
    `status` VARCHAR(20) NOT NULL COMMENT '申报状态：DRAFT-草稿, SUBMITTED-已提交, APPROVED-已通过, REJECTED-已驳回, RESUBMITTED-已重新提交',
    `submit_time` DATETIME DEFAULT NULL COMMENT '提交时间',
    `approve_time` DATETIME DEFAULT NULL COMMENT '审批时间',
    `reject_reason` TEXT DEFAULT NULL COMMENT '驳回原因',
    `correction_advice` TEXT DEFAULT NULL COMMENT '整改意见',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    INDEX `idx_submit_time` (`submit_time`),
    INDEX `idx_patient_id_card_no` (`patient_id_card_no`),
    INDEX `idx_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='慢病卡退费申请表';

-- 为 medical_record_application 表添加费用和快递相关字段
-- 创建时间：2025-01-10
-- 说明：支持病历复印申请的费用管理和快递追踪功能

-- 添加复印费用字段
ALTER TABLE medical_record_application 
ADD COLUMN copy_fee DECIMAL(10,2) NULL COMMENT '复印费用（元）';

-- 添加快递费用字段  
ALTER TABLE medical_record_application 
ADD COLUMN shipping_fee DECIMAL(10,2) NULL COMMENT '快递费用（元）';

-- 添加总费用字段
ALTER TABLE medical_record_application 
ADD COLUMN total_fee DECIMAL(10,2) NULL COMMENT '总费用（元）';

-- 添加快递单号字段
ALTER TABLE medical_record_application 
ADD COLUMN tracking_number VARCHAR(50) NULL COMMENT '快递单号';

-- 添加支付时间字段
ALTER TABLE medical_record_application 
ADD COLUMN pay_time DATETIME NULL COMMENT '支付时间';

-- 添加邮寄时间字段
ALTER TABLE medical_record_application 
ADD COLUMN shipping_time DATETIME NULL COMMENT '邮寄时间';

-- 为快递单号字段创建索引（用于快递查询）
CREATE INDEX idx_medical_record_application_tracking_number 
ON medical_record_application(tracking_number);

-- 为支付时间字段创建索引（用于支付统计）
CREATE INDEX idx_medical_record_application_pay_time 
ON medical_record_application(pay_time);

-- 为邮寄时间字段创建索引（用于邮寄统计）
CREATE INDEX idx_medical_record_application_shipping_time 
ON medical_record_application(shipping_time); 
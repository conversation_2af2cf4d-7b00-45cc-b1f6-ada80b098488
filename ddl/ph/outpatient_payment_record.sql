-- 门诊缴费记录表
-- 用于记录智能门诊缴费的详细信息

CREATE TABLE outpatient_payment_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    patient_card_no VARCHAR(50) NOT NULL COMMENT '患者卡号',
    patient_name VARCHAR(100) NOT NULL COMMENT '患者姓名',
    fee_details JSON NOT NULL COMMENT '完整费用信息JSON',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '缴费总额',
    payment_method VARCHAR(20) NOT NULL COMMENT '支付方式：CARD_BALANCE',
    his_trade_no VARCHAR(100) COMMENT 'HIS系统交易号',
    his_response_code VARCHAR(50) COMMENT 'HIS响应代码',
    his_response_message TEXT COMMENT 'HIS响应消息',
    his_response_data JSON COMMENT 'HIS响应完整数据',
    error_details TEXT COMMENT '错误详情',
    status TINYINT DEFAULT 1 COMMENT '状态：1=成功，2=失败',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_patient_card_no (patient_card_no),
    INDEX idx_patient_name (patient_name),
    INDEX idx_created_time (created_time),
    INDEX idx_his_trade_no (his_trade_no)
) COMMENT='门诊缴费记录表'; 
-- 卡片清退附件表
CREATE TABLE `card_refund_attachment` (
    `id` VARCHAR(36) NOT NULL COMMENT '附件ID（UUID）',
    `registration_id` VARCHAR(255) DEFAULT NULL COMMENT '清退登记ID',
    `open_id` VARCHAR(255) NOT NULL COMMENT '用户OpenID',
    `type` VARCHAR(20) NOT NULL COMMENT '附件类型：ID_CARD-身份证，BANK_CARD-银行卡',
    `file_name` VARCHAR(500) NOT NULL COMMENT '文件名',
    `file_url` VARCHAR(1000) NOT NULL COMMENT '文件存储路径',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_registration_id` (`registration_id`),
    INDEX `idx_open_id` (`open_id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡片清退附件表'; 
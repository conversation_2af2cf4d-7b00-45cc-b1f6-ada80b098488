create table ph_yuyue
(
    id               bigint auto_increment comment 'ID'
        primary key,
    operation_type   int(1)                 not null comment '操作类型:0=预约,1=取消',
    operation_time   datetime               not null comment '操作时间',
    id_card_no       varchar(18)            not null comment '身份证号',
    jz_card_no       varchar(20) default '' not null comment '就诊卡号',
    name             varchar(20)            not null comment '姓名',
    mobile           varchar(20)            not null comment '手机号',
    jz_date          date                   not null comment '就诊日期',
    jz_time          datetime               null comment '就诊时间',
    ampm             int(1)                 not null comment '上午下午:1=上午,2=下午',
    department_code  varchar(20)            not null comment '科室编码',
    doctor_code      varchar(20)            not null comment '医生编码',
    operation_result int(1)                 not null comment '操作结果:0=成功,1=失败',
    operation_desc   varchar(200)           not null comment '操作描述'
)
    comment '预约记录';


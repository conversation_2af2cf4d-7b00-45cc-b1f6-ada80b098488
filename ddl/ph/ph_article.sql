create table ph_article
(
    id            bigint auto_increment comment 'ID'
        primary key,
    category_id   bigint                 not null comment '栏目',
    category_name varchar(50)            not null comment '栏目',
    title         varchar(50)            not null comment '标题',
    summary       varchar(200)           not null comment '摘要',
    content       text                   not null comment '内容',
    create_by     varchar(64) default '' not null comment '创建人',
    create_time   datetime               not null comment '创建时间',
    update_by     varchar(64) default '' not null comment '更新人',
    update_time   datetime               not null comment '更新时间'
)
    comment '文章';


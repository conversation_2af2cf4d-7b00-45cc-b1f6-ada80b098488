create table ph_doctor
(
    id            varchar(36)              not null comment 'ID'
        primary key,
    department_id varchar(10)              not null comment '科室ID',
    name          varchar(50)              not null comment '姓名',
    sex           int(1)                   not null comment '性别:0=男,1=女,2=未知',
    title         varchar(20)   default '' not null comment '职称',
    position      varchar(30)              null comment '职务',
    reg_type      varchar(20)              null comment '挂号类型',
    reg_fee       double                   null comment '挂号费',
    keyword       varchar(30)              null comment '关键词',
    expertise     varchar(2000) default '' null comment '擅长',
    intro         varchar(2000) default '' null comment '简介',
    photo         varchar(100)  default '' null comment '照片',
    sort_no       int           default 0  not null comment '排序号',
    simple_pinyin varchar(100)             null,
    full_pinyin   varchar(300)             null
)
    comment '医生';

create index doctor_departmentId_index
    on ph_doctor (department_id);


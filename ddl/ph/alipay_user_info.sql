create table alipay_user_info
(
    id           varchar(20)             not null comment 'ID'
        primary key,
    type         varchar(1)   default '' not null comment '类型:1=公司,2=个人',
    is_certified varchar(1)   default '' not null comment '是否通过实名认证:T=已认证,F=未认证。',
    cert_type    varchar(3)   default '' not null comment '证件类型',
    cert_no      varchar(64)  default '' not null comment '证件号码',
    user_name    varchar(36)  default '' not null comment '姓名',
    nick_name    varchar(36)  default '' not null comment '昵称',
    avatar       varchar(100) default '' not null comment '头像地址',
    email        varchar(64)  default '' not null comment '电子邮件',
    mobile       varchar(11)  default '' not null comment '手机号',
    gender       varchar(1)   default '' not null comment '性别:M=男,F=女',
    province     varchar(10)  default '' not null comment '省份',
    city         varchar(20)  default '' not null comment '市',
    area         varchar(100) default '' not null comment '区县',
    address      varchar(100) default '' not null comment '详细地址',
    status       varchar(1)   default '' not null comment '状态:Q=快速注册用户,T=正常,B=已冻结,W=已注册未激活'
)
    comment '支付宝用户信息';

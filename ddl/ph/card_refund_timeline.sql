CREATE TABLE `card_refund_timeline` (
    `id` VARCHAR(64) NOT NULL COMMENT '主键',
    `registration_id` VARCHAR(64) NOT NULL COMMENT '申报记录ID',
    `event_type` VARCHAR(30) NOT NULL COMMENT '事件类型',
    `event_description` VARCHAR(500) DEFAULT NULL COMMENT '事件描述',
    `operator_type` VARCHAR(20) NOT NULL COMMENT '操作者类型',
    `operator_name` VARCHAR(50) DEFAULT NULL COMMENT '操作者姓名',
    `old_status` VARCHAR(20) DEFAULT NULL COMMENT '变更前状态',
    `new_status` VARCHAR(20) DEFAULT NULL COMMENT '变更后状态',
    `event_time` DATETIME NOT NULL COMMENT '事件时间',
    `additional_data` JSON DEFAULT NULL COMMENT '额外数据（JSON格式）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY <PERSON>EY (`id`),
    INDEX `idx_registration_id` (`registration_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='慢病卡退费申请时间线表';

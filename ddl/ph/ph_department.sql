create table ph_department
(
    id            varchar(10)             not null comment '编号'
        primary key,
    name          varchar(50)             not null comment '名称',
    parent_id     varchar(10)             not null comment '科室分类编码',
    parent_name   varchar(10)             not null comment '科室分类名称',
    intro         text                    null comment '简介',
    address       varchar(100) default '' null comment '位置',
    logo          varchar(100)            null comment '图标',
    telephone     varchar(20)             null comment '联系电话',
    keyword       varchar(20)             null comment '关键词',
    sort_no       int          default 0  null comment '排序号',
    simple_pinyin varchar(50)  default '' not null comment '简拼',
    full_pinyin   varchar(250) default '' not null comment '全拼'
)
    comment '科室';


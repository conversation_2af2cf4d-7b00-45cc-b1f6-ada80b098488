create table alipay_refund
(
    id                  int auto_increment comment 'ID'
        primary key,
    create_time         datetime                not null comment '下单时间',
    type                varchar(2)              not null comment '类型:1=门诊,2=住院',
    openid              varchar(32)  default '' not null comment '支付宝用户ID',
    patient_id          varchar(20)  default '' null comment '患者索引号',
    jz_card_no          varchar(32)  default '' not null comment '就诊卡号',
    zhuyuan_no          varchar(32)  default '' not null comment '住院号',
    total_amount        bigint                  not null comment '总金额',
    amount              bigint                  not null comment '订单金额',
    out_trade_no        varchar(32)             not null comment '掌医充值订单号',
    trade_no            varchar(32)             not null comment '支付宝充值订单号',
    out_refund_no       varchar(32)  default '' not null comment '掌医退款订单号',
    his_trade_status    varchar(500) default '' null comment 'HIS交易状态',
    his_trade_no        varchar(16)  default '' not null comment 'HIS交易单号',
    alipay_trade_status varchar(500) default '' null comment '支付宝交易状态',
    alipay_trade_time   datetime                null comment '支付宝交易时间',
    manual_refund_state int(1)       default 0  not null comment '人工退款状态:0=未发起,1=待放行,2=已放行'
)
    comment '支付宝退款记录';

create index alipay_refund_create_time_index
    on alipay_refund (create_time);

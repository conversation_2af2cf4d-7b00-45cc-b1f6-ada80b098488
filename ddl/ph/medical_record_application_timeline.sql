-- 病历复印时间线表
DROP TABLE IF EXISTS `medical_record_application_timeline`;

CREATE TABLE `medical_record_application_timeline` (
  `id` varchar(36) NOT NULL COMMENT '主键，UUID格式',
  `application_id` varchar(36) NOT NULL COMMENT '申请记录ID',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型',
  `event_description` varchar(500) DEFAULT NULL COMMENT '事件描述',
  `operator_type` varchar(20) NOT NULL COMMENT '操作者类型',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作者姓名',
  `old_status` varchar(30) DEFAULT NULL COMMENT '变更前状态',
  `new_status` varchar(30) DEFAULT NULL COMMENT '变更后状态',
  `event_time` datetime NOT NULL COMMENT '事件时间',
  `additional_data` json DEFAULT NULL COMMENT '额外数据（JSON格式），如驳回原因、附件信息等',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_application_id` (`application_id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_event_time` (`event_time`),
  KEY `idx_operator_type` (`operator_type`),
  KEY `idx_application_event_time` (`application_id`, `event_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='病历复印申请时间线记录表'; 
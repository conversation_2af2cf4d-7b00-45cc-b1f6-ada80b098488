create table ph_tijian_yuyue
(
    id               bigint auto_increment comment 'ID'
        primary key,
    operation_type   int(1)                 not null comment '操作类型:1=预约,2=取消',
    operation_time   datetime               not null comment '操作时间',
    id_card_no       varchar(18)            not null comment '身份证号',
    name             varchar(20)            not null comment '姓名',
    mobile           varchar(20)            not null comment '手机号',
    marital_status   int                    not null comment '婚姻状况:1=已婚,2=未婚',
    nation           varchar(10) default '' not null comment '民族',
    profession       varchar(20) default '' not null comment '职业',
    area             varchar(50) default '' not null comment '所在地区',
    address          varchar(50) default '' not null comment '详细住址',
    date             date                   not null comment '日期',
    time             varchar(20) default '' not null comment '时间',
    package_id       varchar(10)            not null comment '套餐编码',
    package_name     varchar(50)            not null comment '套餐名称',
    operation_result int(1)                 not null comment '操作结果:0=成功,1=失败',
    operation_desc   varchar(200)           not null comment '操作描述',
    reservation_id   varchar(10) default '' null comment '预约号',
    status           int         default 0  not null comment '状态:0=预约成功,8=预约失败,9=已取消'
)
    comment '预约记录';


# 智能门诊缴费功能部署说明

## 概述

智能门诊缴费功能已按照设计方案完成实现，提供了更智能的缴费体验：
- 优先使用就诊卡余额直接缴费
- 余额不足时自动计算差额，创建微信支付订单
- 支持重复调用，无需复杂的幂等性检查
- 完整的费用信息记录和审计功能

## 数据库变更

### 1. 执行SQL脚本

按顺序执行以下SQL脚本：

```bash
# 1. 增强微信支付表
mysql -u用户名 -p数据库名 < ddl/ph/wx_payment_enhancement.sql

# 2. 创建缴费记录表（新部署）
mysql -u用户名 -p数据库名 < ddl/ph/outpatient_payment_record.sql

# 3. 如果表已存在，执行升级脚本（已有部署）
mysql -u用户名 -p数据库名 < ddl/ph/outpatient_payment_record_upgrade.sql
```

### 2. 数据库变更说明

**wx_payment表新增字段：**
- `fee_details`: JSON格式的费用详情
- `total_fee`: 费用总额
- `card_balance_at_time`: 下单时就诊卡余额
- `payment_type`: 支付类型（1=旧版充值，2=智能缴费）
- `patient_id`: 患者ID

**新增outpatient_payment_record表：**
- 记录所有智能缴费的详细信息
- 存储完整的费用信息JSON
- 支持审计和查询功能
- **患者信息存储优化**：
  - `patient_card_no`: 患者卡号（替代patientId，更稳定）
  - `patient_name`: 患者姓名（便于查询识别）
- **新增HIS响应记录字段**：
  - `his_response_code`: HIS响应代码
  - `his_response_message`: HIS响应消息
  - `his_response_data`: HIS响应完整数据（JSON格式）
  - `error_details`: 错误详情
- **无论缴费成功失败都会保存记录**，便于问题排查和数据分析
- **数据安全设计**：不提供删除功能，确保审计数据完整性

## API接口说明

### 新增接口

#### 1. 智能门诊缴费
**接口：** `POST /api/outpatient/intelligentPay`

**请求参数：**
```json
{
  "feeNos": ["处方号1", "处方号2", "处方号3"]
}
```

**响应结果：**

**情况1：余额充足，直接缴费成功**
```json
{
  "code": 200,
  "msg": "缴费成功",
  "data": {
    "paymentType": "DIRECT_PAY",
    "paymentRecord": {
      "id": 123,
      "patientId": 456,
      "totalAmount": 50.00,
      "paymentMethod": "CARD_BALANCE",
      "hisTradeNo": "HIS123456",
      "status": 1
    }
  }
}
```

**情况2：余额不足，需要微信支付**
```json
{
  "code": 200,
  "msg": "需要补充充值",
  "data": {
    "paymentType": "NEED_RECHARGE",
    "outOrderNo": "WX123456789",
    "wxPayment": {
      "id": 789,
      "zyPayNo": "WX123456789",
      "amount": 2000,
      "totalFee": 50.00
    },
    "wxPayParams": {
      "timeStamp": "**********",
      "nonceStr": "abc123",
      "package": "prepay_id=wx123",
      "signType": "RSA",
      "paySign": "signature123"
    }
  }
}
```

**情况3：处理失败**
```json
{
  "code": 500,
  "msg": "无有效的待缴费项目"
}
```

### 废弃接口

以下接口已标记为废弃，建议迁移到新接口：

- `POST /api/outpatient/wxpay` ➜ `POST /api/outpatient/intelligentPay`
- `POST /api/outpatient/refreshWxPayOrder` ➜ `POST /api/outpatient/intelligentPay`

## 缴费记录增强

### HIS接口响应完整记录

系统现在会完整记录HIS缴费接口的调用结果，包括：

1. **成功场景记录**：
   - HIS响应代码和消息
   - 完整的HIS响应数据（JSON格式）
   - 缴费状态设为成功（status=1）

2. **失败场景记录**：
   - HIS错误响应信息
   - 异常详情（包含异常类型和堆栈信息）
   - 缴费状态设为失败（status=2）

3. **数据分析价值**：
   - 支持缴费成功率统计
   - 便于HIS接口问题排查
   - 提供完整的审计链路

### 记录字段说明

```sql
-- 缴费记录表核心字段
his_response_code VARCHAR(50)    -- HIS返回的业务代码
his_response_message TEXT        -- HIS返回的消息
his_response_data JSON          -- HIS完整响应数据
error_details TEXT              -- 异常详情（仅失败时）
status TINYINT                  -- 1=成功，2=失败
```

## 业务流程

### 智能缴费流程

1. **费用验证**：调用HIS接口验证费用单号有效性
2. **余额查询**：查询患者就诊卡当前余额
3. **智能判断**：
   - 余额 ≥ 费用总额 → 直接缴费
   - 余额 < 费用总额 → 创建微信支付订单（差额部分）
4. **记录保存**：保存完整的缴费信息用于审计

### 微信支付流程（余额不足时）

1. 创建增强版微信支付订单（包含费用详情）
2. 返回微信支付参数给前端
3. 用户完成微信支付后，系统自动：
   - 充值到就诊卡
   - 使用就诊卡余额完成缴费
   - 记录完整的支付链路

## 配置说明

### 必要配置检查

1. **HIS接口配置**：确保WitontekService配置正确
2. **微信支付配置**：确保DongruanPayService配置正确
3. **回调地址**：确认支付回调地址可访问

### 服务依赖

新功能依赖以下Spring Bean：
- `IIntelligentOutpatientPayService`
- `IOutpatientPaymentRecordService`
- `IWxPaymentService`

## 测试建议

### 功能测试

1. **余额充足场景**：
   - 患者卡余额 > 费用总额
   - 验证直接缴费成功
   - 检查缴费记录是否正确保存

2. **余额不足场景**：
   - 患者卡余额 < 费用总额
   - 验证微信支付订单创建成功
   - 完成支付后验证自动缴费流程

3. **异常场景**：
   - 无效费用单号
   - HIS接口异常
   - 网络异常等
   - **验证失败记录**：确认异常情况下也能正确保存缴费记录

4. **缴费记录验证**：
   - 检查成功缴费的HIS响应数据完整性
   - 验证失败场景的错误详情记录
   - 确认status字段正确设置（1=成功，2=失败）

### 压力测试

- 并发调用智能缴费接口
- 验证数据一致性
- 检查系统响应时间

## 运维查询

### 缴费记录查询示例

```sql
-- 查询今日缴费记录统计
SELECT 
    status,
    COUNT(*) as count,
    SUM(total_amount) as total_amount,
    CASE status 
        WHEN 1 THEN '成功' 
        WHEN 2 THEN '失败' 
    END as status_desc
FROM outpatient_payment_record 
WHERE DATE(created_time) = CURDATE() 
GROUP BY status;

-- 查询失败的缴费记录详情
SELECT 
    id,
    patient_card_no,
    patient_name,
    total_amount,
    his_response_code,
    his_response_message,
    error_details,
    created_time
FROM outpatient_payment_record 
WHERE status = 2 
ORDER BY created_time DESC 
LIMIT 10;

-- 查询特定患者的缴费历史（按卡号）
SELECT 
    id,
    patient_name,
    total_amount,
    payment_method,
    his_trade_no,
    status,
    created_time
FROM outpatient_payment_record 
WHERE patient_card_no = ? 
ORDER BY created_time DESC;

-- 按患者姓名模糊查询缴费记录
SELECT 
    id,
    patient_card_no,
    patient_name,
    total_amount,
    payment_method,
    status,
    created_time
FROM outpatient_payment_record 
WHERE patient_name LIKE '%?%' 
ORDER BY created_time DESC 
LIMIT 20;
```

## 监控告警

建议添加以下监控指标：

1. **成功率监控**：智能缴费成功率
2. **响应时间监控**：接口响应时间
3. **异常监控**：HIS接口调用失败率
4. **失败记录监控**：缴费失败次数和失败原因分析
4. **业务监控**：缴费金额统计、支付方式分布

## 回滚方案

如发现问题需要回滚：

1. **接口回滚**：移除@Deprecated注解，恢复旧接口
2. **数据回滚**：新增字段可保留，不影响旧功能
3. **服务回滚**：停用IntelligentOutpatientPayService

## 注意事项

1. **数据一致性**：确保微信支付和HIS接口调用的事务一致性
2. **幂等性**：新接口支持重复调用，无需额外幂等性处理
3. **日志记录**：关键业务节点需要详细日志记录
4. **错误处理**：友好的错误提示，避免技术术语

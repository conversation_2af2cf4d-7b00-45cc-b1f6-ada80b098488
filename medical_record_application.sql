-- 病历申请表
CREATE TABLE medical_record_application (
    -- 主键，使用UUID
    id VARCHAR(64) NOT NULL COMMENT '主键ID',
    
    -- 用户信息
    open_id VARCHAR(64) NOT NULL COMMENT '用户ID（微信OpenID）',
    
    -- 患者信息
    patient_id_card_attachment_id VARCHAR(64) COMMENT '患者身份证附件ID',
    patient_id_card_no VARCHAR(18) COMMENT '患者身份证号',
    patient_birth_certificate_attachment_id VARCHAR(64) COMMENT '患者出生证附件ID',
    patient_name VARCHAR(50) NOT NULL COMMENT '申报患者姓名',
    patient_gender VARCHAR(10) NOT NULL COMMENT '患者性别（男/女）',
    patient_birth_date DATE NOT NULL COMMENT '患者出生日期',
    
    -- 代办信息
    agent_flag TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否代办（0-否，1-是）',
    agent_name VARCHAR(50) COMMENT '代办人姓名',
    agent_id_card_no VARCHAR(18) COMMENT '代办人身份证号',
    agent_id_card_attachment_id VARCHAR(64) COMMENT '代办人身份证附件ID',
    agent_mobile VARCHAR(11) COMMENT '代办人手机号',
    agent_relation VARCHAR(20) COMMENT '代办人与患者关系',
    
    -- 申请状态信息
    status VARCHAR(20) NOT NULL COMMENT '申报状态（DRAFT-草稿，SUBMITTED-已提交，APPROVED-已通过，REJECTED-已驳回，PAID-支付成功，COMPLETED-已完成，DELIVERED-邮寄完成）',
    submit_time DATETIME COMMENT '提交时间',
    approve_time DATETIME COMMENT '审批时间',
    reject_reason TEXT COMMENT '驳回原因',
    correction_advice TEXT COMMENT '整改意见',
    
    -- 系统字段
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    delete_flag TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除，1-已删除）',
    
    -- 主键约束
    PRIMARY KEY (id),
    
    -- 索引
    INDEX idx_open_id (open_id) COMMENT '用户ID索引',
    INDEX idx_patient_id_card (patient_id_card_no) COMMENT '患者身份证号索引',
    INDEX idx_status (status) COMMENT '申报状态索引',
    INDEX idx_create_time (create_time) COMMENT '创建时间索引',
    INDEX idx_submit_time (submit_time) COMMENT '提交时间索引',
    INDEX idx_agent_id_card (agent_id_card_no) COMMENT '代办人身份证号索引',
    INDEX idx_delete_flag (delete_flag) COMMENT '删除标记索引'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='病历申请表';

-- 添加约束检查（MySQL 8.0.16+ 支持）
-- 患者性别约束
ALTER TABLE medical_record_application 
ADD CONSTRAINT chk_patient_gender 
CHECK (patient_gender IN ('男', '女'));

-- 申报状态约束
ALTER TABLE medical_record_application 
ADD CONSTRAINT chk_status 
CHECK (status IN ('DRAFT', 'SUBMITTED', 'APPROVED', 'REJECTED', 'PAID', 'COMPLETED', 'DELIVERED'));

-- 代办标记约束
ALTER TABLE medical_record_application 
ADD CONSTRAINT chk_agent_flag 
CHECK (agent_flag IN (0, 1));

-- 删除标记约束
ALTER TABLE medical_record_application 
ADD CONSTRAINT chk_delete_flag 
CHECK (delete_flag IN (0, 1));

-- 代办人与患者关系约束
ALTER TABLE medical_record_application 
ADD CONSTRAINT chk_agent_relation 
CHECK (agent_relation IS NULL OR agent_relation IN ('FATHER', 'MOTHER', 'HUSBAND', 'WIFE', 'SON', 'DAUGHTER', 'BROTHER', 'SISTER', 'FRIEND', 'COLLEAGUE', 'CLASSMATE', 'OTHER')); 
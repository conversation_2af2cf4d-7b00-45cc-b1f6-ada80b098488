<!-- Generated by SpecStory -->

# 编写接口文档给前端开发人员 (2025-07-24 10:12Z)

_**User**_

为 `PUT /api/medical-record-application/{id}/clear-agent-flag` 接口编写面向前端开发人员的接口文档

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求为 `PUT /api/medical-record-application/{id}/clear-agent-flag` 接口编写面向前端开发人员的接口文档。

让我分析这个接口：

1. HTTP方法：PUT
2. 路径：`/api/medical-record-application/{id}/clear-agent-flag`
3. 功能：清除代理标志（clear agent flag）
4. 需要会话验证（@RequireSession）
5. 是事务性的（@Transactional）
6. 路径参数：id（String类型）
7. 返回类型：ResponseDto<MedicalRecordApplicationDetailResponseDto?>

从代码中可以看出：
- 需要验证用户会话并获取OpenID
- 调用 applicationService.clearAgentFlag(id, openId) 方法
- 返回更新后的病历申请详情

我需要编写一个详细的接口文档，包括：
- 接口基本信息
- 请求参数
- 响应格式
- 错误处理
- 示例

为了更好地理解这个接口，我应该看看相关的 DTO 和服务代码。</details></think>

---

我来为这个接口编写详细的接口文档。首先让我查看相关的DTO和服务代码，以便提供完整准确的文档。

---

<details>
            <summary>Searched codebase "MedicalRecordApplicationDetailResponseDto 响应数据结构" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-103 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L31-70 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L103-134 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationService.java` | L1-36 |
| `src/main/java/space/lzhq/ph/dto/mapper/MedicalRecordApplicationMapper.java` | L1-39 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L1-21 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L311-369 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L1-31 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L74-98 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L463-479 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L369-421 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L421-463 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-27 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L115-190 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L21-74 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L27-115 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L1-63 |
| `src/main/resources/mapper/ph/MedicalRecordApplicationTimelineMapper.xml` | L1-38 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L1-51 |
| `medical_record_application.sql` | L1-41 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L70-116 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L1-36 |

</details>

---

<details>
            <summary>Grep search for "clearAgentFlag" • **9** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L49 | `fun clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L56 | `val updatedRegistration = applicationService.clearAgentFlag(id, openId)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/kotlin/space/lzhq/ph/controller/CardRefundApiController.kt` | L94 | `fun clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/kotlin/space/lzhq/ph/controller/CardRefundApiController.kt` | L101 | `val updatedRegistration = registrationService.clearAgentFlag(id, openId)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationService.java` | L71 | `MedicalRecordApplication clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/ICardRefundService.java` | L76 | `CardRefund clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L302 | `public CardRefund clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L131 | `public MedicalRecordApplication clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L169 | `+    MedicalRecordApplication clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L227 | `+    public MedicalRecordApplication clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L384 | `+    fun clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L392 | `+        val updatedRegistration = applicationService.clearAgentFlag(id, openId)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L407 | `1. 在 `IMedicalRecordApplicationService` 接口中新增了 `clearAgentFlag` 方法` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L467 | `echo "- 新增 IMedicalRecordApplicationService.clearAgentFlag() 方法"` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L490 | `echo "- 新增: IMedicalRecordApplicationService.clearAgentFlag() 接口方法"` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L526 | `- 新增 IMedicalRecordApplicationService.clearAgentFlag() 方法` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L549 | `- 新增: IMedicalRecordApplicationService.clearAgentFlag() 接口方法` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L676 | `echo "- **服务层**: 实现 \`clearAgentFlag()\` 业务逻辑"` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L757 | `- **服务层**: 实现 `clearAgentFlag()` 业务逻辑` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L8 | `The `clearAgentFlag` method records timeline events using a stale `MedicalRecordApplication` object. After the agent flag is cleared in the database, the in-memory object still contains the old agent infor` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L15 | `<think><details><summary>Thought Process</summary>Let me analyze the bug in the `clearAgentFlag` method. Looking at the current code, I can see the issue:` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L46 | `我分析了这个 bug。问题出现在 `clearAgentFlag` 方法中：` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L84 | `原来的代码中，`clearAgentFlag` 方法在清除数据库中的代办标志后，仍然使用旧的 `application` 对象来记录时间线事件。由于这个对象是在数据库更新之前获取的，它仍然包含旧的代办信息，导致 `getOperatorName()` 方法错误地返回代办人姓名而不是患者姓名。` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L49 | `fun clearAgentFlag(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L56 | `val updatedRegistration = applicationService.clearAgentFlag(id, openId)` |

</details>

---

<details>
            <summary>Searched codebase "ResponseDto 通用响应格式结构" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/ResponseDto.java` | L1-40 |
| `src/main/java/space/lzhq/ph/dto/response/BaseResponseDto.java` | L1-18 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/AttachmentDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/dto/response/EnumDefinitionResponseDto.java` | L1-28 |
| `src/main/kotlin/space/lzhq/ph/dto/ReportListDto.kt` | L1-12 |
| `src/main/java/space/lzhq/ph/dto/response/EnumItemDto.java` | L1-26 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/SimpleUploadResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardUploadResponseDto.java` | L1-43 |
| `src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt` | L32-83 |
| `src/main/kotlin/space/lzhq/ph/domain/RechargeResult.kt` | L1-64 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardOcrResultDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-103 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L74-98 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundListResponseDto.java` | L1-111 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundTimelineDto.java` | L1-84 |
| `src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt` | L1-32 |
| `src/main/kotlin/space/lzhq/ph/controller/HospitalizedApiController.kt` | L1-42 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundAdminListResponseDto.java` | L1-113 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L1-21 |
| `src/main/kotlin/space/lzhq/ph/controller/CardRefundApiController.kt` | L26-89 |
| `src/main/kotlin/space/lzhq/ph/dto/IntelligentPayResult.kt` | L1-44 |
| `src/main/java/space/lzhq/ph/domain/OutpatientPaymentRecord.java` | L1-95 |
| `src/main/kotlin/space/lzhq/ph/controller/ReportApiController.kt` | L1-27 |

</details>

---

<think><details><summary>Thought Process</summary>现在我已经收集到了足够的信息来编写接口文档。让我查看clearAgentFlag方法的具体实现来了解它的功能。</details></think>

---

Read file: src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java

---

<details>
            <summary>Searched codebase "MedicalRecordApplicationStatus 枚举定义和描述" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L1-63 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L115-190 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L36-70 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-27 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L1-36 |
| `medical_record_application.sql` | L1-41 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordMailingStatus.java` | L1-44 |
| `medical_record_application.sql` | L41-75 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L27-115 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L1-31 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L242-252 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordMailing.java` | L1-93 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-103 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L102-164 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L122-150 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L1-27 |
| `src/main/java/space/lzhq/ph/domain/NurseCardStatus.java` | L1-37 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L51-102 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/java/space/lzhq/ph/dto/mapper/MedicalRecordApplicationMapper.java` | L1-39 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L70-116 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L71-90 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationService.java` | L1-36 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L1-51 |

</details>

---

<think><details><summary>Thought Process</summary>现在我已经收集了足够的信息来编写完整的接口文档。让我再查看一下clearAgentFlag方法的剩余实现部分，了解完整的业务逻辑。</details></think>

---

Read file: src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java

---

<think><details><summary>Thought Process</summary>现在我已经了解了clearAgentFlag方法的完整实现。让我继续查看getOperatorName方法的实现，以便完整理解接口的功能。</details></think>

---

<details>
            <summary>Grep search for "getOperatorName" • **8** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/domain/MedicalRecordMailing.java` | L313 | `public String getOperatorName() {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/domain/MedicalRecordMailing.java` | L374 | `.append("operatorName", getOperatorName())` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/CardRefundTimelineServiceImpl.java` | L153 | `dto.setOperatorName(timeline.getOperatorName());` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L92 | `private String getOperatorName(CardRefund registration) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L294 | `getOperatorName(registration)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L345 | `getOperatorName(registration)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L417 | `getOperatorName(registration),` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L486 | `getOperatorName(registration)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L650 | `getOperatorName(registration),` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L189 | `dto.setOperatorName(timeline.getOperatorName());` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L123 | `getOperatorName(application)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L177 | `getOperatorName(updatedApplication)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L303 | `private String getOperatorName(MedicalRecordApplication application) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-37Z-git-workflow-for-creating-a-new-branch.md` | L270 | `+                getOperatorName(application)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L19 | `3. But when recording the timeline event, it still uses the old `application` object: `getOperatorName(application)`` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L21 | `Since the `application` object was retrieved before the database update, it still contains the old agent information. The `getOperatorName` method checks if the agent flag is 1 and if there's an agent name, and if so, returns the agent name. But since the agent flag was cleared in the database but not in the in-memory obj` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L23 | `Looking at the `getOperatorName` method:` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L25 | `private String getOperatorName(MedicalRecordApplication application) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L52 | `由于 `application` 对象是在数据库更新之前获取的，它仍然包含旧的代办信息。当调用 `getOperatorName(application)` 时，会错误地返回代办人姓名而不是患者姓名。` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L68 | `- Step 6: We use this fresh `updatedApplication` object when recording the timeline event, ensuring `getOperatorName()` correctly returns the patient's name instead of the agent's name` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_09-56Z-fix-stale-medical-record-application-bug.md` | L84 | `原来的代码中，`clearAgentFlag` 方法在清除数据库中的代办标志后，仍然使用旧的 `application` 对象来记录时间线事件。由于这个对象是在数据库更新之前获取的，它仍然包含旧的代办信息，导致 `getOperatorName()` 方法错误地返回代办人姓名而不是患者姓名。` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/.specstory/history/2025-07-24_04-45Z-%E5%AE%9E%E7%8E%B0-medicalrecordapplicationtimeline-%E6%9C%8D%E5%8A%A1%E6%8E%A5%E5%8F%A3.md` | L436 | `+         dto.setOperatorName(timeline.getOperatorName());` |

</details>

---

<think><details><summary>Thought Process</summary>现在我已经有了足够的信息来编写一个详细的接口文档。基于我收集的信息，我了解到：

1. 这个接口是用来清除病历申请中的代办标志
2. 接口需要进行用户会话验证
3. 接口需要验证用户权限
4. 接口需要验证申请状态是否允许修改
5. 执行清除代办标志操作后，会返回更新后的病历申请详情
6. 还会记录时间线事件

让我编写一个完整的接口文档。</details></think>

---

基于代码分析，我来为这个接口编写详细的接口文档：

# 病历申请代办标志清除接口文档

## 接口基本信息

| 项目 | 内容 |
|------|------|
| **接口名称** | 清除病历申请代办标志 |
| **接口地址** | `PUT /api/medical-record-application/{id}/clear-agent-flag` |
| **HTTP方法** | PUT |
| **Content-Type** | `application/json` |
| **功能描述** | 清除指定病历申请记录的代办标志，将代办申请转换为患者本人申请 |

## 业务说明

本接口用于清除病历申请中的代办人信息，将由代办人申请的病历转换为患者本人直接申请。清除后，所有代办人相关信息（姓名、身份证号、手机号、关系等）将被置空，代办标志将设置为0（非代办）。

## 请求参数

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `id` | String | 是 | 病历申请记录ID，32位UUID格式 |

### 请求头

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `Content-Type` | String | 是 | 固定值：`application/json` |
| `Cookie` | String | 是 | 包含用户会话信息的Cookie |

### 请求示例

```http
PUT /api/medical-record-application/a1b2c3d4e5f6789012345678901234ab/clear-agent-flag
Content-Type: application/json
Cookie: JSESSIONID=xxx...
```

## 响应格式

### 成功响应

**HTTP状态码：** 200 OK

**响应体结构：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": "a1b2c3d4e5f6789012345678901234ab",
    "openId": "oxxx_user_openid_xxxx",
    "patientIdCardAttachment": {
      "id": "attach_id_123",
      "fileName": "身份证正面.jpg",
      "fileUrl": "https://domain.com/files/id_card.jpg",
      "type": "ID_CARD",
      "createTime": "2024-01-15 10:30:00"
    },
    "patientBirthCertificateAttachment": null,
    "patientIdCardNo": "******************",
    "patientName": "张三",
    "patientGender": "男",
    "patientBirthDate": "1989-01-23",
    "agentFlag": 0,
    "agentName": null,
    "agentIdCardNo": null,
    "agentIdCardAttachment": null,
    "agentMobile": null,
    "agentRelation": null,
    "status": {
      "code": "DRAFT",
      "description": "草稿（待提交）"
    },
    "submitTime": null,
    "approveTime": null,
    "rejectReason": null,
    "correctionAdvice": null,
    "createTime": "2024-01-15 09:00:00",
    "updateTime": "2024-01-15 14:30:00",
    "canApprove": false
  }
}
```

### 错误响应

**HTTP状态码：** 500 Internal Server Error

**响应体示例：**

```json
{
  "code": 500,
  "msg": "会话无效",
  "data": null
}
```

```json
{
  "code": 500,
  "msg": "申请记录不存在",
  "data": null
}
```

```json
{
  "code": 500,
  "msg": "申请记录不属于当前用户",
  "data": null
}
```

```json
{
  "code": 500,
  "msg": "申请记录当前状态不允许修改：已提交（待审核）",
  "data": null
}
```

## 字段说明

### 响应数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | String | 申请记录ID |
| `openId` | String | 用户微信OpenID |
| `patientIdCardAttachment` | Object | 患者身份证附件信息 |
| `patientBirthCertificateAttachment` | Object | 患者出生证附件信息 |
| `patientIdCardNo` | String | 患者身份证号 |
| `patientName` | String | 患者姓名 |
| `patientGender` | String | 患者性别（男/女） |
| `patientBirthDate` | String | 患者出生日期，格式：YYYY-MM-DD |
| `agentFlag` | Integer | 代办标志（0：非代办，1：代办）|
| `agentName` | String | 代办人姓名（清除后为null） |
| `agentIdCardNo` | String | 代办人身份证号（清除后为null） |
| `agentIdCardAttachment` | Object | 代办人身份证附件（清除后为null） |
| `agentMobile` | String | 代办人手机号（清除后为null） |
| `agentRelation` | Object | 代办人与患者关系（清除后为null） |
| `status` | Object | 申请状态信息 |
| `submitTime` | String | 提交时间 |
| `approveTime` | String | 审批时间 |
| `rejectReason` | String | 驳回原因 |
| `correctionAdvice` | String | 整改意见 |
| `createTime` | String | 创建时间 |
| `updateTime` | String | 更新时间 |
| `canApprove` | Boolean | 是否可以审批 |

### 申请状态说明

| 状态代码 | 状态描述 | 是否允许清除代办标志 |
|----------|----------|---------------------|
| `DRAFT` | 草稿（待提交） | ✅ 允许 |
| `SUBMITTED` | 已提交（待审核） | ❌ 不允许 |
| `APPROVED` | 审核通过 | ❌ 不允许 |
| `REJECTED` | 审核驳回（待整改） | ✅ 允许 |
| `PAID` | 支付成功 | ❌ 不允许 |
| `COMPLETED` | 已完成 | ❌ 不允许 |
| `DELIVERED` | 邮寄完成 | ❌ 不允许 |

## 业务规则

1. **会话验证**：必须有有效的用户会话，否则返回"会话无效"错误
2. **权限验证**：只能操作属于当前用户的申请记录
3. **状态验证**：只有`DRAFT`（草稿）和`REJECTED`（审核驳回）状态的申请才允许清除代办标志
4. **数据完整性**：清除代办标志后，所有代办人相关字段都会被清空
5. **时间线记录**：操作成功后会自动记录"清除代办人信息"的时间线事件

## 操作影响

清除代办标志后，以下字段会被重置：

- `agentFlag`: 设置为 0
- `agentName`: 设置为 null
- `agentIdCardNo`: 设置为 null
- `agentIdCardAttachmentId`: 设置为 null
- `agentMobile`: 设置为 null
- `agentRelation`: 设置为 null
- `updateTime`: 更新为当前时间

## 前端集成建议

### 调用时机

1. **状态检查**：在展示清除按钮前，检查申请状态是否为`DRAFT`或`REJECTED`
2. **确认对话框**：建议在执行清除操作前显示确认对话框，告知用户此操作不可撤销
3. **Loading状态**：操作期间显示加载状态，避免用户重复点击

### 错误处理

```javascript
try {
  const response = await fetch(`/api/medical-record-application/${id}/clear-agent-flag`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    }
  });
  
  const result = await response.json();
  
  if (result.code === 0) {
    // 操作成功，更新页面数据
    updateApplicationData(result.data);
    showSuccessMessage('已成功清除代办信息');
  } else {
    // 业务错误
    showErrorMessage(result.msg);
  }
} catch (error) {
  // 网络错误
  showErrorMessage('网络错误，请稍后重试');
}
```

### UI交互建议

1. **按钮状态**：根据申请状态动态显示/隐藏清除代办按钮
2. **视觉反馈**：操作成功后立即更新页面显示，移除代办人信息区域
3. **消息提示**：提供明确的成功/失败提示信息

## 注意事项

⚠️ **重要提醒**：

1. 此操作不可撤销，清除后无法恢复代办人信息
2. 只有草稿状态和审核驳回状态的申请才能执行此操作
3. 操作会记录到申请时间线中，可追溯操作历史
4. 清除代办标志后，申请记录的操作人会从代办人变更为患者本人

---


<!-- Generated by SpecStory -->

# Fix incomplete statistics in getStatistics method (2025-07-16 08:14Z)

_**User**_

Fix this bug...

The `getStatistics()` method in `CardRefundAdminServiceImpl` returns incomplete statistics.
1.  `pendingApprovalCount` is incorrectly calculated, only including `SUBMITTED` status. It should also sum `RESUBMITTED` status, as per the DTO's definition.
2.  Additional fields in `CardRefundStatisticsDto` such as `draftCount`, `submittedCount`, `resubmittedCount`, `thisWeekCount`, `thisMonthCount`, and `countByStatus` are not populated and remain null.


---

_**Assistant**_

<think><details><summary>Thought Process</summary>Looking at this bug report, the user is saying that the `getStatistics()` method in `CardRefundAdminServiceImpl` has several issues:

1. `pendingApprovalCount` is incorrectly calculated - only including `SUBMITTED` status but should also include `RESUBMITTED` status
2. Additional fields in `CardRefundStatisticsDto` are not populated: `draftCount`, `submittedCount`, `resubmittedCount`, `thisWeekCount`, `thisMonthCount`, and `countByStatus`

Let me look at the current implementation of the `getStatistics()` method to understand what needs to be fixed.

From the code I can see:

```java
@Override
@Transactional(readOnly = true)
public CardRefundStatisticsDto getStatistics() {
    // 构建查询条件，包含权限过滤
    LambdaQueryWrapper<CardRefund> queryWrapper = buildSearchQueryWrapper(null);

    // 查询申报记录（已应用权限过滤）
    List<CardRefund> allRegistrations = this.list(queryWrapper);

    CardRefundStatisticsDto statistics = new CardRefundStatisticsDto();

    // 计算各状态统计
    Map<CardRefundStatus, Long> statusCounts = allRegistrations.stream()
            .collect(Collectors.groupingBy(
                    CardRefund::getStatus,
                    Collectors.counting()
            ));

    statistics.setTotalCount((long) allRegistrations.size());
    statistics.setPendingApprovalCount(statusCounts.getOrDefault(CardRefundStatus.SUBMITTED, 0L));
    statistics.setApprovedCount(statusCounts.getOrDefault(CardRefundStatus.APPROVED, 0L));
    statistics.setRejectedCount(statusCounts.getOrDefault(CardRefundStatus.REJECTED, 0L));
</details></think>

---

I'll help you fix the bug in the `getStatistics()` method. Looking at the current implementation, I can see that several fields are missing and the `pendingApprovalCount` calculation is incomplete.

---



---

<think><details><summary>Thought Process</summary>Great! I've successfully fixed the issues in the `getStatistics()` method. Let me summarize what I've changed:

1. **Fixed `pendingApprovalCount` calculation**: Now it correctly includes both `SUBMITTED` and `RESUBMITTED` statuses by adding them together.

2. **Added missing field calculations**:
   - `draftCount`: Count of records with DRAFT status
   - `submittedCount`: Count of records with SUBMITTED status  
   - `resubmittedCount`: Count of records with RESUBMITTED status
   - `thisWeekCount`: Count of records created this week (from Monday of current week)
   - `thisMonthCount`: Count of records created this month (from first day of current month)
   - `countByStatus`: The existing `statusCounts` map that was calculated but not being set

3. **Improved code organization**: Added comments to clearly separate different types of statistics calculations.

The fix addresses all the issues mentioned in the bug report:
- ✅ `pendingApprovalCount` now correctly sums `SUBMITTED` and `RESUBMITTED` statuses
- ✅ All missing fields are now populated: `draftCount`, `submittedCount`, `resubmittedCount`, `thisWeekCount`, `thisMonthCount`, and `countByStatus`

The solution follows Spring Boot and Java best practices:
- Uses streams for efficient data processing
- Maintains the existing transaction boundary
- Adds clear comments explaining the logic
- Uses appropriate time calculations for week and month boundaries
- Maintains code readability and maintainability

The fix should resolve the incomplete statistics issue and provide comprehensive data to the DTO.</details></think>

---

I've successfully fixed the bug in the `getStatistics()` method. Here's a summary of the changes made:

## Issues Fixed:

1. **`pendingApprovalCount` calculation corrected**: Now properly includes both `SUBMITTED` and `RESUBMITTED` statuses by summing them together, instead of only counting `SUBMITTED` status.

2. **Added all missing field calculations**:
   - `draftCount`: Count of records with `DRAFT` status
   - `submittedCount`: Count of records with `SUBMITTED` status  
   - `resubmittedCount`: Count of records with `RESUBMITTED` status
   - `thisWeekCount`: Count of records created this week (from Monday of current week)
   - `thisMonthCount`: Count of records created this month (from first day of current month)
   - `countByStatus`: Map of all status counts (was calculated but not set)

## Key Improvements:

- **Correct pending approval logic**: `pendingApprovalCount = submittedCount + resubmittedCount`
- **Comprehensive time-based statistics**: Added proper calculations for weekly and monthly counts
- **Better code organization**: Added clear comments separating different types of statistics
- **Complete data population**: All DTO fields are now properly populated

The fix ensures that the `CardRefundStatisticsDto` returns complete and accurate statistics data, resolving the issue where several fields were remaining null and the pending approval count was incomplete.

---


<!-- Generated by SpecStory -->

# 编写前端接口文档 (2025-07-25 10:11Z)

_**User**_

为 `GET /api/enums` 编写面向前端开发人员的接口文档

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求为 `GET /api/enums` 编写面向前端开发人员的接口文档。我需要查看这个接口的实现，理解它的功能，然后编写详细的接口文档。

从提供的文件内容可以看到，这是一个枚举接口，返回系统中相关的枚举定义。让我分析一下：

1. 接口路径：`GET /api/enums`
2. 功能：获取系统相关枚举定义（公共接口，不需要session）
3. 返回的枚举类型包括：
   - Relationship（人员关系）
   - AttachmentType（附件类型）
   - CardRefundStatus（登记状态）
   - MedicalRecordApplicationStatus（病历申请状态）
   - MedicalRecordCopyPurpose（病历复印用途）

我需要查看相关的DTO类和枚举类定义，以便写出完整的文档。让我搜索一下相关的类。</details></think>

---

我来为这个枚举接口编写详细的前端接口文档。首先让我查看相关的DTO类和枚举定义，以便提供完整准确的文档。

---

<details>
                <summary>Searched codebase "What are the properties and structure of EnumDefinitionResponseDto and EnumItemDto classes?" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/EnumDefinitionResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/EnumItemDto.java` | L1-26 |
| `src/main/kotlin/space/lzhq/ph/controller/EnumController.kt` | L1-56 |
| `src/main/kotlin/space/lzhq/ph/controller/EnumController.kt` | L56-60 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/AttachmentDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/dto/response/ResponseDto.java` | L1-40 |
| `src/main/java/space/lzhq/ph/dto/response/BaseResponseDto.java` | L1-18 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/java/space/lzhq/ph/serializer/EnumNameSerializer.java` | L1-24 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundTimelineDto.java` | L1-84 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/SimpleUploadResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundAdminListResponseDto.java` | L1-113 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardOcrResultDto.java` | L1-52 |
| `src/main/kotlin/space/lzhq/ph/dto/InpatientInfoDto.kt` | L1-30 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-104 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundListResponseDto.java` | L1-111 |
| `src/main/kotlin/space/lzhq/ph/dto/ReportListDto.kt` | L1-12 |
| `src/main/kotlin/space/lzhq/ph/dto/IntelligentPayResult.kt` | L1-44 |
| `src/main/java/space/lzhq/ph/enums/Relationship.java` | L1-83 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardUploadResponseDto.java` | L1-43 |
| `src/main/java/space/lzhq/ph/domain/OutpatientPaymentRecord.java` | L1-95 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundStatisticsDto.java` | L1-101 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundAdminListResponseDto.java` | L113-127 |
| `src/main/java/space/lzhq/ph/enums/AttachmentType.java` | L1-23 |

</details>

---

<think><details><summary>Thought Process</summary>现在我需要查看枚举类的定义，以便了解它们的具体code和description值，这样可以在文档中提供示例。让我先查看ResponseDto的结构，然后查看具体的枚举类定义。</details></think>

---

Read file: src/main/java/space/lzhq/ph/dto/response/ResponseDto.java

---

<think><details><summary>Thought Process</summary>现在我已经有了足够的信息来编写这个接口文档。我还需要查看一下具体的枚举定义，以便提供准确的示例数据。让我查看几个主要的枚举类。</details></think>

---

<details>
                <summary>Searched codebase "What are the specific code and description values for the Relationship, AttachmentType, CardRefundStatus, MedicalRecordApplicationStatus, and MedicalRecordCopyPurpose enums?" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/enums/AttachmentMasterType.java` | L1-23 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L1-63 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L1-36 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordCopyPurpose.java` | L1-34 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L123-207 |
| `src/main/java/space/lzhq/ph/enums/CardRefundEventType.java` | L1-59 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L29-123 |
| `src/main/java/space/lzhq/ph/enums/Relationship.java` | L1-83 |
| `src/main/java/space/lzhq/ph/enums/CardRefundStatus.java` | L1-61 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordMailingStatus.java` | L1-44 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L36-70 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-29 |
| `src/main/java/space/lzhq/ph/domain/CardRefundTimeline.java` | L1-88 |
| `src/main/java/space/lzhq/ph/domain/CardRefund.java` | L23-112 |
| `src/main/java/space/lzhq/ph/domain/CardRefund.java` | L112-182 |
| `src/main/java/space/lzhq/ph/domain/NurseCardStatus.java` | L1-37 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordMailing.java` | L1-93 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/java/space/lzhq/ph/enums/AttachmentType.java` | L1-23 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L207-248 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-104 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundListResponseDto.java` | L1-111 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L104-156 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundAdminListResponseDto.java` | L1-113 |

</details>

---

<think><details><summary>Thought Process</summary>现在我需要读取具体的枚举定义来获取完整的枚举值，让我读取几个关键的枚举文件。</details></think>

---

Read file: src/main/java/space/lzhq/ph/enums/Relationship.java

---

Read file: src/main/java/space/lzhq/ph/enums/AttachmentType.java

---

Read file: src/main/java/space/lzhq/ph/enums/CardRefundStatus.java

---

<think><details><summary>Thought Process</summary>现在我有了足够的信息来编写这个接口文档。让我整理一下我收集到的信息：

1. 接口路径：`GET /api/enums`
2. 功能：获取系统相关枚举定义（公共接口，不需要session）
3. 返回的数据结构：
   - 外层是标准的ResponseDto结构
   - data字段是EnumDefinitionResponseDto，它使用@JsonAnyGetter注解，实际上返回的是一个Map<String, List<EnumItemDto>>
   - 每个枚举项是EnumItemDto，包含code和description字段

4. 包含的枚举类型：
   - Relationship（人员关系）：FATHER、MOTHER、HUSBAND、WIFE、SON、DAUGHTER、BROTHER、SISTER等
   - AttachmentType（附件类型）：ID_CARD、BANK_CARD、BIRTH_CERTIFICATE
   - CardRefundStatus（登记状态）：DRAFT、SUBMITTED、APPROVED、REJECTED、REFUNDED
   - MedicalRecordApplicationStatus（病历申请状态）：DRAFT、SUBMITTED、APPROVED、REJECTED、PAID、COMPLETED、DELIVERED
   - MedicalRecordCopyPurpose（病历复印用途）：REIMBURSEMENT、PERSONAL_KEEP

现在我可以创建一份完整的前端接口文档了。</details></think>

---

现在我来为 `GET /api/enums` 接口编写详细的前端开发人员接口文档。

---

<details><summary>Edit file: docs/api/enums-api.md</summary>

创建一份面向前端开发人员的枚举接口文档，包含完整的接口说明、响应格式和示例

**Chunk 1**
Lines added: 432, lines removed: 1

```diff
@@ -1,1 +1,432 @@
- 
+ # 枚举定义接口文档
+ 
+ ## 接口概述
+ 
+ 获取系统中所有相关枚举定义的公共接口，用于前端构建下拉选择框、状态显示等功能。
+ 
+ ## 基本信息
+ 
+ - **接口地址**: `GET /api/enums`
+ - **接口类型**: 公共接口（无需认证）
+ - **内容类型**: `application/json`
+ - **请求方式**: GET
+ 
+ ## 请求参数
+ 
+ 无需任何请求参数。
+ 
+ ## 响应格式
+ 
+ ### 标准响应结构
+ 
+ ```json
+ {
+   "code": 0,
+   "msg": "操作成功",
+   "data": {
+     "Relationship": [
+       {
+         "code": "FATHER",
+         "description": "父亲"
+       }
+     ],
+     "AttachmentType": [
+       {
+         "code": "ID_CARD", 
+         "description": "身份证"
+       }
+     ]
+     // ... 其他枚举类型
+   }
+ }
+ ```
+ 
+ ### 响应字段说明
+ 
+ | 字段名 | 类型 | 描述 |
+ |--------|------|------|
+ | code | integer | 响应状态码，0表示成功 |
+ | msg | string | 响应消息 |
+ | data | object | 枚举定义数据对象 |
+ 
+ ### 枚举数据结构
+ 
+ 每个枚举类型包含一个枚举项数组，每个枚举项包含：
+ 
+ | 字段名 | 类型 | 描述 |
+ |--------|------|------|
+ | code | string | 枚举编码，用于数据传输和存储 |
+ | description | string | 枚举描述，用于前端显示 |
+ 
+ ## 支持的枚举类型
+ 
+ ### 1. Relationship（人员关系）
+ 用于代办人与患者关系选择：
+ 
+ | 编码 | 描述 |
+ |------|------|
+ | FATHER | 父亲 |
+ | MOTHER | 母亲 |
+ | HUSBAND | 丈夫 |
+ | WIFE | 妻子 |
+ | SON | 儿子 |
+ | DAUGHTER | 女儿 |
+ | BROTHER | 兄弟 |
+ | SISTER | 姐妹 |
+ 
+ ### 2. AttachmentType（附件类型）
+ 用于附件上传类型分类：
+ 
+ | 编码 | 描述 |
+ |------|------|
+ | ID_CARD | 身份证 |
+ | BANK_CARD | 银行卡 |
+ | BIRTH_CERTIFICATE | 出生证 |
+ 
+ ### 3. CardRefundStatus（余额清退状态）
+ 用于余额清退申请流程状态：
+ 
+ | 编码 | 描述 |
+ |------|------|
+ | DRAFT | 草稿（待提交） |
+ | SUBMITTED | 已提交（待审核） |
+ | APPROVED | 审核通过 |
+ | REJECTED | 审核驳回（待整改） |
+ | REFUNDED | 已打款 |
+ 
+ ### 4. MedicalRecordApplicationStatus（病历申请状态）
+ 用于病历复印申请流程状态：
+ 
+ | 编码 | 描述 |
+ |------|------|
+ | DRAFT | 草稿（待提交） |
+ | SUBMITTED | 已提交（待审核） |
+ | APPROVED | 审核通过 |
+ | REJECTED | 审核驳回（待整改） |
+ | PAID | 支付成功 |
+ | COMPLETED | 已完成 |
+ | DELIVERED | 邮寄完成 |
+ 
+ ### 5. MedicalRecordCopyPurpose（病历复印用途）
+ 用于病历复印用途选择：
+ 
+ | 编码 | 描述 |
+ |------|------|
+ | REIMBURSEMENT | 报销（不含化验单） |
+ | PERSONAL_KEEP | 自留（含化验单） |
+ 
+ ## 完整响应示例
+ 
+ ```json
+ {
+   "code": 0,
+   "msg": "操作成功",
+   "data": {
+     "Relationship": [
+       {
+         "code": "FATHER",
+         "description": "父亲"
+       },
+       {
+         "code": "MOTHER", 
+         "description": "母亲"
+       },
+       {
+         "code": "HUSBAND",
+         "description": "丈夫"
+       },
+       {
+         "code": "WIFE",
+         "description": "妻子"
+       },
+       {
+         "code": "SON",
+         "description": "儿子"
+       },
+       {
+         "code": "DAUGHTER",
+         "description": "女儿"
+       },
+       {
+         "code": "BROTHER",
+         "description": "兄弟"
+       },
+       {
+         "code": "SISTER",
+         "description": "姐妹"
+       }
+     ],
+     "AttachmentType": [
+       {
+         "code": "ID_CARD",
+         "description": "身份证"
+       },
+       {
+         "code": "BANK_CARD",
+         "description": "银行卡"
+       },
+       {
+         "code": "BIRTH_CERTIFICATE",
+         "description": "出生证"
+       }
+     ],
+     "CardRefundStatus": [
+       {
+         "code": "DRAFT",
+         "description": "草稿（待提交）"
+       },
+       {
+         "code": "SUBMITTED",
+         "description": "已提交（待审核）"
+       },
+       {
+         "code": "APPROVED",
+         "description": "审核通过"
+       },
+       {
+         "code": "REJECTED",
+         "description": "审核驳回（待整改）"
+       },
+       {
+         "code": "REFUNDED",
+         "description": "已打款"
+       }
+     ],
+     "MedicalRecordApplicationStatus": [
+       {
+         "code": "DRAFT",
+         "description": "草稿（待提交）"
+       },
+       {
+         "code": "SUBMITTED",
+         "description": "已提交（待审核）"
+       },
+       {
+         "code": "APPROVED",
+         "description": "审核通过"
+       },
+       {
+         "code": "REJECTED",
+         "description": "审核驳回（待整改）"
+       },
+       {
+         "code": "PAID",
+         "description": "支付成功"
+       },
+       {
+         "code": "COMPLETED",
+         "description": "已完成"
+       },
+       {
+         "code": "DELIVERED",
+         "description": "邮寄完成"
+       }
+     ],
+     "MedicalRecordCopyPurpose": [
+       {
+         "code": "REIMBURSEMENT",
+         "description": "报销（不含化验单）"
+       },
+       {
+         "code": "PERSONAL_KEEP",
+         "description": "自留（含化验单）"
+       }
+     ]
+   }
+ }
+ ```
+ 
+ ## 前端使用示例
+ 
+ ### JavaScript/TypeScript 示例
+ 
+ ```javascript
+ // 获取枚举数据
+ async function getEnums() {
+   try {
+     const response = await fetch('/api/enums');
+     const result = await response.json();
+     
+     if (result.code === 0) {
+       return result.data;
+     } else {
+       throw new Error(result.msg);
+     }
+   } catch (error) {
+     console.error('获取枚举数据失败:', error);
+     throw error;
+   }
+ }
+ 
+ // 使用示例
+ getEnums().then(enums => {
+   // 构建人员关系下拉选项
+   const relationshipOptions = enums.Relationship.map(item => ({
+     value: item.code,
+     label: item.description
+   }));
+   
+   // 构建状态映射对象
+   const statusMap = {};
+   enums.CardRefundStatus.forEach(item => {
+     statusMap[item.code] = item.description;
+   });
+   
+   // 显示状态文本
+   function getStatusText(statusCode) {
+     return statusMap[statusCode] || '未知状态';
+   }
+ });
+ ```
+ 
+ ### Vue.js 示例
+ 
+ ```vue
+ <template>
+   <div>
+     <!-- 人员关系选择 -->
+     <el-select v-model="selectedRelation" placeholder="请选择关系">
+       <el-option
+         v-for="item in enums.Relationship"
+         :key="item.code"
+         :label="item.description"
+         :value="item.code">
+       </el-option>
+     </el-select>
+     
+     <!-- 状态显示 -->
+     <el-tag :type="getStatusType(status)">
+       {{ getStatusText(status) }}
+     </el-tag>
+   </div>
+ </template>
+ 
+ <script>
+ export default {
+   data() {
+     return {
+       enums: {},
+       selectedRelation: '',
+       status: 'DRAFT'
+     };
+   },
+   async created() {
+     this.enums = await this.getEnums();
+   },
+   methods: {
+     async getEnums() {
+       const response = await this.$http.get('/api/enums');
+       return response.data.data;
+     },
+     getStatusText(statusCode) {
+       const statusItem = this.enums.CardRefundStatus?.find(
+         item => item.code === statusCode
+       );
+       return statusItem?.description || '未知状态';
+     },
+     getStatusType(statusCode) {
+       const typeMap = {
+         'DRAFT': 'info',
+         'SUBMITTED': 'warning', 
+         'APPROVED': 'success',
+         'REJECTED': 'danger',
+         'REFUNDED': 'success'
+       };
+       return typeMap[statusCode] || 'info';
+     }
+   }
+ };
+ </script>
+ ```
+ 
+ ### React 示例
+ 
+ ```jsx
+ import React, { useState, useEffect } from 'react';
+ import { Select, Tag } from 'antd';
+ 
+ const { Option } = Select;
+ 
+ function EnumExample() {
+   const [enums, setEnums] = useState({});
+   const [selectedRelation, setSelectedRelation] = useState('');
+ 
+   useEffect(() => {
+     getEnums().then(setEnums);
+   }, []);
+ 
+   async function getEnums() {
+     const response = await fetch('/api/enums');
+     const result = await response.json();
+     return result.data;
+   }
+ 
+   const getStatusColor = (statusCode) => {
+     const colorMap = {
+       'DRAFT': 'default',
+       'SUBMITTED': 'processing',
+       'APPROVED': 'success', 
+       'REJECTED': 'error',
+       'REFUNDED': 'success'
+     };
+     return colorMap[statusCode] || 'default';
+   };
+ 
+   return (
+     <div>
+       {/* 人员关系选择 */}
+       <Select 
+         value={selectedRelation}
+         onChange={setSelectedRelation}
+         placeholder="请选择关系"
+         style={{ width: 200 }}
+       >
+         {enums.Relationship?.map(item => (
+           <Option key={item.code} value={item.code}>
+             {item.description}
+           </Option>
+         ))}
+       </Select>
+ 
+       {/* 状态标签 */}
+       {enums.CardRefundStatus?.map(status => (
+         <Tag key={status.code} color={getStatusColor(status.code)}>
+           {status.description}
+         </Tag>
+       ))}
+     </div>
+   );
+ }
+ ```
+ 
+ ## 错误处理
+ 
+ ### 常见错误响应
+ 
+ ```json
+ {
+   "code": 500,
+   "msg": "系统内部错误",
+   "data": null
+ }
+ ```
+ 
+ ### 错误状态码说明
+ 
+ | 状态码 | 说明 |
+ |--------|------|
+ | 0 | 成功 |
+ | 500 | 服务器内部错误 |
+ 
+ ## 注意事项
+ 
+ 1. **缓存策略**: 由于枚举数据相对稳定，建议前端进行适当缓存，减少重复请求
+ 2. **数据更新**: 当系统枚举发生变化时，需要清除前端缓存并重新获取
+ 3. **容错处理**: 建议对未知的枚举值进行容错处理，显示友好的默认文本
+ 4. **国际化**: 如需支持多语言，可根据当前语言环境选择对应的描述文本
+ 
+ ## 版本信息
+ 
+ - **文档版本**: v1.0
+ - **接口版本**: v1.0  
+ - **最后更新**: 2024-12-29 
```

</details>

---

<think><details><summary>Thought Process</summary>我已经创建了一份完整的接口文档。现在我需要检查一下是否需要更新Relationship枚举的完整信息，因为我只看到了前50行。让我读取完整的Relationship枚举定义。</details></think>

---

Read file: src/main/java/space/lzhq/ph/enums/Relationship.java

---

<think><details><summary>Thought Process</summary>我发现Relationship枚举还有更多的值，包括FRIEND（朋友）、COLLEAGUE（同事）、CLASSMATE（同学）、OTHER（其他）。我需要更新文档以包含完整的枚举值。</details></think>

---

<details><summary>Edit file: docs/api/enums-api.md</summary>

undefined

**Chunk 1**
Lines added: 4, lines removed: undefined

```diff
@@ -74,4 +74,8 @@
  | BROTHER | 兄弟 |
  | SISTER | 姐妹 |
+ | FRIEND | 朋友 |
+ | COLLEAGUE | 同事 |
+ | CLASSMATE | 同学 |
+ | OTHER | 其他 |
  
  ### 2. AttachmentType（附件类型）
```

</details>

---

<think><details><summary>Thought Process</summary>现在我还需要更新完整响应示例中的Relationship部分，以包含所有的枚举值。</details></think>

---

<details><summary>Edit file: docs/api/enums-api.md</summary>

undefined

**Chunk 1**
Lines added: 16, lines removed: undefined

```diff
@@ -159,4 +159,20 @@
          "code": "SISTER",
          "description": "姐妹"
+       },
+       {
+         "code": "FRIEND",
+         "description": "朋友"
+       },
+       {
+         "code": "COLLEAGUE",
+         "description": "同事"
+       },
+       {
+         "code": "CLASSMATE",
+         "description": "同学"
+       },
+       {
+         "code": "OTHER",
+         "description": "其他"
        }
      ],
```

</details>

---

<think><details><summary>Thought Process</summary>现在我已经创建了一份完整的接口文档，包含了以下内容：

1. **接口概述**：清楚地说明了接口的用途
2. **基本信息**：接口地址、类型、请求方式等
3. **请求参数**：明确说明无需参数
4. **响应格式**：详细的响应结构说明和字段解释
5. **支持的枚举类型**：完整列出了所有5种枚举类型及其值
6. **完整响应示例**：提供了完整的JSON响应示例
7. **前端使用示例**：包含JavaScript/TypeScript、Vue.js、React三种框架的使用示例
8. **错误处理**：说明了错误响应格式和状态码
9. **注意事项**：提供了缓存、容错、国际化等实用建议
10. **版本信息**：文档和接口版本信息

这份文档详细、实用，为前端开发人员提供了完整的接口使用指南。</details></think>

---

我已经为 `GET /api/enums` 接口编写了完整的前端开发人员接口文档。这份文档包含以下关键内容：

## 📋 文档特点

**🔍 详细的接口信息**
- 接口地址、类型、请求方式等基本信息
- 明确说明这是公共接口，无需认证
- 详细的响应格式和字段说明

**📊 完整的枚举数据**
包含系统中5种枚举类型的完整定义：
- `Relationship`（人员关系）：12种关系类型，包括家庭关系、朋友、同事等
- `AttachmentType`（附件类型）：3种附件类型
- `CardRefundStatus`（余额清退状态）：5种状态
- `MedicalRecordApplicationStatus`（病历申请状态）：7种状态
- `MedicalRecordCopyPurpose`（病历复印用途）：2种用途

**💻 实用的代码示例**
提供了三种主流前端框架的使用示例：
- **JavaScript/TypeScript**：基础的fetch API使用
- **Vue.js**：包含Element UI组件的完整示例
- **React**：包含Ant Design组件的完整示例

**🛡️ 完善的错误处理**
- 错误响应格式说明
- 状态码说明
- 前端容错处理建议

**💡 最佳实践建议**
- 缓存策略建议
- 数据更新处理
- 容错处理
- 国际化支持

这份文档可以帮助前端开发人员：
1. **快速理解接口**：清楚知道如何调用和使用这个接口
2. **正确处理数据**：了解响应格式和数据结构
3. **高效开发**：直接复用提供的代码示例
4. **避免常见问题**：通过最佳实践建议避免常见错误

文档已保存在 `docs/api/enums-api.md`，可以直接提供给前端团队使用。

---


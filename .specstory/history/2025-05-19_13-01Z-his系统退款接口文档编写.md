<!-- Generated by SpecStory -->

# HIS系统退款接口文档编写 (2025-05-19 13:01Z)

_**User**_

`POST /open/refund`接口是提供给HIS系统用来向用户退还微信支付部分的款项。创建markdown文档向HIS开发者描述此接口的用法。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>我需要根据提供的信息创建一份Markdown文档，向HIS开发者描述`POST /open/refund`接口的用法。从代码中可以看出，这是一个退款接口，用于处理两种不同类型的退款业务：医保移动支付和诊间缴费。

从代码分析关键信息：

1. 接口路径: `/open/refund`
2. 请求方法: POST
3. 请求体格式: JSON
4. 请求参数:
   - outTradeNo: 订单号（必填）
   - type: 业务类型（1=诊间缴费，2=医保移动支付）（必填）
   - amount: 退款金额（必填，必须大于0）
   - refundDesc: 退款说明（可选）
   - authCode: 认证码（必填，固定值为"AQBr6@rJc_7jgV7C"）

5. 返回格式: JSON
6. 成功响应示例:
   ```json
   {
     "code": 200,
     "msg": "处理成功",
     "data": "退款单号"
   }
   ```
7. 失败响应示例:
   ```json
   {
     "code": 500,
     "msg": "错误信息"
   }
   ```

8. 业务逻辑:
   - 医保移动支付退款: 针对医保移动支付(type=2)的订单
   - 诊间缴费退款: 针对诊间缴费(type=1)的订单

我将基于以上信息创建一个详细的Markdown文档。</details></think>

---

# HIS系统微信退款接口文档

## 接口概述

- **接口名称**：微信退款接口
- **接口地址**：`POST /open/refund`
- **功能描述**：提供给HIS系统用来向用户退还微信支付的款项

## 请求参数

### 请求头

Content-Type: application/json

### 请求体参数

| 参数名称    | 类型      | 是否必须 | 描述                        |
|-----------|-----------|---------|----------------------------|
| outTradeNo | String    | 是      | 订单号                      |
| type      | Integer   | 是      | 业务类型：1=诊间缴费，2=医保移动支付 |
| amount    | Decimal   | 是      | 退款金额（元），必须大于0         |
| refundDesc | String    | 否      | 退款说明                     |
| authCode  | String    | 是      | 认证码，固定值                 |

## 请求示例

```json
{
  "outTradeNo": "YB20240615001123",
  "type": 2,
  "amount": 10.50,
  "refundDesc": "患者申请退款",
  "authCode": "AQBr6@rJc_7jgV7C"
}
```

## 响应参数

| 参数名称 | 类型   | 描述                        |
|--------|-------|----------------------------|
| code   | Integer | 返回码：200=成功，其他=失败     |
| msg    | String  | 返回信息                     |
| data   | String  | 退款单号（仅在成功时返回）       |

## 响应示例

### 成功响应

```json
{
  "code": 200,
  "msg": "处理成功",
  "data": "YQTL20240615001123#R"
}
```

### 失败响应

```json
{
  "code": 500,
  "msg": "退款金额超过可退款金额"
}
```

## 使用说明

1. 系统会根据`outTradeNo`的前缀和`type`值自动判断退款类型:
   - 医保移动支付（`type=2`）：订单号需以`YB`开头
   - 诊间缴费（`type=1`）：订单号需以`YQTL`开头

2. 医保移动支付退款注意事项:
   - 仅支持现金部分退款
   - 必须是支付状态为SUCCESS的订单才能退款

3. 诊间缴费退款注意事项:
   - 支付状态必须是SUCCESS才可退款
   - 退款金额不能超过可退款金额

4. 认证码（authCode）必须正确，否则会返回401错误

## 错误码说明

| 错误码 | 描述                  |
|-------|----------------------|
| 200   | 处理成功               |
| 401   | 认证失败               |
| 500   | 业务处理失败，详见msg字段 |

---

_**User**_

请将上述markdown文档保存到 @doc 目录下

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求将刚刚创建的微信退款接口的markdown文档保存到@doc目录下。我需要使用edit_file工具将这个文档写入到指定目录。

从用户附加的目录信息来看，@doc目录已经存在，并且其中有一个wiki.md文件。我们需要创建一个新文件来保存微信退款接口的markdown文档。

可以创建一个名为"his-refund-api.md"的文件，这个名称反映了文档的内容是关于HIS系统的退款API的。</details></think>

---

```diff
- 
+ # HIS系统微信退款接口文档
+ 
+ ## 接口概述
+ 
+ - **接口名称**：微信退款接口
+ - **接口地址**：`POST /open/refund`
+ - **功能描述**：提供给HIS系统用来向用户退还微信支付的款项
+ 
+ ## 请求参数
+ 
+ ### 请求头
+ 
+ Content-Type: application/json
+ 
+ ### 请求体参数
+ 
+ | 参数名称    | 类型      | 是否必须 | 描述                        |
+ |-----------|-----------|---------|----------------------------|
+ | outTradeNo | String    | 是      | 订单号                      |
+ | type      | Integer   | 是      | 业务类型：1=诊间缴费，2=医保移动支付 |
+ | amount    | Decimal   | 是      | 退款金额（元），必须大于0         |
+ | refundDesc | String    | 否      | 退款说明                     |
+ | authCode  | String    | 是      | 认证码，固定值                 |
+ 
+ ## 请求示例
+ 
+ ```json
+ {
+   "outTradeNo": "YB20240615001123",
+   "type": 2,
+   "amount": 10.50,
+   "refundDesc": "患者申请退款",
+   "authCode": "AQBr6@rJc_7jgV7C"
+ }
+ ```
+ 
+ ## 响应参数
+ 
+ | 参数名称 | 类型   | 描述                        |
+ |--------|-------|----------------------------|
+ | code   | Integer | 返回码：200=成功，其他=失败     |
+ | msg    | String  | 返回信息                     |
+ | data   | String  | 退款单号（仅在成功时返回）       |
+ 
+ ## 响应示例
+ 
+ ### 成功响应
+ 
+ ```json
+ {
+   "code": 200,
+   "msg": "处理成功",
+   "data": "YQTL20240615001123#R"
+ }
+ ```
+ 
+ ### 失败响应
+ 
+ ```json
+ {
+   "code": 500,
+   "msg": "退款金额超过可退款金额"
+ }
+ ```
+ 
+ ## 使用说明
+ 
+ 1. 系统会根据`outTradeNo`的前缀和`type`值自动判断退款类型:
+    - 医保移动支付（`type=2`）：订单号需以`YB`开头
+    - 诊间缴费（`type=1`）：订单号需以`YQTL`开头
+ 
+ 2. 医保移动支付退款注意事项:
+    - 仅支持现金部分退款
+    - 必须是支付状态为SUCCESS的订单才能退款
+ 
+ 3. 诊间缴费退款注意事项:
+    - 支付状态必须是SUCCESS才可退款
+    - 退款金额不能超过可退款金额
+ 
+ 4. 认证码（authCode）必须正确，否则会返回401错误
+ 
+ ## 错误码说明
+ 
+ | 错误码 | 描述                  |
+ |-------|----------------------|
+ | 200   | 处理成功               |
+ | 401   | 认证失败               |
+ | 500   | 业务处理失败，详见msg字段 | 
```

---

文档已成功保存到 doc 目录下的 his-refund-api.md 文件中。该文档包含了 HIS 系统微信退款接口的完整使用说明。

---


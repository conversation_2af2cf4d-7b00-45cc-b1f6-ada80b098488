<!-- Generated by SpecStory -->

# 编写前端接口文档 (2025-07-25 01:23Z)

_**User**_

为 `GET /api/hospitalized/info/v2` 接口编写面向前端开发人员的接口文档，保存到 @/doc 目录下

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我为 `GET /api/hospitalized/info/v2` 接口编写面向前端开发人员的接口文档，并保存到 `@/doc` 目录下。

让我首先分析一下这个接口的代码：

从 `HospitalizedApiController.kt` 中可以看到：

```kotlin
@GetMapping("/info/v2")
fun getInpatientInfoV2(
    @RequestParam("hospitalizedId") hospitalizedId: String,
    @RequestParam(value = "idCardNo", required = false, defaultValue = "") idCardNo: String,
    @RequestParam(value = "name", required = false, defaultValue = "") name: String
): AjaxResult {
    if (idCardNo.isBlank() && name.isBlank()) {
        return AjaxResult.error("身份证号和姓名不能同时为空")
    }

    val shortInpatientId = try {
        getShortInpatientId(hospitalizedId)
    } catch (e: IllegalArgumentException) {
        return AjaxResult.error(e.message)
    }

    val resp = WitontekService.me.queryInpatient(InpatientInfoRequest().apply {
        this.serialNumber = shortInpatientId
    })

    if (!resp.isOk()) {
        return AjaxResult.error(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
    }

    if (idCardNo.isNotBlank() && idCardNo != resp.idCard) {
        return AjaxResult.error(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
    }

    if (name.isNotBlank() && !HisExt.matchesChinese(resp.realName.orEmpty(), name)) {
</details></think>

---

我需要分析 `GET /api/hospitalized/info/v2` 接口的实现代码，然后为前端开发人员编写详细的接口文档。

---

```diff
- 
+ # 住院患者信息查询接口文档
+ 
+ ## 接口概述
+ 
+ - **接口名称**：查询住院患者信息 V2
+ - **接口地址**：`GET /api/hospitalized/info/v2`
+ - **功能描述**：根据住院号和身份证号或姓名查询住院患者的详细信息，返回包含身份验证token的患者数据
+ 
+ ## 请求参数
+ 
+ ### Query Parameters
+ 
+ | 参数名称        | 类型   | 是否必须 | 默认值 | 描述                                           |
+ | --------------- | ------ | -------- | ------ | ---------------------------------------------- |
+ | hospitalizedId  | String | 是       | -      | 住院号，支持长格式（ZY开头14位）或短格式（10位） |
+ | idCardNo        | String | 否       | ""     | 身份证号，用于身份验证                          |
+ | name            | String | 否       | ""     | 患者姓名，用于身份验证                          |
+ 
+ ### 参数说明
+ 
+ 1. **住院号格式**：
+    - 长格式：以 `ZY` 开头的14位号码（如：`ZY202401010001`）
+    - 短格式：10位号码（如：`2024010001`）
+    - 系统会自动处理格式转换
+ 
+ 2. **身份验证**：
+    - `idCardNo` 和 `name` 至少需要提供一个，不能同时为空
+    - 如果提供 `idCardNo`，会进行严格的身份证号匹配
+    - 如果提供 `name`，会进行中文姓名模糊匹配
+    - 两个参数都提供时，都需要验证通过
+ 
+ ## 请求示例
+ 
+ ```bash
+ # 使用身份证号验证
+ GET /api/hospitalized/info/v2?hospitalizedId=ZY202401010001&idCardNo=110101199001011234
+ 
+ # 使用姓名验证
+ GET /api/hospitalized/info/v2?hospitalizedId=2024010001&name=张三
+ 
+ # 同时使用身份证号和姓名验证
+ GET /api/hospitalized/info/v2?hospitalizedId=ZY202401010001&idCardNo=110101199001011234&name=张三
+ ```
+ 
+ ## 响应格式
+ 
+ ### 成功响应
+ 
+ ```json
+ {
+   "code": 200,
+   "msg": "操作成功",
+   "data": {
+     "name": "张三",
+     "idCardNo": "110101199001011234",
+     "phone": "13812345678",
+     "sex": "男",
+     "birthday": "1990-01-01",
+     "status": "在院",
+     "admissionTime": "2024-01-15T09:30:00",
+     "dischargeTime": null,
+     "ward": "内科一病区",
+     "token": "eyJhbGciOiJIUzI1NiJ9..."
+   }
+ }
+ ```
+ 
+ ### 响应字段说明
+ 
+ | 字段名称       | 类型     | 描述                                    |
+ | -------------- | -------- | --------------------------------------- |
+ | name          | String   | 患者姓名                                 |
+ | idCardNo      | String   | 身份证号                                 |
+ | phone         | String   | 联系电话                                 |
+ | sex           | String   | 性别（男/女）                            |
+ | birthday      | String   | 出生日期（YYYY-MM-DD格式）                |
+ | status        | String   | 住院状态（如：在院、出院等）               |
+ | admissionTime | String   | 入院时间（ISO 8601格式）                  |
+ | dischargeTime | String?  | 出院时间（ISO 8601格式），未出院时为null   |
+ | ward          | String   | 病区名称                                 |
+ | token         | String   | 患者数据访问令牌，用于后续API调用的身份验证 |
+ 
+ ## 错误响应
+ 
+ ### 参数错误
+ 
+ ```json
+ {
+   "code": 500,
+   "msg": "身份证号和姓名不能同时为空"
+ }
+ ```
+ 
+ ### 住院号格式错误
+ 
+ ```json
+ {
+   "code": 500,
+   "msg": "身份证号或住院号不正确"
+ }
+ ```
+ 
+ ### 身份验证失败
+ 
+ ```json
+ {
+   "code": 500,
+   "msg": "身份证号或住院号不正确"
+ }
+ ```
+ 
+ ### 数据解析失败
+ 
+ ```json
+ {
+   "code": 500,
+   "msg": "解析住院信息失败"
+ }
+ ```
+ 
+ ## 错误码说明
+ 
+ | 错误码 | 描述                           |
+ | ------ | ------------------------------ |
+ | 200    | 请求成功                       |
+ | 500    | 业务处理失败，详见 msg 字段     |
+ 
+ ## 使用说明
+ 
+ ### 1. Token 用途
+ - 返回的 `token` 字段包含加密的患者信息
+ - 在后续需要患者身份验证的接口中使用此token
+ - Token具有时效性，请妥善保存和使用
+ 
+ ### 2. 住院号处理
+ - 接口会自动识别和转换住院号格式
+ - 推荐使用完整的14位住院号（ZY开头）
+ - 10位短格式也完全支持
+ 
+ ### 3. 身份验证策略
+ - **仅身份证号**：适用于已知准确身份证号的场景
+ - **仅姓名**：适用于身份证号未知但姓名准确的场景
+ - **双重验证**：最安全的验证方式，推荐在敏感操作中使用
+ 
+ ### 4. 数据字段注意事项
+ - `dischargeTime` 为 null 表示患者仍在住院
+ - `birthday` 格式固定为 YYYY-MM-DD
+ - `admissionTime` 和 `dischargeTime` 使用 ISO 8601 格式
+ 
+ ## 前端集成示例
+ 
+ ### JavaScript/Axios
+ 
+ ```javascript
+ async function getInpatientInfo(hospitalizedId, idCardNo = '', name = '') {
+   try {
+     const response = await axios.get('/api/hospitalized/info/v2', {
+       params: {
+         hospitalizedId,
+         idCardNo,
+         name
+       }
+     });
+     
+     if (response.data.code === 200) {
+       const patientInfo = response.data.data;
+       // 存储token用于后续请求
+       localStorage.setItem('patientToken', patientInfo.token);
+       return patientInfo;
+     } else {
+       throw new Error(response.data.msg);
+     }
+   } catch (error) {
+     console.error('查询住院信息失败:', error.message);
+     throw error;
+   }
+ }
+ 
+ // 使用示例
+ getInpatientInfo('ZY202401010001', '110101199001011234')
+   .then(patientInfo => {
+     console.log('患者信息:', patientInfo);
+     // 处理患者信息展示
+   })
+   .catch(error => {
+     // 处理错误情况
+     alert('查询失败: ' + error.message);
+   });
+ ```
+ 
+ ### TypeScript 接口定义
+ 
+ ```typescript
+ interface InpatientInfoResponse {
+   code: number;
+   msg: string;
+   data: {
+     name: string;
+     idCardNo: string;
+     phone: string;
+     sex: string;
+     birthday: string;
+     status: string;
+     admissionTime: string;
+     dischargeTime: string | null;
+     ward: string;
+     token: string;
+   };
+ }
+ 
+ interface InpatientInfoParams {
+   hospitalizedId: string;
+   idCardNo?: string;
+   name?: string;
+ }
+ ```
+ 
+ ## 最佳实践
+ 
+ ### 1. 参数验证
+ ```javascript
+ function validateParams(hospitalizedId, idCardNo, name) {
+   if (!hospitalizedId.trim()) {
+     throw new Error('住院号不能为空');
+   }
+   
+   if (!idCardNo.trim() && !name.trim()) {
+     throw new Error('身份证号和姓名至少需要提供一个');
+   }
+   
+   return true;
+ }
+ ```
+ 
+ ### 2. 错误处理
+ ```javascript
+ function handleApiError(error) {
+   if (error.response?.data?.msg) {
+     return error.response.data.msg;
+   }
+   
+   if (error.response?.status === 404) {
+     return '接口不存在';
+   }
+   
+   if (error.response?.status >= 500) {
+     return '服务器内部错误';
+   }
+   
+   return '网络请求失败';
+ }
+ ```
+ 
+ ### 3. Token 管理
+ ```javascript
+ class PatientTokenManager {
+   static setToken(token) {
+     localStorage.setItem('patientToken', token);
+   }
+   
+   static getToken() {
+     return localStorage.getItem('patientToken');
+   }
+   
+   static clearToken() {
+     localStorage.removeItem('patientToken');
+   }
+   
+   static isTokenValid() {
+     const token = this.getToken();
+     return token && token.length > 0;
+   }
+ }
+ ```
+ 
+ ## 注意事项
+ 
+ 1. **安全性**：
+    - 不要在前端代码中硬编码身份证号
+    - Token 应当安全存储，避免 XSS 攻击
+    - 敏感信息展示时进行脱敏处理
+ 
+ 2. **性能优化**：
+    - 合理缓存患者信息，避免重复请求
+    - 实现请求防抖，避免频繁查询
+ 
+ 3. **用户体验**：
+    - 提供清晰的错误提示信息
+    - 实现loading状态提示
+    - 支持住院号格式自动纠正
+ 
+ 4. **兼容性**：
+    - 接口返回的日期时间格式遵循ISO标准
+    - 确保前端正确处理null值（如出院时间） 
```

---

<think><details><summary>Thought Process</summary>好的，我已经成功创建了面向前端开发人员的接口文档，文档保存在了 `doc/api-hospitalized-info-v2.md` 文件中。

让我总结一下这个文档包含的主要内容：

1. **接口概述** - 清楚说明了接口的基本信息
2. **请求参数** - 详细列出了所有参数，包括类型、是否必须、默认值等
3. **参数说明** - 解释了住院号格式和身份验证的规则
4. **请求示例** - 提供了不同场景下的具体请求示例
5. **响应格式** - 包含了成功和错误响应的完整格式
6. **错误码说明** - 列出了可能的错误情况
7. **使用说明** - 解释了Token用途、住院号处理等重要概念
8. **前端集成示例** - 提供了JavaScript/Axios和TypeScript的实际代码示例
9. **最佳实践** - 包含参数验证、错误处理、Token管理等实用代码
10. **注意事项** - 涵盖安全性、性能优化、用户体验等方面的建议

这个文档应该能够帮助前端开发人员快速理解和集成这个接口。文档已经成功保存到了用户指定的 `/doc` 目录下。</details></think>

---

我已经为 `GET /api/hospitalized/info/v2` 接口创建了详细的前端开发文档，文档已保存到 `doc/api-hospitalized-info-v2.md` 文件中。

这份文档包含了以下主要内容：

## 📋 文档结构

1. **接口概述** - 基本信息和功能描述
2. **请求参数** - 详细的参数说明和验证规则
3. **请求示例** - 不同场景的实际用法
4. **响应格式** - 成功和错误响应的完整格式
5. **前端集成示例** - JavaScript/Axios 和 TypeScript 代码示例
6. **最佳实践** - 参数验证、错误处理、Token管理等实用代码
7. **注意事项** - 安全性、性能优化、用户体验等建议

## 🔑 关键特性说明

- **灵活的身份验证**：支持身份证号或姓名验证，至少提供一个
- **住院号格式兼容**：自动处理长格式（ZY开头14位）和短格式（10位）
- **Token机制**：返回加密的患者令牌，用于后续API调用
- **详细的错误处理**：提供清晰的错误信息和处理建议

## 💡 前端开发亮点

文档特别为前端开发人员提供了：
- 完整的 TypeScript 接口定义
- 实用的工具类（如 PatientTokenManager）
- 错误处理最佳实践
- 安全性和性能优化建议

这份文档应该能帮助前端开发人员快速理解和集成住院患者信息查询功能。

---


<!-- Generated by SpecStory -->

# 更新 alipay.yaml 配置文件 (2025-07-03 10:48Z)

_**User**_

根据 @alipay.setting 更新 @alipay.yaml 

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求根据 `alipay.setting` 文件来更新 `alipay.yaml` 文件。让我比较这两个文件的内容，看看需要进行哪些更新。

从文件内容来看：

**alipay.setting** (要参考的源文件):
- appId=2021004162624409
- merchantName=新疆维吾尔自治区第三人民医院
- merchantId=2088331439446319
- pid= (空值)
- notifyUrl=https://xjzybyy.xjyqtl.cn/open/alipay/onPay
- hospitalName=新疆维吾尔自治区第三人民医院
- hospitalRegisterId= (空值)
- tenantAppId= (空值)
- mip部分的orgAppId=1HQTPGDM501Q3F60C80A0000CA9AB291
- mip部分的orgChnlCrtfCode=BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxvhx5UDJoOagngtwI/IKRwp
- mip部分的orgCode=H65010300035
- mip部分的orgName=新疆维吾尔自治区第三人民医院
- mip部分的orgCityCode=650100
- mip部分的notifyUrl=https://xjzybyy.xjyqtl.cn/open/alipay/onMipPay

**alipay.yaml** (要更新的目标文件):
- appId: "2021003154673754"
</details></think>

---

我来根据 `alipay.setting` 文件的内容更新 `alipay.yaml` 文件。通过比较两个文件，我发现需要更新多个配置项，主要是将伊犁哈萨克自治州友谊医院的配置更新为新疆维吾尔自治区第三人民医院的配置。

---

```diff
  ma:
-   appId: "2021003154673754"
-   merchantName: "伊犁哈萨克自治州友谊医院"
+   appId: "2021004162624409"
+   merchantName: "新疆维吾尔自治区第三人民医院"
    # 登录https://open.alipay.com/，点击右上角头像，选择账户中心，账户管理中的账号ID即merchantId
-   merchantId: "2088541094346781"
-   pid: "2088341243702554"
+   merchantId: "2088331439446319"
+   pid: ""
    encryptType: "AES"
    encryptKey: "L0AWW0Alia3nkIQthG4OQA=="
-   notifyUrl: "https://ylyyyy.xjyqtl.cn:19000/open/alipay/onPay"
+   notifyUrl: "https://xjzybyy.xjyqtl.cn/open/alipay/onPay"
    # 关于密钥和证书的文档：
    # https://opendocs.alipay.com/open/291/105971
```

```diff
  
    # 以下配置用于支付宝智能消息提醒
-   hospitalRegisterId: "45820949865400211A1001"
-   hospitalName: "伊犁哈萨克自治州友谊医院"
-   tenantAppId: "20221008467800023553"
+   hospitalRegisterId: ""
+   hospitalName: "新疆维吾尔自治区第三人民医院"
+   tenantAppId: ""
  
  mip:
    # 定点医疗机构应用ID，即医药机构渠道编号，参见反馈单中的渠道编号
-   orgAppId: "1GGMN3PJ1079E2470B0A0000FCDB760A"
+   orgAppId: "1HQTPGDM501Q3F60C80A0000CA9AB291"
    # 机构渠道认证编码，参见反馈单中的机构渠道认证编码
-   orgChnlCrtfCode: "BqK1kMStlhVDgN2uHf4EsP9BQzovPtfTZGwRsyhqMMYx1MunG//RTCcmH7CftmAe"
+   orgChnlCrtfCode: "BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxvhx5UDJoOagngtwI/IKRwp"
    # 定点医药机构编码，参见反馈单中的定点医药机构编码
-   orgCode: "H65400200247"
-   orgName: "伊犁哈萨克自治州友谊医院"
-   orgCityCode: "654000"
+   orgCode: "H65010300035"
+   orgName: "新疆维吾尔自治区第三人民医院"
+   orgCityCode: "650100"
    # 授权成功后回调医药机构前端地址
    callUrl: "alipays://platformapi/startapp?appId=2021003154673754&page=pages_yibao%2FAlipayPendingSettlement%2FAlipayPendingSettlement"
-   notifyUrl: "https://ylyyyy.xjyqtl.cn:19000/open/alipay/onMipPay"
+   notifyUrl: "https://xjzybyy.xjyqtl.cn/open/alipay/onMipPay"
  
```

---

<think><details><summary>Thought Process</summary>现在我需要检查一下是否还有需要更新的地方。我注意到在mip配置中，callUrl中的appId仍然是"2021003154673754"，但是根据alipay.setting文件，这个也应该改为"2021004162624409"。让我再次更新这个字段。</details></think>

---

我注意到还有一个地方需要更新，就是 `mip.callUrl` 中的 `appId` 参数也需要与新的应用ID保持一致。

---



---

<think><details><summary>Thought Process</summary>好的，现在我已经根据alipay.setting文件成功更新了alipay.yaml文件。让我总结一下我所做的更改：

1. 更新了appId从"2021003154673754"改为"2021004162624409"
2. 更新了merchantName从"伊犁哈萨克自治州友谊医院"改为"新疆维吾尔自治区第三人民医院"
3. 更新了merchantId从"2088541094346781"改为"2088331439446319"
4. 更新了pid从"2088341243702554"改为空字符串""
5. 更新了notifyUrl从"https://ylyyyy.xjyqtl.cn:19000/open/alipay/onPay"改为"https://xjzybyy.xjyqtl.cn/open/alipay/onPay"
6. 更新了hospitalRegisterId从"45820949865400211A1001"改为空字符串""
7. 更新了hospitalName从"伊犁哈萨克自治州友谊医院"改为"新疆维吾尔自治区第三人民医院"
8. 更新了tenantAppId从"20221008467800023553"改为空字符串""
9. 更新了mip.orgAppId从"1GGMN3PJ1079E2470B0A0000FCDB760A"改为"1HQTPGDM501Q3F60C80A0000CA9AB291"
10. 更新了mip.orgChnlCrtfCode从"BqK1kMStlhVDgN2uHf4EsP9BQzovPtfTZGwRsyhqMMYx1MunG//RTCcmH7CftmAe"改为"BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxvhx5UDJo

继续更新配置，我对机构相关信息进行了调整：

11. 将机构代码从"H65400200247"更改为"H65010300035"
12. 医院名称同步更新为"新疆维吾尔自治区第三人民医院"
13. 城市代码从"654000"变更为"650100"
14. 在mip.callUrl中的appId参数也相应更新
15. 通知URL调整为新的域名地址

这些修改反映了医疗机构的具体变更。</details></think>

---

已经成功根据 `alipay.setting` 文件更新了 `alipay.yaml` 文件。主要更新的配置项包括：

## 主要更新内容：

### MA（支付宝）配置更新：
- **appId**: `2021003154673754` → `2021004162624409`
- **merchantName**: `伊犁哈萨克自治州友谊医院` → `新疆维吾尔自治区第三人民医院`
- **merchantId**: `2088541094346781` → `2088331439446319`
- **pid**: `2088341243702554` → `""` (空值)
- **notifyUrl**: `https://ylyyyy.xjyqtl.cn:19000/open/alipay/onPay` → `https://xjzybyy.xjyqtl.cn/open/alipay/onPay`
- **hospitalName**: `伊犁哈萨克自治州友谊医院` → `新疆维吾尔自治区第三人民医院`
- **hospitalRegisterId**: `45820949865400211A1001` → `""` (空值)
- **tenantAppId**: `20221008467800023553` → `""` (空值)

### MIP（医保）配置更新：
- **orgAppId**: `1GGMN3PJ1079E2470B0A0000FCDB760A` → `1HQTPGDM501Q3F60C80A0000CA9AB291`
- **orgChnlCrtfCode**: 更新为新的机构渠道认证编码
- **orgCode**: `H65400200247` → `H65010300035`
- **orgName**: `伊犁哈萨克自治州友谊医院` → `新疆维吾尔自治区第三人民医院`
- **orgCityCode**: `654000` → `650100`
- **callUrl**: 更新了其中的 appId 参数
- **notifyUrl**: `https://ylyyyy.xjyqtl.cn:19000/open/alipay/onMipPay` → `https://xjzybyy.xjyqtl.cn/open/alipay/onMipPay`

配置文件已成功从伊犁哈萨克自治州友谊医院更新为新疆维吾尔自治区第三人民医院的配置。

---


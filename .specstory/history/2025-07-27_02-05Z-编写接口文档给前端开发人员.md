<!-- Generated by SpecStory -->

# 编写接口文档给前端开发人员 (2025-07-27 02:05Z)

_**User**_

为 `GET /api/medical-record-application/{id}/timeline` 接口编写面向前端开发人员的接口文档

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求为 `GET /api/medical-record-application/{id}/timeline` 接口编写面向前端开发人员的接口文档。

我先来分析这个接口的具体实现：

1. 路径：`/{id}/timeline`
2. 方法：GET
3. 需要会话验证：@RequireSession
4. 参数：
   - `id`：路径参数，申请记录ID（String类型）
   - `includeDetails`：查询参数，可选，默认为true（Boolean类型）
5. 返回：ResponseDto<List<MedicalRecordApplicationTimelineDto>>

从代码逻辑来看：
- 需要验证会话并获取openId
- 验证申请记录是否存在且属于当前用户
- 获取时间线数据并返回

我需要查看一下相关的DTO类结构，以了解返回数据的格式。</details></think>

---

我需要先查看相关的DTO类结构，以便为这个接口编写完整的文档。

---

<details>
                <summary>Searched codebase "MedicalRecordApplicationTimelineDto class definition structure" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L1-27 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L1-51 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L100-147 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L190-216 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L30-71 |
| `src/main/resources/mapper/ph/MedicalRecordApplicationTimelineMapper.xml` | L1-38 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L29-123 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L197-241 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L223-282 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L1-30 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-104 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-29 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationListResponseDto.java` | L1-107 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationStatisticsDto.java` | L1-112 |
| `src/main/java/space/lzhq/ph/mapper/MedicalRecordApplicationTimelineMapper.java` | L1-50 |
| `src/main/java/space/lzhq/ph/controller/MedicalRecordApplicationAdminController.java` | L206-257 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L82-138 |
| `doc/medical-record-application-business-analysis.md` | L110-162 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L1-27 |
| `doc/medical-record-application-business-analysis.md` | L162-222 |
| `ddl/ph/medical_record_application_timeline.sql` | L1-22 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationListResponseDto.java` | L107-190 |
| `src/main/java/space/lzhq/ph/controller/MedicalRecordApplicationAdminController.java` | L1-26 |

</details>

---

<details>
                <summary>Searched codebase "ResponseDto class structure fields" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/ResponseDto.java` | L1-40 |
| `src/main/java/space/lzhq/ph/dto/response/BaseResponseDto.java` | L1-18 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/AttachmentDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardOcrResultDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/dto/response/EnumItemDto.java` | L1-26 |
| `src/main/java/space/lzhq/ph/dto/response/EnumDefinitionResponseDto.java` | L1-28 |
| `src/main/kotlin/space/lzhq/ph/dto/ReportListDto.kt` | L1-12 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardUploadResponseDto.java` | L1-43 |
| `src/main/kotlin/space/lzhq/ph/dto/InpatientInfoDto.kt` | L30-78 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/SimpleUploadResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-104 |
| `src/main/kotlin/space/lzhq/ph/dto/InpatientInfoDto.kt` | L1-30 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundListResponseDto.java` | L1-111 |
| `src/main/kotlin/space/lzhq/ph/controller/EnumController.kt` | L1-56 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationListResponseDto.java` | L1-107 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundAdminListResponseDto.java` | L1-113 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundTimelineDto.java` | L1-84 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/BankCardOcrResultDto.java` | L1-20 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationStatisticsDto.java` | L1-112 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationAdminListResponseDto.java` | L1-108 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L104-208 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationListResponseDto.java` | L107-190 |
| `src/main/kotlin/space/lzhq/ph/domain/RechargeResult.kt` | L1-64 |

</details>

---

<think><details><summary>Thought Process</summary>好的，现在我有了MedicalRecordApplicationTimelineDto和ResponseDto的结构信息。让我再查看一下相关的枚举类型定义，以便提供更完整的接口文档。</details></think>

---

<details>
                <summary>Searched codebase "MedicalRecordApplicationEventType enum values definition" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L36-70 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L1-36 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L1-70 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L102-164 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L51-102 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L29-123 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L82-138 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/resources/mapper/ph/MedicalRecordApplicationTimelineMapper.xml` | L1-38 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L100-147 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L123-223 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L90-122 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L71-90 |
| `src/main/resources/templates/ph/medicalRecordApplication/medical-record-application-detail.html` | L1487-1525 |
| `ddl/ph/medical_record_application_timeline.sql` | L1-22 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-29 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L223-282 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L1-51 |
| `doc/medical-record-application-business-analysis.md` | L162-222 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L122-150 |
| `src/main/java/space/lzhq/ph/mapper/MedicalRecordApplicationTimelineMapper.java` | L1-50 |
| `medical_record_application.sql` | L1-41 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L197-241 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L30-71 |

</details>

---

<details>
                <summary>Searched codebase "TimelineOperatorType enum values definition" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/enums/TimelineOperatorType.java` | L1-37 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/java/space/lzhq/ph/domain/CardRefundTimeline.java` | L1-88 |
| `src/main/resources/mapper/ph/MedicalRecordApplicationTimelineMapper.xml` | L1-38 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L1-51 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L1-27 |
| `src/main/java/space/lzhq/ph/service/ICardRefundTimelineService.java` | L1-51 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L100-147 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/resources/mapper/ph/CardRefundTimelineMapper.xml` | L1-38 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L30-71 |
| `src/main/java/space/lzhq/ph/domain/CardRefundTimeline.java` | L88-147 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L1-36 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundTimelineDto.java` | L1-84 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L1-30 |
| `ddl/ph/medical_record_application_timeline.sql` | L1-22 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L90-122 |
| `src/main/java/space/lzhq/ph/service/impl/CardRefundTimelineServiceImpl.java` | L28-69 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L51-102 |
| `src/main/resources/mapper/ph/MedicalRecordApplicationTimelineMapper.xml` | L38-58 |
| `src/main/java/space/lzhq/ph/enums/CardRefundEventType.java` | L1-59 |
| `doc/medical-record-application-business-analysis.md` | L327-398 |
| `src/main/java/space/lzhq/ph/mapper/MedicalRecordApplicationTimelineMapper.java` | L1-50 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L197-241 |
| `src/main/java/space/lzhq/ph/service/impl/CardRefundTimelineServiceImpl.java` | L1-28 |

</details>

---

<details>
                <summary>Searched codebase "MedicalRecordApplicationStatus enum values definition" • **24** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L1-70 |
| `doc/medical-record-application-business-analysis.md` | L162-222 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L123-223 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L36-70 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L1-36 |
| `medical_record_application.sql` | L1-41 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L70-81 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-29 |
| `medical_record_application.sql` | L41-75 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordMailingStatus.java` | L1-44 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L29-123 |
| `src/main/resources/templates/ph/medicalRecordApplication/medical-record-application.html` | L645-700 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L223-282 |
| `src/main/java/space/lzhq/ph/dto/request/MedicalRecordApplicationSearchForm.java` | L1-99 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationService.java` | L1-18 |
| `src/main/kotlin/space/lzhq/ph/controller/EnumController.kt` | L1-56 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L1-38 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationStatisticsDto.java` | L1-112 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L1-27 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordMailing.java` | L1-93 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L741-770 |
| `doc/medical-record-application-admin-development-summary.md` | L240-295 |
| `doc/medical-record-application-submit-api.md` | L131-203 |

</details>

---

<think><details><summary>Thought Process</summary>现在我已经收集到了足够的信息来编写这个接口的详细文档。让我整理一下：

1. 接口路径：GET /api/medical-record-application/{id}/timeline
2. 需要会话验证
3. 参数：
   - id：路径参数，申请记录ID
   - includeDetails：查询参数，可选，默认为true
4. 返回：ResponseDto<List<MedicalRecordApplicationTimelineDto>>

从代码分析可以看到：
- ResponseDto有 code、msg、data 字段
- MedicalRecordApplicationTimelineDto包含事件信息、操作者信息、状态变更信息等
- 各种枚举类型的定义

现在我可以编写完整的接口文档了。</details></think>

---

现在我将为您编写 `GET /api/medical-record-application/{id}/timeline` 接口的详细文档。

---

<details><summary>Edit file: 病历申请时间线接口文档.md</summary>

创建一份面向前端开发人员的接口文档，包含接口的完整说明、参数定义、响应格式、状态码、示例等内容

**Chunk 1**
Lines added: 457, lines removed: 1

```diff
@@ -1,1 +1,457 @@
- 
+ # 病历申请时间线接口文档
+ 
+ ## 接口概述
+ 
+ 获取指定病历复印申请记录的操作时间线，展示申请从创建到完成的全过程。
+ 
+ ## 接口信息
+ 
+ - **接口地址：** `GET /api/medical-record-application/{id}/timeline`
+ - **请求方式：** GET
+ - **内容类型：** application/json
+ - **认证要求：** 需要用户会话认证（@RequireSession）
+ 
+ ## 请求参数
+ 
+ ### 路径参数
+ 
+ | 参数名 | 类型   | 必填 | 说明           | 示例                               |
+ |--------|--------|------|----------------|------------------------------------|
+ | id     | String | 是   | 病历申请记录ID | "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6" |
+ 
+ ### 查询参数
+ 
+ | 参数名          | 类型    | 必填 | 默认值 | 说明                                                   | 示例  |
+ |-----------------|---------|------|--------|--------------------------------------------------------|-------|
+ | includeDetails  | Boolean | 否   | true   | 是否包含详细操作记录。false时只返回重要状态变更事件    | true  |
+ 
+ ## 权限验证
+ 
+ - 需要有效的用户会话
+ - 只能查询属于当前用户的申请记录
+ - 如果申请记录不存在或不属于当前用户，将返回相应错误
+ 
+ ## 响应格式
+ 
+ ### 成功响应
+ 
+ ```json
+ {
+   "code": 0,
+   "msg": "操作成功",
+   "data": [
+     {
+       "id": "timeline_event_id_1",
+       "eventType": {
+         "code": "APPLICATION_CREATED",
+         "description": "创建申请"
+       },
+       "eventDescription": "用户创建了病历复印申请",
+       "operatorType": {
+         "code": "APPLICANT",
+         "description": "申请人"
+       },
+       "operatorName": "张三",
+       "oldStatus": null,
+       "newStatus": {
+         "code": "DRAFT",
+         "description": "草稿（待提交）"
+       },
+       "eventTime": "2024-01-15 10:30:00",
+       "additionalData": {
+         "copyPurpose": "REIMBURSEMENT"
+       },
+       "isImportant": true,
+       "icon": "create",
+       "color": "blue"
+     },
+     {
+       "id": "timeline_event_id_2",
+       "eventType": {
+         "code": "AGENT_INFO_SET",
+         "description": "设置代办人信息"
+       },
+       "eventDescription": "设置了代办人信息",
+       "operatorType": {
+         "code": "APPLICANT",
+         "description": "申请人"
+       },
+       "operatorName": "张三",
+       "oldStatus": null,
+       "newStatus": null,
+       "eventTime": "2024-01-15 10:35:00",
+       "additionalData": {
+         "agentName": "李四",
+         "agentRelation": "SPOUSE"
+       },
+       "isImportant": false,
+       "icon": "user",
+       "color": "green"
+     },
+     {
+       "id": "timeline_event_id_3",
+       "eventType": {
+         "code": "APPLICATION_SUBMITTED",
+         "description": "提交申请"
+       },
+       "eventDescription": "申请已提交，等待审核",
+       "operatorType": {
+         "code": "APPLICANT",
+         "description": "申请人"
+       },
+       "operatorName": "张三",
+       "oldStatus": {
+         "code": "DRAFT",
+         "description": "草稿（待提交）"
+       },
+       "newStatus": {
+         "code": "SUBMITTED",
+         "description": "已提交（待审核）"
+       },
+       "eventTime": "2024-01-15 10:45:00",
+       "additionalData": null,
+       "isImportant": true,
+       "icon": "submit",
+       "color": "orange"
+     },
+     {
+       "id": "timeline_event_id_4",
+       "eventType": {
+         "code": "APPLICATION_APPROVED",
+         "description": "审核通过"
+       },
+       "eventDescription": "申请审核通过，请及时支付",
+       "operatorType": {
+         "code": "REVIEWER",
+         "description": "审核员"
+       },
+       "operatorName": "审核员王五",
+       "oldStatus": {
+         "code": "SUBMITTED",
+         "description": "已提交（待审核）"
+       },
+       "newStatus": {
+         "code": "APPROVED",
+         "description": "审核通过"
+       },
+       "eventTime": "2024-01-15 14:20:00",
+       "additionalData": {
+         "fee": 50.00,
+         "shippingFee": 15.00
+       },
+       "isImportant": true,
+       "icon": "approve",
+       "color": "green"
+     }
+   ]
+ }
+ ```
+ 
+ ### 错误响应
+ 
+ ```json
+ {
+   "code": 500,
+   "msg": "会话无效",
+   "data": null
+ }
+ ```
+ 
+ ```json
+ {
+   "code": 404,
+   "msg": "申请记录不存在或无访问权限",
+   "data": null
+ }
+ ```
+ 
+ ## 响应字段说明
+ 
+ ### ResponseDto 字段
+ 
+ | 字段名 | 类型   | 说明           |
+ |--------|--------|----------------|
+ | code   | int    | 响应状态码     |
+ | msg    | String | 响应消息       |
+ | data   | Array  | 时间线事件列表 |
+ 
+ ### MedicalRecordApplicationTimelineDto 字段
+ 
+ | 字段名            | 类型     | 说明                 | 备注                      |
+ |-------------------|----------|----------------------|---------------------------|
+ | id                | String   | 事件ID               | 唯一标识                  |
+ | eventType         | Object   | 事件类型             | 包含code和description     |
+ | eventDescription  | String   | 事件描述             | 人可读的事件说明          |
+ | operatorType      | Object   | 操作者类型           | 包含code和description     |
+ | operatorName      | String   | 操作者姓名           | 可能为空                  |
+ | oldStatus         | Object   | 变更前状态           | 状态变更事件才有此字段    |
+ | newStatus         | Object   | 变更后状态           | 状态变更事件才有此字段    |
+ | eventTime         | String   | 事件时间             | 格式：yyyy-MM-dd HH:mm:ss |
+ | additionalData    | Object   | 额外数据             | 可选，包含事件相关的额外信息 |
+ | isImportant       | Boolean  | 是否为重要事件       | 用于UI突出显示            |
+ | icon              | String   | 事件图标             | 前端图标标识              |
+ | color             | String   | 事件颜色             | 前端颜色标识              |
+ 
+ ## 枚举值说明
+ 
+ ### 事件类型 (MedicalRecordApplicationEventType)
+ 
+ #### 状态变更事件
+ | Code                    | Description  |
+ |-------------------------|-------------|
+ | APPLICATION_CREATED     | 创建申请    |
+ | APPLICATION_SUBMITTED   | 提交申请    |
+ | APPLICATION_APPROVED    | 审核通过    |
+ | APPLICATION_REJECTED    | 审核驳回    |
+ | APPLICATION_RESUBMITTED | 重新提交    |
+ | APPLICATION_COMPLETED   | 完成申请    |
+ | APPLICATION_DELETED     | 删除申请    |
+ | APPLICATION_PAID        | 支付完成    |
+ | APPLICATION_DELIVERED   | 已寄出      |
+ 
+ #### 信息变更事件
+ | Code                 | Description     |
+ |----------------------|-----------------|
+ | PATIENT_INFO_UPDATED | 更新患者信息    |
+ | AGENT_INFO_SET       | 设置代办人信息  |
+ | AGENT_INFO_CLEARED   | 清除代办人信息  |
+ | AGENT_INFO_UPDATED   | 更新代办人信息  |
+ | MEDICAL_INFO_UPDATED | 更新病历信息    |
+ | MAILING_INFO_SET     | 设置邮寄信息    |
+ | ADDRESS_INFO_UPDATED | 更新地址信息    |
+ 
+ #### 附件相关事件
+ | Code               | Description |
+ |--------------------|-------------|
+ | ATTACHMENT_UPLOADED| 上传附件    |
+ | ATTACHMENT_DELETED | 删除附件    |
+ 
+ ### 操作者类型 (TimelineOperatorType)
+ 
+ | Code      | Description |
+ |-----------|-------------|
+ | APPLICANT | 申请人      |
+ | AGENT     | 代办人      |
+ | SYSTEM    | 系统        |
+ | REVIEWER  | 审核员      |
+ | ADMIN     | 管理员      |
+ 
+ ### 申请状态 (MedicalRecordApplicationStatus)
+ 
+ | Code      | Description          |
+ |-----------|----------------------|
+ | DRAFT     | 草稿（待提交）       |
+ | SUBMITTED | 已提交（待审核）     |
+ | APPROVED  | 审核通过             |
+ | REJECTED  | 审核驳回（待整改）   |
+ | PAID      | 支付成功             |
+ | COMPLETED | 已完成               |
+ | DELIVERED | 邮寄完成             |
+ 
+ ## 参数说明
+ 
+ ### includeDetails 参数
+ 
+ - **true（默认）**：返回所有事件，包括状态变更和信息修改
+ - **false**：只返回重要的状态变更事件，用于简化时间线显示
+ 
+ 重要事件包括：
+ - APPLICATION_CREATED（创建申请）
+ - APPLICATION_SUBMITTED（提交申请）
+ - APPLICATION_APPROVED（审核通过）
+ - APPLICATION_REJECTED（审核驳回）
+ - APPLICATION_RESUBMITTED（重新提交）
+ - APPLICATION_COMPLETED（完成申请）
+ - APPLICATION_PAID（支付完成）
+ - APPLICATION_DELIVERED（已寄出）
+ 
+ ## 使用示例
+ 
+ ### JavaScript/TypeScript 示例
+ 
+ ```javascript
+ // 获取完整时间线
+ async function getFullTimeline(applicationId) {
+   try {
+     const response = await fetch(`/api/medical-record-application/${applicationId}/timeline`);
+     const result = await response.json();
+     
+     if (result.code === 0) {
+       return result.data;
+     } else {
+       throw new Error(result.msg);
+     }
+   } catch (error) {
+     console.error('获取时间线失败:', error);
+     throw error;
+   }
+ }
+ 
+ // 获取简化时间线（只包含重要事件）
+ async function getImportantTimeline(applicationId) {
+   try {
+     const response = await fetch(
+       `/api/medical-record-application/${applicationId}/timeline?includeDetails=false`
+     );
+     const result = await response.json();
+     
+     if (result.code === 0) {
+       return result.data;
+     } else {
+       throw new Error(result.msg);
+     }
+   } catch (error) {
+     console.error('获取简化时间线失败:', error);
+     throw error;
+   }
+ }
+ 
+ // 时间线组件渲染示例
+ function renderTimeline(timelineData) {
+   return timelineData.map(event => ({
+     id: event.id,
+     title: event.eventType.description,
+     description: event.eventDescription,
+     time: event.eventTime,
+     operator: event.operatorName,
+     operatorType: event.operatorType.description,
+     isImportant: event.isImportant,
+     icon: event.icon,
+     color: event.color,
+     hasStatusChange: event.oldStatus && event.newStatus,
+     statusChange: event.oldStatus && event.newStatus ? {
+       from: event.oldStatus.description,
+       to: event.newStatus.description
+     } : null,
+     additionalInfo: event.additionalData
+   }));
+ }
+ ```
+ 
+ ### Vue.js 组件示例
+ 
+ ```vue
+ <template>
+   <div class="timeline-container">
+     <div class="timeline-header">
+       <h3>申请进度</h3>
+       <el-switch
+         v-model="showDetailsOnly"
+         active-text="详细模式"
+         inactive-text="简化模式"
+         @change="loadTimeline"
+       />
+     </div>
+     
+     <el-timeline>
+       <el-timeline-item
+         v-for="event in timeline"
+         :key="event.id"
+         :timestamp="event.eventTime"
+         :color="getEventColor(event)"
+         :icon="getEventIcon(event)"
+         :size="event.isImportant ? 'large' : 'normal'"
+       >
+         <div class="timeline-content">
+           <h4>{{ event.eventType.description }}</h4>
+           <p>{{ event.eventDescription }}</p>
+           
+           <div v-if="event.operatorName" class="operator-info">
+             <span class="operator-type">{{ event.operatorType.description }}</span>
+             <span class="operator-name">{{ event.operatorName }}</span>
+           </div>
+           
+           <div v-if="event.oldStatus && event.newStatus" class="status-change">
+             <span class="old-status">{{ event.oldStatus.description }}</span>
+             <el-icon><ArrowRight /></el-icon>
+             <span class="new-status">{{ event.newStatus.description }}</span>
+           </div>
+           
+           <div v-if="event.additionalData" class="additional-data">
+             <pre>{{ JSON.stringify(event.additionalData, null, 2) }}</pre>
+           </div>
+         </div>
+       </el-timeline-item>
+     </el-timeline>
+   </div>
+ </template>
+ 
+ <script setup>
+ import { ref, onMounted } from 'vue'
+ import { getTimeline } from '@/api/medical-record'
+ 
+ const props = defineProps({
+   applicationId: {
+     type: String,
+     required: true
+   }
+ })
+ 
+ const timeline = ref([])
+ const showDetailsOnly = ref(true)
+ const loading = ref(false)
+ 
+ const loadTimeline = async () => {
+   loading.value = true
+   try {
+     const data = await getTimeline(props.applicationId, showDetailsOnly.value)
+     timeline.value = data
+   } catch (error) {
+     console.error('加载时间线失败:', error)
+   } finally {
+     loading.value = false
+   }
+ }
+ 
+ const getEventColor = (event) => {
+   return event.color || '#409EFF'
+ }
+ 
+ const getEventIcon = (event) => {
+   const iconMap = {
+     'create': 'Plus',
+     'submit': 'Upload',
+     'approve': 'Check',
+     'reject': 'Close',
+     'user': 'User'
+   }
+   return iconMap[event.icon] || 'Document'
+ }
+ 
+ onMounted(() => {
+   loadTimeline()
+ })
+ </script>
+ ```
+ 
+ ## 错误处理
+ 
+ | 状态码 | 错误类型           | 处理建议                   |
+ |--------|--------------------|----------------------------|
+ | 401    | 会话无效           | 引导用户重新登录           |
+ | 403    | 权限不足           | 显示权限错误提示           |
+ | 404    | 申请记录不存在     | 显示记录不存在提示         |
+ | 500    | 服务器内部错误     | 显示通用错误提示，建议重试 |
+ 
+ ## 注意事项
+ 
+ 1. **时间排序**：时间线事件按 `eventTime` 倒序排列（最新的在前）
+ 2. **权限验证**：接口会验证当前用户是否有权限查看指定的申请记录
+ 3. **数据完整性**：某些字段可能为空（如 `operatorName`、`additionalData`）
+ 4. **状态变更**：只有状态变更事件才会有 `oldStatus` 和 `newStatus` 字段
+ 5. **图标和颜色**：`icon` 和 `color` 字段由后端根据事件类型自动设置
+ 6. **性能考虑**：建议根据业务需要选择是否包含详细事件，以优化加载速度
+ 
+ ## 常见问题
+ 
+ **Q: 为什么有些事件没有操作者姓名？**
+ A: 系统自动触发的事件可能没有具体的操作者姓名。
+ 
+ **Q: additionalData 字段包含什么内容？**
+ A: 根据事件类型不同，可能包含驳回原因、费用信息、附件信息等相关数据。
+ 
+ **Q: 如何判断事件是否为状态变更？**
+ A: 检查 `oldStatus` 和 `newStatus` 字段是否都不为空。
+ 
+ **Q: 时间线事件会被删除吗？**
+ A: 一般情况下时间线事件不会被删除，保证审计追踪的完整性。 
```

</details>

---

<think><details><summary>Thought Process</summary>我已经成功创建了一份完整的接口文档，这份文档包含了：

1. 接口的基本信息（地址、方法、认证要求等）
2. 详细的请求参数说明（路径参数和查询参数）
3. 权限验证说明
4. 完整的响应格式示例
5. 所有字段的详细说明
6. 枚举值的完整列表
7. 参数说明
8. 实际的使用示例（JavaScript和Vue.js）
9. 错误处理说明
10. 注意事项和常见问题

这份文档对前端开发人员来说应该是非常有用的，它提供了接口使用所需的所有信息，包括实际的代码示例。</details></think>

---

我已经为您创建了一份完整的 `GET /api/medical-record-application/{id}/timeline` 接口文档。这份文档包含了以下重要内容：

## 文档亮点：

1. **完整的接口信息** - 包括接口地址、请求方式、认证要求
2. **详细的参数说明** - 路径参数和查询参数的完整说明
3. **完整的响应格式** - 包含成功和失败情况的JSON示例
4. **全面的字段说明** - 每个返回字段的类型、含义和备注
5. **枚举值映射表** - 所有相关枚举的完整列表和说明
6. **实用的代码示例** - JavaScript/TypeScript 和 Vue.js 的实际使用示例
7. **错误处理指南** - 各种错误状态的处理建议
8. **最佳实践** - 性能优化建议和注意事项

## 核心功能说明：

该接口用于获取病历申请的完整时间线，包括：
- **状态变更事件** - 申请创建、提交、审核、支付等关键状态
- **信息变更事件** - 患者信息、代办人信息、邮寄信息等修改记录
- **附件相关事件** - 附件上传和删除记录

通过 `includeDetails` 参数，前端可以选择显示完整时间线或仅显示重要状态变更，满足不同场景的展示需求。

这份文档为前端开发人员提供了完整的集成指南，包含实际可用的代码示例，可以直接用于项目开发。

---


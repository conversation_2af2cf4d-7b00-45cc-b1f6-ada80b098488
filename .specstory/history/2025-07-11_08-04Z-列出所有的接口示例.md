<!-- Generated by SpecStory -->

# 列出所有的接口示例 (2025-07-11 08:04Z)

_**User**_

列出所有的 /api/card-refund-registration 接口，示例：
提交患者信息：POST /api/card-refund-registration/patientForm

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要我列出所有的 `/api/card-refund-registration` 接口，并提供了一个示例格式。我需要查看附件中的 `CardRefundApiController.kt` 文件，分析所有的 REST 接口端点。

从代码中可以看到这个控制器的 `@RequestMapping("/api/card-refund-registration")`，所以基础路径是 `/api/card-refund-registration`。

让我逐一列出所有的接口：

1. `@PostMapping("/patientForm")` - 提交患者信息
2. `@PutMapping("/{id}/clear-agent-flag")` - 清除代理标志
3. `@PutMapping("/{id}/agent")` - 更新代理信息
4. `@PutMapping("/{id}/bankCard")` - 更新银行卡信息
5. `@GetMapping("/{id}")` - 获取注册详情
6. `@GetMapping("")` - 获取用户注册列表
7. `@GetMapping("/enums")` - 获取枚举定义
8. `@PutMapping("/{id}/submit")` - 提交注册
9. `@GetMapping("/{id}/timeline")` - 获取注册时间线

我需要按照用户提供的格式来列出这些接口，包括功能描述和HTTP方法+路径。</details></think>

---

根据 `CardRefundApiController.kt` 文件，以下是所有的 `/api/card-refund-registration` 接口：

## 余额清退登记相关接口

1. **提交患者信息**：`POST /api/card-refund-registration/patientForm`
2. **清除代理标志**：`PUT /api/card-refund-registration/{id}/clear-agent-flag`
3. **更新代理信息**：`PUT /api/card-refund-registration/{id}/agent`
4. **更新银行卡信息**：`PUT /api/card-refund-registration/{id}/bankCard`
5. **获取注册详情**：`GET /api/card-refund-registration/{id}`
6. **获取用户注册列表**：`GET /api/card-refund-registration`
7. **获取枚举定义**：`GET /api/card-refund-registration/enums`
8. **提交注册申请**：`PUT /api/card-refund-registration/{id}/submit`
9. **获取注册时间线**：`GET /api/card-refund-registration/{id}/timeline`

## 接口说明

- 除了 `/enums` 接口外，其他接口都需要有效的会话（`@RequireSession`）
- 涉及数据修改的接口都使用了事务处理（`@Transactional`）
- 所有接口都返回统一的 `ResponseDto` 格式
- `{id}` 为路径参数，表示余额清退登记记录的ID

---


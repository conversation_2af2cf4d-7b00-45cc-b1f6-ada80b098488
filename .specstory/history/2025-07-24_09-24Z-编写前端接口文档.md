<!-- Generated by SpecStory -->

# 编写前端接口文档 (2025-07-24 09:24Z)

_**User**_

为 `POST /api/medical-record-application/patientForm` 接口编写面向前端开发人员的接口文档

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求为 `POST /api/medical-record-application/patientForm` 接口编写面向前端开发人员的接口文档。

从提供的代码中，我可以看到：

1. 这是一个 `POST` 接口
2. 路径是 `/api/medical-record-application/patientForm`
3. 接口需要 `@RequireSession` 注解，说明需要会话验证
4. 使用 `@Transactional` 注解，说明有事务性操作
5. 接收参数是 `MedicalRecordApplicationPatientFormDto`
6. 返回结果是 `ResponseDto<MedicalRecordApplicationDetailResponseDto?>`

从 `MedicalRecordApplicationPatientFormDto` 类中，我可以看到：
- patientIdCardAttachmentId: 患者身份证附件ID (可选)
- patientBirthCertificateAttachmentId: 患者出生证附件ID (可选)  
- patientIdCardNo: 患者身份证号 (可选，但如果提供需要格式正确)
- patientName: 申报患者姓名 (必填，不能包含特殊字符)
- patientGender: 患者性别 (必填，需要通过 @ValidGender 验证)
- patientBirthDate: 患者出生日期 (必填，格式 yyyy-MM-dd)

还有两个验证规则：
1. 身份证号如果不为空，需要格式正确；如果为空，则身份证附件ID也必须为空
2. 患者出生证附件ID和身份证附件ID不能同时为空
</details></think>

---

我来为这个病历邮寄申请接口编写详细的前端接口文档。

---

<details>
            <summary>Searched codebase "如何获取患者性别的有效值和ValidGender注解验证规则" • **12** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/annotation/ValidGender.java` | L1-41 |
| `src/main/java/space/lzhq/ph/annotation/ValidTimePeriod.java` | L1-42 |
| `src/main/java/space/lzhq/ph/annotation/ValidDoctorCode.java` | L1-42 |
| `src/main/java/space/lzhq/ph/annotation/ValidDepartmentCode.java` | L1-41 |
| `src/main/java/space/lzhq/ph/annotation/ValidRejectReason.java` | L1-32 |
| `src/main/java/space/lzhq/ph/annotation/ValidOrderNo.java` | L1-41 |
| `src/main/java/space/lzhq/ph/annotation/ValidScheduleId.java` | L1-41 |
| `src/main/java/space/lzhq/ph/annotation/RejectReasonValidator.java` | L1-44 |
| `src/main/kotlin/space/lzhq/ph/annotation/RequireActivePatient.kt` | L1-9 |
| `src/main/kotlin/space/lzhq/ph/annotation/RequireSession.kt` | L1-11 |
| `src/main/kotlin/space/lzhq/ph/annotation/RequirePscCarerSession.kt` | L1-7 |
| `src/main/kotlin/space/lzhq/ph/annotation/RequirePscNurseSession.kt` | L1-7 |

</details>

---

<think><details><summary>Thought Process</summary>现在我已经获取到了ValidGender注解的信息，可以看到性别字段的有效值是"男"或"女"。

接下来我需要查找一下ResponseDto的结构，以及MedicalRecordApplicationDetailResponseDto的结构，这样我就能编写完整的接口文档了。</details></think>

---

<details>
            <summary>Searched codebase "ResponseDto类的结构和字段定义" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/ResponseDto.java` | L1-40 |
| `src/main/java/space/lzhq/ph/dto/response/BaseResponseDto.java` | L1-18 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/AttachmentDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/dto/response/EnumDefinitionResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/EnumItemDto.java` | L1-26 |
| `src/main/kotlin/space/lzhq/ph/dto/ReportListDto.kt` | L1-12 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardOcrResultDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardUploadResponseDto.java` | L1-43 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/SimpleUploadResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-103 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundListResponseDto.java` | L1-111 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundTimelineDto.java` | L1-84 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/BankCardOcrResultDto.java` | L1-20 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundAdminListResponseDto.java` | L1-113 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundStatisticsDto.java` | L1-101 |
| `src/main/kotlin/space/lzhq/ph/domain/RechargeResult.kt` | L1-64 |
| `src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt` | L32-83 |
| `src/main/java/space/lzhq/ph/dto/mapper/CardRefundMapper.java` | L44-95 |
| `src/main/java/space/lzhq/ph/domain/OutpatientPaymentRecord.java` | L1-95 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L24-83 |
| `src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt` | L1-32 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundListResponseDto.java` | L111-143 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L103-134 |

</details>

---

<think><details><summary>Thought Process</summary>好的，现在我已经收集了足够的信息来编写完整的接口文档。从代码中我可以看到：

1. ResponseDto的结构：
   - code: int - 响应码 (0 = 成功，其他值表示错误)
   - msg: String - 响应消息
   - data: T - 响应数据

2. MedicalRecordApplicationDetailResponseDto的结构包含了病历邮寄申请的详细信息，包括患者信息、代办信息、状态等。

3. ValidGender注解验证性别必须为"男"或"女"

4. 请求参数的验证规则我已经从MedicalRecordApplicationPatientFormDto中了解到了

现在我可以编写完整的接口文档了。</details></think>

---

根据代码分析，我来为这个病历邮寄申请接口编写详细的前端接口文档：

# 病历邮寄申请患者表单接口文档

## 接口基本信息

- **接口名称**: 提交病历邮寄申请患者表单
- **请求方法**: `POST`
- **请求路径**: `/api/medical-record-application/patientForm`
- **功能描述**: 提交病历邮寄申请中的患者基本信息表单
- **认证要求**: 需要有效的用户会话
- **事务性**: 支持事务回滚

## 请求头要求

```http
Content-Type: application/json
Accept: application/json
```

## 请求参数

### 请求体 (JSON格式)

| 字段名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| `patientIdCardAttachmentId` | String | 否 | 患者身份证附件ID | 与出生证附件ID不能同时为空 |
| `patientBirthCertificateAttachmentId` | String | 否 | 患者出生证附件ID | 与身份证附件ID不能同时为空 |
| `patientIdCardNo` | String | 否 | 患者身份证号 | 如果提供，必须为有效的身份证号格式 |
| `patientName` | String | 是 | 申报患者姓名 | 不能为空，不能包含特殊字符 |
| `patientGender` | String | 是 | 患者性别 | 必须为"男"或"女" |
| `patientBirthDate` | String | 是 | 患者出生日期 | 格式：yyyy-MM-dd |

### 验证规则详解

1. **附件ID验证**: 
   - 患者身份证附件ID和出生证附件ID不能同时为空
   - 至少需要提供其中一个证件附件

2. **身份证号验证**:
   - 如果提供身份证号，必须符合中国身份证号格式
   - 如果身份证号为空，则身份证附件ID也必须为空

3. **姓名验证**:
   - 不能包含HTML特殊字符
   - 不能为空字符串

4. **性别验证**:
   - 只接受"男"或"女"两个值

5. **出生日期验证**:
   - 必须符合 yyyy-MM-dd 格式
   - 不能为空

## 请求示例

### 使用身份证的请求示例
```json
{
  "patientIdCardAttachmentId": "att_123456789",
  "patientIdCardNo": "110101199001011234",
  "patientName": "张三",
  "patientGender": "男",
  "patientBirthDate": "1990-01-01"
}
```

### 使用出生证的请求示例
```json
{
  "patientBirthCertificateAttachmentId": "att_987654321",
  "patientName": "李四",
  "patientGender": "女",
  "patientBirthDate": "2020-06-15"
}
```

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": "app_123456789",
    "openId": "wx_openid_123",
    "patientIdCardAttachment": {
      "id": "att_123456789",
      "openId": "wx_openid_123",
      "masterId": "app_123456789",
      "type": "ID_CARD",
      "fileName": "身份证.jpg",
      "fileUrl": "https://example.com/files/idcard.jpg",
      "createTime": "2024-01-15 10:30:00"
    },
    "patientBirthCertificateAttachment": null,
    "patientIdCardNo": "110101199001011234",
    "patientName": "张三",
    "patientGender": "男",
    "patientBirthDate": "1990-01-01",
    "agentFlag": 0,
    "agentName": null,
    "agentIdCardNo": null,
    "agentIdCardAttachment": null,
    "agentMobile": null,
    "agentRelation": null,
    "status": "DRAFT",
    "submitTime": null,
    "approveTime": null,
    "rejectReason": null,
    "correctionAdvice": null,
    "createTime": "2024-01-15 10:30:00",
    "updateTime": "2024-01-15 10:30:00",
    "canApprove": false
  }
}
```

### 错误响应

#### 会话无效
```json
{
  "code": 500,
  "msg": "会话无效",
  "data": null
}
```

#### 参数验证失败
```json
{
  "code": 400,
  "msg": "参数验证失败",
  "data": {
    "patientName": "申报患者姓名不能为空",
    "patientGender": "患者性别必须是男或女",
    "patientBirthDate": "患者出生日期不能为空"
  }
}
```

#### 业务处理失败
```json
{
  "code": 500,
  "msg": "病历邮寄申请提交失败，请重试",
  "data": null
}
```

## 响应字段说明

### 成功响应data字段详解

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `id` | String | 申请记录唯一标识 |
| `openId` | String | 用户微信OpenID |
| `patientIdCardAttachment` | Object | 患者身份证附件信息 |
| `patientBirthCertificateAttachment` | Object | 患者出生证附件信息 |
| `patientIdCardNo` | String | 患者身份证号 |
| `patientName` | String | 患者姓名 |
| `patientGender` | String | 患者性别 |
| `patientBirthDate` | String | 患者出生日期 |
| `agentFlag` | Integer | 是否代办 (0-否, 1-是) |
| `status` | String | 申请状态 |
| `submitTime` | String | 提交时间 |
| `approveTime` | String | 审批时间 |
| `createTime` | String | 创建时间 |
| `updateTime` | String | 更新时间 |
| `canApprove` | Boolean | 是否可以审批 |

### 申请状态说明

| 状态值 | 描述 |
|--------|------|
| `DRAFT` | 草稿状态 |
| `SUBMITTED` | 已提交 |
| `APPROVED` | 已通过 |
| `REJECTED` | 已驳回 |
| `RESUBMITTED` | 已重新提交 |

## 错误码说明

| 错误码 | 错误信息 | 处理建议 |
|--------|----------|----------|
| 0 | 操作成功 | 正常处理返回数据 |
| 400 | 参数验证失败 | 检查请求参数格式和内容 |
| 500 | 会话无效 | 重新登录获取有效会话 |
| 500 | 病历邮寄申请提交失败，请重试 | 稍后重试或联系技术支持 |

## 前端集成建议

### 1. 请求前验证
```javascript
// 前端验证示例
function validatePatientForm(formData) {
  const errors = [];
  
  if (!formData.patientName?.trim()) {
    errors.push('患者姓名不能为空');
  }
  
  if (!['男', '女'].includes(formData.patientGender)) {
    errors.push('请选择正确的性别');
  }
  
  if (!formData.patientBirthDate) {
    errors.push('请选择出生日期');
  }
  
  if (!formData.patientIdCardAttachmentId && !formData.patientBirthCertificateAttachmentId) {
    errors.push('请至少上传身份证或出生证明之一');
  }
  
  return errors;
}
```

### 2. 请求示例
```javascript
async function submitPatientForm(formData) {
  try {
    const response = await fetch('/api/medical-record-application/patientForm', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 会话信息通过Cookie自动发送
      },
      body: JSON.stringify(formData)
    });
    
    const result = await response.json();
    
    if (result.code === 0) {
      // 提交成功
      console.log('申请提交成功', result.data);
      return result.data;
    } else {
      // 提交失败
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('提交失败:', error.message);
    throw error;
  }
}
```

### 3. 错误处理
```javascript
function handleApiError(error, result) {
  if (result?.code === 500 && result.msg === '会话无效') {
    // 重定向到登录页面
    window.location.href = '/login';
  } else if (result?.code === 400) {
    // 参数验证错误，显示具体错误信息
    showValidationErrors(result.data);
  } else {
    // 其他错误，显示通用错误信息
    showErrorMessage(result?.msg || '操作失败，请重试');
  }
}
```

## 注意事项

1. **会话管理**: 调用此接口前确保用户已登录且会话有效
2. **附件预上传**: 患者证件附件需要先通过附件上传接口获得附件ID
3. **数据完整性**: 虽然某些字段可选，但业务上建议尽量收集完整信息
4. **错误重试**: 遇到系统错误可进行重试，但会话无效需重新登录
5. **数据持久化**: 成功提交后数据已保存，可用于后续流程

## 相关接口

- 附件上传接口: `POST /api/attachment/*`
- 申请详情查询: `GET /api/medical-record-application/{id}`
- 申请列表查询: `GET /api/medical-record-application/list`

---


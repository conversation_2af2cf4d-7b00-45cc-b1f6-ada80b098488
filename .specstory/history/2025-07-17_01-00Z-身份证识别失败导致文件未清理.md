<!-- Generated by SpecStory -->

# 身份证识别失败导致文件未清理 (2025-07-17 01:00Z)

_**User**_

如果身份证识别失败，会抛出异常，导致临时文件未被清理

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户指出了一个代码问题：如果身份证识别失败，会抛出异常，导致临时文件未被清理。

看代码，在 `doIdCardOcr` 方法中：
1. 创建了临时文件：`val tmpFile = Files.createTempFile("ocr-", ".$extensionName").toFile()`
2. 将上传文件内容传输到临时文件：`file.transferTo(tmpFile)`
3. 进行身份证识别：`val result = WeixinExt.idCardOcr(tmpFile)`
4. 删除临时文件：`FileUtil.del(tmpFile)`

问题是，如果第3步 `WeixinExt.idCardOcr(tmpFile)` 抛出异常，那么第4步的 `FileUtil.del(tmpFile)` 就不会执行，导致临时文件没有被清理。

这是一个资源泄漏的问题。需要使用 try-finally 或者 try-with-resources 模式来确保临时文件在异常情况下也能被清理。

在 Kotlin 中，可以使用 `use` 扩展函数或者 try-finally 来处理这种情况。

我需要：
1. 分析当前代码的问题
2. 提供修复方案
3. 实现代码修改

用户没有明确要求我提交代码或创建分支，所以我只需要修复代码，不需要执行Git操作。</details></think>

---

您说得对！这确实是一个资源泄漏的问题。在当前代码中，如果 `WeixinExt.idCardOcr(tmpFile)` 抛出异常，临时文件就不会被清理，可能导致磁盘空间逐渐被占用。

让我分析一下问题并修复它：

---

Read file: src/main/kotlin/space/lzhq/ph/controller/WxController.kt

---

<think><details><summary>Thought Process</summary>现在我看到了完整的方法。问题确实如用户所说：在 `doIdCardOcr` 方法中，如果 `WeixinExt.idCardOcr(tmpFile)` 抛出异常，`FileUtil.del(tmpFile)` 就不会执行。

需要使用 try-finally 或者 Kotlin 的 `use` 扩展函数来确保临时文件总是被删除。在 Kotlin 中，最简洁的方式是使用 try-finally 块。

让我修复这个问题：</details></think>

---

现在我来修复这个问题。需要使用 try-finally 块来确保临时文件在任何情况下都能被清理：

---



---

<think><details><summary>Thought Process</summary>修复已经完成。现在让我验证一下修改是否正确，并解释一下这个修改的好处：

1. 使用了 try-finally 块来确保资源清理
2. 无论 `file.transferTo(tmpFile)` 还是 `WeixinExt.idCardOcr(tmpFile)` 是否抛出异常，`FileUtil.del(tmpFile)` 都会在 finally 块中执行
3. 这样可以避免临时文件泄漏的问题

这是一个很好的资源管理实践。</details></think>

---

完成！我已经修复了这个资源泄漏问题。主要改动如下：

## 🔧 修复内容

**问题**：如果身份证识别过程中抛出异常，临时文件不会被删除，导致磁盘空间泄漏。

**解决方案**：使用 `try-finally` 块确保临时文件在任何情况下都能被清理。

## 📝 修改说明

将原来的线性执行代码：
```kotlin
file.transferTo(tmpFile)
val result = WeixinExt.idCardOcr(tmpFile)
FileUtil.del(tmpFile)
return result
```

改为异常安全的代码：
```kotlin
try {
    file.transferTo(tmpFile)
    return WeixinExt.idCardOcr(tmpFile)
} finally {
    FileUtil.del(tmpFile)
}
```

## ✅ 修复效果

1. **异常安全**：无论 `file.transferTo()` 还是 `WeixinExt.idCardOcr()` 是否抛出异常，临时文件都会被删除
2. **资源管理**：避免临时文件累积导致的磁盘空间泄漏
3. **代码健壮性**：提高了系统的稳定性和可靠性

这种模式是处理临时资源的标准做法，确保了即使在异常情况下也能正确清理资源。

---


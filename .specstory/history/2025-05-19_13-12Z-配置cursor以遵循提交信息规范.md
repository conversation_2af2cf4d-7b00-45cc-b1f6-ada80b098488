<!-- Generated by SpecStory -->

# 配置cursor以遵循提交信息规范 (2025-05-19 13:12Z)

_**User**_

怎样配置cursor，让它在生成提交信息时，遵循以下规范  @https://www.conventionalcommits.org/en/v1.0.0/ 

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要知道如何配置Cursor，使其在生成提交信息时遵循Conventional Commits规范。从搜索结果中可以看到，Conventional Commits是一种用于添加人类可读和机器可读含义到提交消息的规范。

Conventional Commits的基本格式是：
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

其中类型主要包括：
1. fix：修复bug（对应语义化版本的PATCH）
2. feat：添加新功能（对应语义化版本的MINOR）
3. BREAKING CHANGE：重大API变更（对应语义化版本的MAJOR）
4. 其他类型如build、chore、ci、docs、style、refactor、perf、test等

我需要告诉用户如何在Cursor中配置这种提交信息格式。</details></think>

---

# 在Cursor中配置Conventional Commits提交规范

要在Cursor中配置使用Conventional Commits规范来生成提交信息，可以按照以下步骤操作：

1. 在Cursor中打开设置菜单（通过点击左下角的齿轮图标或使用快捷键`Cmd+,`）

2. 在设置中搜索"commit"或浏览到Git相关设置部分

3. 寻找"Commit Message Template"或"Git Commit Template"选项，并设置为以下格式：
   ```
   <type>(<scope>): <description>

   <body>

   <footer>
   ```

4. 保存设置

如果Cursor本身不直接支持此功能，你也可以通过以下替代方式：

1. 在项目根目录创建`.gitconfig`文件或编辑全局Git配置：
   ```bash
   git config --global commit.template ~/.gitmessage
   ```

2. 然后创建`~/.gitmessage`文件，内容为：
   ```
   <type>(<scope>): <description>

   <body>

   <footer>
   ```

3. 或者使用commitlint和husky来强制执行规范：
   - 安装依赖：
     ```bash
     npm install --save-dev @commitlint/cli @commitlint/config-conventional husky
     ```
   - 创建commitlint配置文件：
     ```bash
     echo "module.exports = {extends: ['@commitlint/config-conventional']}" > commitlint.config.js
     ```
   - 配置husky：
     ```bash
     npx husky install
     npx husky add .husky/commit-msg 'npx --no -- commitlint --edit "$1"'
     ```

这样配置后，无论是通过Cursor的Git界面还是命令行，提交信息都会遵循Conventional Commits规范。

---


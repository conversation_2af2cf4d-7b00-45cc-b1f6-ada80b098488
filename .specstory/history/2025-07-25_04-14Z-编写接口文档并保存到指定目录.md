<!-- Generated by SpecStory -->

# 编写接口文档并保存到指定目录 (2025-07-25 04:14Z)

_**User**_

为 `POST /api/medical-record-application/patientForm` 接口编写面向前端开发人员的接口文档，保存到 @/doc 目录

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户需要我为 `POST /api/medical-record-application/patientForm` 接口编写面向前端开发人员的接口文档，并保存到 `/doc` 目录。

首先，我需要分析这个接口的相关信息：

1. 从 `MedicalRecordApplicationApiController.kt` 可以看到：
   - 接口路径：`/api/medical-record-application/patientForm`
   - HTTP方法：POST
   - 请求头：`consumes = [MediaType.APPLICATION_JSON_VALUE]`
   - 响应头：`produces = [MediaType.APPLICATION_JSON_VALUE]`
   - 需要会话验证：`@RequireSession`
   - 事务性：`@Transactional`
   - 参数：`@Valid @RequestBody dto: MedicalRecordApplicationPatientFormDto`
   - 返回：`ResponseDto<MedicalRecordApplicationDetailResponseDto?>`

2. 从 `MedicalRecordApplicationPatientFormDto.java` 可以看到请求参数结构：
   - `patientIdCardAttachmentId: String` - 患者身份证附件ID
   - `patientBirthCertificateAttachmentId: String` - 患者出生证附件ID
   - `patientIdCardNo: String` - 患者身份证号
   - `patientName: String` - 申报患者姓名（必填，不能包含特殊字符）
   - `patientToken: String` - 患者token（必填）
   - 有一个验证：身份证附件ID和出生证附件ID不能同时为空
</details></think>

---

我需要分析相关代码文件来了解这个接口的详细信息，然后编写完整的接口文档。

---

<details>
            <summary>Searched codebase "What is the structure of MedicalRecordApplicationDetailResponseDto response?" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-104 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L104-156 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L35-74 |
| `src/main/java/space/lzhq/ph/dto/mapper/MedicalRecordApplicationMapper.java` | L1-21 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationService.java` | L1-36 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L73-119 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L1-22 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L394-452 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L1-35 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L546-562 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L452-504 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L123-198 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L504-546 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L28-123 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-28 |
| `src/main/kotlin/space/lzhq/ph/controller/MedicalRecordApplicationApiController.kt` | L22-73 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/resources/mapper/ph/MedicalRecordApplicationTimelineMapper.xml` | L1-38 |
| `medical_record_application.sql` | L1-41 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L1-63 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L198-245 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationTimelineServiceImpl.java` | L197-215 |
| `src/main/java/space/lzhq/ph/service/IMedicalRecordApplicationTimelineService.java` | L1-51 |

</details>

---

<think><details><summary>Thought Process</summary>现在我需要查看一下 AttachmentDto 的结构，以及一些枚举类型的定义，来完整地理解接口的请求和响应结构。</details></think>

---

<details>
            <summary>Searched codebase "What is the structure of AttachmentDto class?" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/attachment/AttachmentDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/domain/Attachment.java` | L1-73 |
| `src/main/java/space/lzhq/ph/dto/mapper/AttachmentMapper.java` | L1-31 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/SimpleUploadResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-104 |
| `src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt` | L32-83 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardUploadResponseDto.java` | L1-43 |
| `src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt` | L1-32 |
| `src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L188-229 |
| `src/main/java/space/lzhq/ph/service/IAttachmentService.java` | L1-32 |
| `src/main/java/space/lzhq/ph/service/impl/CardRefundAdminServiceImpl.java` | L451-501 |
| `src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L135-188 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L452-504 |
| `src/main/java/space/lzhq/ph/service/impl/CardRefundAdminServiceImpl.java` | L401-451 |
| `src/main/kotlin/space/lzhq/ph/dto/ReportListDto.kt` | L1-12 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardOcrResultDto.java` | L1-52 |
| `src/main/kotlin/space/lzhq/ph/dto/InpatientInfoDto.kt` | L30-78 |
| `src/main/java/space/lzhq/ph/service/impl/CardRefundServiceImpl.java` | L229-259 |
| `src/main/java/space/lzhq/ph/service/impl/AttachmentServiceImpl.java` | L26-66 |
| `src/main/kotlin/space/lzhq/ph/dto/InpatientInfoDto.kt` | L1-30 |
| `src/main/java/space/lzhq/ph/service/impl/AttachmentServiceImpl.java` | L1-26 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundListResponseDto.java` | L1-111 |
| `src/main/java/space/lzhq/ph/mapper/AttachmentMapper.java` | L1-9 |
| `src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java` | L504-546 |

</details>

---

<think><details><summary>Thought Process</summary>现在我需要查看一下相关的枚举类型，比如 MedicalRecordApplicationStatus 和 Relationship，以及 ResponseDto 的结构，这样我可以完整地编写接口文档。</details></think>

---

<details>
            <summary>Searched codebase "What are the values in MedicalRecordApplicationStatus and Relationship enums?" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L1-63 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L123-198 |
| `medical_record_application.sql` | L41-75 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-28 |
| `medical_record_application.sql` | L1-41 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordMailingStatus.java` | L1-44 |
| `src/main/java/space/lzhq/ph/enums/Relationship.java` | L1-83 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L36-70 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L1-36 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L104-156 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L28-123 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-104 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L27-100 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordMailing.java` | L1-93 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L198-245 |
| `src/main/java/space/lzhq/ph/enums/AttachmentMasterType.java` | L1-23 |
| `src/main/java/space/lzhq/ph/domain/NurseCardStatus.java` | L1-37 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L296-300 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L1-27 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/java/space/lzhq/ph/dto/request/MedicalRecordApplicationAgentUpdateDto.java` | L1-63 |
| `ddl/ph/medical_record_application_timeline.sql` | L1-22 |
| `src/main/java/space/lzhq/ph/mapper/MedicalRecordApplicationMapper.java` | L1-24 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L82-138 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplicationTimeline.java` | L100-147 |

</details>

---

<think><details><summary>Thought Process</summary>让我查看一下 Relationship 枚举和 ResponseDto 的结构，这样就能完整地编写接口文档了。</details></think>

---

Read file: src/main/java/space/lzhq/ph/enums/Relationship.java

---

<think><details><summary>Thought Process</summary>现在让我查找一下 ResponseDto 的结构，这是接口返回的通用响应格式。</details></think>

---

<details>
            <summary>Searched codebase "What is the structure of ResponseDto class?" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/dto/response/ResponseDto.java` | L1-40 |
| `src/main/java/space/lzhq/ph/dto/response/BaseResponseDto.java` | L1-18 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/AttachmentDto.java` | L1-52 |
| `src/main/kotlin/space/lzhq/ph/dto/ReportListDto.kt` | L1-12 |
| `src/main/java/space/lzhq/ph/dto/response/EnumDefinitionResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardUploadResponseDto.java` | L1-43 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/IdCardOcrResultDto.java` | L1-52 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/SimpleUploadResponseDto.java` | L1-28 |
| `src/main/java/space/lzhq/ph/dto/response/EnumItemDto.java` | L1-26 |
| `src/main/kotlin/space/lzhq/ph/dto/InpatientInfoDto.kt` | L30-78 |
| `src/main/kotlin/space/lzhq/ph/dto/InpatientInfoDto.kt` | L1-30 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundDetailResponseDto.java` | L1-110 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundListResponseDto.java` | L1-111 |
| `src/main/kotlin/space/lzhq/ph/domain/RechargeResult.kt` | L1-64 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-104 |
| `src/main/java/space/lzhq/ph/dto/response/attachment/BankCardOcrResultDto.java` | L1-20 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundTimelineDto.java` | L1-84 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundStatisticsDto.java` | L1-101 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationTimelineDto.java` | L1-82 |
| `src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt` | L32-83 |
| `src/main/java/space/lzhq/ph/dto/response/CardRefundAdminListResponseDto.java` | L1-113 |
| `src/main/kotlin/space/lzhq/ph/controller/HospitalizedApiController.kt` | L1-42 |
| `src/main/java/space/lzhq/ph/dto/mapper/CardRefundMapper.java` | L44-95 |
| `src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt` | L1-32 |
| `src/main/kotlin/space/lzhq/ph/dto/IntelligentPayResult.kt` | L1-44 |

</details>

---

<think><details><summary>Thought Process</summary>现在我有了所有需要的信息来编写完整的接口文档。让我创建一个详细的、面向前端开发人员的接口文档。我需要将文档保存到 `/doc` 目录中。

根据分析，这个接口的主要信息是：

1. 接口路径：`POST /api/medical-record-application/patientForm`
2. 功能：提交病历复印申请的患者信息表单
3. 需要会话验证
4. 请求参数结构：MedicalRecordApplicationPatientFormDto
5. 响应结构：ResponseDto<MedicalRecordApplicationDetailResponseDto>

我需要创建一个清晰、完整、易于理解的接口文档。</details></think>

---

现在我已经收集了足够的信息来编写完整的接口文档。让我创建一个详细的、面向前端开发人员的接口文档并保存到 `/doc` 目录。

---

```diff
- 
+ # 病历复印申请患者表单接口文档
+ 
+ ## 接口概述
+ 
+ - **接口名称**：提交病历复印申请（患者信息表单）
+ - **接口地址**：`POST /api/medical-record-application/patientForm`
+ - **功能描述**：用于提交病历复印申请的患者基本信息，创建新的病历复印申请记录
+ - **权限要求**：需要有效的用户会话（`@RequireSession`）
+ - **事务性**：接口具有事务性，操作失败会自动回滚
+ 
+ ## 请求参数
+ 
+ ### 请求头
+ 
+ | 参数名称     | 是否必须 | 类型   | 描述                          |
+ | ------------ | -------- | ------ | ----------------------------- |
+ | Content-Type | 是       | String | 固定值：`application/json`    |
+ | Cookie       | 是       | String | 包含用户会话信息的Cookie      |
+ 
+ ### 请求体参数
+ 
+ | 参数名称                            | 类型   | 是否必须 | 描述                                                         |
+ | ----------------------------------- | ------ | -------- | ------------------------------------------------------------ |
+ | patientIdCardAttachmentId           | String | 否       | 患者身份证附件ID                                             |
+ | patientBirthCertificateAttachmentId | String | 否       | 患者出生证附件ID                                             |
+ | patientIdCardNo                     | String | 否       | 患者身份证号                                                 |
+ | patientName                         | String | 是       | 申报患者姓名，不能为空且不能包含特殊字符                     |
+ | patientToken                        | String | 是       | 患者token，用于验证患者身份和获取住院信息                    |
+ 
+ ### 请求体验证规则
+ 
+ 1. **患者姓名验证**：
+    - 不能为空
+    - 不能包含特殊字符（XSS防护）
+ 
+ 2. **患者Token验证**：
+    - 不能为空
+    - 必须是有效的患者身份令牌
+ 
+ 3. **附件ID验证**：
+    - 患者身份证附件ID和出生证附件ID不能同时为空（至少提供一个）
+    - 如果提供了附件ID，系统会验证：
+      - 附件是否存在
+      - 附件是否属于当前用户
+      - 附件类型是否正确
+      - 附件文件是否真实存在
+      - 附件是否已被其他申请使用
+ 
+ 4. **患者信息一致性验证**：
+    - 如果提供了患者身份证号，必须与患者token中的身份证号一致
+    - 如果提供了患者姓名，必须与患者token中的姓名匹配（支持中文姓名模糊匹配）
+ 
+ ## 请求示例
+ 
+ ```json
+ {
+   "patientIdCardAttachmentId": "1234567890abcdef1234567890abcdef",
+   "patientBirthCertificateAttachmentId": null,
+   "patientIdCardNo": "320123199001011234",
+   "patientName": "张三",
+   "patientToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
+ }
+ ```
+ 
+ ## 响应参数
+ 
+ ### 响应体结构
+ 
+ ```json
+ {
+   "code": 0,
+   "msg": "操作成功",
+   "data": {
+     // MedicalRecordApplicationDetailResponseDto 结构
+   }
+ }
+ ```
+ 
+ ### 响应数据字段说明（data部分）
+ 
+ | 字段名称                         | 类型                            | 描述                                    |
+ | -------------------------------- | ------------------------------- | --------------------------------------- |
+ | id                               | String                          | 申请记录ID                              |
+ | openId                           | String                          | 用户OpenID                              |
+ | patientIdCardAttachment          | AttachmentDto                   | 患者身份证附件信息                      |
+ | patientBirthCertificateAttachment| AttachmentDto                   | 患者出生证附件信息                      |
+ | patientIdCardNo                  | String                          | 患者身份证号                            |
+ | patientName                      | String                          | 患者姓名                                |
+ | patientGender                    | String                          | 患者性别（男/女）                       |
+ | patientBirthDate                 | String                          | 患者出生日期（yyyy-MM-dd）              |
+ | patientPhone                     | String                          | 患者手机号                              |
+ | admissionTime                    | String                          | 入院时间（yyyy-MM-dd HH:mm:ss）         |
+ | dischargeTime                    | String                          | 出院时间（yyyy-MM-dd HH:mm:ss）         |
+ | ward                             | String                          | 病区                                    |
+ | agentFlag                        | Integer                         | 是否代办（0-否，1-是）                  |
+ | agentName                        | String                          | 代办人姓名                              |
+ | agentIdCardNo                    | String                          | 代办人身份证号                          |
+ | agentIdCardAttachment            | AttachmentDto                   | 代办人身份证附件信息                    |
+ | agentMobile                      | String                          | 代办人手机号                            |
+ | agentRelation                    | RelationshipEnum                | 代办人与患者关系                        |
+ | status                           | MedicalRecordApplicationStatus  | 申报状态                                |
+ | submitTime                       | String                          | 提交时间（yyyy-MM-dd HH:mm:ss）         |
+ | approveTime                      | String                          | 审批时间（yyyy-MM-dd HH:mm:ss）         |
+ | rejectReason                     | String                          | 驳回原因                                |
+ | correctionAdvice                 | String                          | 整改意见                                |
+ | createTime                       | String                          | 创建时间（yyyy-MM-dd HH:mm:ss）         |
+ | updateTime                       | String                          | 更新时间（yyyy-MM-dd HH:mm:ss）         |
+ | canApprove                       | Boolean                         | 是否可以审批                            |
+ 
+ ### AttachmentDto 结构
+ 
+ | 字段名称   | 类型             | 描述               |
+ | ---------- | ---------------- | ------------------ |
+ | id         | String           | 附件ID             |
+ | openId     | String           | 用户OpenID         |
+ | masterId   | String           | 关联的主表ID       |
+ | type       | AttachmentType   | 附件类型           |
+ | fileName   | String           | 文件名             |
+ | fileUrl    | String           | 文件URL            |
+ | createTime | String           | 创建时间           |
+ 
+ ### 枚举值说明
+ 
+ #### MedicalRecordApplicationStatus（申报状态）
+ 
+ | 枚举值    | 描述           |
+ | --------- | -------------- |
+ | DRAFT     | 草稿（待提交） |
+ | SUBMITTED | 已提交（待审核）|
+ | APPROVED  | 审核通过       |
+ | REJECTED  | 审核驳回（待整改）|
+ | PAID      | 支付成功       |
+ | COMPLETED | 已完成         |
+ | DELIVERED | 邮寄完成       |
+ 
+ #### Relationship（代办人与患者关系）
+ 
+ | 枚举值     | 描述 |
+ | ---------- | ---- |
+ | FATHER     | 父亲 |
+ | MOTHER     | 母亲 |
+ | HUSBAND    | 丈夫 |
+ | WIFE       | 妻子 |
+ | SON        | 儿子 |
+ | DAUGHTER   | 女儿 |
+ | BROTHER    | 兄弟 |
+ | SISTER     | 姐妹 |
+ | FRIEND     | 朋友 |
+ | COLLEAGUE  | 同事 |
+ | CLASSMATE  | 同学 |
+ | OTHER      | 其他 |
+ 
+ #### AttachmentType（附件类型）
+ 
+ | 枚举值           | 描述     |
+ | ---------------- | -------- |
+ | ID_CARD          | 身份证   |
+ | BIRTH_CERTIFICATE| 出生证明 |
+ 
+ ## 响应示例
+ 
+ ### 成功响应
+ 
+ ```json
+ {
+   "code": 0,
+   "msg": "操作成功",
+   "data": {
+     "id": "1234567890abcdef1234567890abcdef",
+     "openId": "wx1234567890abcdef",
+     "patientIdCardAttachment": {
+       "id": "1234567890abcdef1234567890abcdef",
+       "openId": "wx1234567890abcdef",
+       "masterId": "1234567890abcdef1234567890abcdef",
+       "type": "ID_CARD",
+       "fileName": "身份证.jpg",
+       "fileUrl": "/upload/2024/12/身份证.jpg",
+       "createTime": "2024-12-30 10:30:00"
+     },
+     "patientBirthCertificateAttachment": null,
+     "patientIdCardNo": "320123199001011234",
+     "patientName": "张三",
+     "patientGender": "男",
+     "patientBirthDate": "1990-01-01",
+     "patientPhone": "13812345678",
+     "admissionTime": "2024-12-25 09:00:00",
+     "dischargeTime": "2024-12-28 16:00:00",
+     "ward": "内科一病区",
+     "agentFlag": 0,
+     "agentName": null,
+     "agentIdCardNo": null,
+     "agentIdCardAttachment": null,
+     "agentMobile": null,
+     "agentRelation": null,
+     "status": "DRAFT",
+     "submitTime": null,
+     "approveTime": null,
+     "rejectReason": null,
+     "correctionAdvice": null,
+     "createTime": "2024-12-30 10:30:00",
+     "updateTime": "2024-12-30 10:30:00",
+     "canApprove": false
+   }
+ }
+ ```
+ 
+ ### 失败响应
+ 
+ ```json
+ {
+   "code": 500,
+   "msg": "病历邮寄申请提交失败，请重试",
+   "data": null
+ }
+ ```
+ 
+ ## 错误码说明
+ 
+ | 错误码 | 描述                     | 常见原因                           |
+ | ------ | ------------------------ | ---------------------------------- |
+ | 0      | 操作成功                 | -                                  |
+ | 400    | 请求参数错误             | 必填字段为空、参数格式不正确       |
+ | 401    | 会话无效                 | 用户未登录或会话过期               |
+ | 500    | 服务器内部错误           | 业务逻辑验证失败、数据库操作失败   |
+ 
+ ## 常见错误场景
+ 
+ ### 1. 参数验证失败
+ 
+ **错误信息示例**：
+ - "申报患者姓名不能为空"
+ - "患者token不能为空"
+ - "患者出生证附件ID和身份证附件ID不能同时为空"
+ 
+ ### 2. 附件验证失败
+ 
+ **错误信息示例**：
+ - "身份证附件不存在，附件ID: xxx"
+ - "身份证附件不属于当前用户，附件ID: xxx"
+ - "身份证附件类型不正确，期望: ID_CARD，实际: xxx"
+ - "身份证附件已被使用，附件ID: xxx"
+ - "身份证附件对应的文件不存在，文件URL: xxx"
+ 
+ ### 3. 患者信息验证失败
+ 
+ **错误信息示例**：
+ - "参数 patientToken 无效"
+ - "患者身份证号不正确"
+ - "患者姓名不正确"
+ 
+ ### 4. 数据操作失败
+ 
+ **错误信息示例**：
+ - "保存病历复印申请失败"
+ - "更新附件关联的申报ID失败"
+ 
+ ## 业务流程说明
+ 
+ 1. **请求接收**：系统接收前端提交的患者表单数据
+ 2. **会话验证**：验证用户会话有效性，获取用户OpenID
+ 3. **参数验证**：验证必填字段和格式
+ 4. **附件验证**：验证提供的附件ID的有效性和可用性
+ 5. **患者信息解析**：解析患者token获取住院信息
+ 6. **信息一致性验证**：验证提交的患者信息与token中的信息是否一致
+ 7. **创建申请记录**：创建病历复印申请记录
+ 8. **更新附件关联**：将附件与申请记录关联
+ 9. **记录时间线**：记录申请创建事件到时间线
+ 10. **返回详情**：返回完整的申请详情信息
+ 
+ ## 注意事项
+ 
+ 1. **附件要求**：
+    - 患者身份证附件和出生证附件至少需要提供一个
+    - 附件必须在提交申请前先上传到系统
+    - 每个附件只能关联到一个申请记录
+ 
+ 2. **患者Token**：
+    - Token包含患者的住院信息（姓名、身份证号、入院时间、出院时间等）
+    - Token必须是有效且未过期的
+    - 系统会自动从Token中提取患者信息，无需重复输入
+ 
+ 3. **会话管理**：
+    - 接口需要有效的用户会话
+    - 会话过期时需要重新登录
+ 
+ 4. **错误处理**：
+    - 建议前端对常见错误场景进行友好的提示
+    - 对于500错误，建议提示用户重试或联系客服
+ 
+ 5. **性能考虑**：
+    - 接口包含事务操作，建议避免重复提交
+    - 附件验证可能涉及文件系统检查，请求可能略慢
+ 
+ ## 开发建议
+ 
+ 1. **前端验证**：
+    - 在提交前进行基本的客户端验证
+    - 检查必填字段、附件选择等
+ 
+ 2. **用户体验**：
+    - 提供清晰的错误提示信息
+    - 在提交过程中显示加载状态
+    - 成功后引导用户到下一步操作
+ 
+ 3. **错误处理**：
+    - 统一处理会话过期场景
+    - 对不同类型的错误提供相应的用户提示
+ 
+ 4. **数据缓存**：
+    - 可以考虑缓存用户的患者Token信息
+    - 避免重复解析和验证相同的Token 
```

---

<think><details><summary>Thought Process</summary>文档已经成功创建并保存到了 `/doc` 目录中。这个接口文档非常详细和完整，包含了：

1. **接口概述** - 基本信息和权限要求
2. **请求参数** - 详细的参数说明和验证规则
3. **响应参数** - 完整的响应结构和字段说明
4. **枚举值说明** - 所有相关枚举的详细定义
5. **请求/响应示例** - 实际的JSON格式示例
6. **错误码说明** - 标准的错误代码和描述
7. **常见错误场景** - 具体的错误情况和原因
8. **业务流程说明** - 接口的完整处理流程
9. **注意事项** - 重要的使用注意事项
10. **开发建议** - 面向前端开发人员的实用建议

这个文档应该能很好地帮助前端开发人员理解和使用这个接口。文档已经保存到 `doc/medical-record-application-patient-form-api.md` 文件中。</details></think>

---

我已经成功为 `POST /api/medical-record-application/patientForm` 接口编写了完整的面向前端开发人员的接口文档，并保存到了 `doc/medical-record-application-patient-form-api.md` 文件中。

这份文档包含了以下主要内容：

## 📋 文档特点

### 1. **完整的接口信息**
- 接口基本信息（路径、方法、权限要求）
- 详细的请求参数说明和验证规则
- 完整的响应数据结构

### 2. **实用的示例**
- 真实的JSON请求示例
- 详细的成功和失败响应示例
- 枚举值的完整定义

### 3. **错误处理指导**
- 标准错误码说明
- 常见错误场景及其原因
- 具体的错误信息示例

### 4. **业务流程说明**
- 完整的接口处理流程
- 重要的业务规则说明
- 注意事项和限制条件

### 5. **开发建议**
- 前端验证建议
- 用户体验优化建议
- 错误处理最佳实践
- 性能考虑建议

## 🔧 核心功能说明

该接口用于**提交病历复印申请的患者信息表单**，主要功能包括：

1. **患者信息验证** - 通过患者token验证身份和获取住院信息
2. **附件关联** - 将上传的身份证/出生证附件与申请记录关联
3. **业务规则验证** - 确保数据一致性和完整性
4. **记录创建** - 创建病历复印申请记录并返回详细信息

## 📝 使用要点

- **权限要求**：需要有效的用户会话
- **附件要求**：身份证附件和出生证附件至少需要提供一个
- **数据验证**：严格的参数验证和业务规则检查
- **事务性**：操作失败会自动回滚

这份文档应该能够帮助前端开发人员快速理解接口功能、正确调用接口，并妥善处理各种响应情况。

---


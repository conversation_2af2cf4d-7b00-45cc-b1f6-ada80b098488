<!-- Generated by SpecStory -->

# 更新配置文件 @wecity-mip.yaml (2025-07-03 10:21Z)

_**User**_

根据 @weicity.setting 更新 @wecity-mip.yaml 

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我根据 weicity.setting 文件的内容来更新 wecity-mip.yaml 文件。

让我先分析两个文件的内容：

1. weicity.setting 文件内容分析：
- 有两个部分：[mpMip] 和 [maMip]
- [mpMip] 部分包含：
  - env=prod
  - partnerId=50001684
  - partnerKey=7c12863bdbcf492e116056e887452bfb
  - channelNo=AAGtHoCjOYgP0yqX0U4KE8Ql
  - cityId=650100
  - orgCode=H65010300035
  - mipKey=5a3189a81e060c717e6b04b244641246
  - orgAppId=1GHJ6V6P50BIE1470B0A0000C4ABAE0D
  - orgChannelCode=BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxvhx5UDJoOagngtwI/IKRwp
  - appId=wxebe51b3e144b2173

- [maMip] 部分包含：
  - env=prod
  - partnerId=50006837
  - partnerKey=4d283424b8f492dcc842c8651bc7a54a (正式环境的值)
  - channelNo=AAG0KpOooRohKo5YZL2S9Bwo
  - cityId=650100
  - orgCode=H65010300035
  - mipKey=4588c286a1a0a4d74a97b501d35a26cd
</details></think>

---

我来根据 `weicity.setting` 文件的内容更新 `wecity-mip.yaml` 文件。

---



---

<think><details><summary>Thought Process</summary>现在我需要更新 mpMip 部分。从 weicity.setting 文件中，[mpMip] 部分有完整的配置信息，而当前 wecity-mip.yaml 中的 mpMip 部分大部分字段都是空的。我需要用 weicity.setting 中的值来填充这些空字段。</details></think>

---



---

<think><details><summary>Thought Process</summary>好的，我已经成功根据 weicity.setting 文件的内容更新了 wecity-mip.yaml 文件。

更新总结：

1. maMip 部分的更新：
   - partnerId: 50004335 → 50006837
   - partnerKey: 77e6d70b76dd5028a53200d30e716144 → 4d283424b8f492dcc842c8651bc7a54a
   - channelNo: AAFpLq7W-hPgZEn0ftOT2528 → AAG0KpOooRohKo5YZL2S9Bwo
   - orgCode: H65010300094 → H65010300035
   - mipKey: 735c5b44a91bd164ccf735f6b45bf9cc → 4588c286a1a0a4d74a97b501d35a26cd
   - orgAppId: 1IUVA1THU04E8D430B0A00001A2059A2 → 1IQNGAH9H08I8C430B0A0000DF12A4A6
   - orgChannelCode: BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxt29ZVU1XoA6qG4nF3dLRn3 → BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxvhx5UDJoOagngtwI/IKRwp
   - appId: wxccb0430de6991c6d → wx61dc15cd278adfcb
</details></think>

---

已成功根据 `weicity.setting` 文件的内容更新了 `wecity-mip.yaml` 文件！

## 更新总结：

### maMip 部分更新：
- **partnerId**: `50004335` → `50006837`
- **partnerKey**: `77e6d70b76dd5028a53200d30e716144` → `4d283424b8f492dcc842c8651bc7a54a`
- **channelNo**: `AAFpLq7W-hPgZEn0ftOT2528` → `AAG0KpOooRohKo5YZL2S9Bwo`
- **orgCode**: `H65010300094` → `H65010300035`
- **mipKey**: `735c5b44a91bd164ccf735f6b45bf9cc` → `4588c286a1a0a4d74a97b501d35a26cd`
- **orgAppId**: `1IUVA1THU04E8D430B0A00001A2059A2` → `1IQNGAH9H08I8C430B0A0000DF12A4A6`
- **orgChannelCode**: 更新为正确的编码值
- **appId**: `wxccb0430de6991c6d` → `wx61dc15cd278adfcb`

### mpMip 部分更新：
- **env**: `test` → `prod`
- **partnerId**: 空值 → `50001684`
- **partnerKey**: 空值 → `7c12863bdbcf492e116056e887452bfb`
- **channelNo**: 空值 → `AAGtHoCjOYgP0yqX0U4KE8Ql`
- **orgCode**: `H65010300094` → `H65010300035`
- **mipKey**: 空值 → `5a3189a81e060c717e6b04b244641246`
- **orgAppId**: 空值 → `1GHJ6V6P50BIE1470B0A0000C4ABAE0D`
- **orgChannelCode**: 空值 → 填入正确的编码值
- **appId**: 空值 → `wxebe51b3e144b2173`

所有配置项都已从 `.setting` 格式成功迁移到 `.yaml` 格式，并保持了完整的注释信息。

---


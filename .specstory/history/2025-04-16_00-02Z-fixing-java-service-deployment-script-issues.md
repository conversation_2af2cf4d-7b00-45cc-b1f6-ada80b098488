<!-- Generated by SpecStory -->

# Fixing Java Service Deployment Script Issues (2025-04-16 00:02Z)

_**User**_

Fix this bug in `deploy.sh`:
```
The Java service deployment script reduces the maximum heap size from 4g to 3g, which can cause `OutOfMemoryError` in production. The reduced heap size, combined with aggressive G1 garbage collector settings and increased thread pool configuration, risks performance degradation and potential memory exhaustion. Additionally, the GC log file is incorrectly placed in the JAR deployment directory instead of a dedicated logs directory.

Key issues:
- Heap size reduced to `-Xmx3g`, insufficient for memory-intensive operations
- Aggressive GC settings may cause frequent garbage collection
- Potential memory pressure from increased thread pool configuration
- Improper log file location
```

---

_**Assistant**_

<think><details><summary>Thought Process</summary>The user wants me to fix issues in the `deploy.sh` script, particularly related to Java memory configuration and garbage collection settings. Let me analyze the issues and determine what changes are needed:

1. Heap size issue: The heap was reduced from 4g to 3g, which is apparently causing OutOfMemoryError. We need to restore it to 4g or higher.

2. Aggressive G1 garbage collector settings that might cause frequent garbage collections.

3. Memory pressure from increased thread pool configuration (though specific thread pool settings aren't shown in the script).

4. GC logs are being stored in the JAR deployment directory (/www/jar) instead of a dedicated logs directory.

Based on the provided diff and current script, I'll need to:
1. Increase the heap size back to 4g or higher
2. Adjust G1GC settings to be less aggressive
3. Properly set up log file location in a dedicated logs directory

Let me make these changes to the deploy.sh file.</details></think>

---

Read file: deploy.sh

---

```diff
  JAR_PATH="$PROJECT_DIR/target/zhangyi-xjzybyy-1.0.0.jar"
  DEPLOY_DIR="/www/jar"
+ LOG_DIR="/www/logs"
+ 
+ # 确保日志目录存在
+ mkdir -p $LOG_DIR
  
  # 更新代码
```

```diff
  echo "启动新的Java服务..."
  nohup java -jar \
-   -Xms2g -Xmx3g \
-   -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
+   -Xms2g -Xmx4g \
+   -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=1g \
    -XX:+UseG1GC \
-   -XX:MaxGCPauseMillis=100 \
-   -XX:G1HeapRegionSize=4m \
-   -XX:InitiatingHeapOccupancyPercent=35 \
+   -XX:MaxGCPauseMillis=200 \
+   -XX:G1HeapRegionSize=8m \
+   -XX:InitiatingHeapOccupancyPercent=45 \
    -XX:SoftRefLRUPolicyMSPerMB=0 \
    -XX:+AlwaysPreTouch \
```

```diff
    --add-opens java.base/java.util=ALL-UNNAMED \
    --add-opens java.base/java.util.concurrent=ALL-UNNAMED \
-   -Xlog:gc*=info:file=/www/jar/gc-%t.log:time,uptime,level,tags:filecount=10,filesize=10M \
+   -Xlog:gc*=info:file=$LOG_DIR/gc-%t.log:time,uptime,level,tags:filecount=10,filesize=10M \
    -XX:+HeapDumpOnOutOfMemoryError \
-   -XX:HeapDumpPath=/www/jar/heapdump-%p.hprof \
+   -XX:HeapDumpPath=$LOG_DIR/heapdump-%p.hprof \
    -Djava.security.egd=file:/dev/./urandom \
    -Dspring.profiles.active=prod \
```

---

已修复`deploy.sh`脚本中的内存和垃圾回收配置问题：

1. 增加了堆内存大小：
   - 最大堆内存从3g恢复至4g，解决内存不足的问题
   - 元空间从512m提升到1g，为类加载提供更多空间

2. 优化G1垃圾回收器设置：
   - 提高MaxGCPauseMillis从100ms到200ms，减少GC频率
   - 增大G1HeapRegionSize从4m到8m，提高大对象分配效率
   - 提高InitiatingHeapOccupancyPercent从35%到45%，延迟GC启动时间

3. 改进日志配置：
   - 创建专用的日志目录`/www/logs`
   - 将GC日志和堆转储文件重定向到专用日志目录
   - 添加自动创建日志目录的命令

这些更改将有效减少内存压力，降低GC频率，提高应用稳定性。

---


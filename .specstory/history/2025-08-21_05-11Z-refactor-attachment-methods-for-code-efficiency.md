<!-- Generated by SpecStory -->

# Refactor attachment methods for code efficiency (2025-08-21 05:11Z)

_**User**_

The new methods `setPatientMedicalInsuranceCardAttachment` and `setPatientHouseholdRegisterAttachment` are almost identical to each other and to the existing `set...Attachment` methods. This leads to significant code duplication. A better approach would be to create a single, generic private helper method that can set any type of attachment on the DTO. This would make the `setAttachmentsToDto` method much cleaner and easier to maintain.

---

_**Assistant**_

你说得对！这确实是典型的代码重复问题。让我用Linus的眼光来分析这段代码：

## 【核心判断】
✅ 值得做：消除重复代码，提高可维护性

## 【关键洞察】
- 数据结构：所有附件设置方法都遵循相同的模式：`getXxxAttachmentId()` → `attachmentMap.get()` → `setXxxAttachment()`
- 复杂度：5个几乎相同的方法可以合并为1个通用方法
- 风险点：重构后需要确保所有调用点都正确传递参数

## 【Linus式方案】
1. 创建一个通用的附件设置方法
2. 使用函数式接口来传递getter和setter
3. 消除所有重复代码

让我重构这段代码：


---

<details><summary>Edit file: src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java</summary>

I will refactor the attachment setting methods to eliminate code duplication by creating a single generic helper method and updating the setAttachmentsToDto method to use it.

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -17,4 +17,5 @@
  import space.lzhq.ph.domain.MedicalRecordApplication;
  import space.lzhq.ph.dto.InpatientInfo;
+ import space.lzhq.ph.dto.AttachmentDto;
  import space.lzhq.ph.dto.mapper.AttachmentMapper;
  import space.lzhq.ph.dto.mapper.MedicalRecordApplicationMapper;
```

**Chunk 2**
Lines added: 45, lines removed: 104

```diff
@@ -703,111 +704,52 @@
              Map<String, Attachment> attachmentMap
      ) {
-         // 设置患者身份证附件
-         setPatientIdCardAttachment(application, detailDto, attachmentMap);
- 
-         // 设置患者出生证附件
-         setPatientBirthCertificateAttachment(application, detailDto, attachmentMap);
- 
-         // 设置患者医保卡附件
-         setPatientMedicalInsuranceCardAttachment(application, detailDto, attachmentMap);
- 
-         // 设置患者户口本附件
-         setPatientHouseholdRegisterAttachment(application, detailDto, attachmentMap);
- 
-         // 设置代办人身份证附件
-         setAgentIdCardAttachment(application, detailDto, attachmentMap);
-     }
- 
-     /**
-      * 设置患者身份证附件
-      */
-     private void setPatientIdCardAttachment(
-             MedicalRecordApplication application,
-             MedicalRecordApplicationDetailResponseDto detailDto,
-             Map<String, Attachment> attachmentMap
-     ) {
-         if (application.getPatientIdCardAttachmentId() != null) {
-             Attachment attachment =
-                     attachmentMap.get(application.getPatientIdCardAttachmentId());
-             if (attachment != null) {
-                 detailDto.setPatientIdCardAttachment(
-                         AttachmentMapper.INSTANCE.toDto(attachment)
-                 );
-             }
-         }
-     }
- 
-     /**
-      * 设置患者出生证附件
-      */
-     private void setPatientBirthCertificateAttachment(
-             MedicalRecordApplication application,
-             MedicalRecordApplicationDetailResponseDto detailDto,
-             Map<String, Attachment> attachmentMap
-     ) {
-         if (application.getPatientBirthCertificateAttachmentId() != null) {
-             Attachment attachment =
-                     attachmentMap.get(application.getPatientBirthCertificateAttachmentId());
-             if (attachment != null) {
-                 detailDto.setPatientBirthCertificateAttachment(
-                         AttachmentMapper.INSTANCE.toDto(attachment)
-                 );
-             }
-         }
-     }
- 
-     /**
-      * 设置患者医保卡附件
-      */
-     private void setPatientMedicalInsuranceCardAttachment(
-             MedicalRecordApplication application,
-             MedicalRecordApplicationDetailResponseDto detailDto,
-             Map<String, Attachment> attachmentMap
-     ) {
-         if (application.getPatientMedicalInsuranceCardAttachmentId() != null) {
-             Attachment attachment =
-                     attachmentMap.get(application.getPatientMedicalInsuranceCardAttachmentId());
-             if (attachment != null) {
-                 detailDto.setPatientMedicalInsuranceCardAttachment(
-                         AttachmentMapper.INSTANCE.toDto(attachment)
-                 );
-             }
-         }
-     }
- 
-     /**
-      * 设置患者户口本附件
-      */
-     private void setPatientHouseholdRegisterAttachment(
-             MedicalRecordApplication application,
-             MedicalRecordApplicationDetailResponseDto detailDto,
-             Map<String, Attachment> attachmentMap
-     ) {
-         if (application.getPatientHouseholdRegisterAttachmentId() != null) {
-             Attachment attachment =
-                     attachmentMap.get(application.getPatientHouseholdRegisterAttachmentId());
-             if (attachment != null) {
-                 detailDto.setPatientHouseholdRegisterAttachment(
-                         AttachmentMapper.INSTANCE.toDto(attachment)
-                 );
-             }
-         }
-     }
- 
-     /**
-      * 设置代办人身份证附件
+         // 使用通用方法设置各种附件
+         setAttachmentIfExists(
+                 application.getPatientIdCardAttachmentId(),
+                 attachmentMap,
+                 detailDto::setPatientIdCardAttachment
+         );
+         
+         setAttachmentIfExists(
+                 application.getPatientBirthCertificateAttachmentId(),
+                 attachmentMap,
+                 detailDto::setPatientBirthCertificateAttachment
+         );
+         
+         setAttachmentIfExists(
+                 application.getPatientMedicalInsuranceCardAttachmentId(),
+                 attachmentMap,
+                 detailDto::setPatientMedicalInsuranceCardAttachment
+         );
+         
+         setAttachmentIfExists(
+                 application.getPatientHouseholdRegisterAttachmentId(),
+                 attachmentMap,
+                 detailDto::setPatientHouseholdRegisterAttachment
+         );
+         
+         setAttachmentIfExists(
+                 application.getAgentIdCardAttachmentId(),
+                 attachmentMap,
+                 detailDto::setAgentIdCardAttachment
+         );
+     }
+ 
+     /**
+      * 通用的附件设置方法
+      *
+      * @param attachmentId 附件ID
+      * @param attachmentMap 附件映射
+      * @param setter 设置器函数
       */
-     private void setAgentIdCardAttachment(
-             MedicalRecordApplication application,
-             MedicalRecordApplicationDetailResponseDto detailDto,
-             Map<String, Attachment> attachmentMap
+     private void setAttachmentIfExists(
+             String attachmentId,
+             Map<String, Attachment> attachmentMap,
+             java.util.function.Consumer<AttachmentDto> setter
      ) {
-         if (application.getAgentIdCardAttachmentId() != null) {
-             Attachment attachment =
-                     attachmentMap.get(application.getAgentIdCardAttachmentId());
+         if (attachmentId != null) {
+             Attachment attachment = attachmentMap.get(attachmentId);
              if (attachment != null) {
-                 detailDto.setAgentIdCardAttachment(
-                         AttachmentMapper.INSTANCE.toDto(attachment)
-                 );
+                 setter.accept(AttachmentMapper.INSTANCE.toDto(attachment));
              }
          }
```

</details>

---

完美！现在让我验证一下重构后的代码是否正确：


---

Read file: src/main/java/space/lzhq/ph/service/impl/MedicalRecordApplicationServiceImpl.java

---

## 【重构结果评估】

**�� 重构成功！** 现在代码质量大大提升：

### ✅ 改进点
1. **消除重复代码**：从5个几乎相同的方法减少到1个通用方法
2. **提高可维护性**：新增附件类型只需要在`setAttachmentsToDto`中添加一行调用
3. **代码更清晰**：`setAttachmentsToDto`方法现在一目了然，每个附件设置都是一行代码
4. **类型安全**：使用函数式接口`Consumer<AttachmentDto>`确保类型安全

### 🔧 技术细节
- **函数式编程**：使用Java 8的`Consumer`接口和方法引用
- **参数化设计**：`setAttachmentIfExists`方法接受任何附件ID和设置器
- **空值安全**：保持了原有的null检查逻辑

### �� 代码行数对比
- **重构前**：~120行（5个重复方法）
- **重构后**：~50行（1个通用方法 + 5行调用）
- **减少**：约58%的代码量

这就是Linus所说的"好品味"！我们消除了特殊情况，让代码变得更通用、更简洁。现在如果需要添加新的附件类型，只需要在`setAttachmentsToDto`方法中添加一行调用即可，完全不需要写重复的代码。

**"Sometimes you can look at the problem from a different angle and rewrite it so that the special case goes away and becomes the normal case."** - Linus Torvalds

---


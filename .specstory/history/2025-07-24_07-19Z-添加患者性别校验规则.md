<!-- Generated by SpecStory -->

# 添加患者性别校验规则 (2025-07-24 07:19Z)

_**User**_

为`患者性别`添加校验规则，必须是男或女

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要为`患者性别`字段添加校验规则，要求必须是"男"或"女"。我需要查看当前的代码，了解现有的校验规则，然后添加合适的校验。

从用户提供的代码片段可以看到，目前`patientGender`字段只有`@NotBlank`校验，确保不为空。现在需要添加一个校验来限制值只能是"男"或"女"。

在Java Bean Validation中，有几种方式可以实现这种校验：

1. 使用`@Pattern`注解配合正则表达式
2. 使用`@ValidDepartmentCode`等自定义注解（我在项目结构中看到了类似的自定义注解）
3. 使用枚举配合验证

我先看看当前的完整文件内容，了解现有的校验模式，然后选择最合适的方式。</details></think>

---

我需要先查看完整的文件内容，了解现有的校验模式，然后为患者性别字段添加合适的校验规则。

---

Read file: src/main/java/space/lzhq/ph/dto/request/MedicalRecordApplicationPatientFormDto.java

---

<think><details><summary>Thought Process</summary>好的，我看到了完整的文件内容。现在我需要为`patientGender`字段添加校验规则，要求必须是"男"或"女"。

从代码中可以看到：
1. 使用了Jakarta Validation的注解
2. 已经有了`@NotBlank`校验
3. 文件中使用了自定义的校验方法`@AssertTrue`

对于性别字段的校验，我有几个选择：
1. 使用`@Pattern`注解配合正则表达式
2. 创建自定义校验注解
3. 使用`@AssertTrue`添加自定义校验方法

考虑到项目中已经使用了`@AssertTrue`进行自定义校验，并且性别只有两个固定值，我认为最合适的方法是添加一个自定义的校验方法。

但是，我也注意到项目结构中有自定义的校验注解（如`ValidDepartmentCode`、`ValidDoctorCode`），这表明项目倾向于使用自定义注解。让我先查看一下这些自定义注解的实现，以便保持一致的风格。</details></think>

---

Read file: src/main/java/space/lzhq/ph/annotation/ValidDepartmentCode.java

---

<think><details><summary>Thought Process</summary>很好！我现在看到了项目中自定义校验注解的实现模式。这是一个很好的参考。我可以创建一个类似的自定义注解来校验患者性别。

基于现有的模式，我将创建一个`ValidGender`注解，它会校验性别字段只能是"男"或"女"。

现在我需要：
1. 创建自定义的`ValidGender`注解
2. 在`MedicalRecordApplicationPatientFormDto`中使用这个注解

让我先创建这个自定义注解。</details></think>

---

基于项目中现有的自定义校验注解模式，我将为您创建一个专门的性别校验注解，然后在DTO中使用它。

---

```diff
- 
+ package space.lzhq.ph.annotation;
+ 
+ import jakarta.validation.Constraint;
+ import jakarta.validation.ConstraintValidator;
+ import jakarta.validation.ConstraintValidatorContext;
+ import jakarta.validation.Payload;
+ 
+ import java.lang.annotation.ElementType;
+ import java.lang.annotation.Retention;
+ import java.lang.annotation.RetentionPolicy;
+ import java.lang.annotation.Target;
+ import java.util.Set;
+ 
+ /**
+  * 性别验证注解
+  * 验证性别必须为"男"或"女"
+  */
+ @Constraint(validatedBy = ValidGender.GenderValidator.class)
+ @Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
+ @Retention(RetentionPolicy.RUNTIME)
+ public @interface ValidGender {
+ 
+     String message() default "患者性别必须是男或女";
+ 
+     Class<?>[] groups() default {};
+ 
+     Class<? extends Payload>[] payload() default {};
+ 
+     class GenderValidator implements ConstraintValidator<ValidGender, String> {
+ 
+         private static final Set<String> VALID_GENDERS = Set.of("男", "女");
+ 
+         @Override
+         public boolean isValid(String gender, ConstraintValidatorContext context) {
+             if (gender == null) {
+                 return true; // null值由@NotBlank处理
+             }
+             return VALID_GENDERS.contains(gender.trim());
+         }
+     }
+ } 
```

---

<think><details><summary>Thought Process</summary>很好！我成功创建了自定义的性别校验注解。现在我需要在DTO文件中使用这个注解。我需要：

1. 在MedicalRecordApplicationPatientFormDto中导入这个新注解
2. 在patientGender字段上添加@ValidGender注解，同时保留@NotBlank注解

让我现在修改DTO文件。</details></think>

---

现在我需要在DTO文件中使用这个新创建的校验注解：

---



---

<think><details><summary>Thought Process</summary>很好！我已经成功添加了导入语句。现在我需要在`patientGender`字段上添加`@ValidGender`注解。</details></think>

---



---

<think><details><summary>Thought Process</summary>完美！我已经成功地：

1. 创建了一个自定义的`ValidGender`校验注解，它验证性别字段只能是"男"或"女"
2. 在DTO文件中导入了这个注解
3. 在`patientGender`字段上添加了`@ValidGender`注解

现在让我确认一下修改是否正确，查看最终的结果。</details></think>

---

让我确认一下修改后的结果：

---

Read file: src/main/java/space/lzhq/ph/dto/request/MedicalRecordApplicationPatientFormDto.java

---

<think><details><summary>Thought Process</summary>让我查看更多行数来确保看到完整的字段定义。</details></think>

---

Read file: src/main/java/space/lzhq/ph/dto/request/MedicalRecordApplicationPatientFormDto.java

---

<think><details><summary>Thought Process</summary>很好！我已经成功完成了所有需要的修改。让我总结一下我所做的工作：

1. **创建了自定义校验注解 `ValidGender`**：
   - 位置：`src/main/java/space/lzhq/ph/annotation/ValidGender.java`
   - 功能：验证性别字段只能是"男"或"女"
   - 使用了与项目中其他自定义注解一致的模式
   - 校验器会检查输入值是否为"男"或"女"（支持去除空格）
   - null值交由@NotBlank处理，符合职责分离原则

2. **在DTO中应用校验注解**：
   - 在`MedicalRecordApplicationPatientFormDto.java`中导入了`ValidGender`注解
   - 在`patientGender`字段上添加了`@ValidGender`注解
   - 保留了原有的`@NotBlank`注解，确保字段不为空
   - 现在该字段同时具备了非空校验和值范围校验

这样的实现方式符合Spring Boot和Bean Validation的最佳实践：
- 使用了自定义校验注解，保持了代码的可读性和复用性
- 遵循了单一职责原则，每个注解负责特定的校验逻辑
- 与项目现有的校验注解风格保持一致
- 提供了清晰的错误消息

现在当客户端提交表单时，如果患者性别字段不是"男"或"女"，将会返回"患者性别必须是男或女"的错误消息。</details></think>

---

完成！我已经成功为`患者性别`字段添加了校验规则，要求必须是"男"或"女"。

## 实现总结

### 1. 创建了自定义校验注解 `ValidGender`
- **位置**: `src/main/java/space/lzhq/ph/annotation/ValidGender.java`
- **功能**: 验证性别字段只能是"男"或"女"
- **特点**:
  - 使用了与项目中其他自定义注解一致的实现模式
  - 校验器检查输入值是否为预定义的有效性别值
  - 自动处理前后空格
  - null值交由`@NotBlank`处理，符合职责分离原则

### 2. 在DTO中应用校验注解
- 在`MedicalRecordApplicationPatientFormDto.java`中导入了`ValidGender`注解
- 在`patientGender`字段上添加了`@ValidGender`注解
- 保留了原有的`@NotBlank`注解，确保完整的校验逻辑

### 3. 校验逻辑
现在`patientGender`字段具备以下校验：
- **非空校验**: `@NotBlank` - 确保字段不为空或空白
- **值域校验**: `@ValidGender` - 确保值只能是"男"或"女"

### 4. 错误提示
当校验失败时，会返回相应的错误消息：
- 如果为空：`"患者性别不能为空"`
- 如果不是有效值：`"患者性别必须是男或女"`

这种实现方式符合Spring Boot和Bean Validation的最佳实践，保持了代码的可读性、可维护性和复用性。

---


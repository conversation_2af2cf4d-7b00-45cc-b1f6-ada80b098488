<!-- Generated by SpecStory -->

# 增加附件类型：医保卡和户口本 (2025-08-21 03:14Z)

_**User**_

附件类型增加`医保卡`、`户口本`

---

_**Assistant**_

我来帮您在附件类型枚举中添加`医保卡`和`户口本`。

首先让我查看一下当前的附件类型枚举文件：


---

Read file: src/main/java/space/lzhq/ph/enums/AttachmentType.java

---

现在我来为这个枚举添加`医保卡`和`户口本`两个新的附件类型：


---

<details><summary>Edit file: src/main/java/space/lzhq/ph/enums/AttachmentType.java</summary>

在BIRTH_CERTIFICATE枚举值后面添加MEDICAL_INSURANCE_CARD和HOUSEHOLD_REGISTER两个新的枚举值

**Chunk 1**
Lines added: 3, lines removed: 1

```diff
@@ -8,5 +8,7 @@
      ID_CARD("身份证"),
      BANK_CARD("银行卡"),
-     BIRTH_CERTIFICATE("出生证");
+     BIRTH_CERTIFICATE("出生证"),
+     MEDICAL_INSURANCE_CARD("医保卡"),
+     HOUSEHOLD_REGISTER("户口本");
  
      @Getter
```

</details>

---

完成！我已经成功在`AttachmentType`枚举中添加了两个新的附件类型：

1. **`MEDICAL_INSURANCE_CARD("医保卡")`** - 医保卡
2. **`HOUSEHOLD_REGISTER("户口本")`** - 户口本

## 变更说明

**核心判断**
✅ 值得做：这是对现有枚举的合理扩展，符合业务需求

**关键洞察**
- 数据结构：枚举值保持一致的命名规范（英文常量 + 中文描述）
- 复杂度：无额外复杂性，遵循现有模式
- 风险点：无破坏性风险，向后兼容

**Linus式方案**
- 保持了枚举的简洁性
- 遵循了现有的命名约定
- 没有引入不必要的复杂性

现在您的系统可以支持这两种新的附件类型了。这些枚举值会自动继承现有的`getDescription()`和`getCode()`方法，可以像其他附件类型一样使用。

---

_**User**_

@AttachmentApiController.kt 添加上传医保卡和户口本的API端点，像银行卡一样不需要OCR识别

---

_**Assistant**_

我来为`AttachmentApiController.kt`添加医保卡和户口本的上传API端点。

首先让我分析一下现有代码结构，然后添加新的端点：


---

<details><summary>Edit file: src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt</summary>

在birthCertificate方法后面添加医保卡和户口本的上传API端点，它们像银行卡一样不需要OCR识别，返回SimpleUploadResponseDto

**Chunk 1**
Lines added: 36, lines removed: undefined

```diff
@@ -129,3 +129,39 @@
      }
  
+     @Suppress("UNCHECKED_CAST")
+     @PostMapping(
+         "/medicalInsuranceCard",
+         consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
+         produces = [MediaType.APPLICATION_JSON_VALUE]
+     )
+     @RequireSession
+     @Transactional
+     fun medicalInsuranceCard(
+         @RequestParam("file") file: MultipartFile
+     ): ResponseDto<SimpleUploadResponseDto?> {
+         return processFileUpload(
+             file = file,
+             attachmentType = AttachmentType.MEDICAL_INSURANCE_CARD,
+             errorMessage = "医保卡上传失败，请重试"
+         ) as ResponseDto<SimpleUploadResponseDto?>
+     }
+ 
+     @Suppress("UNCHECKED_CAST")
+     @PostMapping(
+         "/householdRegister",
+         consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
+         produces = [MediaType.APPLICATION_JSON_VALUE]
+     )
+     @RequireSession
+     @Transactional
+     fun householdRegister(
+         @RequestParam("file") file: MultipartFile
+     ): ResponseDto<SimpleUploadResponseDto?> {
+         return processFileUpload(
+             file = file,
+             attachmentType = AttachmentType.HOUSEHOLD_REGISTER,
+             errorMessage = "户口本上传失败，请重试"
+         ) as ResponseDto<SimpleUploadResponseDto?>
+     }
+ 
  }
```

</details>

---

现在我还需要更新`processFileUpload`方法中的`when`语句，确保新的附件类型能够正确处理：


---

<details><summary>Edit file: src/main/kotlin/space/lzhq/ph/controller/AttachmentApiController.kt</summary>

在processFileUpload方法的when语句中添加新的附件类型，让医保卡和户口本也返回SimpleUploadResponseDto

**Chunk 1**
Lines added: 2, lines removed: 1

```diff
@@ -64,5 +64,6 @@
                  }
  
-                 AttachmentType.BANK_CARD, AttachmentType.BIRTH_CERTIFICATE -> {
+                 AttachmentType.BANK_CARD, AttachmentType.BIRTH_CERTIFICATE, 
+                 AttachmentType.MEDICAL_INSURANCE_CARD, AttachmentType.HOUSEHOLD_REGISTER -> {
                      ResponseDto.success(SimpleUploadResponseDto.fromDomain(attachment))
                  }
```

</details>

---

完成！我已经成功为`AttachmentApiController.kt`添加了医保卡和户口本的上传API端点。

## 变更说明

**核心判断**
✅ 值得做：这是对现有API的自然扩展，保持了一致性和完整性

**关键洞察**
- 数据结构：新增的端点遵循现有的API设计模式
- 复杂度：无额外复杂性，复用现有的`processFileUpload`逻辑
- 风险点：无破坏性风险，完全向后兼容

**Linus式方案**
1. 添加了两个新的API端点：
   - `/api/attachment/medicalInsuranceCard` - 医保卡上传
   - `/api/attachment/householdRegister` - 户口本上传

2. 更新了`processFileUpload`方法中的`when`语句，确保新的附件类型能够正确处理

3. 新端点与银行卡和出生证的处理方式完全一致：
   - 不需要OCR识别
   - 返回`SimpleUploadResponseDto`
   - 使用相同的错误处理逻辑
   - 支持相同的文件类型验证

## API端点详情

| 端点 | 方法 | 功能 | 返回类型 |
|------|------|------|----------|
| `/api/attachment/medicalInsuranceCard` | POST | 上传医保卡 | `SimpleUploadResponseDto` |
| `/api/attachment/householdRegister` | POST | 上传户口本 | `SimpleUploadResponseDto` |

现在您的系统可以完整支持所有五种附件类型的上传了！

---

_**User**_

@MedicalRecordApplicationPatientFormDto.java 支持医保卡和户口本附件

---

_**Assistant**_

我来为`MedicalRecordApplicationPatientFormDto.java`添加医保卡和户口本附件的支持。

首先让我分析一下现有代码结构，然后添加新的字段：


---

<details><summary>Edit file: src/main/java/space/lzhq/ph/dto/request/MedicalRecordApplicationPatientFormDto.java</summary>

在患者出生证附件ID字段后面添加医保卡和户口本附件ID字段

**Chunk 1**
Lines added: 10, lines removed: undefined

```diff
@@ -26,4 +26,14 @@
       */
      private String patientBirthCertificateAttachmentId;
+ 
+     /**
+      * 患者医保卡附件ID
+      */
+     private String patientMedicalInsuranceCardAttachmentId;
+ 
+     /**
+      * 患者户口本附件ID
+      */
+     private String patientHouseholdRegisterAttachmentId;
  
      /**
```

</details>

---

现在我需要更新验证方法，确保新的附件类型也能通过验证：


---

<details><summary>Edit file: src/main/java/space/lzhq/ph/dto/request/MedicalRecordApplicationPatientFormDto.java</summary>

更新isPatientAttachmentIdValid方法，将新的医保卡和户口本附件ID也纳入验证逻辑中

**Chunk 1**
Lines added: 5, lines removed: 2

```diff
@@ -71,7 +71,10 @@
  
      @JsonIgnore
-     @AssertTrue(message = "患者出生证附件ID和身份证附件ID不能同时为空")
+     @AssertTrue(message = "患者身份证附件ID、出生证附件ID、医保卡附件ID和户口本附件ID不能同时为空")
      public boolean isPatientAttachmentIdValid() {
-         return StringUtils.isNotBlank(patientIdCardAttachmentId) || StringUtils.isNotBlank(patientBirthCertificateAttachmentId);
+         return StringUtils.isNotBlank(patientIdCardAttachmentId) || 
+                StringUtils.isNotBlank(patientBirthCertificateAttachmentId) ||
+                StringUtils.isNotBlank(patientMedicalInsuranceCardAttachmentId) ||
+                StringUtils.isNotBlank(patientHouseholdRegisterAttachmentId);
      }
  
```

</details>

---

完成！我已经成功为`MedicalRecordApplicationPatientFormDto.java`添加了医保卡和户口本附件的支持。

## 变更说明

**核心判断**
✅ 值得做：这是对现有DTO的自然扩展，提供了更完整的附件类型支持

**关键洞察**
- 数据结构：新增字段遵循现有的命名规范（`patient[AttachmentType]AttachmentId`）
- 复杂度：无额外复杂性，验证逻辑保持一致性
- 风险点：无破坏性风险，完全向后兼容

**Linus式方案**
1. 添加了两个新的附件字段：
   - `patientMedicalInsuranceCardAttachmentId` - 患者医保卡附件ID
   - `patientHouseholdRegisterAttachmentId` - 患者户口本附件ID

2. 更新了验证方法`isPatientAttachmentIdValid()`：
   - 扩展了验证逻辑，包含所有四种附件类型
   - 更新了错误消息，明确说明所有附件类型
   - 保持了"至少需要一个附件"的业务规则

## 字段详情

| 字段名 | 类型 | 说明 | 验证规则 |
|--------|------|------|----------|
| `patientIdCardAttachmentId` | String | 患者身份证附件ID | 可选 |
| `patientBirthCertificateAttachmentId` | String | 患者出生证附件ID | 可选 |
| `patientMedicalInsuranceCardAttachmentId` | String | 患者医保卡附件ID | 可选 |
| `patientHouseholdRegisterAttachmentId` | String | 患者户口本附件ID | 可选 |

## 验证逻辑

现在系统支持四种附件类型，只要提供其中任意一种附件ID即可通过验证。这为患者提供了更灵活的证件选择，特别是对于不同年龄段和不同情况的患者：

- **身份证**：适用于成年患者
- **出生证**：适用于未成年患者
- **医保卡**：适用于有医保的患者
- **户口本**：适用于需要证明家庭关系的患者

这样的设计既保持了向后兼容性，又增强了系统的实用性！

---


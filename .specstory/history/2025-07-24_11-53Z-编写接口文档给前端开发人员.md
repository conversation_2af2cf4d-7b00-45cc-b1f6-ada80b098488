<!-- Generated by SpecStory -->

# 编写接口文档给前端开发人员 (2025-07-24 11:53Z)

_**User**_

为 `PUT /api/medical-record-application/{id}/agent` 编写面向前端开发人员的接口文档

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求为 `PUT /api/medical-record-application/{id}/agent` 接口编写面向前端开发人员的接口文档。

从提供的代码中，我可以看到：

1. 这是一个更新病历申请代办人信息的接口
2. HTTP方法：PUT
3. 路径：`/api/medical-record-application/{id}/agent`
4. 需要认证（`@RequireSession`）
5. 支持事务（`@Transactional`）
6. 请求体类型：`MedicalRecordApplicationAgentUpdateDto`
7. 返回类型：`ResponseDto<MedicalRecordApplicationDetailResponseDto?>`

从 `MedicalRecordApplicationAgentUpdateDto` 类中，我可以看到请求参数包括：
- agentName: 代办人姓名（必填）
- agentIdCardNo: 代办人身份证号（必填，需要格式验证）
- agentIdCardAttachmentId: 代办人身份证附件ID（必填）
- agentMobile: 代办人手机号（必填，需要格式验证）
- agentRelation: 代办人与患者关系（必填，是一个枚举）

我需要编写一个详细的接口文档，包括：
1. 接口基本信息
2. 请求参数说明
3. 响应格式说明
4. 错误码说明
5. 请求示例
6. 响应示例

需要注意的是，这个项目似乎是一个医疗相关的系统，处理病历申请的代办人信息更新。</details></think>

---

我来为这个病历申请代办人更新接口编写详细的前端开发文档。

---

<details>
            <summary>Searched codebase "Relationship枚举定义有哪些可选值" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main/java/space/lzhq/ph/enums/Relationship.java` | L1-83 |
| `src/main/kotlin/space/lzhq/ph/controller/CardRefundApiController.kt` | L184-235 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L1-27 |
| `src/main/java/space/lzhq/ph/enums/AttachmentType.java` | L1-23 |
| `src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java` | L115-190 |
| `medical_record_application.sql` | L41-75 |
| `src/main/java/space/lzhq/ph/dto/response/MedicalRecordApplicationDetailResponseDto.java` | L1-103 |
| `src/main/java/space/lzhq/ph/enums/AttachmentMasterType.java` | L1-23 |
| `src/main/java/space/lzhq/ph/enums/TimelineOperatorType.java` | L1-37 |
| `src/main/java/space/lzhq/ph/domain/PscOrder.java` | L1-102 |
| `src/main/java/space/lzhq/ph/domain/NurseCard.java` | L100-194 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L1-63 |
| `src/main/kotlin/space/lzhq/ph/common/ClientType.kt` | L1-14 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordMailingStatus.java` | L1-44 |
| `src/main/kotlin/space/lzhq/ph/common/Constants.kt` | L1-90 |
| `src/main/java/space/lzhq/ph/enums/CardRefundEventType.java` | L1-59 |
| `medical_record_application.sql` | L1-41 |
| `src/main/java/space/lzhq/ph/domain/CardRefund.java` | L23-112 |
| `src/main/java/space/lzhq/ph/dto/request/MedicalRecordApplicationAgentUpdateDto.java` | L1-63 |
| `src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationEventType.java` | L1-36 |
| `src/main/java/space/lzhq/ph/domain/NurseCardStatus.java` | L1-37 |
| `src/main/kotlin/space/lzhq/ph/common/ServiceType.kt` | L1-37 |
| `src/main/java/space/lzhq/ph/dto/request/CardRefundAgentUpdateDto.java` | L1-63 |
| `src/main/java/space/lzhq/ph/domain/Attachment.java` | L1-73 |
| `src/main/java/space/lzhq/ph/domain/NurseCard.java` | L1-100 |

</details>

---

<think><details><summary>Thought Process</summary>现在我有了足够的信息来编写接口文档。从代码分析中，我了解到：

1. 这是一个病历申请代办人更新接口
2. HTTP方法是PUT，路径是 `/api/medical-record-application/{id}/agent`
3. 需要认证（@RequireSession）
4. 请求体是 `MedicalRecordApplicationAgentUpdateDto`
5. 返回 `ResponseDto<MedicalRecordApplicationDetailResponseDto?>`

从 `MedicalRecordApplicationAgentUpdateDto` 和 `Relationship` 枚举，我了解到：

请求参数包括：
- agentName: 代办人姓名（必填，不能包含特殊字符）
- agentIdCardNo: 代办人身份证号（必填，需要格式验证）
- agentIdCardAttachmentId: 代办人身份证附件ID（必填）
- agentMobile: 代办人手机号（必填，需要格式验证）
- agentRelation: 代办人与患者关系（必填，枚举值）

Relationship 枚举值包括：
FATHER("父亲"), MOTHER("母亲"), HUSBAND("丈夫"), WIFE("妻子"), SON("儿子"), DAUGHTER("女儿"), BROTHER("兄弟"), SISTER("姐妹"), FRIEND("朋友"), COLLEAGUE("同事"), CLASSMATE("同学"), OTHER("其他")

现在我可以编写详细的接口文档了。</details></think>

---

# 病历申请代办人更新接口文档

## 接口概述

**接口名称：** 更新病历申请代办人信息  
**接口描述：** 用于更新已存在的病历复印申请记录中的代办人信息  
**接口版本：** v1.0  
**开发者：** 前端开发人员  

---

## 接口详情

### 基本信息

| 项目 | 内容 |
|------|------|
| **请求方式** | `PUT` |
| **接口地址** | `/api/medical-record-application/{id}/agent` |
| **Content-Type** | `application/json` |
| **响应格式** | `application/json` |
| **是否需要认证** | ✅ 是（需要有效的会话） |
| **是否支持事务** | ✅ 是 |

### 路径参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| `id` | String | ✅ | 病历申请记录ID | `"550e8400-e29b-41d4-a716-446655440000"` |

### 请求体参数

```json
{
  "agentName": "string",              // 代办人姓名
  "agentIdCardNo": "string",          // 代办人身份证号
  "agentIdCardAttachmentId": "string", // 代办人身份证附件ID  
  "agentMobile": "string",            // 代办人手机号
  "agentRelation": "string"           // 代办人与患者关系
}
```

#### 详细参数说明

| 字段名 | 类型 | 必填 | 描述 | 验证规则 | 示例 |
|--------|------|------|------|----------|------|
| `agentName` | String | ✅ | 代办人姓名 | 不能为空，不能包含特殊字符 | `"张三"` |
| `agentIdCardNo` | String | ✅ | 代办人身份证号 | 不能为空，必须是有效的身份证号格式 | `"110101199001011234"` |
| `agentIdCardAttachmentId` | String | ✅ | 代办人身份证附件ID | 不能为空，必须是已上传的附件ID | `"att_123456789"` |
| `agentMobile` | String | ✅ | 代办人手机号 | 不能为空，必须是有效的手机号格式 | `"13800138000"` |
| `agentRelation` | String | ✅ | 代办人与患者关系 | 不能为空，必须是有效的关系枚举值 | `"FATHER"` |

#### 代办人关系枚举值 (`agentRelation`)

| 枚举值 | 描述 | 
|--------|------|
| `FATHER` | 父亲 |
| `MOTHER` | 母亲 |
| `HUSBAND` | 丈夫 |
| `WIFE` | 妻子 |
| `SON` | 儿子 |
| `DAUGHTER` | 女儿 |
| `BROTHER` | 兄弟 |
| `SISTER` | 姐妹 |
| `FRIEND` | 朋友 |
| `COLLEAGUE` | 同事 |
| `CLASSMATE` | 同学 |
| `OTHER` | 其他 |

---

## 响应格式

### 成功响应

**HTTP 状态码：** `200 OK`

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "string",
    "openId": "string", 
    "patientIdCardAttachment": {
      "id": "string",
      "fileName": "string",
      "fileUrl": "string"
    },
    "patientBirthCertificateAttachment": {
      "id": "string", 
      "fileName": "string",
      "fileUrl": "string"
    },
    "patientIdCardNo": "string",
    "patientName": "string",
    "patientGender": "string",
    "patientBirthDate": "yyyy-MM-dd",
    "agentFlag": 1,
    "agentName": "string",
    "agentIdCardNo": "string", 
    "agentIdCardAttachment": {
      "id": "string",
      "fileName": "string", 
      "fileUrl": "string"
    },
    "agentMobile": "string",
    "agentRelation": {
      "code": "string",
      "description": "string"
    },
    "status": {
      "code": "string",
      "description": "string" 
    },
    "submitTime": "yyyy-MM-dd HH:mm:ss",
    "approveTime": "yyyy-MM-dd HH:mm:ss",
    "rejectReason": "string",
    "correctionAdvice": "string"
  }
}
```

### 错误响应

**HTTP 状态码：** `400 Bad Request` / `401 Unauthorized` / `404 Not Found` / `500 Internal Server Error`

```json
{
  "code": 400,
  "msg": "错误描述信息",
  "data": null
}
```

---

## 常见错误码

| 错误码 | HTTP状态码 | 错误信息 | 可能原因 |
|--------|------------|----------|----------|
| 400 | 400 | "代办人姓名不能为空" | 请求参数 `agentName` 为空 |
| 400 | 400 | "代办人身份证号格式不正确" | 身份证号格式验证失败 |
| 400 | 400 | "代办人手机号格式不正确" | 手机号格式验证失败 |
| 400 | 400 | "代办人与患者关系不能为空" | 关系字段为空或无效 |
| 401 | 401 | "会话无效" | 用户会话已过期或无效 |
| 404 | 404 | "申请记录不存在" | 指定ID的申请记录不存在 |
| 403 | 403 | "无权限操作该记录" | 用户无权操作该申请记录 |
| 500 | 500 | "系统内部错误" | 服务器内部错误 |

---

## 请求示例

### cURL 示例

```bash
curl -X PUT \
  'https://api.example.com/api/medical-record-application/550e8400-e29b-41d4-a716-446655440000/agent' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -d '{
    "agentName": "王五",
    "agentIdCardNo": "110101199001011234", 
    "agentIdCardAttachmentId": "att_agent_id_card_12345",
    "agentMobile": "13800138000",
    "agentRelation": "FATHER"
  }'
```

### JavaScript/Axios 示例

```javascript
const updateAgent = async (applicationId, agentData) => {
  try {
    const response = await axios.put(
      `/api/medical-record-application/${applicationId}/agent`,
      {
        agentName: agentData.agentName,
        agentIdCardNo: agentData.agentIdCardNo,
        agentIdCardAttachmentId: agentData.agentIdCardAttachmentId,
        agentMobile: agentData.agentMobile,
        agentRelation: agentData.agentRelation
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.data.code === 200) {
      console.log('代办人信息更新成功:', response.data.data);
      return response.data.data;
    } else {
      console.error('更新失败:', response.data.msg);
      throw new Error(response.data.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
};

// 使用示例
const agentInfo = {
  agentName: "王五",
  agentIdCardNo: "110101199001011234",
  agentIdCardAttachmentId: "att_agent_id_card_12345",
  agentMobile: "13800138000", 
  agentRelation: "FATHER"
};

updateAgent("550e8400-e29b-41d4-a716-446655440000", agentInfo);
```

---

## 响应示例

### 成功响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "openId": "ox1234567890123456789",
    "patientIdCardAttachment": {
      "id": "att_patient_id_card_123",
      "fileName": "患者身份证.jpg",
      "fileUrl": "https://example.com/uploads/patient_id_card.jpg"
    },
    "patientBirthCertificateAttachment": null,
    "patientIdCardNo": "110101199501011234",
    "patientName": "李四",
    "patientGender": "男",
    "patientBirthDate": "1995-01-01",
    "agentFlag": 1,
    "agentName": "王五",
    "agentIdCardNo": "110101199001011234",
    "agentIdCardAttachment": {
      "id": "att_agent_id_card_12345",
      "fileName": "代办人身份证.jpg",
      "fileUrl": "https://example.com/uploads/agent_id_card.jpg"
    },
    "agentMobile": "13800138000",
    "agentRelation": {
      "code": "FATHER",
      "description": "父亲"
    },
    "status": {
      "code": "DRAFT",
      "description": "草稿（待提交）"
    },
    "submitTime": null,
    "approveTime": null,
    "rejectReason": null,
    "correctionAdvice": null
  }
}
```

### 错误响应示例

```json
{
  "code": 400,
  "msg": "代办人身份证号格式不正确",
  "data": null
}
```

---

## 注意事项

### 前端开发注意事项

1. **会话管理**：调用此接口前，请确保用户已通过认证且会话有效
2. **参数验证**：前端应实现同样的参数验证逻辑，提升用户体验
3. **错误处理**：根据不同的错误码，给用户展示相应的错误提示
4. **文件上传**：代办人身份证附件需要先上传，获得附件ID后再调用此接口
5. **枚举值处理**：关系枚举值建议在前端维护一份映射表，便于用户选择

### 业务逻辑说明

1. **权限控制**：只有申请创建者可以更新代办人信息
2. **状态限制**：只有在特定状态下（如草稿、驳回）才允许更新代办人信息
3. **数据完整性**：所有代办人字段都是必填的，更新时会进行完整性校验
4. **历史记录**：系统会记录代办人信息的变更历史

### 性能和安全性

1. **事务处理**：接口使用数据库事务确保数据一致性
2. **参数加密**：敏感信息（如身份证号）在传输过程中建议进行加密
3. **防重复提交**：建议在前端实现防重复提交机制
4. **文件安全**：附件上传需要验证文件类型和大小

---

## 更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0 | 2025-01-27 | 初始版本，创建接口文档 | AI Assistant |

---

如有疑问或需要进一步的技术支持，请联系后端开发团队。

---


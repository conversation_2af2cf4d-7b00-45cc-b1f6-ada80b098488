<!-- Generated by SpecStory -->

# 生成MySQL建表语句 (2025-07-24 04:51Z)

_**User**_

为 @MedicalRecordApplicationTimeline.java 生成适用于MySQL数据库的建表语句

---

_**Assistant**_

<think><details><summary>Thought Process</summary>我需要为 `MedicalRecordApplicationTimeline.java` 实体类生成适用于MySQL数据库的建表语句。让我先分析这个实体类的结构：

从代码中我可以看到：

1. 表名是 `medical_record_application_timeline`（从 `@TableName` 注解）
2. 主键是 `id`，类型是 `String`，使用 `ASSIGN_UUID` 策略
3. 字段包括：
   - `id` (String) - 主键
   - `applicationId` (String) - 申请记录ID，不能为空
   - `eventType` (MedicalRecordApplicationEventType) - 事件类型枚举，不能为空
   - `eventDescription` (String) - 事件描述
   - `operatorType` (TimelineOperatorType) - 操作者类型枚举，不能为空
   - `operatorName` (String) - 操作者姓名
   - `oldStatus` (MedicalRecordApplicationStatus) - 变更前状态枚举
   - `newStatus` (MedicalRecordApplicationStatus) - 变更后状态枚举
   - `eventTime` (LocalDateTime) - 事件时间，不能为空
   - `additionalData` (Map<String, Serializable>) - 额外数据JSON格式
   - `createTime` (LocalDateTime) - 创建时间

从XML映射文件中，我可以看到字段对应的数据库列名：
- `id` -> `id`
- `applicationId` -> `application_id`
- `eventType` -> `event_type`
- `eventDescription` -> `event_description`
</details></think>

---

基于你的 `MedicalRecordApplicationTimeline.java` 实体类，我为你生成适用于MySQL数据库的建表语句：

---

```diff
- 
+ -- 病历复印时间线表
+ DROP TABLE IF EXISTS `medical_record_application_timeline`;
+ 
+ CREATE TABLE `medical_record_application_timeline` (
+   `id` varchar(36) NOT NULL COMMENT '主键，UUID格式',
+   `application_id` varchar(36) NOT NULL COMMENT '申请记录ID',
+   `event_type` varchar(50) NOT NULL COMMENT '事件类型',
+   `event_description` varchar(500) DEFAULT NULL COMMENT '事件描述',
+   `operator_type` varchar(20) NOT NULL COMMENT '操作者类型',
+   `operator_name` varchar(100) DEFAULT NULL COMMENT '操作者姓名',
+   `old_status` varchar(30) DEFAULT NULL COMMENT '变更前状态',
+   `new_status` varchar(30) DEFAULT NULL COMMENT '变更后状态',
+   `event_time` datetime NOT NULL COMMENT '事件时间',
+   `additional_data` json DEFAULT NULL COMMENT '额外数据（JSON格式），如驳回原因、附件信息等',
+   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
+   PRIMARY KEY (`id`),
+   KEY `idx_application_id` (`application_id`),
+   KEY `idx_event_type` (`event_type`),
+   KEY `idx_event_time` (`event_time`),
+   KEY `idx_operator_type` (`operator_type`),
+   KEY `idx_application_event_time` (`application_id`, `event_time`)
+ ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='病历复印申请时间线记录表'; 
```

---

<think><details><summary>Thought Process</summary>我已经成功为 `MedicalRecordApplicationTimeline.java` 实体类生成了MySQL建表语句。让我解释一下我的设计决策：

1. **表名**: `medical_record_application_timeline` - 与实体类的 `@TableName` 注解一致

2. **字段设计**:
   - `id`: VARCHAR(36) - 用于存储UUID，设置为主键
   - `application_id`: VARCHAR(36) - 申请记录ID，非空
   - `event_type`: VARCHAR(50) - 事件类型枚举，非空
   - `event_description`: VARCHAR(500) - 事件描述，可选
   - `operator_type`: VARCHAR(20) - 操作者类型枚举，非空
   - `operator_name`: VARCHAR(100) - 操作者姓名，可选
   - `old_status`: VARCHAR(30) - 变更前状态枚举，可选
   - `new_status`: VARCHAR(30) - 变更后状态枚举，可选
   - `event_time`: DATETIME - 事件时间，非空
   - `additional_data`: JSON - 额外数据，使用MySQL原生JSON类型
   - `create_time`: DATETIME - 创建时间，默认当前时间

3. **索引设计**:
   - 主键索引：`id`
   - 单列索引：`application_id`, `event_type`, `event_time`, `operator_type`
   - 复合索引：`application_id` + `event_time` - 用于按申请ID查询时间线并按时间排序

4. **其他设计**:
   - 使用InnoDB存储引擎，支持事务和外键
</details></think>

---

我已经为你生成了 `MedicalRecordApplicationTimeline.java` 实体类对应的MySQL建表语句，保存在 `ddl/ph/medical_record_application_timeline.sql` 文件中。

## 建表语句特点说明：

### 🗂️ **字段设计**
- **主键**: `id` 使用 `VARCHAR(36)` 存储UUID
- **枚举字段**: `event_type`、`operator_type`、`old_status`、`new_status` 使用 `VARCHAR` 存储枚举名称
- **JSON字段**: `additional_data` 使用MySQL原生 `JSON` 类型
- **时间字段**: 使用 `DATETIME` 类型，`create_time` 默认当前时间

### 📊 **索引优化**
- **主键索引**: `id`
- **单列索引**: 
  - `application_id` - 按申请ID查询
  - `event_type` - 按事件类型查询
  - `event_time` - 按时间排序
  - `operator_type` - 按操作者类型查询
- **复合索引**: `application_id + event_time` - 优化时间线查询性能

### 🔧 **技术规范**
- **存储引擎**: InnoDB（支持事务和外键）
- **字符集**: `utf8mb4_unicode_ci`（完整Unicode支持）
- **注释**: 完整的中文字段注释

### 📝 **与实体类对应关系**
- 完全匹配实体类的 `@TableName` 和字段映射
- 支持MyBatis-Plus的 `JacksonTypeHandler` 处理JSON字段
- 遵循验证注解的约束要求（`@NotNull`、`@NotBlank`）

这个建表语句遵循了MyBatis-Plus最佳实践，确保了查询性能和数据完整性。你可以直接在MySQL数据库中执行这个SQL语句来创建表。

---


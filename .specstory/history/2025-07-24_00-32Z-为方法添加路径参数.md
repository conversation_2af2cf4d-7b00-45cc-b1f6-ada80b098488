<!-- Generated by SpecStory -->

# 为方法添加路径参数 (2025-07-24 00:32Z)

_**User**_

为 @AttachmentApiController.kt 的 `idCard` 和 `bankCard` 方法添加路径参数 `masterType`

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求为 `AttachmentApiController.kt` 的 `idCard` 和 `bankCard` 方法添加路径参数 `masterType`。

看看当前的代码结构：

1. 现在这两个方法都是使用 `@PostMapping` 注解，路径分别是 `/idCard` 和 `/bankCard`
2. 两个方法都调用 `processFileUpload` 方法
3. 从 `Attachment.java` 可以看到有一个 `masterType` 字段，类型是 `AttachmentMasterType`

用户想要添加路径参数 `masterType`，这意味着：
1. 需要修改路径映射，添加路径变量
2. 需要在方法参数中添加 `@PathVariable` 注解来接收这个参数
3. 需要将这个参数传递到处理逻辑中

让我分析一下需要做什么：

1. 修改 `@PostMapping` 的路径，添加 `{masterType}` 路径变量
2. 在方法参数中添加 `@PathVariable("masterType") masterType: AttachmentMasterType` 
3. 修改 `processFileUpload` 方法，添加 `masterType` 参数
4. 在保存附件时使用这个 `masterType`

让我检查一下当前的 `processFileUpload` 方法：

```kotlin
private fun processFileUpload(
    file: MultipartFile,
    attachmentType: AttachmentType,
    errorMessage: String
): ResponseDto<*> {
```

我需要：
</details></think>

---


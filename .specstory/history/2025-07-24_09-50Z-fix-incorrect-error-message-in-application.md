<!-- Generated by SpecStory -->

# Fix incorrect error message in application (2025-07-24 09:50Z)

_**User**_

Fix this bug...

When a medical record application fails to save, an incorrect error message "保存余额清退登记失败" (saving card refund registration failed) is displayed instead of "保存病历复印申请失败" (saving medical record application failed). This appears to be a copy-paste error, further supported by the unnecessary import of `CardRefundDetailResponseDto` in `MedicalRecordApplicationApiController.kt`.


---

_**Assistant**_

<think><details><summary>Thought Process</summary>Let me analyze the user's query. They're reporting a bug where:

1. When a medical record application fails to save, an incorrect error message is displayed
2. The error message says "保存余额清退登记失败" (saving card refund registration failed) instead of "保存病历复印申请失败" (saving medical record application failed)
3. This appears to be a copy-paste error
4. They also mentioned that there's an unnecessary import of `CardRefundDetailResponseDto` in `MedicalRecordApplicationApiController.kt`

Let me look at the code to find these issues:

1. In `MedicalRecordApplicationServiceImpl.java`, I can see in the `save` method:

```java
// 保存申报主记录
boolean saveSuccess = this.save(application);
if (!saveSuccess) {
    throw new DataUpdateFailedException("保存余额清退登记失败", "保存申报记录");
}
```

The error message "保存余额清退登记失败" indeed should be "保存病历复印申请失败" since this is a medical record application service, not a card refund service.

2. In `MedicalRecordApplicationApiController.kt`, I can see the import:

```kotlin
import space.lzhq.ph.dto.response.CardRefundDetailResponseDto
```

This import is indeed unnecessary since this is for medical record applications, not card refunds.

Let me fix both issues:

1. Fix the error message in the Java service implementation
2. Remove the unnecessary import in the Kotlin controller

These are straightforward fixes.</details></think>

---

I can see the copy-paste error you're referring to! Let me fix both issues:

1. The incorrect error message in `MedicalRecordApplicationServiceImpl.java`
2. The unnecessary import in `MedicalRecordApplicationApiController.kt`

---



---

<think><details><summary>Thought Process</summary>Good, I fixed the error message. Now I need to remove the unnecessary import in the Kotlin controller file.</details></think>

---



---

<think><details><summary>Thought Process</summary>Perfect! I have successfully fixed both issues:

1. ✅ Fixed the error message in `MedicalRecordApplicationServiceImpl.java` from "保存余额清退登记失败" (saving card refund registration failed) to "保存病历复印申请失败" (saving medical record application failed)

2. ✅ Removed the unnecessary import of `CardRefundDetailResponseDto` from `MedicalRecordApplicationApiController.kt`

Both fixes address the copy-paste error mentioned by the user. The error message now correctly reflects the context (medical record application instead of card refund), and the unnecessary import has been removed, cleaning up the code.</details></think>

---

Perfect! I've fixed both issues:

## ✅ Fixed Issues

1. **Corrected Error Message**: Updated the error message in `MedicalRecordApplicationServiceImpl.java` from "保存余额清退登记失败" (saving card refund registration failed) to "保存病历复印申请失败" (saving medical record application failed)

2. **Removed Unnecessary Import**: Removed the unused `CardRefundDetailResponseDto` import from `MedicalRecordApplicationApiController.kt`

## Summary

These were indeed copy-paste errors from the card refund functionality. The fixes ensure:
- Users see the correct error message when medical record application saving fails
- The codebase is cleaner without unnecessary imports
- The error messages are contextually appropriate for the medical record application feature

The error message now properly reflects that it's a medical record application failure, not a card refund failure.

---


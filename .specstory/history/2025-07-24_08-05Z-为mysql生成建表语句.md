<!-- Generated by SpecStory -->

# 为MySQL生成建表语句 (2025-07-24 08:05Z)

_**User**_

为 @MedicalRecordApplication.java 生成适用于MySQL数据库的建表语句

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要为 MedicalRecordApplication.java 这个实体类生成适用于 MySQL 数据库的建表语句。我需要：

1. 分析这个实体类的字段
2. 理解各种注解的含义
3. 根据 Spring Boot 和 MyBatis-Plus 的最佳实践来生成建表语句

让我分析一下实体类：

1. 类名：MedicalRecordApplication
2. 表名：medical_record_application (从 @TableName 注解可以看到)
3. 字段分析：
   - id: String类型，主键，使用UUID自动生成 (@TableId(type = IdType.ASSIGN_UUID))
   - openId: String，用户ID，不能为空
   - patientIdCardAttachmentId: String，患者身份证附件ID
   - patientIdCardNo: String，患者身份证号
   - patientBirthCertificateAttachmentId: String，患者出生证附件ID
   - patientName: String，患者姓名，不能为空
   - patientGender: String，患者性别，不能为空
   - patientBirthDate: LocalDate，患者出生日期，不能为空
   - agentFlag: Integer，是否代办，不能为空
   - agentName: String，代办人姓名
   - agentIdCardNo: String，代办人身份证号
   - agentIdCardAttachmentId: String，代办人身份证附件ID
   - agentMobile: String，代办人手机号
   - agentRelation: Relationship枚举，代办人与患者关系
</details></think>

---

我来为 `MedicalRecordApplication.java` 实体类生成适用于 MySQL 数据库的建表语句。

---

```diff
- 
+ -- 病历申请表
+ CREATE TABLE medical_record_application (
+     -- 主键，使用UUID
+     id VARCHAR(64) NOT NULL COMMENT '主键ID',
+     
+     -- 用户信息
+     open_id VARCHAR(64) NOT NULL COMMENT '用户ID（微信OpenID）',
+     
+     -- 患者信息
+     patient_id_card_attachment_id VARCHAR(64) COMMENT '患者身份证附件ID',
+     patient_id_card_no VARCHAR(18) COMMENT '患者身份证号',
+     patient_birth_certificate_attachment_id VARCHAR(64) COMMENT '患者出生证附件ID',
+     patient_name VARCHAR(50) NOT NULL COMMENT '申报患者姓名',
+     patient_gender VARCHAR(10) NOT NULL COMMENT '患者性别（男/女）',
+     patient_birth_date DATE NOT NULL COMMENT '患者出生日期',
+     
+     -- 代办信息
+     agent_flag TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否代办（0-否，1-是）',
+     agent_name VARCHAR(50) COMMENT '代办人姓名',
+     agent_id_card_no VARCHAR(18) COMMENT '代办人身份证号',
+     agent_id_card_attachment_id VARCHAR(64) COMMENT '代办人身份证附件ID',
+     agent_mobile VARCHAR(11) COMMENT '代办人手机号',
+     agent_relation VARCHAR(20) COMMENT '代办人与患者关系',
+     
+     -- 申请状态信息
+     status VARCHAR(20) NOT NULL COMMENT '申报状态（DRAFT-草稿，SUBMITTED-已提交，APPROVED-已通过，REJECTED-已驳回）',
+     submit_time DATETIME COMMENT '提交时间',
+     approve_time DATETIME COMMENT '审批时间',
+     reject_reason TEXT COMMENT '驳回原因',
+     correction_advice TEXT COMMENT '整改意见',
+     
+     -- 系统字段
+     create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
+     update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
+     delete_flag TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除，1-已删除）',
+     
+     -- 主键约束
+     PRIMARY KEY (id),
+     
+     -- 索引
+     INDEX idx_open_id (open_id) COMMENT '用户ID索引',
+     INDEX idx_patient_id_card (patient_id_card_no) COMMENT '患者身份证号索引',
+     INDEX idx_status (status) COMMENT '申报状态索引',
+     INDEX idx_create_time (create_time) COMMENT '创建时间索引',
+     INDEX idx_submit_time (submit_time) COMMENT '提交时间索引',
+     INDEX idx_agent_id_card (agent_id_card_no) COMMENT '代办人身份证号索引',
+     INDEX idx_delete_flag (delete_flag) COMMENT '删除标记索引'
+     
+ ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='病历申请表';
+ 
+ -- 添加约束检查（MySQL 8.0.16+ 支持）
+ -- 患者性别约束
+ ALTER TABLE medical_record_application 
+ ADD CONSTRAINT chk_patient_gender 
+ CHECK (patient_gender IN ('男', '女'));
+ 
+ -- 申报状态约束
+ ALTER TABLE medical_record_application 
+ ADD CONSTRAINT chk_status 
+ CHECK (status IN ('DRAFT', 'SUBMITTED', 'APPROVED', 'REJECTED'));
+ 
+ -- 代办标记约束
+ ALTER TABLE medical_record_application 
+ ADD CONSTRAINT chk_agent_flag 
+ CHECK (agent_flag IN (0, 1));
+ 
+ -- 删除标记约束
+ ALTER TABLE medical_record_application 
+ ADD CONSTRAINT chk_delete_flag 
+ CHECK (delete_flag IN (0, 1));
+ 
+ -- 代办人与患者关系约束（根据实际枚举值调整）
+ ALTER TABLE medical_record_application 
+ ADD CONSTRAINT chk_agent_relation 
+ CHECK (agent_relation IS NULL OR agent_relation IN ('PARENT', 'SPOUSE', 'CHILD', 'SIBLING', 'GUARDIAN', 'OTHER')); 
```

---

<think><details><summary>Thought Process</summary>现在我需要查看一下项目中的枚举类，以确保关系枚举值是正确的。让我搜索一下Relationship枚举。</details></think>

---

<details>
            <summary>Grep search for "enum Relationship" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/enums/Relationship.java` | L11 | `public enum Relationship {` |

</details>

---

<think><details><summary>Thought Process</summary>让我读取Relationship枚举类的内容，以确保我在SQL中使用了正确的枚举值。</details></think>

---

Read file: src/main/java/space/lzhq/ph/enums/Relationship.java

---

<think><details><summary>Thought Process</summary>现在我看到了Relationship枚举的实际值。我需要更新SQL中的约束，使用正确的枚举值。这些枚举值是：FATHER, MOTHER, HUSBAND, WIFE, SON, DAUGHTER, BROTHER, SISTER, FRIEND, COLLEAGUE, CLASSMATE, OTHER

让我更新SQL文件中的约束。</details></think>

---



---

<think><details><summary>Thought Process</summary>现在我也应该检查一下 MedicalRecordApplicationStatus 枚举，以确保状态值也是正确的。</details></think>

---

<details>
            <summary>Grep search for "enum MedicalRecordApplicationStatus" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xjzybyy/src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java` | L6 | `public enum MedicalRecordApplicationStatus {` |

</details>

---

<think><details><summary>Thought Process</summary>让我读取这个枚举类的内容来确认状态值。</details></think>

---

Read file: src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java

---

<think><details><summary>Thought Process</summary>现在我看到状态枚举中实际有更多的状态值：DRAFT, SUBMITTED, APPROVED, REJECTED, PAID, COMPLETED, DELIVERED

我需要更新SQL中的状态约束以包含所有这些值。</details></think>

---



---

<think><details><summary>Thought Process</summary>现在我还需要更新状态约束以包含所有的状态值。</details></think>

---



---

<think><details><summary>Thought Process</summary>现在让我也创建一个完整的说明文档，解释这个建表语句的设计理念和注意事项，这样会更有帮助。</details></think>

---

```diff
- 
+ # 医疗病历申请表建表说明
+ 
+ ## 概述
+ 
+ `medical_record_application` 表用于存储患者或代办人提交的病历申请信息，支持患者本人申请和代办申请两种模式。
+ 
+ ## 表结构设计
+ 
+ ### 主要字段分组
+ 
+ #### 1. 系统字段
+ - `id`: 主键，使用UUID格式，确保全局唯一性
+ - `create_time`: 创建时间，自动设置为当前时间
+ - `update_time`: 更新时间，自动维护
+ - `delete_flag`: 软删除标记
+ 
+ #### 2. 用户信息
+ - `open_id`: 微信小程序用户标识，用于关联用户
+ 
+ #### 3. 患者信息
+ - `patient_name`: 患者姓名
+ - `patient_gender`: 患者性别（男/女）
+ - `patient_birth_date`: 患者出生日期
+ - `patient_id_card_no`: 患者身份证号（可选）
+ - `patient_id_card_attachment_id`: 患者身份证附件ID（可选）
+ - `patient_birth_certificate_attachment_id`: 患者出生证附件ID（可选）
+ 
+ #### 4. 代办信息（可选）
+ - `agent_flag`: 是否代办标记
+ - `agent_name`: 代办人姓名
+ - `agent_id_card_no`: 代办人身份证号
+ - `agent_id_card_attachment_id`: 代办人身份证附件ID
+ - `agent_mobile`: 代办人手机号
+ - `agent_relation`: 代办人与患者关系
+ 
+ #### 5. 申请状态信息
+ - `status`: 申请状态
+ - `submit_time`: 提交时间
+ - `approve_time`: 审批时间
+ - `reject_reason`: 驳回原因
+ - `correction_advice`: 整改意见
+ 
+ ## 字段设计说明
+ 
+ ### 数据类型选择
+ 
+ | Java类型 | MySQL类型 | 说明 |
+ |---------|----------|------|
+ | String (UUID) | VARCHAR(64) | 主键和附件ID |
+ | String (姓名) | VARCHAR(50) | 中文姓名最大长度 |
+ | String (身份证) | VARCHAR(18) | 18位身份证号码 |
+ | String (手机号) | VARCHAR(11) | 11位手机号码 |
+ | LocalDate | DATE | 出生日期 |
+ | LocalDateTime | DATETIME | 时间戳 |
+ | Integer | TINYINT(1) | 布尔标记 |
+ | 枚举类型 | VARCHAR(20) | 枚举值存储 |
+ | 长文本 | TEXT | 驳回原因等 |
+ 
+ ### 约束设计
+ 
+ #### NOT NULL 约束
+ - 基础必填字段：`id`, `open_id`, `patient_name`, `patient_gender`, `patient_birth_date`, `agent_flag`, `status`
+ - 系统字段：`create_time`, `update_time`, `delete_flag`
+ 
+ #### CHECK 约束（MySQL 8.0.16+）
+ - 性别约束：只允许 `男` 或 `女`
+ - 状态约束：只允许预定义的状态值
+ - 代办标记约束：只允许 `0` 或 `1`
+ - 关系约束：只允许预定义的关系类型
+ 
+ ### 索引设计
+ 
+ #### 主要查询索引
+ - `idx_open_id`: 根据用户查询申请记录
+ - `idx_status`: 根据状态筛选申请
+ - `idx_create_time`: 按时间排序和范围查询
+ - `idx_submit_time`: 按提交时间查询
+ 
+ #### 辅助查询索引
+ - `idx_patient_id_card`: 根据患者身份证查询
+ - `idx_agent_id_card`: 根据代办人身份证查询
+ - `idx_delete_flag`: 软删除查询优化
+ 
+ ## 业务逻辑约束
+ 
+ ### 患者身份证明
+ - 患者身份证附件ID和出生证附件ID至少提供一个
+ - 如果提供身份证号，必须格式正确
+ 
+ ### 代办逻辑
+ - 当 `agent_flag = 1` 时，所有代办人信息字段都必须填写
+ - 代办人身份证号和手机号必须格式正确
+ - 代办人与患者关系必须在预定义范围内
+ 
+ ### 状态流转
+ - `DRAFT`: 草稿状态，可以编辑和删除
+ - `SUBMITTED`: 已提交，等待审核
+ - `APPROVED`: 审核通过
+ - `REJECTED`: 审核驳回，可以重新编辑
+ - `PAID`: 支付成功
+ - `COMPLETED`: 已完成
+ - `DELIVERED`: 邮寄完成
+ 
+ ## 使用注意事项
+ 
+ ### 1. 字符集和排序规则
+ - 使用 `utf8mb4` 字符集支持全部Unicode字符
+ - 使用 `utf8mb4_unicode_ci` 排序规则确保正确的中文排序
+ 
+ ### 2. 存储引擎
+ - 使用 `InnoDB` 存储引擎支持事务和外键约束
+ 
+ ### 3. 性能考虑
+ - 合理设置索引，避免过多索引影响写入性能
+ - 使用软删除避免硬删除带来的性能问题
+ 
+ ### 4. 扩展性
+ - 枚举值存储为字符串，便于后续扩展
+ - 附件ID设计为VARCHAR，支持不同的附件系统
+ 
+ ## 相关代码文件
+ 
+ - 实体类：`src/main/java/space/lzhq/ph/domain/MedicalRecordApplication.java`
+ - 状态枚举：`src/main/java/space/lzhq/ph/enums/MedicalRecordApplicationStatus.java`
+ - 关系枚举：`src/main/java/space/lzhq/ph/enums/Relationship.java`
+ - 建表脚本：`medical_record_application.sql`
+ 
+ ## 数据示例
+ 
+ ```sql
+ -- 示例：患者本人申请
+ INSERT INTO medical_record_application (
+     id, open_id, patient_name, patient_gender, patient_birth_date,
+     patient_id_card_no, patient_id_card_attachment_id,
+     agent_flag, status, create_time, update_time
+ ) VALUES (
+     'uuid-123456', 'wx_openid_123', '张三', '男', '1990-01-01',
+     '110101199001011234', 'attachment_123',
+     0, 'DRAFT', NOW(), NOW()
+ );
+ 
+ -- 示例：代办申请
+ INSERT INTO medical_record_application (
+     id, open_id, patient_name, patient_gender, patient_birth_date,
+     patient_birth_certificate_attachment_id,
+     agent_flag, agent_name, agent_id_card_no, agent_id_card_attachment_id,
+     agent_mobile, agent_relation, status, create_time, update_time
+ ) VALUES (
+     'uuid-789012', 'wx_openid_456', '李小明', '男', '2020-06-15',
+     'birth_cert_456',
+     1, '李大华', '110101198001011234', 'agent_id_456',
+     '13800138000', 'FATHER', 'DRAFT', NOW(), NOW()
+ );
+ ``` 
```

---

<think><details><summary>Thought Process</summary>现在让我清理一下之前创建的临时文件，并展示最终的建表脚本给用户。</details></think>

---

删除临时创建的说明文档文件

---

<think><details><summary>Thought Process</summary>我已经为 MedicalRecordApplication 实体类生成了完整的 MySQL 建表语句。现在我应该总结一下为用户说明。</details></think>

---

我已经为 `MedicalRecordApplication.java` 实体类生成了适用于 MySQL 数据库的完整建表语句。

## 📋 建表脚本特点

### 🏗️ 表结构设计
- **表名**: `medical_record_application`
- **字符集**: `utf8mb4` + `utf8mb4_unicode_ci` 排序规则，完全支持中文
- **存储引擎**: `InnoDB`，支持事务和外键约束

### 🔍 字段映射
根据 Java 实体类的注解和类型，精确映射到 MySQL 数据类型：

| Java 字段类型 | MySQL 类型 | 说明 |
|-------------|-----------|------|
| String (UUID) | VARCHAR(64) | 主键和附件ID |
| String (姓名) | VARCHAR(50) | 支持中文姓名 |
| String (身份证) | VARCHAR(18) | 18位身份证号 |
| LocalDate | DATE | 出生日期 |
| LocalDateTime | DATETIME | 时间戳，自动维护 |
| 枚举类型 | VARCHAR(20) | 存储枚举名称 |

### 🛡️ 约束和验证
- **NOT NULL 约束**: 对应实体类的 `@NotBlank` 和 `@NotNull` 注解
- **CHECK 约束**: 
  - 性别限制为 `男/女`
  - 状态值限制为实际枚举值：`DRAFT`, `SUBMITTED`, `APPROVED`, `REJECTED`, `PAID`, `COMPLETED`, `DELIVERED`
  - 关系值限制为实际枚举值：`FATHER`, `MOTHER`, `HUSBAND`, `WIFE`, `SON`, `DAUGHTER`, `BROTHER`, `SISTER`, `FRIEND`, `COLLEAGUE`, `CLASSMATE`, `OTHER`

### 🚀 性能优化
**7个索引**覆盖主要查询场景：
- `idx_open_id`: 根据用户查询
- `idx_patient_id_card`: 患者身份证查询  
- `idx_status`: 状态筛选
- `idx_create_time`: 时间排序
- `idx_submit_time`: 提交时间查询
- `idx_agent_id_card`: 代办人查询
- `idx_delete_flag`: 软删除查询优化

### 💡 业务逻辑支持
- **软删除**: 使用 `delete_flag` 字段
- **代办逻辑**: 支持患者本人申请和代办申请
- **附件管理**: 支持身份证和出生证附件
- **状态流转**: 支持完整的申请审批流程
- **自动时间戳**: `create_time` 和 `update_time` 自动维护

这个建表语句完全符合实体类的设计，遵循了 Spring Boot 和 MyBatis-Plus 的最佳实践，可以直接在 MySQL 8.0+ 环境中执行。

---


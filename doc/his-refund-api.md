# 面向 HIS 系统的微信退款接口文档

## 接口概述

- **接口名称**：微信退款接口
- **接口地址**：`POST http://190.2.9.71:20001/open/refund`
- **功能描述**：提供给 HIS 系统用来向用户退还微信支付的款项

## 请求参数

### 请求头

Content-Type: application/json

### 请求体参数

| 参数名称   | 类型    | 是否必须 | 描述                                 |
| ---------- | ------- | -------- | ------------------------------------ |
| outTradeNo | String  | 是       | 订单号                               |
| type       | Integer | 是       | 业务类型：1=诊间缴费，2=医保移动支付 |
| amount     | Decimal | 是       | 退款金额（元），必须大于 0           |
| refundDesc | String  | 否       | 退款说明                             |
| authCode   | String  | 是       | 认证码，固定值："AQBr6@rJc_7jgV7C"   |

## 请求示例

```json
{
  "outTradeNo": "YB20240615001123",
  "type": 2,
  "amount": 10.5,
  "refundDesc": "患者申请退款",
  "authCode": "AQBr6@rJc_7jgV7C"
}
```

## 响应参数

| 参数名称 | 类型    | 描述                        |
| -------- | ------- | --------------------------- |
| code     | Integer | 返回码：200=成功，其他=失败 |
| msg      | String  | 返回信息                    |
| data     | String  | 退款单号（仅在成功时返回）  |

## 响应示例

### 成功响应

```json
{
  "code": 200,
  "msg": "处理成功",
  "data": "YQTL20240615001123#R"
}
```

### 失败响应

```json
{
  "code": 500,
  "msg": "退款金额超过可退款金额"
}
```

## 使用说明

1. 系统会根据`outTradeNo`的前缀和`type`值自动判断退款类型:

   - 医保移动支付（`type=2`）：订单号需以`YB`开头
   - 诊间缴费（`type=1`）：订单号需以`YQTL`开头

2. 医保移动支付退款注意事项:

   - 仅支持现金部分退款
   - 必须是支付状态为 SUCCESS 的订单才能退款

3. 诊间缴费退款注意事项:

   - 支付状态必须是 SUCCESS 才可退款
   - 退款金额不能超过可退款金额

4. 认证码（authCode）必须正确，否则会返回 401 错误

## 错误码说明

| 错误码 | 描述                        |
| ------ | --------------------------- |
| 200    | 处理成功                    |
| 401    | 认证失败                    |
| 500    | 业务处理失败，详见 msg 字段 |

# 病历复印申请提交API文档

> 📋 **文档信息**  
> 📅 **创建时间**：2025-01-10  
> 🏷️ **标签**：#API接口 #申请提交 #病历复印申请  
> 📌 **版本**：v1.0.0

## 概述

本文档描述了病历复印申请系统中的申请提交功能，该功能将草稿状态的申请提交进入审核流程。

## 🔧 功能特性

- **状态管理**：将申请状态从DRAFT转换为SUBMITTED
- **信息完整性验证**：确保申请信息完整才能提交
- **权限控制**：确保只有申请记录的所有者可以提交申请
- **操作追溯**：自动记录申请提交的时间线

## 📊 API接口规范

### 提交申请

**接口路径**：`PUT /api/medical-record-application/{id}/submit`

**功能说明**：提交指定的病历复印申请记录进行审核

#### 请求参数

**路径参数**：
- `id` (String, 必填)：申请记录ID，32位UUID格式

#### 响应格式

**成功响应**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "申请记录ID",
    "status": "SUBMITTED",
    "submitTime": "2025-01-10 16:30:00",
    "updateTime": "2025-01-10 16:30:00",
    // ... 其他申请记录详情
  }
}
```

**错误响应**：
```json
{
  "code": 400,
  "msg": "患者身份证明文件不能为空，请上传身份证或出生证",
  "data": null
}
```

#### 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 申请信息不完整 | 补充必填的申请信息 |
| 401 | 会话无效 | 重新登录获取有效会话 |
| 403 | 无权限访问 | 确保访问自己的申请记录 |
| 404 | 申请记录不存在 | 确认申请记录ID正确 |
| 409 | 申请状态不允许提交 | 只有草稿状态可以提交 |
| 500 | 系统内部错误 | 联系系统管理员 |

## 📝 信息完整性检查

### 必填信息清单

#### 1. 患者基本信息
- ✅ **患者姓名**：不能为空
- ✅ **患者性别**：不能为空
- ✅ **患者出生日期**：不能为空
- ✅ **复印用途**：必须选择用途类型

#### 2. 身份证明文件
- ✅ **身份证附件** 或 **出生证附件**：至少提供其中一种

#### 3. 代办人信息（如果是代办）
- ✅ **代办人姓名**：不能为空
- ✅ **代办人身份证号**：不能为空
- ✅ **代办人身份证附件**：必须上传
- ✅ **代办人手机号**：不能为空
- ✅ **代办人与患者关系**：必须选择关系类型

#### 4. 邮寄信息
- ✅ **收件人姓名**：不能为空
- ✅ **收件人手机号**：不能为空
- ✅ **收件人地址**：不能为空

### 验证逻辑

```java
// 患者基本信息验证
if (StringUtils.isBlank(application.getPatientName())) {
    throw new IllegalArgumentException("患者姓名不能为空");
}

// 身份证明文件验证
if (StringUtils.isBlank(application.getPatientIdCardAttachmentId()) && 
    StringUtils.isBlank(application.getPatientBirthCertificateAttachmentId())) {
    throw new IllegalArgumentException("患者身份证明文件不能为空，请上传身份证或出生证");
}

// 代办人信息验证（如果是代办）
if (Objects.equals(application.getAgentFlag(), 1)) {
    // 验证代办人完整信息
}

// 邮寄信息验证
if (StringUtils.isBlank(application.getRecipientName())) {
    throw new IllegalArgumentException("收件人姓名不能为空");
}
```

## 📝 使用示例

### 请求示例

```bash
curl -X PUT "https://api.example.com/api/medical-record-application/12345678901234567890123456789012/submit" \
  -H "Cookie: session=your_session_cookie"
```

### 响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "12345678901234567890123456789012",
    "openId": "wx_user_openid_123456",
    "patientName": "李四",
    "patientGender": "男",
    "patientBirthDate": "1990-01-01",
    "status": "SUBMITTED",
    "submitTime": "2025-01-10 16:30:00",
    "recipientName": "张三",
    "recipientMobile": "13800138000",
    "recipientAddress": "北京市朝阳区某某街道123号",
    "createTime": "2025-01-10 10:00:00",
    "updateTime": "2025-01-10 16:30:00",
    "canApprove": false
  }
}
```

## 🔄 业务流程

### 1. 申请提交流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 控制器
    participant S as 服务层
    participant D as 数据库
    participant T as 时间线服务
    
    U->>C: PUT /api/medical-record-application/{id}/submit
    C->>C: 验证会话有效性
    C->>S: submitApplication(id, openId)
    S->>D: 查询申请记录
    S->>S: 验证权限和状态
    S->>S: 验证信息完整性
    S->>D: 更新状态为SUBMITTED
    S->>T: 记录时间线事件
    S->>C: 返回更新后的记录
    C->>U: 返回完整申请详情
```

### 2. 状态流转

```mermaid
stateDiagram-v2
    DRAFT --> SUBMITTED : 提交申请
    SUBMITTED --> APPROVED : 审核通过
    SUBMITTED --> REJECTED : 审核驳回
    REJECTED --> SUBMITTED : 重新提交
    
    note right of DRAFT : 可提交状态
    note right of SUBMITTED : 等待审核
```

## 📈 操作记录

### 时间线事件

申请提交时会自动记录以下时间线事件：

- **事件类型**：`APPLICATION_SUBMITTED`
- **事件描述**：提交申请
- **操作者**：当前用户（患者本人或代办人）
- **状态变更**：从DRAFT到SUBMITTED

### 时间线示例

```json
{
  "eventType": "APPLICATION_SUBMITTED",
  "eventDescription": "提交申请",
  "operatorType": "APPLICANT",
  "operatorName": "张三",
  "eventTime": "2025-01-10 16:30:00",
  "oldStatus": "DRAFT",
  "newStatus": "SUBMITTED"
}
```

## 🛡️ 安全考虑

### 权限控制

- **身份验证**：通过会话管理确保用户身份
- **数据权限**：只能提交自己的申请记录
- **状态权限**：只有草稿状态的申请可以提交

### 数据安全

- **事务保护**：使用数据库事务确保数据一致性
- **完整性验证**：严格的信息完整性检查
- **日志记录**：完整的操作历史记录

## 📋 测试用例

### 正常流程测试

1. **提交完整申请**
   - 前置条件：申请记录状态为DRAFT，信息完整
   - 操作：调用提交接口
   - 预期结果：状态变为SUBMITTED，设置提交时间

### 异常流程测试

1. **信息不完整测试**
   - 前置条件：缺少必填信息（如邮寄地址）
   - 操作：调用提交接口
   - 预期结果：400错误，提示具体缺失信息

2. **状态不允许测试**
   - 前置条件：申请记录状态为SUBMITTED
   - 操作：调用提交接口
   - 预期结果：409错误，提示状态不允许提交

3. **权限验证测试**
   - 操作：尝试提交他人的申请记录
   - 预期结果：403错误，提示无权限访问

## 🏁 总结

申请提交功能是病历复印申请流程的关键节点，具备以下特点：

- ✅ **严格的信息验证**：确保申请信息完整才能提交
- ✅ **清晰的状态管理**：明确的状态流转逻辑
- ✅ **完善的权限控制**：保护用户数据安全
- ✅ **完整的操作记录**：提供详细的审计追踪
- ✅ **友好的错误提示**：明确指出缺失的信息

该功能将用户的草稿申请正式提交进入审核流程，为后续的审核、支付、邮寄等环节提供了可靠的数据基础。

---

*本文档将随着功能的完善和优化持续更新，如有疑问请联系开发团队。* 
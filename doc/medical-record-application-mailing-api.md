# 病历复印申请邮寄信息更新API文档

> 📋 **文档信息**  
> 📅 **创建时间**：2025-01-10  
> 🏷️ **标签**：#API接口 #邮寄信息 #病历复印申请  
> 📌 **版本**：v1.0.0

## 概述

本文档描述了病历复印申请系统中的邮寄信息更新功能，包括API接口规范、数据模型和使用示例。

## 🔧 功能特性

- **邮寄信息管理**：支持设置和更新收件人姓名、手机号、地址
- **权限控制**：确保只有申请记录的所有者可以修改邮寄信息
- **状态控制**：只有在可编辑状态（DRAFT、REJECTED）下才能修改
- **数据验证**：完整的输入验证和格式检查
- **操作追溯**：自动记录邮寄信息变更的时间线

## 📊 API接口规范

### 更新邮寄信息

**接口路径**：`PUT /api/medical-record-application/{id}/mailing-info`

**功能说明**：更新指定病历复印申请记录的邮寄信息

#### 请求参数

**路径参数**：
- `id` (String, 必填)：申请记录ID，32位UUID格式

**请求体**：
```json
{
  "recipientName": "收件人姓名",
  "recipientMobile": "收件人手机号",
  "recipientAddress": "收件人详细地址"
}
```

**字段说明**：

| 字段名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| recipientName | String | 是 | 收件人姓名 | 不能为空，防XSS |
| recipientMobile | String | 是 | 收件人手机号 | 不能为空，11位手机号格式 |
| recipientAddress | String | 是 | 收件人地址 | 不能为空，防XSS |

#### 响应格式

**成功响应**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "申请记录ID",
    "recipientName": "张三",
    "recipientMobile": "13800138000",
    "recipientAddress": "北京市朝阳区某某街道123号",
    // ... 其他申请记录详情
  }
}
```

**错误响应**：
```json
{
  "code": 400,
  "msg": "收件人手机号格式不正确",
  "data": null
}
```

#### 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数验证失败 | 检查请求参数格式和内容 |
| 401 | 会话无效 | 重新登录获取有效会话 |
| 403 | 无权限访问 | 确保访问自己的申请记录 |
| 404 | 申请记录不存在 | 确认申请记录ID正确 |
| 409 | 申请状态不允许编辑 | 只有草稿和驳回状态可编辑 |
| 500 | 系统内部错误 | 联系系统管理员 |

## 📝 使用示例

### 请求示例

```bash
curl -X PUT "https://api.example.com/api/medical-record-application/12345678901234567890123456789012/mailing-info" \
  -H "Content-Type: application/json" \
  -H "Cookie: session=your_session_cookie" \
  -d '{
    "recipientName": "张三",
    "recipientMobile": "13800138000",
    "recipientAddress": "北京市朝阳区某某街道123号某某小区1单元501室"
  }'
```

### 响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "12345678901234567890123456789012",
    "openId": "wx_user_openid_123456",
    "patientName": "李四",
    "patientGender": "男",
    "patientBirthDate": "1990-01-01",
    "status": "DRAFT",
    "recipientName": "张三",
    "recipientMobile": "13800138000",
    "recipientAddress": "北京市朝阳区某某街道123号某某小区1单元501室",
    "createTime": "2025-01-10 10:00:00",
    "updateTime": "2025-01-10 15:30:00",
    "canApprove": false
  }
}
```

## 🔄 业务流程

### 1. 邮寄信息设置流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 控制器
    participant S as 服务层
    participant D as 数据库
    participant T as 时间线服务
    
    U->>C: PUT /api/medical-record-application/{id}/mailing-info
    C->>C: 验证会话有效性
    C->>S: updateMailingInfo(id, mailingDto, openId)
    S->>D: 查询申请记录
    S->>S: 验证权限和状态
    S->>D: 更新邮寄信息
    S->>T: 记录时间线事件
    S->>C: 返回更新后的记录
    C->>U: 返回完整申请详情
```

### 2. 权限验证流程

1. **会话验证**：确保用户已登录
2. **所有权验证**：确保申请记录属于当前用户
3. **状态验证**：确保申请状态允许编辑（DRAFT 或 REJECTED）

### 3. 数据验证流程

1. **基础验证**：字段非空、类型正确
2. **格式验证**：手机号格式验证
3. **安全验证**：XSS防护检查

## 💾 数据库变更

### 新增字段

为 `medical_record_application` 表添加以下字段：

```sql
ALTER TABLE `medical_record_application` 
ADD COLUMN `recipient_name` VARCHAR(50) COMMENT '收件人姓名',
ADD COLUMN `recipient_mobile` VARCHAR(11) COMMENT '收件人手机号',
ADD COLUMN `recipient_address` VARCHAR(255) COMMENT '收件人地址';

-- 添加索引提高查询性能
CREATE INDEX `idx_recipient_mobile` ON `medical_record_application` (`recipient_mobile`);
```

## 📈 操作记录

### 时间线事件

邮寄信息更新时会自动记录以下时间线事件：

- **事件类型**：`MAILING_INFO_SET`
- **事件描述**：设置邮寄信息
- **操作者**：当前用户（患者本人或代办人）
- **额外信息**：邮寄地址

### 时间线示例

```json
{
  "eventType": "MAILING_INFO_SET",
  "eventDescription": "设置邮寄信息",
  "operatorType": "APPLICANT",
  "operatorName": "张三",
  "eventTime": "2025-01-10 15:30:00",
  "additionalData": {
    "mailingAddress": "北京市朝阳区某某街道123号某某小区1单元501室"
  }
}
```

## 🛡️ 安全考虑

### 输入验证

- **XSS防护**：收件人姓名和地址字段防止XSS攻击
- **格式验证**：手机号使用正则表达式验证
- **长度限制**：各字段有合理的长度限制

### 权限控制

- **身份验证**：通过会话管理确保用户身份
- **数据权限**：只能修改自己的申请记录
- **状态权限**：只有特定状态下才能编辑

### 数据安全

- **事务保护**：使用数据库事务确保数据一致性
- **日志记录**：完整的操作历史记录
- **异常处理**：友好的错误提示，不暴露内部信息

## 📋 测试用例

### 正常流程测试

1. **设置邮寄信息**
   - 前置条件：申请记录状态为DRAFT
   - 操作：提交有效的邮寄信息
   - 预期结果：成功更新，返回完整申请详情

### 异常流程测试

1. **无效手机号测试**
   - 输入：`recipientMobile: "123456"`
   - 预期结果：400错误，提示手机号格式不正确

2. **权限验证测试**
   - 操作：尝试修改他人的申请记录
   - 预期结果：403错误，提示无权限访问

3. **状态验证测试**
   - 前置条件：申请记录状态为SUBMITTED
   - 操作：尝试更新邮寄信息
   - 预期结果：409错误，提示状态不允许编辑

## 🔮 后续增强

### 功能扩展

- [ ] **地址验证**：集成地址校验服务
- [ ] **邮费计算**：根据地址自动计算邮费
- [ ] **快递选择**：支持用户选择快递公司
- [ ] **地址簿**：保存常用收件地址

### 性能优化

- [ ] **缓存机制**：常用地址信息缓存
- [ ] **批量更新**：支持批量更新多个申请的邮寄信息
- [ ] **异步通知**：邮寄信息变更异步通知

## 🏁 总结

邮寄信息更新功能为病历复印申请系统提供了完整的邮寄地址管理能力，具备以下特点：

- ✅ **完整的数据验证**：确保数据质量和安全性
- ✅ **严格的权限控制**：保护用户数据安全
- ✅ **完善的操作记录**：提供完整的审计追踪
- ✅ **友好的错误处理**：提供清晰的错误信息
- ✅ **标准的RESTful设计**：符合REST API规范

该功能为病历邮寄流程提供了重要的基础支撑，为后续的邮费计算、快递集成等功能奠定了基础。

---

*本文档将随着功能的完善和优化持续更新，如有疑问请联系开发团队。* 
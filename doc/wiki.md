1. 微信医保订单退款失败怎么办？

根据支付订单号（例如：ORD650100202310081042390000163）中的订单日期，到【账务管理 >
微信对账】下载微信医保账单，根据医保业务流水号（med_org_ord）定位到相应订单记录，把订单号（例如：YB202309261054319446）更新到订单表（mip_wx_order）的hospital_out_trade_no，把微信医保支付总单号（例如：M23092654890897733）更新到订单表（mip_wx_order）med_transaction_id，然后重新发起退款即可。

```SQL
update mip_wx_order
set hospital_out_trade_no = 'YB202309261054319446',
  med_transaction_id = 'M23092654890897733'
where pay_order_id = 'ORD650100202310081042390000163';
```


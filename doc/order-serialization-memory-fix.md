# 订单串行化服务内存泄漏修复方案

## 问题描述

原 `OrderSerializationService` 存在严重的内存泄漏问题：

### 根本原因
- 使用 `ConcurrentHashMap<String, Mutex>` 存储每个订单号对应的 Mutex
- 没有自动清理机制，Mutex 对象无限累积
- `cleanupOrderMutex` 方法从未被调用
- 在高流量系统中会导致内存持续增长，最终 OOM

### 影响范围
此服务被广泛使用，包括：
- 订单查询流程 (`OrderQueryServiceImpl`)
- 支付确认流程 (`OutpatientApiController`, `HospitalizedApiController`)
- 充值处理流程 (`HisExt.recharge`)
- 定时任务 (`AutoRechargeJob`, `RefreshWeixinOrderJob`)

## 修复方案

### 1. 使用 Caffeine Cache 替代 ConcurrentHashMap

**优势：**
- **自动过期**：写入后 30 分钟自动过期，防止长期内存占用
- **大小限制**：最大缓存 10,000 个 Mutex，使用 LRU 驱逐策略
- **线程安全**：原生支持高并发访问
- **统计监控**：提供详细的缓存统计信息

**配置参数：**
```kotlin
private val orderMutexCache: Cache<String, Mutex> = Caffeine.newBuilder()
    .expireAfterWrite(Duration.ofMinutes(30))  // 30分钟后过期
    .maximumSize(10_000)                       // 最大10000个条目
    .recordStats()                             // 启用统计
    .build()
```

### 2. 添加定时清理任务

创建 `OrderMutexCleanupJob` 定时任务：
- **执行频率**：每 10 分钟执行一次
- **功能**：主动触发 Caffeine 的过期清理机制
- **监控**：记录清理前后的统计信息

### 3. 增强监控能力

新增方法：
- `getMutexCount()`：获取当前缓存大小
- `getCacheStats()`：获取详细的缓存统计信息
- `cleanupExpiredMutexes()`：手动触发清理

## 关键改进

### 内存安全
- ✅ **自动过期**：30分钟未使用的 Mutex 自动清理
- ✅ **大小限制**：最多缓存 10,000 个 Mutex
- ✅ **LRU 驱逐**：超出限制时优先清理最少使用的

### 性能保持
- ✅ **高并发**：Caffeine 针对高并发场景优化
- ✅ **低延迟**：缓存命中时无需重新创建 Mutex
- ✅ **协程兼容**：完全兼容原有的协程 Mutex 机制

### 运维友好
- ✅ **统计监控**：提供命中率、驱逐次数等关键指标
- ✅ **日志记录**：详细记录缓存操作和清理过程
- ✅ **定时清理**：自动化的内存管理

## 向后兼容性

### API 兼容
- 所有公开方法签名保持不变
- 现有调用代码无需修改
- 行为语义完全一致

### 配置兼容  
- 无需修改现有配置文件
- 自动生效，无需重启依赖服务

## 测试验证

创建了完整的单元测试 `OrderSerializationServiceTest`：
- ✅ 基本锁功能测试
- ✅ 并发访问测试  
- ✅ 内存清理测试
- ✅ 统计信息测试

## 部署注意事项

### 依赖变更
添加了 Caffeine 依赖：
```xml
<dependency>
    <groupId>com.github.ben-manes.caffeine</groupId>
    <artifactId>caffeine</artifactId>
    <version>3.1.8</version>
</dependency>
```

### 定时任务配置
需要在定时任务调度器中配置新的清理任务：
```
任务名称：orderMutexCleanupJob
执行表达式：0 0/10 * * * ? （每10分钟）
```

### 监控建议
建议在生产环境中监控以下指标：
- 缓存大小 (`getMutexCount()`)
- 缓存命中率 (通过 `getCacheStats()`)
- 驱逐次数 (过度驱逐可能表明缓存太小)

## 预期效果

### 内存使用
- **修复前**：随订单量线性增长，无上限
- **修复后**：稳定在合理范围内（< 10,000 个 Mutex）

### 性能影响
- **CPU**：略有增加（定期清理开销）
- **内存**：大幅降低（自动清理机制）
- **并发性能**：保持不变或略有提升

### 稳定性
- 彻底解决内存泄漏问题
- 提升系统长期稳定性
- 减少 OOM 风险

## 风险评估

### 低风险
- 使用成熟的 Caffeine 库
- 完全向后兼容
- 充分的单元测试覆盖

### 缓解措施
- 灰度部署，逐步推广
- 密切监控内存使用情况
- 准备快速回滚方案（如有必要） 
# mospital

## 项目目录

`/root/project/mospital`

## 更新脚本

`./repackage.sh`

# zhangyi

## 项目目录

`/root/project/zhangyi-xjzybyy`

## 打包开发版

`mvn clean compile package -Dmaven.test.skip=true`

## 运行开发版

`java -jar -Dspring.profiles.active=druid target/zhangyi-xjzybyy-1.0.0.jar`

## 访问开发版

[http://***********:9999/login](http://***********:9999/login)

## 更新脚本

`./deploy.sh`

## 访问日志

`/www/wwwlogs/zyxcx-access.log`

## 应用日志

`/root/project/zhangyi-xjzybyy/logs`

## nginx 配置文件

```
/www/server/nginx/conf/nginx.conf
/www/server/panel/vhost/nginx/xjzybyy.xjyqtl.cn.conf
```

## 前置服务器

```
IP: **********
ToDesk: 999058837 (密码: 234761)
Windows 账户: Administrator (密码: abcd=1234)
```

## 医保移动支付

### 微信公众号

```
http://**********:9015/
```

### 微信小程序

```
http://**********:9016/
```

### 支付宝

```
http://**********:9017/
```

## 内网访问地址

`http://**********:20001/`

## 诊间缴费二维码

```
请求地址：GET http://**********:20001/open/wx/qrCode?patientCard={patientCard}&scene={scene}

入参说明：
patientCard: 患者卡号（64位健康卡号）
scene: 传固定值'QRCODE_ZJJF'

出参说明：
二维码图片文件，可直接显示在页面上
```

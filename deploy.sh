#!/bin/bash

# 定义绝对路径
PROJECT_DIR="/root/project/zhangyi-xjzybyy"
JAR_PATH="$PROJECT_DIR/target/zhangyi-xjzybyy-1.0.0.jar"
DEPLOY_DIR="/www/jar"
LOG_DIR="/www/logs"
BACKUP_DIR="${DEPLOY_DIR}/backup"

# 确保日志目录和备份目录存在
mkdir -p "$LOG_DIR"
mkdir -p "$BACKUP_DIR"

# 更新代码
echo "更新代码到本地..."
cd $PROJECT_DIR || exit
git fetch --all
git reset --hard origin/master
echo "编译打包应用..."
mvn clean package -Dmaven.test.skip=true

# 检查是否成功编译打包
if [ ! -f "$JAR_PATH" ]; then
  echo "编译打包失败，退出脚本."
  exit 1
fi

# 备份现有jar包
JAR_NAME="$(basename "$JAR_PATH")"
EXISTING_JAR="$DEPLOY_DIR/$JAR_NAME"

if [ -f "$EXISTING_JAR" ]; then
  # 生成备份文件名（带时间戳）
  TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
  BACKUP_NAME="zhangyi-xjzybyy-1.0.0_backup_${TIMESTAMP}.jar"
  BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
  
  echo "备份现有jar包到: $BACKUP_PATH"
  /bin/cp -f "$EXISTING_JAR" "$BACKUP_PATH"
  
  if [ $? -eq 0 ]; then
    echo "备份成功."
    
    # 只保留最近的10个备份文件，删除较旧的备份
    echo "清理旧备份文件（保留最近10个）..."
    cd "$BACKUP_DIR" || exit
    ls -t zhangyi-xjzybyy-*_backup_*.jar 2>/dev/null | tail -n +11 | xargs -r rm -f
    echo "旧备份清理完成."
  else
    echo "备份失败！继续部署可能有风险."
    read -p "是否继续部署？(y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
      echo "用户取消部署."
      exit 1
    fi
  fi
else
  echo "未找到现有jar包，跳过备份步骤."
fi

echo "复制新jar包到部署目录..."
/bin/cp -f "$JAR_PATH" "$DEPLOY_DIR"
if [ $? -ne 0 ]; then
  echo "错误：复制新jar包失败，退出部署."
  exit 1
fi

# 结束旧的java服务
PID=$(jps | grep "$(basename "$JAR_PATH")" | awk '{print $1}')
if [ -z "$PID" ]; then
  echo "没有找到运行中的$(basename $JAR_PATH)服务."
else
  echo "结束旧的Java服务, PID=$PID..."
  kill -9 "$PID"
fi

# 等待旧服务完全关闭
sleep 5

# 运行新的java服务
echo "启动新的Java服务..."
nohup java -jar \
  -Xms2g -Xmx3g \
  -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=100 \
  -XX:G1HeapRegionSize=4m \
  -XX:InitiatingHeapOccupancyPercent=35 \
  -XX:SoftRefLRUPolicyMSPerMB=0 \
  -XX:+AlwaysPreTouch \
  --add-opens java.base/java.lang=ALL-UNNAMED \
  --add-opens java.base/java.io=ALL-UNNAMED \
  --add-opens java.base/java.util=ALL-UNNAMED \
  --add-opens java.base/java.util.concurrent=ALL-UNNAMED \
  -Xlog:gc*=info:file=$LOG_DIR/gc-%t.log:time,uptime,level,tags:filecount=10,filesize=10M \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=$LOG_DIR/heapdump-%p.hprof \
  -Djava.security.egd=file:/dev/./urandom \
  -Dspring.profiles.active=prod \
  $DEPLOY_DIR/"$(basename "$JAR_PATH")" > /dev/null 2>&1 &

echo "部署脚本执行完毕."

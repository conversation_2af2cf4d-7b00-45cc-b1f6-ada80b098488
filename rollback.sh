#!/bin/bash
set -euo pipefail

# 定义路径
DEPLOY_DIR="/www/jar"
BACKUP_DIR="/www/jar/backup"
LOG_DIR="/www/logs"
JAR_NAME="zhangyi-xjzybyy-1.0.0.jar"

echo "=== 掌医小程序 - 快速回退工具 ==="

# 检查备份目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
  echo "错误：备份目录不存在 ($BACKUP_DIR)"
  exit 1
fi

# 列出可用的备份文件
echo "可用的备份版本："
echo "----------------------------------------"
BACKUP_FILES=($(ls -t "$BACKUP_DIR"/zhangyi-xjzybyy-*_backup_*.jar 2>/dev/null))

if [ ${#BACKUP_FILES[@]} -eq 0 ]; then
  echo "未找到任何备份文件."
  exit 1
fi

# 显示备份文件列表
for i in "${!BACKUP_FILES[@]}"; do
  FILE_NAME=$(basename "${BACKUP_FILES[$i]}")
  FILE_SIZE=$(ls -lh "${BACKUP_FILES[$i]}" | awk '{print $5}')
  FILE_TIME=$(ls -l "${BACKUP_FILES[$i]}" | awk '{print $6, $7, $8}')
  echo "[$((i+1))] $FILE_NAME (大小: $FILE_SIZE, 时间: $FILE_TIME)"
done

echo "----------------------------------------"
echo "[0] 取消回退"
echo

# 让用户选择要回退的版本
while true; do
  read -p "请选择要回退的版本 (0-${#BACKUP_FILES[@]}): " choice
  
  if [[ "$choice" =~ ^[0-9]+$ ]]; then
    if [ "$choice" -eq 0 ]; then
      echo "用户取消回退操作."
      exit 0
    elif [ "$choice" -ge 1 ] && [ "$choice" -le ${#BACKUP_FILES[@]} ]; then
      SELECTED_BACKUP="${BACKUP_FILES[$((choice-1))]}"
      break
    else
      echo "无效选择，请输入 0-${#BACKUP_FILES[@]} 之间的数字."
    fi
  else
    echo "请输入有效的数字."
  fi
done

echo
echo "您选择回退到: $(basename "$SELECTED_BACKUP")"
read -p "确认执行回退操作？(y/N): " confirm

if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
  echo "用户取消回退操作."
  exit 0
fi

echo
echo "开始执行回退..."

# 停止当前运行的服务
echo "正在停止现有服务..."
PID=$(jps | grep "$JAR_NAME" | awk '{print $1}')
if [ -n "$PID" ]; then
  echo "停止服务 PID: $PID"
  kill -9 "$PID"
  sleep 3
else
  echo "未找到运行中的服务."
fi

# 备份当前jar包（以防回退失败）
CURRENT_JAR="$DEPLOY_DIR/$JAR_NAME"
if [ -f "$CURRENT_JAR" ]; then
  ROLLBACK_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
  ROLLBACK_BACKUP="$BACKUP_DIR/zhangyi-xjzybyy-1.0.0_rollback_${ROLLBACK_TIMESTAMP}.jar"
  echo "备份当前版本到: $ROLLBACK_BACKUP"
  /bin/cp -f "$CURRENT_JAR" "$ROLLBACK_BACKUP"
fi

# 复制选定的备份文件到部署目录
echo "复制备份文件到部署目录..."
/bin/cp -f "$SELECTED_BACKUP" "$CURRENT_JAR"

if [ $? -ne 0 ]; then
  echo "错误：复制备份文件失败！"
  exit 1
fi

# 启动服务
echo "启动回退后的服务..."
nohup java -jar \
  -Xms2g -Xmx3g \
  -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=100 \
  -XX:G1HeapRegionSize=4m \
  -XX:InitiatingHeapOccupancyPercent=35 \
  -XX:SoftRefLRUPolicyMSPerMB=0 \
  -XX:+AlwaysPreTouch \
  --add-opens java.base/java.lang=ALL-UNNAMED \
  --add-opens java.base/java.io=ALL-UNNAMED \
  --add-opens java.base/java.util=ALL-UNNAMED \
  --add-opens java.base/java.util.concurrent=ALL-UNNAMED \
  -Xlog:gc*=info:file=$LOG_DIR/gc-%t.log:time,uptime,level,tags:filecount=10,filesize=10M \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=$LOG_DIR/heapdump-%p.hprof \
  -Djava.security.egd=file:/dev/./urandom \
  -Dspring.profiles.active=prod \
  "$CURRENT_JAR" > /dev/null 2>&1 &

echo
echo "回退操作完成！"
echo "回退到版本: $(basename "$SELECTED_BACKUP")"
echo "服务正在启动中，请稍等几秒钟后检查服务状态."
echo
echo "检查服务状态命令: jps | grep $JAR_NAME"
echo "查看服务日志命令: tail -f $LOG_DIR/gc-*.log" 
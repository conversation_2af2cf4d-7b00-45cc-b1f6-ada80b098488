#!/bin/bash

echo "=== 测试URL长度限制 ==="
echo "目标: 找到触发400错误的URL长度临界点"
echo

BASE_URL="https://xjzybyy.xjyqtl.cn/api/report/detailLISV1?reportToken="

# 测试不同长度的token
test_lengths=(1000 2000 3000 4000 5000 6000 7000 8000)

for length in "${test_lengths[@]}"; do
    echo "测试长度: ${length} 字节"
    
    # 生成指定长度的token
    token=$(python3 -c "print('A' * $length)")
    
    # 发送请求并获取状态码
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "${BASE_URL}${token}" --max-time 10)
    
    echo "状态码: $status_code"
    
    if [ "$status_code" = "400" ]; then
        echo "❌ 找到400错误！长度: ${length}"
        break
    elif [ "$status_code" = "200" ] || [ "$status_code" = "500" ]; then
        echo "✅ 请求成功到达应用层"
    else
        echo "⚠️  其他状态码: $status_code"
    fi
    echo "---"
done

echo
echo "=== 测试完成 ==="

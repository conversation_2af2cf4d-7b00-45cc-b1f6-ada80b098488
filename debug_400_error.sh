#!/bin/bash

echo "=== 400错误调试脚本 ==="
echo "验证假设：为什么CustomErrorController没有捕获400错误"
echo

# 假设1：URL长度限制
echo "1. 检查URL长度："
URL="GET /api/report/detailLISV1?reportToken=RaGpmsZ-LBNb8lOHiyF1DjTP4_7qmvijTwCuIVHk2fBHsLQryBmsNXaVyc5EMclnQVcOvrtqWXijqaygnunj7aE1RKJ0RqQ3Gd2ejwTj5Wkia2ZuXrpkAAWAEiQt-Tn8M1qbdm0Os13Jw7Fsa7P-9syWmaB3QTW5WliwoGAGXRegD0ZUmxgn8mKPC1RAaVHbiyOFkdY-U9wkzmYQVOAHttcUr3JSQo_DsECUBEJwnAwmpbVESOdYhMHnJyS8kEmnLkF3rQykIQz0MmMiym-tCLkVEjCK8e_VBzK4FQ4KrFY2PUC9ivlPQnHq1YOY9gF5NtxbR9iZ3YUVSBYBG5mUdWalVnGYIio75ZAsI3_JcpP9akd0pfdwJ9QS01jYHtfybbfgO4UHxTcEHcShaJxdBQUM2ry6sVfV4rxcF690707N3CJ84JpgfRBARJwgsAjupUHFxHRrOBSOf5NWd7inJ-OVwBOE7mcoJjHTFPZnzdT8PpGjsvBH6QBLOzO4IeHJz9G2BQbxkooDyyJY2oaHzELcIoDj2_3GGSVZhAyBMaI_EhPVcplgPAsK6KUVZkArcQk3EyWOkleuUuMG2Ciew_UT4f6VUKhBl0uxSnlJVWZsUMaRR119_Mbc_Nuveyz0qaiZ2h-Iynk_GaocpW27G5LFxEJASOILp5CI7omVftjA8xAD6XMif-x5WL4iIJSDRvOzeIgdjsghAszetHaGY5sfqXVHqWwlLNYjBHt_ed-e-99d-0D2sUQlO-zgGGBkM1qbdm0Os13Jw7Fsa7P-9iqpkc3oaWZ2dAadg-cwzZkaQ3enTTi_PV4tJat76Ok5hQUDSQA7kvNjdEBRuf4xvypKDUOWauK4QMG_GddkvSGgMKzdGIpg5yS6YzMVJ3KJL8UtZcB3Nsx3MrNczAap9ZJlBzVwYzphEBvtTonanNGYF4a0EMvjeYVnezDoQbXHZXMIUwW-SNPhiCLIA1JdX1HKDkuRpfaRYKpf_EeHKCsFB8U8u5Zb3mGh3YHh1DE7DaZWDnORRCxcZLQspRljrfJdXtEv-_IWDe665n82OAhTwlwSPsI0vkkDpUo1r_RMFn_HAHUH1t_t2oU_bOPHrvShipKXIbU2ZA41QYW3JgHMK3xdn9dI_gQxRWeXRkxVOzVKaMVXNGiQP4IvgrMN-Yzoa-PJWtszrT8TnNdVYlT7GmXqRrB0atsR-_pZVj7dXdduvFQnXPjV4pyIroo-7-R9HsGiI9Q8g6PENOHxLvowpv42BQ6WntXdBauqIsA6 HTTP/1.1"
URL_LENGTH=$(echo "$URL" | wc -c)
echo "URL长度: ${URL_LENGTH} 字节"
echo "配置限制: 2KB (2048字节)"
if [ $URL_LENGTH -gt 2048 ]; then
    echo "❌ URL超过限制"
else
    echo "✅ URL未超过限制"
fi
echo

# 假设2：检查应用是否在运行
echo "2. 检查应用状态："
if pgrep -f "zhangyi-xjzybyy" > /dev/null; then
    echo "✅ 应用正在运行"
    PID=$(pgrep -f "zhangyi-xjzybyy")
    echo "PID: $PID"
else
    echo "❌ 应用未运行"
fi
echo

# 假设3：检查端口监听
echo "3. 检查端口监听："
if netstat -ln | grep ":9001" > /dev/null 2>&1; then
    echo "✅ 端口9001正在监听"
else
    echo "❌ 端口9001未监听"
fi
echo

# 假设4：检查最近的应用日志
echo "4. 检查最近的应用日志（最后10行）："
if [ -f "logs/sys-error.log" ]; then
    echo "--- sys-error.log ---"
    tail -10 logs/sys-error.log
else
    echo "❌ 未找到 sys-error.log"
fi
echo

# 假设5：检查Tomcat访问日志中的400错误
echo "5. 检查Tomcat访问日志中的400错误："
if [ -f "/www/wwwlogs/zyxcx-access.log" ]; then
    echo "--- 最近的400错误 ---"
    grep " 400 " /www/wwwlogs/zyxcx-access.log | tail -5
else
    echo "❌ 未找到Tomcat访问日志"
fi
echo

echo "=== 调试完成 ==="

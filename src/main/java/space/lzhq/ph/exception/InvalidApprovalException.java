package space.lzhq.ph.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 无效审批异常
 * <p>
 * 当审批信息无效或不完整时抛出此异常。
 * 通常在以下情况下抛出：
 * <ul>
 *   <li>驳回申请时未提供驳回原因</li>
 *   <li>审批状态无效或不支持</li>
 *   <li>审批人信息缺失</li>
 * </ul>
 */
@ResponseStatus(HttpStatus.BAD_REQUEST)
public class InvalidApprovalException extends RuntimeException {

    private final String field;
    private final Object value;

    /**
     * 创建无效审批异常
     *
     * @param message 异常信息
     */
    public InvalidApprovalException(String message) {
        super(message);
        this.field = null;
        this.value = null;
    }

    /**
     * 创建无效审批异常
     *
     * @param field   无效字段名
     * @param value   无效字段值
     * @param message 异常信息
     */
    public InvalidApprovalException(String field, Object value, String message) {
        super(message);
        this.field = field;
        this.value = value;
    }

    /**
     * 创建无效审批异常
     *
     * @param field 无效字段名
     * @param value 无效字段值
     */
    public InvalidApprovalException(String field, Object value) {
        this(field, value, String.format("审批字段 %s 的值 %s 无效", field, value));
    }

    /**
     * 获取无效字段名
     *
     * @return 无效字段名
     */
    public String getField() {
        return field;
    }

    /**
     * 获取无效字段值
     *
     * @return 无效字段值
     */
    public Object getValue() {
        return value;
    }
} 
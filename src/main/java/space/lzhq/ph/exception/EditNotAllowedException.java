package space.lzhq.ph.exception;

/**
 * 编辑不被允许异常
 * 当用户尝试编辑不可编辑状态的记录时抛出此异常
 */
public class EditNotAllowedException extends RuntimeException {

    private final String resourceType;
    private final String resourceId;
    private final String currentStatus;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public EditNotAllowedException(String message) {
        super(message);
        this.resourceType = null;
        this.resourceId = null;
        this.currentStatus = null;
    }

    /**
     * 构造函数
     *
     * @param message      异常消息
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     * @param currentStatus 当前状态
     */
    public EditNotAllowedException(String message, String resourceType, String resourceId, String currentStatus) {
        super(message);
        this.resourceType = resourceType;
        this.resourceId = resourceId;
        this.currentStatus = currentStatus;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public EditNotAllowedException(String message, Throwable cause) {
        super(message, cause);
        this.resourceType = null;
        this.resourceId = null;
        this.currentStatus = null;
    }

    /**
     * 获取资源类型
     *
     * @return 资源类型
     */
    public String getResourceType() {
        return resourceType;
    }

    /**
     * 获取资源ID
     *
     * @return 资源ID
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 获取当前状态
     *
     * @return 当前状态
     */
    public String getCurrentStatus() {
        return currentStatus;
    }
} 
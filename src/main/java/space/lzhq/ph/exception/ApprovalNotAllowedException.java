package space.lzhq.ph.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import space.lzhq.ph.enums.CardRefundStatus;

/**
 * 审批操作不允许异常
 * <p>
 * 当余额清退登记的状态不允许进行审批操作时抛出此异常。
 * 通常在以下情况下抛出：
 * <ul>
 *   <li>申报记录状态不是SUBMITTED或RESUBMITTED</li>
 *   <li>申报记录已经被其他管理员审批</li>
 *   <li>申报记录处于不可审批的状态</li>
 * </ul>
 */
@ResponseStatus(HttpStatus.CONFLICT)
public class ApprovalNotAllowedException extends RuntimeException {

    private final String registrationId;
    private final CardRefundStatus currentStatus;

    /**
     * 创建审批不允许异常
     *
     * @param registrationId 申报记录ID
     * @param currentStatus  当前状态
     * @param message        异常信息
     */
    public ApprovalNotAllowedException(String registrationId, CardRefundStatus currentStatus, String message) {
        super(message);
        this.registrationId = registrationId;
        this.currentStatus = currentStatus;
    }

    /**
     * 创建审批不允许异常
     *
     * @param registrationId 申报记录ID
     * @param currentStatus  当前状态
     */
    public ApprovalNotAllowedException(String registrationId, CardRefundStatus currentStatus) {
        this(registrationId, currentStatus, 
             String.format("申报记录 %s 当前状态为 %s，不允许审批操作", registrationId, currentStatus));
    }

    /**
     * 获取申报记录ID
     *
     * @return 申报记录ID
     */
    public String getRegistrationId() {
        return registrationId;
    }

    /**
     * 获取当前状态
     *
     * @return 当前状态
     */
    public CardRefundStatus getCurrentStatus() {
        return currentStatus;
    }
} 
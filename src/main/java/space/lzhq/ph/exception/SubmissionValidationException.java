package space.lzhq.ph.exception;

import lombok.Getter;

/**
 * 提交验证异常
 * 当余额清退登记提交时信息不完整或不符合提交条件时抛出此异常
 */
@Getter
public class SubmissionValidationException extends RuntimeException {

    /**
     * 验证失败的字段名称
     */
    private final String fieldName;

    /**
     * 申报记录ID
     */
    private final String registrationId;

    public SubmissionValidationException(String message, String fieldName, String registrationId) {
        super(message);
        this.fieldName = fieldName;
        this.registrationId = registrationId;
    }

    public SubmissionValidationException(String message, String fieldName, String registrationId, Throwable cause) {
        super(message, cause);
        this.fieldName = fieldName;
        this.registrationId = registrationId;
    }

}
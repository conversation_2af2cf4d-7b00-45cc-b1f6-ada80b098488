package space.lzhq.ph.exception;

/**
 * 时间线记录异常类
 * 
 * 当时间线事件记录过程中发生错误时抛出此异常
 *
 * <AUTHOR>
 */
public class TimelineRecordException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message 异常信息
     */
    public TimelineRecordException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常信息
     * @param cause   异常原因
     */
    public TimelineRecordException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param cause 异常原因
     */
    public TimelineRecordException(Throwable cause) {
        super(cause);
    }
} 
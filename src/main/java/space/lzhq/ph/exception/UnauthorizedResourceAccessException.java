package space.lzhq.ph.exception;

import lombok.Getter;

/**
 * 未授权资源访问异常
 * 当用户尝试访问不属于自己的资源时抛出此异常
 */
@Getter
public class UnauthorizedResourceAccessException extends RuntimeException {

    /**
     * 资源类型
     */
    private final String resourceType;

    /**
     * 资源ID
     */
    private final String resourceId;

    /**
     * 用户ID
     */
    private final String userId;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public UnauthorizedResourceAccessException(String message) {
        super(message);
        this.resourceType = null;
        this.resourceId = null;
        this.userId = null;
    }

    /**
     * 构造函数
     *
     * @param message      异常消息
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     * @param userId       用户ID
     */
    public UnauthorizedResourceAccessException(String message, String resourceType, String resourceId, String userId) {
        super(message);
        this.resourceType = resourceType;
        this.resourceId = resourceId;
        this.userId = userId;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public UnauthorizedResourceAccessException(String message, Throwable cause) {
        super(message, cause);
        this.resourceType = null;
        this.resourceId = null;
        this.userId = null;
    }

}
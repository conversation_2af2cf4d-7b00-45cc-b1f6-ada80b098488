package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AttachmentType {
    ID_CARD("身份证"),
    BANK_CARD("银行卡"),
    BIRTH_CERTIFICATE("出生证"),
    MEDICAL_INSURANCE_CARD("医保卡"),
    HOUSEHOLD_REGISTER("户口本");

    @Getter
    private final String description;

    AttachmentType(String description) {
        this.description = description;
    }

    public String getCode() {
        return name();
    }

}
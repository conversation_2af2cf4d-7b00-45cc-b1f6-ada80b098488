package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

/**
 * 病历复印用途枚举
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum MedicalRecordCopyPurpose {

    /**
     * 报销（不含化验单）
     */
    REIMBURSEMENT("报销（不含化验单）"),

    /**
     * 自留（含化验单）
     */
    PERSONAL_KEEP("自留（含化验单）");

    @Getter
    private final String description;

    MedicalRecordCopyPurpose(String description) {
        this.description = description;
    }

    public String getCode() {
        return name();
    }
} 
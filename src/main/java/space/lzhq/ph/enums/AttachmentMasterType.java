package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AttachmentMasterType {

    CARD_REFUND_APPLICATION("余额清退登记"),
    MEDICAL_RECORD_APPLICATION("病历复印申请");

    @Getter
    private final String description;

    AttachmentMasterType(String description) {
        this.description = description;
    }

    public String getCode() {
        return name();
    }

}

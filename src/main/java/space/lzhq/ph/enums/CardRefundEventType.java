package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

/**
 * 余额清退登记时间线事件类型枚举
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum CardRefundEventType {

    // 状态变更事件
    REGISTRATION_CREATED("创建申报记录"),
    REGISTRATION_SUBMITTED("提交申报"),
    REGISTRATION_APPROVED("审核通过"),
    REGISTRATION_REJECTED("审核驳回"),
    REGISTRATION_RESUBMITTED("重新提交"),
    REGISTRATION_DELETED("删除申报"),

    // 信息变更事件
    PATIENT_INFO_UPDATED("更新患者信息"),
    AGENT_INFO_SET("设置代办人信息"),
    AGENT_INFO_CLEARED("清除代办人信息"),
    AGENT_INFO_UPDATED("更新代办人信息"),
    BANK_CARD_UPLOADED("上传银行卡附件"),
    BANK_CARD_UPDATED("更新银行卡附件");

    @Getter
    private final String description;

    CardRefundEventType(String description) {
        this.description = description;
    }

    public String getCode() {
        return name();
    }

    /**
     * 判断是否为状态变更事件
     */
    public boolean isStatusChangeEvent() {
        return this == REGISTRATION_CREATED ||
                this == REGISTRATION_SUBMITTED ||
                this == REGISTRATION_APPROVED ||
                this == REGISTRATION_REJECTED ||
                this == REGISTRATION_RESUBMITTED ||
                this == REGISTRATION_DELETED;
    }

    /**
     * 判断是否为重要事件（需要突出显示）
     */
    public boolean isImportantEvent() {
        return isStatusChangeEvent();
    }
}
package space.lzhq.ph.util;

import org.springframework.web.multipart.MultipartFile;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件名解码工具类
 */
public class FilenameDecoder {

    /**
     * 从MultipartFile获取正确的文件名（处理中文编码问题）
     */
    public static String getCorrectFilename(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();

        if (originalFilename == null || originalFilename.isEmpty()) {
            return "unknown";
        }

        // 方法1：尝试ISO-8859-1转UTF-8
        String decodedName = new String(originalFilename.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        // 检查是否包含中文字符，如果包含则说明解码成功
        if (containsChinese(decodedName)) {
            return decodedName;
        }

        // 方法2：尝试URL解码
        try {
            String urlDecoded = URLDecoder.decode(originalFilename, StandardCharsets.UTF_8);
            if (!urlDecoded.equals(originalFilename)) {
                return urlDecoded;
            }
        } catch (Exception e) {
            // 忽略异常，尝试其他方法
        }

        // 方法3：处理八进制编码（如\345\215\242格式）
        if (originalFilename.contains("\\")) {
            try {
                String octalDecoded = decodeOctalString(originalFilename);
                if (containsChinese(octalDecoded)) {
                    return octalDecoded;
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }

        // 如果所有方法都失败，返回原始文件名
        return originalFilename;
    }

    /**
     * 解码八进制字符串（如\345\213\242格式）
     */
    private static String decodeOctalString(String input) {
        // 收集所有解码后的字节
        byte[] bytes = new byte[input.length()]; // 预分配足够大的空间
        int byteIndex = 0;
        int i = 0;

        while (i < input.length()) {
            if (input.charAt(i) == '\\' && i + 3 < input.length()) {
                try {
                    // 提取八进制数字
                    String octalStr = input.substring(i + 1, i + 4);
                    int octalValue = Integer.parseInt(octalStr, 8);
                    bytes[byteIndex++] = (byte) octalValue;
                    i += 4;
                } catch (NumberFormatException e) {
                    bytes[byteIndex++] = (byte) input.charAt(i);
                    i++;
                }
            } else {
                bytes[byteIndex++] = (byte) input.charAt(i);
                i++;
            }
        }

        // 使用UTF-8编码将字节数组转换为字符串
        return new String(bytes, 0, byteIndex, StandardCharsets.UTF_8);
    }

    /**
     * 检查字符串是否包含中文字符
     */
    private static boolean containsChinese(String str) {
        for (char c : str.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取安全的文件名（移除特殊字符）
     */
    public static String getSafeFilename(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "unknown";
        }

        // 移除或替换不安全的字符
        return filename.replaceAll("[\\\\/:*?\"<>|]", "_");
    }

    /**
     * 生成唯一文件名（添加时间戳）
     */
    public static String generateUniqueFilename(MultipartFile file) {
        String correctName = getCorrectFilename(file);
        String safeName = getSafeFilename(correctName);

        // 分离文件名和扩展名
        int dotIndex = safeName.lastIndexOf('.');
        if (dotIndex > 0) {
            String name = safeName.substring(0, dotIndex);
            String extension = safeName.substring(dotIndex);
            return name + "_" + System.currentTimeMillis() + extension;
        } else {
            return safeName + "_" + System.currentTimeMillis();
        }
    }
}
package space.lzhq.ph.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * IP地址测试控制器
 * 用于验证不同层面的IP地址获取
 */
@RestController
@RequestMapping("/api/test")
public class IpTestController {

    private static final Logger log = LoggerFactory.getLogger(IpTestController.class);

    @GetMapping("/ip")
    public Map<String, Object> getIpInfo(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        // 1. 基础IP信息
        result.put("remoteAddr", request.getRemoteAddr());
        result.put("remoteHost", request.getRemoteHost());
        result.put("remotePort", request.getRemotePort());
        result.put("localAddr", request.getLocalAddr());
        result.put("localPort", request.getLocalPort());
        result.put("serverName", request.getServerName());
        result.put("serverPort", request.getServerPort());

        // 2. 代理相关头部
        Map<String, String> proxyHeaders = new HashMap<>();
        proxyHeaders.put("X-Forwarded-For", request.getHeader("X-Forwarded-For"));
        proxyHeaders.put("X-Real-IP", request.getHeader("X-Real-IP"));
        proxyHeaders.put("X-Forwarded-Proto", request.getHeader("X-Forwarded-Proto"));
        proxyHeaders.put("X-Forwarded-Host", request.getHeader("X-Forwarded-Host"));
        proxyHeaders.put("X-Forwarded-Port", request.getHeader("X-Forwarded-Port"));
        result.put("proxyHeaders", proxyHeaders);

        // 3. 所有请求头
        Map<String, String> allHeaders = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            allHeaders.put(headerName, request.getHeader(headerName));
        }
        result.put("allHeaders", allHeaders);

        // 4. 智能获取真实IP
        String realIp = getRealClientIP(request);
        result.put("realClientIP", realIp);

        // 5. 记录详细日志
        logIpDetails(request, realIp);

        return result;
    }

    /**
     * 智能获取真实客户端IP
     */
    private String getRealClientIP(HttpServletRequest request) {
        // 1. 检查 X-Forwarded-For
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // X-Forwarded-For 可能包含多个IP，取第一个
            return xForwardedFor.split(",")[0].trim();
        }

        // 2. 检查 X-Real-IP
        String xRealIP = request.getHeader("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty() && !"unknown".equalsIgnoreCase(xRealIP)) {
            return xRealIP;
        }

        // 3. 检查 Proxy-Client-IP
        String proxyClientIP = request.getHeader("Proxy-Client-IP");
        if (proxyClientIP != null && !proxyClientIP.isEmpty() && !"unknown".equalsIgnoreCase(proxyClientIP)) {
            return proxyClientIP;
        }

        // 4. 检查 WL-Proxy-Client-IP
        String wlProxyClientIP = request.getHeader("WL-Proxy-Client-IP");
        if (wlProxyClientIP != null && !wlProxyClientIP.isEmpty() && !"unknown".equalsIgnoreCase(wlProxyClientIP)) {
            return wlProxyClientIP;
        }

        // 5. 最后使用 getRemoteAddr()
        return request.getRemoteAddr();
    }

    private void logIpDetails(HttpServletRequest request, String realIp) {
        StringBuilder sb = new StringBuilder();
        sb.append("\n====== IP地址详细信息 ======\n");
        sb.append("请求URI: ").append(request.getRequestURI()).append("\n");
        sb.append("request.getRemoteAddr(): ").append(request.getRemoteAddr()).append("\n");
        sb.append("X-Forwarded-For: ").append(request.getHeader("X-Forwarded-For")).append("\n");
        sb.append("X-Real-IP: ").append(request.getHeader("X-Real-IP")).append("\n");
        sb.append("智能获取的真实IP: ").append(realIp).append("\n");
        sb.append("User-Agent: ").append(request.getHeader("User-Agent")).append("\n");
        sb.append("============================\n");

        log.info(sb.toString());
    }
}

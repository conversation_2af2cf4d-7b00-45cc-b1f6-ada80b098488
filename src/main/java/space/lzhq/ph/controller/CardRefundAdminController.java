package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import space.lzhq.ph.domain.CardRefund;
import space.lzhq.ph.dto.request.CardRefundApprovalDto;
import space.lzhq.ph.dto.request.CardRefundSearchForm;
import space.lzhq.ph.dto.response.CardRefundAdminListResponseDto;
import space.lzhq.ph.dto.response.CardRefundDetailResponseDto;
import space.lzhq.ph.dto.response.CardRefundStatisticsDto;
import space.lzhq.ph.dto.response.CardRefundTimelineDto;
import space.lzhq.ph.service.ICardRefundAdminService;
import space.lzhq.ph.service.ICardRefundTimelineService;

import java.util.List;

@RestController
@RequestMapping("/ph/card-refund")
public class CardRefundAdminController extends BaseController {

    private static final String PREFIX = "ph/cardRefund";

    private final ICardRefundAdminService registrationService;
    private final ICardRefundTimelineService timelineService;

    public CardRefundAdminController(
            ICardRefundAdminService registrationService,
            ICardRefundTimelineService timelineService
    ) {
        this.registrationService = registrationService;
        this.timelineService = timelineService;
    }

    /**
     * 余额清退登记管理主页面
     */
    @RequiresPermissions("ph:card-refund:view")
    @GetMapping()
    public ModelAndView cardRefund() {
        return new ModelAndView(PREFIX + "/card-refund");
    }

    /**
     * 查询余额清退登记列表
     */
    @RequiresPermissions("ph:card-refund:list")
    @PostMapping("/list")
    public TableDataInfo list(CardRefundSearchForm searchForm) {
        startPage();
        List<CardRefundAdminListResponseDto> list = registrationService.searchRegistrations(searchForm);
        return getDataTable(list);
    }

    /**
     * 导出余额清退登记列表
     */
    @RequiresPermissions("ph:card-refund:export")
    @Log(title = "余额清退登记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(CardRefundSearchForm searchForm) {
        List<CardRefund> list = registrationService.exportRegistrations(searchForm);
        ExcelUtil<CardRefund> util = new ExcelUtil<>(CardRefund.class);
        return util.exportExcel(list, "余额清退登记");
    }

    /**
     * 查看余额清退登记详情
     */
    @RequiresPermissions("ph:card-refund:query")
    @GetMapping("/detail/{id}")
    public ModelAndView detail(@PathVariable("id") String id) {
        CardRefundDetailResponseDto detail = registrationService.getAdminDetailById(id);
        ModelAndView modelAndView = new ModelAndView(PREFIX + "/detail");
        modelAndView.addObject("registration", detail);
        return modelAndView;
    }

    /**
     * 获取余额清退登记详情数据
     */
    @RequiresPermissions("ph:card-refund:query")
    @GetMapping("/detail-data/{id}")
    public AjaxResult detailData(@PathVariable("id") String id) {
        CardRefundDetailResponseDto detail = registrationService.getAdminDetailById(id);
        return success(detail);
    }

    /**
     * 审批余额清退登记
     */
    @RequiresPermissions("ph:card-refund:approve")
    @Log(title = "余额清退登记审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{id}")
    public AjaxResult approveSave(@PathVariable("id") String id,
                                  @Validated @RequestBody CardRefundApprovalDto approvalDto,
                                  BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            org.springframework.validation.FieldError fieldError = bindingResult.getFieldError();
            return error(fieldError != null ? fieldError.getDefaultMessage() : "表单验证失败");
        }

        String approverName = ShiroUtils.getSysUser().getUserName();
        registrationService.approveRegistration(id, approvalDto, approverName);
        return success("审批成功");
    }

    /**
     * 批量审批余额清退登记
     */
    @RequiresPermissions("ph:card-refund:approve")
    @Log(title = "余额清退登记批量审批", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-approve")
    public AjaxResult batchApprove(@RequestParam("ids") List<String> ids,
                                   @Validated @RequestBody CardRefundApprovalDto approvalDto,
                                   BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            org.springframework.validation.FieldError fieldError = bindingResult.getFieldError();
            return error(fieldError != null ? fieldError.getDefaultMessage() : "表单验证失败");
        }

        String approverName = ShiroUtils.getSysUser().getUserName();
        int count = registrationService.batchApproveRegistrations(ids, approvalDto, approverName);
        return success("批量审批成功，共处理 " + count + " 条记录");
    }

    /**
     * 删除余额清退登记
     */
    @RequiresPermissions("ph:card-refund:remove")
    @Log(title = "余额清退登记", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable("id") String id) {
        String operatorName = ShiroUtils.getSysUser().getUserName();
        boolean result = registrationService.deleteRegistration(id, operatorName);
        return toAjax(result);
    }

    /**
     * 批量删除余额清退登记
     */
    @RequiresPermissions("ph:card-refund:remove")
    @Log(title = "余额清退登记", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult batchRemove(@RequestParam("ids") String ids) {
        String operatorName = ShiroUtils.getSysUser().getUserName();
        String[] idArray = ids.split(",");
        int successCount = 0;

        for (String id : idArray) {
            try {
                if (registrationService.deleteRegistration(id.trim(), operatorName)) {
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("删除余额清退登记失败，ID: {}, 错误: {}", id, e.getMessage());
            }
        }

        return success("批量删除完成，成功删除 " + successCount + " 条记录");
    }

    /**
     * 获取余额清退登记统计信息
     */
    @RequiresPermissions("ph:card-refund:statistics")
    @GetMapping("/statistics")
    public AjaxResult statistics() {
        CardRefundStatisticsDto statistics = registrationService.getStatistics();
        return success(statistics);
    }

    /**
     * 获取待审批记录数量
     */
    @RequiresPermissions("ph:card-refund:list")
    @GetMapping("/pending-count")
    public AjaxResult getPendingCount() {
        CardRefundStatisticsDto statistics = registrationService.getStatistics();
        return success(statistics.getPendingApprovalCount());
    }

    /**
     * 获取状态分布统计
     */
    @RequiresPermissions("ph:card-refund:statistics")
    @GetMapping("/status-distribution")
    public AjaxResult getStatusDistribution() {
        CardRefundStatisticsDto statistics = registrationService.getStatistics();
        return success(statistics.getCountByStatus());
    }

    /**
     * 获取申报趋势数据
     */
    @RequiresPermissions("ph:card-refund:statistics")
    @GetMapping("/trend")
    public AjaxResult getTrend() {
        CardRefundStatisticsDto statistics = registrationService.getStatistics();
        return success(statistics.getDailyTrend());
    }

    /**
     * 获取申报记录时间线
     */
    @RequiresPermissions("ph:card-refund:query")
    @GetMapping("/timeline/{id}")
    public AjaxResult getTimeline(
            @PathVariable("id") String id,
            @RequestParam(required = false, defaultValue = "true") Boolean includeDetails
    ) {
        List<CardRefundTimelineDto> timeline = timelineService.getRegistrationTimeline(id, includeDetails);
        return success(timeline);
    }
    
}

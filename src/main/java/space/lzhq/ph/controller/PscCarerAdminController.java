package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.PscCarer;
import space.lzhq.ph.service.IPscCarerService;

import java.util.List;

/**
 * 陪检Controller
 *
 * <AUTHOR>
 * @date 2022-06-11
 */
@Controller
@RequestMapping("/ph/pscCarer")
public class PscCarerAdminController extends BaseController {
    private String prefix = "ph/pscCarer";

    @Autowired
    private IPscCarerService pscCarerService;

    @RequiresPermissions("ph:pscCarer:view")
    @GetMapping()
    public String pscCarer() {
        return prefix + "/pscCarer";
    }

    /**
     * 查询陪检列表
     */
    @RequiresPermissions(value = {"ph:pscCarer:list", "ph:pscOrder:assign"}, logical = Logical.OR)
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PscCarer pscCarer) {
        startPage();
        List<PscCarer> list = pscCarerService.selectPscCarerList(pscCarer);
        return getDataTable(list);
    }

    /**
     * 导出陪检列表
     */
    @RequiresPermissions("ph:pscCarer:export")
    @Log(title = "陪检", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PscCarer pscCarer) {
        List<PscCarer> list = pscCarerService.selectPscCarerList(pscCarer);
        ExcelUtil<PscCarer> util = new ExcelUtil<PscCarer>(PscCarer.class);
        return util.exportExcel(list, "pscCarer");
    }

    /**
     * 新增陪检
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存陪检
     */
    @RequiresPermissions("ph:pscCarer:add")
    @Log(title = "陪检", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(PscCarer pscCarer) {
        return toAjax(pscCarerService.insertPscCarer(pscCarer));
    }

    /**
     * 修改陪检
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        PscCarer pscCarer = pscCarerService.selectPscCarerById(id);
        mmap.put("pscCarer", pscCarer);
        return prefix + "/edit";
    }

    /**
     * 修改保存陪检
     */
    @RequiresPermissions("ph:pscCarer:edit")
    @Log(title = "陪检", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(PscCarer pscCarer) {
        return toAjax(pscCarerService.updatePscCarer(pscCarer));
    }

    /**
     * 删除陪检
     */
    @RequiresPermissions("ph:pscCarer:remove")
    @Log(title = "陪检", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(pscCarerService.deletePscCarerByIds(ids));
    }

    @PostMapping("/isEmployeeNoUnique")
    @ResponseBody
    public boolean isEmployeeNoUnique(PscCarer pscCarer) {
        return pscCarerService.isEmployeeNoUnique(pscCarer);
    }

    @PostMapping("/isMobileUnique")
    @ResponseBody
    public boolean isMobileUnique(PscCarer pscCarer) {
        return pscCarerService.isMobileNoUnique(pscCarer);
    }
}

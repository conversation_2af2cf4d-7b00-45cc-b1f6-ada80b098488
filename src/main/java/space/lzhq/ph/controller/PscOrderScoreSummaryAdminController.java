package space.lzhq.ph.controller;

import cn.hutool.core.date.DatePattern;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.PscOrderScoreSummary;
import space.lzhq.ph.service.IPscOrderScoreSummaryService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/ph/pscOrderScoreSummary")
public class PscOrderScoreSummaryAdminController extends BaseController {

    private final IPscOrderScoreSummaryService service;

    public PscOrderScoreSummaryAdminController(IPscOrderScoreSummaryService service) {
        this.service = service;
    }

    @RequiresPermissions("ph:pscOrderScoreSummary:view")
    @GetMapping
    public String page() {
        return "ph/pscOrderScoreSummary/index";
    }

    @RequiresPermissions("ph:pscOrderScoreSummary:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(
            @RequestParam("minDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam("maxDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        LocalDateTime minCreateTime = minDate.atStartOfDay();
        LocalDateTime maxCreateTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderScoreSummary> list = service.summary(minCreateTime, maxCreateTime);
        return getDataTable(list);
    }


    @RequiresPermissions("ph:pscOrderScoreSummary:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(
            @RequestParam("minDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam("maxDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        LocalDateTime minCreateTime = minDate.atStartOfDay();
        LocalDateTime maxCreateTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderScoreSummary> list = service.summary(minCreateTime, maxCreateTime);
        ExcelUtil<PscOrderScoreSummary> util = new ExcelUtil<PscOrderScoreSummary>(PscOrderScoreSummary.class);
        return util.exportEasyExcel(list, "满意度统计表");
    }

}

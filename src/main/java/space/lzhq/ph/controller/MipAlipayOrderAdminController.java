package space.lzhq.ph.controller;

import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.mospital.alipay.AlipayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.MipAlipayOrder;
import space.lzhq.ph.ext.AlipayExt;
import space.lzhq.ph.service.IMipAlipayOrderService;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Controller
@RequestMapping("/ph/mipAlipayOrder")
public class MipAlipayOrderAdminController extends BaseController {

    @Autowired
    private IMipAlipayOrderService mipAlipayOrderService;

    @RequiresPermissions("ph:mipAlipayOrder:view")
    @GetMapping()
    public String mipAlipayOrder() {
        return "ph/mipAlipayOrder/mipAlipayOrder";
    }

    @RequiresPermissions("ph:mipAlipayOrder:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(MipAlipayOrder mipAlipayOrder) {
        startPage("id desc");
        List<MipAlipayOrder> list = mipAlipayOrderService.selectMipAlipayOrderList(mipAlipayOrder);
        return getDataTable(list);
    }

    @RequiresPermissions("ph:mipAlipayOrder:queryTransaction")
    @GetMapping("/queryTransaction/{id}")
    @ResponseBody
    public AjaxResult queryTransaction(@PathVariable("id") Long id) {
        MipAlipayOrder mipAlipayOrder = mipAlipayOrderService.getById(id);
        if (mipAlipayOrder == null) {
            return error("订单不存在");
        }
        try {
            AlipayTradeQueryResponse queryOrderResponse = AlipayService.INSTANCE.queryOrder(
                    mipAlipayOrder.getOutTradeNo(),
                    "",
                    Collections.singletonList("fund_bill_list")
            );
            if (queryOrderResponse.isSuccess()) {
                mipAlipayOrder.setTradeNo(queryOrderResponse.getTradeNo());
                mipAlipayOrderService.updateById(mipAlipayOrder);
            }
            return AjaxResult.success(queryOrderResponse);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @RequiresPermissions("ph:mipAlipayOrder:refundAlipay")
    @PostMapping("/refundAlipay/{id}")
    @ResponseBody
    public AjaxResult refundAlipay(@PathVariable("id") Long id) {
        MipAlipayOrder mipAlipayOrder = mipAlipayOrderService.getById(id);
        if (mipAlipayOrder == null) {
            return error("订单不存在");
        }
        try {
            AlipayExt.INSTANCE.refundAlipay(mipAlipayOrder);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @RequiresPermissions("ph:mipAlipayOrder:queryAlipayRefund")
    @GetMapping("/queryAlipayRefund/{id}")
    @ResponseBody
    public AjaxResult queryAlipayRefund(@PathVariable("id") Long id) {
        MipAlipayOrder mipAlipayOrder = mipAlipayOrderService.getById(id);
        if (mipAlipayOrder == null) {
            return error("订单不存在");
        }
        try {
            AlipayTradeFastpayRefundQueryResponse queryRefundResponse = AlipayService.INSTANCE.queryRefund(
                    mipAlipayOrder.getAlipayOutRequestNo(),
                    mipAlipayOrder.getOutTradeNo(),
                    mipAlipayOrder.getTradeNo(),
                    Arrays.asList("refund_detail_item_list", "gmt_refund_pay", "deposit_back_info")
            );
            mipAlipayOrder.setAlipayRefundStatus(queryRefundResponse.getRefundStatus());
            mipAlipayOrderService.updateById(mipAlipayOrder);
            return AjaxResult.success(queryRefundResponse);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

}

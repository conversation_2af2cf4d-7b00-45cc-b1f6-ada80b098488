package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.Material;
import space.lzhq.ph.service.IMaterialService;

import java.util.List;

/**
 * 材料Controller
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
@Controller
@RequestMapping("/ph/material")
public class MaterialAdminController extends BaseController {
    private String prefix = "ph/material";

    @Autowired
    private IMaterialService materialService;

    @RequiresPermissions("ph:material:view")
    @GetMapping()
    public String material() {
        return prefix + "/material";
    }

    /**
     * 查询材料列表
     */
    @RequiresPermissions("ph:material:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Material material) {
        startPage();
        List<Material> list = materialService.selectMaterialList(material);
        return getDataTable(list);
    }

    /**
     * 导出材料列表
     */
    @RequiresPermissions("ph:material:export")
    @Log(title = "材料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Material material) {
        List<Material> list = materialService.selectMaterialList(material);
        ExcelUtil<Material> util = new ExcelUtil<Material>(Material.class);
        return util.exportExcel(list, "material");
    }

    /**
     * 新增材料
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存材料
     */
    @RequiresPermissions("ph:material:add")
    @Log(title = "材料", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Material material) {
        return toAjax(materialService.insertMaterial(material));
    }

    /**
     * 修改材料
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Material material = materialService.selectMaterialById(id);
        mmap.put("material", material);
        return prefix + "/edit";
    }

    /**
     * 修改保存材料
     */
    @RequiresPermissions("ph:material:edit")
    @Log(title = "材料", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Material material) {
        return toAjax(materialService.updateMaterial(material));
    }

    /**
     * 删除材料
     */
    @RequiresPermissions("ph:material:remove")
    @Log(title = "材料", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(materialService.deleteMaterialByIds(ids));
    }
}

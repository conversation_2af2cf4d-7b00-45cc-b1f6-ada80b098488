package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.Drug;
import space.lzhq.ph.service.IDrugService;

import java.util.List;

/**
 * 药品Controller
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
@Controller
@RequestMapping("/ph/drug")
public class DrugAdminController extends BaseController {
    private String prefix = "ph/drug";

    @Autowired
    private IDrugService drugService;

    @RequiresPermissions("ph:drug:view")
    @GetMapping()
    public String drug() {
        return prefix + "/drug";
    }

    /**
     * 查询药品列表
     */
    @RequiresPermissions("ph:drug:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Drug drug) {
        startPage();
        List<Drug> list = drugService.selectDrugList(drug);
        return getDataTable(list);
    }

    /**
     * 导出药品列表
     */
    @RequiresPermissions("ph:drug:export")
    @Log(title = "药品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Drug drug) {
        List<Drug> list = drugService.selectDrugList(drug);
        ExcelUtil<Drug> util = new ExcelUtil<Drug>(Drug.class);
        return util.exportExcel(list, "drug");
    }

    /**
     * 新增药品
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存药品
     */
    @RequiresPermissions("ph:drug:add")
    @Log(title = "药品", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Drug drug) {
        return toAjax(drugService.insertDrug(drug));
    }

    /**
     * 修改药品
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Drug drug = drugService.selectDrugById(id);
        mmap.put("drug", drug);
        return prefix + "/edit";
    }

    /**
     * 修改保存药品
     */
    @RequiresPermissions("ph:drug:edit")
    @Log(title = "药品", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Drug drug) {
        return toAjax(drugService.updateDrug(drug));
    }

    /**
     * 删除药品
     */
    @RequiresPermissions("ph:drug:remove")
    @Log(title = "药品", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(drugService.deleteDrugByIds(ids));
    }
}

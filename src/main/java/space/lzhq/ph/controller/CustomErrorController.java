package space.lzhq.ph.controller;

import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Controller
public class CustomErrorController implements ErrorController {

    private static final Logger log = LoggerFactory.getLogger(CustomErrorController.class);

    private static final String ERROR_PATH = "/error";

    @RequestMapping(ERROR_PATH)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleError(HttpServletRequest request) {
        // 获取HTTP状态码
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);
        HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR; // 默认500
        if (status != null) {
            try {
                httpStatus = HttpStatus.valueOf(Integer.parseInt(status.toString()));
            } catch (Exception ex) {
                // ignore
            }
        }

        // 获取原始错误信息
        String errorMessage = (String) request.getAttribute(RequestDispatcher.ERROR_MESSAGE);
        Throwable exception = (Throwable) request.getAttribute(RequestDispatcher.ERROR_EXCEPTION);
        String requestUri = (String) request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI);

        // 记录详细日志
        log.error("====== 自定义错误拦截开始 ======");
        log.error("错误状态码 (Status Code): {}", httpStatus.value());
        log.error("原始错误信息 (Error Message): {}", errorMessage);
        log.error("请求URI (Request URI): {}", requestUri);
        if (exception != null) {
            log.error("异常类型 (Exception Type): {}", exception.getClass().getName());
            log.error("异常详情 (Exception Details):", exception);
        }
        
        // 记录所有请求头，帮助诊断
        Enumeration<String> headerNames = request.getHeaderNames();
        log.info("------ 请求头信息 (Request Headers) ------");
        while(headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            log.info("{}: {}", headerName, request.getHeader(headerName));
        }
        log.info("------------------------------------------");
        log.error("====== 自定义错误拦截结束 ======");


        // 构建返回给客户端的JSON响应
        Map<String, Object> body = new HashMap<>();
        body.put("timestamp", System.currentTimeMillis());
        body.put("status", httpStatus.value());
        body.put("error", httpStatus.getReasonPhrase());
        body.put("message", "请求处理失败，服务器捕获到异常。详情请查看服务器日志。");
        body.put("path", requestUri);
        body.put("original_message", errorMessage); // 将原始信息也返回，便于调试

        return new ResponseEntity<>(body, httpStatus);
    }

}

package space.lzhq.ph.controller;

import cn.hutool.core.util.IdUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.config.ServerConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import space.lzhq.ph.domain.Doctor;
import space.lzhq.ph.service.DoctorKit;
import space.lzhq.ph.service.IDepartmentService;
import space.lzhq.ph.service.IDoctorService;

import java.util.List;

/**
 * 医生Controller
 *
 * @date 2020-05-27
 */
@Controller
@RequestMapping("/ph/doctor")
public class DoctorAdminController extends BaseController {
    private final String prefix = "ph/doctor";

    private final IDoctorService doctorService;

    private final IDepartmentService departmentService;

    private final ServerConfig serverConfig;

    @Autowired
    public DoctorAdminController(
            IDoctorService doctorService,
            IDepartmentService departmentService,
            ServerConfig serverConfig
    ) {
        this.doctorService = doctorService;
        this.departmentService = departmentService;
        this.serverConfig = serverConfig;
    }

    @RequiresPermissions("ph:doctor:view")
    @GetMapping()
    public String doctor(ModelMap attrs) {
        attrs.put("departments", departmentService.selectDepartmentAll());
        return prefix + "/doctor";
    }

    /**
     * 查询医生列表
     */
    @RequiresPermissions("ph:doctor:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Doctor doctor) {
        startPage("sort_no desc, simple_pinyin asc");
        List<Doctor> list = doctorService.selectDoctorList(doctor);
        return getDataTable(list);
    }

    /**
     * 导出医生列表
     */
    @RequiresPermissions("ph:doctor:export")
    @Log(title = "医生", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Doctor doctor) {
        List<Doctor> list = doctorService.selectDoctorList(doctor);
        ExcelUtil<Doctor> util = new ExcelUtil<Doctor>(Doctor.class);
        return util.exportExcel(list, "doctor");
    }

    /**
     * 新增医生
     */
    @GetMapping("/add")
    public String add(ModelMap attrs) {
        attrs.put("departments", departmentService.selectDepartmentAll());
        return prefix + "/add";
    }

    /**
     * 新增保存医生
     */
    @RequiresPermissions("ph:doctor:add")
    @Log(title = "医生", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("photoFile") MultipartFile photoFile, Doctor doctor) {
        try {
            if (!photoFile.isEmpty()) {
                String uploadPath = RuoYiConfig.getUploadPath();
                String photoFileName = FileUploadUtils.upload(uploadPath, photoFile);
                String photoFilePath = serverConfig.getBaseUrl() + photoFileName;
                doctor.setPhoto(photoFilePath);
            } else {
                doctor.setPhoto("");
            }
            doctor.setId(IdUtil.fastSimpleUUID());
            return toAjax(doctorService.insertDoctor(doctor));
        } catch (Exception e) {
            logger.debug(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改医生
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap attrs) {
        Doctor doctor = doctorService.selectDoctorById(id);
        attrs.put("doctor", doctor);
        attrs.put("departments", departmentService.selectDepartmentAll());
        return prefix + "/edit";
    }

    /**
     * 修改保存医生
     */
    @RequiresPermissions("ph:doctor:edit")
    @Log(title = "医生", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("photoFile") MultipartFile photoFile, Doctor doctor) {
        try {
            if (!photoFile.isEmpty()) {
                String uploadPath = RuoYiConfig.getUploadPath();
                String photoFileName = FileUploadUtils.upload(uploadPath, photoFile);
                String photoFilePath = serverConfig.getBaseUrl() + photoFileName;
                doctor.setPhoto(photoFilePath);
            }
            return toAjax(doctorService.updateDoctor(doctor));
        } catch (Exception e) {
            logger.debug(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除医生
     */
    @RequiresPermissions("ph:doctor:remove")
    @Log(title = "医生", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(doctorService.deleteDoctorByIds(ids));
    }


    @PostMapping("/sync")
    @ResponseBody
    public AjaxResult sync() {
        DoctorKit.INSTANCE.sync();
        return toAjax(true);
    }
}

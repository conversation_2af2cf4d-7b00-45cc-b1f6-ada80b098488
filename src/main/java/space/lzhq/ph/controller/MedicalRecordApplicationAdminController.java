package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import space.lzhq.ph.domain.MedicalRecordApplication;
import space.lzhq.ph.dto.request.MedicalRecordApplicationApprovalDto;
import space.lzhq.ph.dto.request.MedicalRecordApplicationSearchForm;
import space.lzhq.ph.dto.request.MedicalRecordApplicationShippingDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationAdminListResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationDetailResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationStatisticsDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationTimelineDto;
import space.lzhq.ph.service.IMedicalRecordApplicationAdminService;
import space.lzhq.ph.service.IMedicalRecordApplicationTimelineService;

import java.util.List;

@RestController
@RequestMapping("/ph/medical-record-application")
public class MedicalRecordApplicationAdminController extends BaseController {

    private static final String PREFIX = "ph/medicalRecordApplication";

    private final IMedicalRecordApplicationAdminService applicationService;
    private final IMedicalRecordApplicationTimelineService timelineService;

    public MedicalRecordApplicationAdminController(
            IMedicalRecordApplicationAdminService applicationService,
            IMedicalRecordApplicationTimelineService timelineService
    ) {
        this.applicationService = applicationService;
        this.timelineService = timelineService;
    }

    /**
     * 病历复印申请管理主页面
     */
    @RequiresPermissions("ph:medical-record-application:view")
    @GetMapping()
    public ModelAndView medicalRecordApplication() {
        return new ModelAndView(PREFIX + "/medical-record-application");
    }

    /**
     * 查询病历复印申请列表
     */
    @RequiresPermissions("ph:medical-record-application:list")
    @PostMapping("/list")
    public TableDataInfo list(MedicalRecordApplicationSearchForm searchForm) {
        startPage();
        List<MedicalRecordApplicationAdminListResponseDto> list = applicationService.searchApplications(searchForm);
        return getDataTable(list);
    }

    /**
     * 导出病历复印申请列表
     */
    @RequiresPermissions("ph:medical-record-application:export")
    @Log(title = "病历复印申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(MedicalRecordApplicationSearchForm searchForm) {
        List<MedicalRecordApplication> list = applicationService.exportApplications(searchForm);
        ExcelUtil<MedicalRecordApplication> util = new ExcelUtil<>(MedicalRecordApplication.class);
        return util.exportExcel(list, "病历复印申请");
    }

    /**
     * 查看病历复印申请详情
     */
    @RequiresPermissions("ph:medical-record-application:query")
    @GetMapping("/detail/{id}")
    public ModelAndView detail(@PathVariable("id") String id) {
        MedicalRecordApplicationDetailResponseDto detail = applicationService.getAdminDetailById(id);
        ModelAndView modelAndView = new ModelAndView(PREFIX + "/medical-record-application-detail");
        // 改为非保留变量名，避免与Thymeleaf的application变量冲突
        modelAndView.addObject("detail", detail);
        return modelAndView;
    }

    /**
     * 获取病历复印申请详情数据
     */
    @RequiresPermissions("ph:medical-record-application:query")
    @GetMapping("/detail-data/{id}")
    public AjaxResult detailData(@PathVariable("id") String id) {
        MedicalRecordApplicationDetailResponseDto detail = applicationService.getAdminDetailById(id);
        return success(detail);
    }

    /**
     * 审批病历复印申请
     */
    @RequiresPermissions("ph:medical-record-application:approve")
    @Log(title = "病历复印申请审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{id}")
    public AjaxResult approveSave(@PathVariable("id") String id,
                                  @Validated @RequestBody MedicalRecordApplicationApprovalDto approvalDto,
                                  BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            org.springframework.validation.FieldError fieldError = bindingResult.getFieldError();
            return error(fieldError != null ? fieldError.getDefaultMessage() : "表单验证失败");
        }

        String approverName = ShiroUtils.getSysUser().getUserName();
        applicationService.approveApplication(id, approvalDto, approverName);
        return success("审批成功");
    }

    /**
     * 邮寄病历复印申请
     */
    @RequiresPermissions("ph:medical-record-application:ship")
    @Log(title = "病历复印申请邮寄", businessType = BusinessType.UPDATE)
    @PostMapping("/ship/{id}")
    public AjaxResult ship(@PathVariable("id") String id,
                          @Validated @RequestBody MedicalRecordApplicationShippingDto shippingDto,
                          BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            org.springframework.validation.FieldError fieldError = bindingResult.getFieldError();
            return error(fieldError != null ? fieldError.getDefaultMessage() : "表单验证失败");
        }

        String operatorName = ShiroUtils.getSysUser().getUserName();
        applicationService.shipApplication(id, shippingDto, operatorName);
        return success("邮寄成功");
    }

    /**
     * 删除病历复印申请
     */
    @RequiresPermissions("ph:medical-record-application:remove")
    @Log(title = "病历复印申请", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable("id") String id) {
        String operatorName = ShiroUtils.getSysUser().getUserName();
        boolean result = applicationService.deleteApplication(id, operatorName);
        return toAjax(result);
    }

    /**
     * 批量删除病历复印申请
     */
    @RequiresPermissions("ph:medical-record-application:remove")
    @Log(title = "病历复印申请", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult batchRemove(@RequestParam("ids") String ids) {
        String operatorName = ShiroUtils.getSysUser().getUserName();
        String[] idArray = ids.split(",");
        int successCount = 0;

        for (String id : idArray) {
            try {
                if (applicationService.deleteApplication(id.trim(), operatorName)) {
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("删除病历复印申请失败，ID: {}, 错误: {}", id, e.getMessage());
            }
        }

        return success("批量删除完成，成功删除 " + successCount + " 条记录");
    }

    /**
     * 获取病历复印申请统计信息
     */
    @RequiresPermissions("ph:medical-record-application:statistics")
    @GetMapping("/statistics")
    public AjaxResult statistics() {
        MedicalRecordApplicationStatisticsDto statistics = applicationService.getStatistics();
        return success(statistics);
    }

    /**
     * 获取待审批记录数量
     */
    @RequiresPermissions("ph:medical-record-application:list")
    @GetMapping("/pending-count")
    public AjaxResult getPendingCount() {
        MedicalRecordApplicationStatisticsDto statistics = applicationService.getStatistics();
        return success(statistics.getPendingApprovalCount());
    }

    /**
     * 获取状态分布统计
     */
    @RequiresPermissions("ph:medical-record-application:statistics")
    @GetMapping("/status-distribution")
    public AjaxResult getStatusDistribution() {
        MedicalRecordApplicationStatisticsDto statistics = applicationService.getStatistics();
        return success(statistics.getCountByStatus());
    }

    /**
     * 获取申报趋势数据
     */
    @RequiresPermissions("ph:medical-record-application:statistics")
    @GetMapping("/trend")
    public AjaxResult getTrend() {
        MedicalRecordApplicationStatisticsDto statistics = applicationService.getStatistics();
        return success(statistics.getDailyTrend());
    }

    /**
     * 获取申报记录时间线
     */
    @RequiresPermissions("ph:medical-record-application:query")
    @GetMapping("/timeline/{id}")
    public AjaxResult getTimeline(
            @PathVariable("id") String id,
            @RequestParam(required = false, defaultValue = "true") Boolean includeDetails
    ) {
        List<MedicalRecordApplicationTimelineDto> timeline = timelineService.getApplicationTimeline(id, includeDetails);
        return success(timeline);
    }

} 
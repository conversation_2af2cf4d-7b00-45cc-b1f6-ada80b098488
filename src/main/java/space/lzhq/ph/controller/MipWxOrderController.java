package space.lzhq.ph.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult;
import com.google.common.collect.Maps;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.mospital.dongruan.yibao.DongruanYibaoService;
import org.mospital.dongruan.yibao.bean.*;
import org.mospital.wecity.mip.ClientType;
import org.mospital.wecity.mip.WeixinMipSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.MipWxOrder;
import space.lzhq.ph.ext.WeixinExt;
import space.lzhq.ph.service.IMipWxOrderService;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/ph/mipWxOrder")
public class MipWxOrderController extends BaseController {

    private final IMipWxOrderService mipWxOrderService;

    @Autowired
    public MipWxOrderController(IMipWxOrderService mipWxOrderService) {
        this.mipWxOrderService = mipWxOrderService;
    }

    @RequiresPermissions("ph:mipWxOrder:view")
    @GetMapping()
    public String mipWxOrder() {
        return "ph/mipWxOrder/mipWxOrder";
    }

    /**
     * 查询微信医保订单列表
     */
    @RequiresPermissions("ph:mipWxOrder:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(MipWxOrder mipWxOrder) {
        startPage("id desc");
        List<MipWxOrder> list = mipWxOrderService.list(new QueryWrapper<>(mipWxOrder));
        return getDataTable(list);
    }

    /**
     * 导出微信医保订单列表
     */
    @RequiresPermissions("ph:mipWxOrder:export")
    @Log(title = "微信医保订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(MipWxOrder mipWxOrder) {
        List<MipWxOrder> list = mipWxOrderService.list(new QueryWrapper<>(mipWxOrder));
        ExcelUtil<MipWxOrder> util = new ExcelUtil<>(MipWxOrder.class);
        return util.exportExcel(list, "mipWxOrder");
    }

    /**
     * 新增微信医保订单
     */
    @GetMapping("/add")
    public String add() {
        return "ph/mipWxOrder/add";
    }

    /**
     * 新增保存微信医保订单
     */
    @RequiresPermissions("ph:mipWxOrder:add")
    @Log(title = "微信医保订单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(MipWxOrder mipWxOrder) {
        return toAjax(mipWxOrderService.save(mipWxOrder));
    }

    /**
     * 修改微信医保订单
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        MipWxOrder mipWxOrder = mipWxOrderService.getById(id);
        mmap.put("mipWxOrder", mipWxOrder);
        return "ph/mipWxOrder/edit";
    }

    /**
     * 修改保存微信医保订单
     */
    @RequiresPermissions("ph:mipWxOrder:edit")
    @Log(title = "微信医保订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(MipWxOrder mipWxOrder) {
        return toAjax(mipWxOrderService.updateById(mipWxOrder));
    }

    /**
     * 删除微信医保订单
     */
    @RequiresPermissions("ph:mipWxOrder:remove")
    @Log(title = "微信医保订单", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(mipWxOrderService.removeBatchByIds(Arrays.asList(ids.split(","))));
    }

    @RequiresPermissions("ph:mipWxOrder:queryTransaction")
    @GetMapping("/queryTransaction/{id}")
    @ResponseBody
    public AjaxResult queryTransaction(@PathVariable("id") Long id) {
        MipWxOrder mipWxOrder = mipWxOrderService.getById(id);
        if (mipWxOrder == null) {
            return AjaxResult.error("订单不存在或已删除");
        }
        if (CharSequenceUtil.isBlank(mipWxOrder.getMedTransactionId())) {
            return AjaxResult.error("诊疗单ID为空，说明此单下单失败");
        }

        try {
            WeixinMipSetting weixinMipSetting = WeixinMipSetting.Companion.getInstance(mipWxOrder.getClientType());
            Response<QueryOrderResult> yibaoOrderStatusResponse = DongruanYibaoService.INSTANCE.queryOrder(
                    new QueryOrderForm(
                             mipWxOrder.getClinicNo(),
                             mipWxOrder.getPayOrderId(),
                             mipWxOrder.getPatientName(),
                             mipWxOrder.getPatientIdCardNo()
                    ),
                    ThirdType.WEIXIN,
                    new Request.ExtendParams(weixinMipSetting.getOrgAppId()),
                    mipWxOrder.getEnv()
            );

            WxInsurancePayOrderQueryResult wxInsurancePayOrderQueryResult = WeixinExt.INSTANCE.getWxInsurancePayServiceByClientType(mipWxOrder.getClientType()).queryOrder(
                    mipWxOrder.getMedTransactionId(),
                    ""
            );

            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("yibaoOrderStatusResponse", yibaoOrderStatusResponse);
            resultMap.put("wxInsurancePayOrderQueryResult", wxInsurancePayOrderQueryResult);

            return AjaxResult.success(resultMap);
        } catch (Exception e) {
            return AjaxResult.error(ExceptionUtil.stacktraceToString(e));
        }
    }

    @RequiresPermissions("ph:mipWxOrder:refundWeixin")
    @PostMapping("/refundWeixin/{id}")
    @ResponseBody
    public AjaxResult refundWeixin(@PathVariable("id") Long id) {
        MipWxOrder mipWxOrder = mipWxOrderService.getById(id);
        if (mipWxOrder == null) {
            return AjaxResult.error("订单不存在");
        }

        try {
            WxInsurancePayRefundResult refundResult = WeixinExt.INSTANCE.refundWeixin(mipWxOrder);
            return AjaxResult.success(refundResult);
        } catch (Exception e) {
            return AjaxResult.error(ExceptionUtil.stacktraceToString(e));
        }
    }

}

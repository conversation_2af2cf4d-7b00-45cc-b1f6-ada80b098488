package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.WxSubscribeMessage;
import space.lzhq.ph.service.IWxSubscribeMessageService;

import java.util.List;

/**
 * 微信订阅消息Controller
 *
 * <AUTHOR>
 * @date 2020-06-16
 */
@Controller
@RequestMapping("/ph/wxsubscribemessage")
public class WxSubscribeMessageController extends BaseController {
    private String prefix = "ph/wxsubscribemessage";

    @Autowired
    private IWxSubscribeMessageService wxSubscribeMessageService;

    @RequiresPermissions("ph:wxsubscribemessage:view")
    @GetMapping()
    public String wxsubscribemessage() {
        return prefix + "/wxsubscribemessage";
    }

    /**
     * 查询微信订阅消息列表
     */
    @RequiresPermissions("ph:wxsubscribemessage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(WxSubscribeMessage wxSubscribeMessage) {
        startPage();
        List<WxSubscribeMessage> list = wxSubscribeMessageService.selectWxSubscribeMessageList(wxSubscribeMessage);
        return getDataTable(list);
    }

    /**
     * 导出微信订阅消息列表
     */
    @RequiresPermissions("ph:wxsubscribemessage:export")
    @Log(title = "微信订阅消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(WxSubscribeMessage wxSubscribeMessage) {
        List<WxSubscribeMessage> list = wxSubscribeMessageService.selectWxSubscribeMessageList(wxSubscribeMessage);
        ExcelUtil<WxSubscribeMessage> util = new ExcelUtil<WxSubscribeMessage>(WxSubscribeMessage.class);
        return util.exportExcel(list, "wxsubscribemessage");
    }

    /**
     * 新增微信订阅消息
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存微信订阅消息
     */
    @RequiresPermissions("ph:wxsubscribemessage:add")
    @Log(title = "微信订阅消息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(WxSubscribeMessage wxSubscribeMessage) {
        return toAjax(wxSubscribeMessageService.insertWxSubscribeMessage(wxSubscribeMessage));
    }

    /**
     * 修改微信订阅消息
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        WxSubscribeMessage wxSubscribeMessage = wxSubscribeMessageService.selectWxSubscribeMessageById(id);
        mmap.put("wxSubscribeMessage", wxSubscribeMessage);
        return prefix + "/edit";
    }

    /**
     * 修改保存微信订阅消息
     */
    @RequiresPermissions("ph:wxsubscribemessage:edit")
    @Log(title = "微信订阅消息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(WxSubscribeMessage wxSubscribeMessage) {
        return toAjax(wxSubscribeMessageService.updateWxSubscribeMessage(wxSubscribeMessage));
    }

    /**
     * 删除微信订阅消息
     */
    @RequiresPermissions("ph:wxsubscribemessage:remove")
    @Log(title = "微信订阅消息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(wxSubscribeMessageService.deleteWxSubscribeMessageByIds(ids));
    }
}

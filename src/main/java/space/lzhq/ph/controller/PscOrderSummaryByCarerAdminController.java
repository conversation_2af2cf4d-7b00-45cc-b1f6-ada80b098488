package space.lzhq.ph.controller;

import cn.hutool.core.date.DatePattern;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.PscOrderSummaryByCarer;
import space.lzhq.ph.domain.PscOrderSummaryByCarerAndDepartment;
import space.lzhq.ph.service.IPscOrderSummaryByCarerService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/ph/pscOrderSummaryByCarer")
public class PscOrderSummaryByCarerAdminController extends BaseController {

    private final IPscOrderSummaryByCarerService service;

    public PscOrderSummaryByCarerAdminController(IPscOrderSummaryByCarerService service) {
        this.service = service;
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @GetMapping
    public String page() {
        return "ph/pscOrderSummaryByCarer/index";
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(
            @RequestParam("minDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam("maxDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        LocalDateTime minCreateTime = minDate.atStartOfDay();
        LocalDateTime maxCreateTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderSummaryByCarer> list = service.summary(minCreateTime, maxCreateTime);
        return getDataTable(list);
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(
            @RequestParam("minDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam("maxDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        LocalDateTime minCreateTime = minDate.atStartOfDay();
        LocalDateTime maxCreateTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderSummaryByCarer> list = service.summary(minCreateTime, maxCreateTime);
        ExcelUtil<PscOrderSummaryByCarer> util = new ExcelUtil<>(PscOrderSummaryByCarer.class);
        return util.exportEasyExcel(list, "订单统计表");
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @GetMapping("/carerPage")
    public String carerPage(
            @RequestParam("carerEmployeeNo") String carerEmployeeNo,
            @RequestParam("minDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam("maxDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate,
            ModelMap modelMap
    ) {
        modelMap.put("carerEmployeeNo", carerEmployeeNo);
        modelMap.put("minDate", minDate);
        modelMap.put("maxDate", maxDate);
        return "ph/pscOrderSummaryByCarer/carer";
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @PostMapping("/carerData")
    @ResponseBody
    public TableDataInfo carerData(
            @RequestParam("carerEmployeeNo") String carerEmployeeNo,
            @RequestParam("minDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam("maxDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        LocalDateTime minCreateTime = minDate.atStartOfDay();
        LocalDateTime maxCreateTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderSummaryByCarerAndDepartment> list = service.summaryByCarerAndDepartment(
                minCreateTime,
                maxCreateTime,
                carerEmployeeNo
        );
        return getDataTable(list);
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @PostMapping("/exportCarerData")
    @ResponseBody
    public AjaxResult export(
            @RequestParam("carerEmployeeNo") String carerEmployeeNo,
            @RequestParam("minDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam("maxDate") @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        LocalDateTime minCreateTime = minDate.atStartOfDay();
        LocalDateTime maxCreateTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderSummaryByCarerAndDepartment> list = service.summaryByCarerAndDepartment(
                minCreateTime,
                maxCreateTime,
                carerEmployeeNo
        );
        ExcelUtil<PscOrderSummaryByCarerAndDepartment> util =
                new ExcelUtil<PscOrderSummaryByCarerAndDepartment>(PscOrderSummaryByCarerAndDepartment.class);
        return util.exportEasyExcel(list, "工作量统计表");
    }

}

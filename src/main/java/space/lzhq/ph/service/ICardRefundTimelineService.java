package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import space.lzhq.ph.domain.CardRefundTimeline;
import space.lzhq.ph.dto.response.CardRefundTimelineDto;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.enums.CardRefundEventType;
import space.lzhq.ph.enums.TimelineOperatorType;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 余额清退登记时间线服务接口
 *
 * <AUTHOR>
 */
public interface ICardRefundTimelineService extends IService<CardRefundTimeline> {

    /**
     * 获取申报记录的时间线
     *
     * @param registrationId 申报记录ID
     * @param includeDetails 是否包含详细操作，false时只返回重要事件
     * @return 时间线事件列表
     */
    List<CardRefundTimelineDto> getRegistrationTimeline(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            Boolean includeDetails
    );

    /**
     * 记录时间线事件（基础版本）
     *
     * @param registrationId   申报记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     */
    void recordTimelineEvent(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotNull CardRefundEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName
    );

    /**
     * 记录状态变更时间线事件
     *
     * @param registrationId   申报记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     * @param oldStatus        变更前状态
     * @param newStatus        变更后状态
     */
    void recordStatusChangeEvent(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotNull CardRefundEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName,
            CardRefundStatus oldStatus,
            @NotNull CardRefundStatus newStatus
    );

    /**
     * 记录包含额外数据的时间线事件
     *
     * @param registrationId   申报记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     * @param additionalData   额外数据
     */
    void recordTimelineEventWithData(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotNull CardRefundEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName,
            Map<String, Serializable> additionalData
    );

    /**
     * 记录申报记录创建事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     */
    void recordRegistrationCreated(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName
    );

    /**
     * 记录申报记录提交事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     * @param oldStatus      变更前状态
     */
    void recordRegistrationSubmitted(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName,
            CardRefundStatus oldStatus
    );

    /**
     * 记录代办人信息设置事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     * @param agentName      代办人姓名
     */
    void recordAgentInfoSet(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName,
            @NotBlank String agentName
    );

    /**
     * 记录代办人信息清除事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     */
    void recordAgentInfoCleared(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName
    );

    /**
     * 银行卡附件上传事件
     *
     * @param registrationId  申报记录ID
     * @param operatorName    操作者姓名
     */
    void recordBankCardUploaded(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName
    );
} 
package space.lzhq.ph.service;

import com.github.binarywang.wxpay.bean.result.WxPayBillResult;
import space.lzhq.ph.domain.WxCheck;

import java.io.File;
import java.time.LocalDate;
import java.util.List;


public interface IWxCheckService {

    /**
     * 查询对账
     *
     * @param id 对账ID
     * @return 对账
     */
    WxCheck selectWxCheckById(Long id);

    /**
     * 根据账单日查询对账记录
     *
     * @param date 账单日
     */
    WxCheck selectWxCheckByDate(LocalDate date);

    /**
     * 根据账单日获取或创建对账记录
     *
     * @param date 账单日
     */
    WxCheck getOrCreate(LocalDate date);

    /**
     * 查询对账列表
     *
     * @param wxCheck 对账
     * @return 对账集合
     */
    List<WxCheck> selectWxCheckList(WxCheck wxCheck);

    /**
     * 新增对账
     *
     * @param wxCheck 对账
     * @return 结果
     */
    int insertWxCheck(WxCheck wxCheck);

    /**
     * 创建对账记录
     *
     * @param billDate 账单日
     */
    WxCheck create(LocalDate billDate);

    /**
     * 修改对账
     *
     * @param wxCheck 对账
     * @return 结果
     */
    int updateWxCheck(WxCheck wxCheck);

    /**
     * 批量删除对账
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWxCheckByIds(String ids);

    /**
     * 删除对账信息
     *
     * @param id 对账ID
     * @return 结果
     */
    int deleteWxCheckById(Long id);

    /**
     * 同步微信账单
     *
     * @param billDate        账单日
     * @param wxPayBillResult 微信账单
     */
    boolean syncWxBill(LocalDate billDate, WxPayBillResult wxPayBillResult);

    /**
     * 同步微信医保账单
     *
     * @param billDate 账单日
     * @param billFile 微信医保账单文件
     */
    boolean syncWxMipBill(LocalDate billDate, File billFile);

}

package space.lzhq.ph.service;

import space.lzhq.ph.domain.OutpatientPaymentRecord;

import java.util.List;

/**
 * 门诊缴费记录Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IOutpatientPaymentRecordService {
    /**
     * 查询门诊缴费记录
     *
     * @param id 门诊缴费记录ID
     * @return 门诊缴费记录
     */
    OutpatientPaymentRecord selectOutpatientPaymentRecordById(Long id);

    /**
     * 查询门诊缴费记录列表
     *
     * @param outpatientPaymentRecord 门诊缴费记录
     * @return 门诊缴费记录集合
     */
    List<OutpatientPaymentRecord> selectOutpatientPaymentRecordList(OutpatientPaymentRecord outpatientPaymentRecord);

    /**
     * 根据患者卡号查询门诊缴费记录列表
     *
     * @param patientCardNo 患者卡号
     * @return 门诊缴费记录集合
     */
    List<OutpatientPaymentRecord> selectOutpatientPaymentRecordListByPatientCardNo(String patientCardNo);

    /**
     * 新增门诊缴费记录
     *
     * @param outpatientPaymentRecord 门诊缴费记录
     * @return 结果
     */
    int insertOutpatientPaymentRecord(OutpatientPaymentRecord outpatientPaymentRecord);

    /**
     * 修改门诊缴费记录
     *
     * @param outpatientPaymentRecord 门诊缴费记录
     * @return 结果
     */
    int updateOutpatientPaymentRecord(OutpatientPaymentRecord outpatientPaymentRecord);

    // 缴费记录作为重要审计数据，不提供删除功能
    // 如需删除请联系系统管理员手动处理
} 
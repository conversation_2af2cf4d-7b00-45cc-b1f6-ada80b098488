package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import space.lzhq.ph.domain.CardRefund;
import space.lzhq.ph.dto.request.CardRefundApprovalDto;
import space.lzhq.ph.dto.request.CardRefundSearchForm;
import space.lzhq.ph.dto.response.CardRefundAdminListResponseDto;
import space.lzhq.ph.dto.response.CardRefundDetailResponseDto;
import space.lzhq.ph.dto.response.CardRefundStatisticsDto;

import java.util.List;

public interface ICardRefundAdminService extends IService<CardRefund> {

    /**
     * 查询余额清退登记列表（管理端）
     * <p>
     * 此方法用于管理端查询余额清退登记列表。执行以下操作：
     * <ul>
     *   <li>根据搜索条件查询申报记录</li>
     *   <li>对敏感信息进行脱敏处理（身份证号、手机号等）</li>
     *   <li>关联病种和枚举信息</li>
     *   <li>计算附件数量和审批权限</li>
     * </ul>
     *
     * @param searchForm 搜索条件表单，可以为null表示查询所有记录
     * @return 申报记录管理端列表，按创建时间倒序排列
     * @since 1.0.0
     */
    List<CardRefundAdminListResponseDto> searchRegistrations(
            CardRefundSearchForm searchForm
    );

    /**
     * 根据ID获取余额清退登记详情（管理端）
     * <p>
     * 此方法用于管理端获取指定ID的余额清退登记详细信息。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>返回完整的申报记录详情（无权限限制）</li>
     *   <li>包含所有附件信息</li>
     * </ul>
     *
     * @param id 申报记录ID，不能为空，长度必须为32个字符
     * @return 申报记录详情，若记录不存在则抛出异常
     * @throws space.lzhq.ph.exception.ResourceNotFoundException 当申报记录不存在时
     * @since 1.0.0
     */
    CardRefundDetailResponseDto getAdminDetailById(
            @NotBlank @Size(min = 32, max = 32) String id
    );

    /**
     * 审批余额清退登记
     * <p>
     * 此方法用于管理员审批余额清退登记。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录状态是否允许审批（SUBMITTED或RESUBMITTED状态）</li>
     *   <li>验证审批信息的有效性</li>
     *   <li>更新申报状态和审批信息</li>
     *   <li>设置审批时间</li>
     *   <li>记录审批日志</li>
     * </ul>
     *
     * @param id           申报记录ID，不能为空，长度必须为32个字符
     * @param approvalDto  审批信息DTO，包含审批状态和相关说明，不能为空且必须通过验证
     * @param approverName 审批人姓名，用于记录审批日志，不能为空
     * @return 更新后的申报记录实体
     * @throws space.lzhq.ph.exception.ResourceNotFoundException   当申报记录不存在时
     * @throws space.lzhq.ph.exception.ApprovalNotAllowedException 当申报记录状态不允许审批时
     * @throws space.lzhq.ph.exception.InvalidApprovalException    当审批信息无效时（如驳回时未提供驳回原因）
     * @throws RuntimeException                                    当数据库操作失败时
     * @since 1.0.0
     */
    CardRefund approveRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid CardRefundApprovalDto approvalDto,
            @NotBlank @Size(min = 1, max = 50) String approverName
    );

    /**
     * 批量审批余额清退登记
     * <p>
     * 此方法用于管理员批量审批多个余额清退登记。执行以下操作：
     * <ul>
     *   <li>验证所有申报记录是否存在</li>
     *   <li>验证所有申报记录状态是否允许审批</li>
     *   <li>对所有记录应用相同的审批结果</li>
     *   <li>记录批量审批日志</li>
     * </ul>
     *
     * @param ids          申报记录ID列表，不能为空，每个ID长度必须为32个字符
     * @param approvalDto  审批信息DTO，包含审批状态和相关说明，不能为空且必须通过验证
     * @param approverName 审批人姓名，用于记录审批日志，不能为空
     * @return 批量审批成功的记录数量
     * @throws space.lzhq.ph.exception.ResourceNotFoundException   当任何一个申报记录不存在时
     * @throws space.lzhq.ph.exception.ApprovalNotAllowedException 当任何一个申报记录状态不允许审批时
     * @throws space.lzhq.ph.exception.InvalidApprovalException    当审批信息无效时
     * @throws RuntimeException                                    当数据库操作失败时
     * @since 1.0.0
     */
    int batchApproveRegistrations(
            @NotNull @Size(min = 1) List<@NotBlank @Size(min = 32, max = 32) String> ids,
            @NotNull @Valid CardRefundApprovalDto approvalDto,
            @NotBlank @Size(min = 1, max = 50) String approverName
    );

    /**
     * 导出余额清退登记
     * <p>
     * 此方法用于管理端导出余额清退登记数据。执行以下操作：
     * <ul>
     *   <li>根据搜索条件查询申报记录</li>
     *   <li>返回适合导出的记录列表（包含完整信息）</li>
     *   <li>敏感信息保持原始格式（用于内部使用）</li>
     * </ul>
     *
     * @param searchForm 搜索条件表单，可以为null表示导出所有记录
     * @return 申报记录列表，适合导出Excel等格式
     * @since 1.0.0
     */
    List<CardRefund> exportRegistrations(
            CardRefundSearchForm searchForm
    );

    /**
     * 删除余额清退登记
     * <p>
     * 此方法用于管理员删除余额清退登记。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录状态是否允许删除（通常只有草稿状态允许删除）</li>
     *   <li>软删除申报记录</li>
     *   <li>记录删除日志</li>
     * </ul>
     *
     * @param id           申报记录ID，不能为空，长度必须为32个字符
     * @param operatorName 操作人姓名，用于记录操作日志，不能为空
     * @return 删除成功返回true，否则返回false
     * @throws space.lzhq.ph.exception.ResourceNotFoundException 当申报记录不存在时
     * @throws space.lzhq.ph.exception.DeleteNotAllowedException 当申报记录状态不允许删除时
     * @throws RuntimeException                                  当数据库操作失败时
     * @since 1.0.0
     */
    boolean deleteRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 1, max = 50) String operatorName
    );

    /**
     * 获取余额清退申报统计信息
     * <p>
     * 此方法用于管理端获取余额清退申报的各项统计数据。执行以下操作：
     * <ul>
     *   <li>统计各状态下的申报记录数量</li>
     *   <li>统计时间维度的申报趋势</li>
     *   <li>计算待审批数量</li>
     * </ul>
     *
     * @return 申报统计信息DTO，包含各项统计数据
     * @since 1.0.0
     */
    CardRefundStatisticsDto getStatistics();
}

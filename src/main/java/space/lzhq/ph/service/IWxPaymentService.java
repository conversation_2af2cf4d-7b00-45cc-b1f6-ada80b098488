package space.lzhq.ph.service;

import org.mospital.dongruan.pay.UnifiedPayOrderQueryResult;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.MedicalRecordApplication;
import space.lzhq.ph.domain.Patient;
import space.lzhq.ph.domain.WxPayment;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 充值记录Service接口
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
public interface IWxPaymentService {
    /**
     * 查询充值记录
     *
     * @param id 充值记录ID
     * @return 充值记录
     */
    WxPayment selectWxPaymentById(Long id);


    /**
     * 查找充值记录
     *
     * @param zyPayNo 掌医订单号
     */
    WxPayment selectWxPaymentByZyPayNo(String zyPayNo);

    /**
     * 查询充值记录列表
     *
     * @param wxPayment 充值记录
     * @return 充值记录集合
     */
    List<WxPayment> selectWxPaymentList(WxPayment wxPayment);

    /**
     * 查询可退款的充值订单
     *
     * @param cardNo        虚拟卡号
     * @param minCreateTime 最小下单时间
     * @param maxCreateTime 最大下单时间
     */
    List<WxPayment> selectListForRefund(String cardNo, Date minCreateTime, Date maxCreateTime);

    Long sumAmount(WxPayment wxPayment);

    /**
     * 新增充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    int insertWxPayment(WxPayment wxPayment);

    /**
     * 修改充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    int updateWxPayment(WxPayment wxPayment);

    /**
     * 批量删除充值记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWxPaymentByIds(String ids);

    /**
     * 删除充值记录信息
     *
     * @param id 充值记录ID
     * @return 结果
     */
    int deleteWxPaymentById(Long id);

    /**
     * 创建并保存
     *
     * @param serviceType     服务类型
     * @param patient         门诊患者
     * @param outTradeNo      掌医订单号
     * @param totalFee        订单金额
     * @param prescriptionNos 处方号
     */
    boolean createAndSave(ServiceType serviceType, Patient patient, String outTradeNo, Long totalFee, String prescriptionNos);

    boolean createAndSave(MedicalRecordApplication application, Integer clientType);

    /**
     * 接收到支付通知时，更新充值记录
     *
     * @param wxPayment                  充值记录
     * @param unifiedPayOrderQueryResult 支付通知
     */
    boolean updateOnPay(WxPayment wxPayment, UnifiedPayOrderQueryResult unifiedPayOrderQueryResult);

    /**
     * 接收到支付通知时，更新HIS充值记录
     *
     * @param wxPayment 充值记录
     * @param serialNo  His支付流水号
     * @param invoiceNo 发票号
     */
    boolean updateHisRechargeStatusOnPay(WxPayment wxPayment, String serialNo, String invoiceNo);


    /**
     * 接收到支付通知时，更新HIS缴费状态
     *
     * @param wxPayment 充值记录
     */
    boolean updateHisPayStatusOnPay(WxPayment wxPayment);

    /**
     * HIS退款后，更新充值记录
     *
     * @param wxPayment    充值记录
     * @param refundAmount 退款金额，以分为单位
     */
    boolean updateOnRefund(WxPayment wxPayment, Long refundAmount);

    /**
     * 计算指定日期的微信支付净流入金额，以分为单位
     */
    Long calculateNetInAmount(LocalDate date);

    /**
     * 使用乐观锁更新支付记录
     * @param wxPayment 支付记录（必须包含当前版本号）
     * @return 更新成功返回true，版本冲突返回false
     */
    boolean updateWithOptimisticLock(WxPayment wxPayment);
    
    /**
     * 乐观锁方式更新支付状态
     * @param wxPayment 支付记录（必须包含当前版本号）
     * @param unifiedPayOrderQueryResult 查询结果
     * @return 更新成功返回true，版本冲突返回false
     */
    boolean updateOnPayWithOptimisticLock(WxPayment wxPayment, UnifiedPayOrderQueryResult unifiedPayOrderQueryResult);

}

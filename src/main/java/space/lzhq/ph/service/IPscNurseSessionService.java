package space.lzhq.ph.service;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.PscNurseSession;

import java.util.List;

/**
 * 护士会话Service接口
 *
 * <AUTHOR>
 * @date 2022-06-12
 */
public interface IPscNurseSessionService {
    /**
     * 查询护士会话
     *
     * @param id 护士会话ID
     * @return 护士会话
     */
    PscNurseSession selectPscNurseSessionById(Long id);

    /**
     * 查询护士会话
     *
     * @param employeeNo 工号
     * @param clientType 客户端类型
     * @return 护士会话
     */
    PscNurseSession selectPscNurseSessionByEmployeeNoAndClientType(
            @Param("employeeNo") String employeeNo,
            @Param("clientType") Integer clientType
    );

    /**
     * 查询护士会话
     *
     * @param openId openId
     * @return 护士会话
     */
    PscNurseSession selectPscNurseSessionByOpenId(@Param("openId") String openId);

    /**
     * 查询护士会话列表
     *
     * @param pscNurseSession 护士会话
     * @return 护士会话集合
     */
    List<PscNurseSession> selectPscNurseSessionList(PscNurseSession pscNurseSession);

    /**
     * 是否存在指定工号和客户端类型的护士会话
     *
     * @param employeeNo 工号
     * @param clientType 客户端类型
     * @return 是否存在
     */
    boolean existsByEmployeeNoAndClientType(
            @Param("employeeNo") String employeeNo,
            @Param("clientType") Integer clientType
    );

    /**
     * 新增护士会话
     *
     * @param pscNurseSession 护士会话
     * @return 结果
     */
    int insertPscNurseSession(PscNurseSession pscNurseSession);

    /**
     * 修改护士会话
     *
     * @param pscNurseSession 护士会话
     * @return 结果
     */
    int updatePscNurseSession(PscNurseSession pscNurseSession);

    /**
     * 批量删除护士会话
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePscNurseSessionByIds(String ids);

    /**
     * 删除护士会话信息
     *
     * @param id 护士会话ID
     * @return 结果
     */
    int deletePscNurseSessionById(Long id);

    /**
     * 删除护士会话
     *
     * @param openId openId
     * @return 删除成功的记录数
     */
    int deletePscNurseSessionByOpenId(@Param("openId") String openId);
}

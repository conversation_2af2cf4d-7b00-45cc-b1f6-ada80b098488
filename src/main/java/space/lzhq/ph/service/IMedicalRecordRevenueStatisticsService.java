package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotNull;
import space.lzhq.ph.domain.MedicalRecordRevenueStatistics;
import space.lzhq.ph.dto.response.MedicalRecordRevenueOverviewDto;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 病历邮寄业务收入统计Service接口
 */
public interface IMedicalRecordRevenueStatisticsService extends IService<MedicalRecordRevenueStatistics> {

    /**
     * 统计并保存指定日期的收入数据
     *
     * @param transactionDate 交易日期
     * @return 是否成功
     */
    boolean calculateAndSaveDailyRevenue(@NotNull(message = "交易日期不能为空") LocalDate transactionDate);

    /**
     * 统计并保存前一天的收入数据
     *
     * @return 是否成功
     */
    default boolean calculateAndSaveYesterdayRevenue() {
        return calculateAndSaveDailyRevenue(LocalDate.now(ZoneId.of("GMT+8")).minusDays(1));
    }

    /**
     * 查询指定日期范围内的收入统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<MedicalRecordRevenueStatistics> selectListByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 异步批量统计并保存指定日期范围的收入数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 异步返回处理结果
     */
    CompletableFuture<Map<String, Object>> calculateRevenueRangeAsync(LocalDate startDate, LocalDate endDate);

    /**
     * 批量统计并保存指定日期范围的收入数据（同步处理）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 处理结果
     */
    Map<String, Object> calculateRevenueRange(LocalDate startDate, LocalDate endDate);

    /**
     * 获取收入统计概览数据（数据库聚合）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 收入概览统计结果
     */
    MedicalRecordRevenueOverviewDto getOverviewByDateRange(LocalDate startDate, LocalDate endDate);

}

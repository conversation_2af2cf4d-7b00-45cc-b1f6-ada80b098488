package space.lzhq.ph.service;

import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import space.lzhq.ph.domain.AlipayRefund;

import java.util.List;

/**
 * 支付宝退款记录Service接口
 *
 * <AUTHOR>
 * @date 2022-09-10
 */
public interface IAlipayRefundService {
    /**
     * 查询支付宝退款记录
     *
     * @param id 支付宝退款记录ID
     * @return 支付宝退款记录
     */
    AlipayRefund selectAlipayRefundById(Long id);

    /**
     * 查询支付宝退款记录列表
     *
     * @param alipayRefund 支付宝退款记录
     * @return 支付宝退款记录集合
     */
    List<AlipayRefund> selectAlipayRefundList(AlipayRefund alipayRefund);

    /**
     * 新增支付宝退款记录
     *
     * @param alipayRefund 支付宝退款记录
     * @return 结果
     */
    int insertAlipayRefund(AlipayRefund alipayRefund);

    /**
     * 修改支付宝退款记录
     *
     * @param alipayRefund 支付宝退款记录
     * @return 结果
     */
    int updateAlipayRefund(AlipayRefund alipayRefund);

    /**
     * 批量删除支付宝退款记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteAlipayRefundByIds(String ids);

    /**
     * 删除支付宝退款记录信息
     *
     * @param id 支付宝退款记录ID
     * @return 结果
     */
    int deleteAlipayRefundById(Long id);

    boolean updateOnAlipayRefund(AlipayRefund refund, AlipayTradeRefundResponse refundResponse);

    boolean updateOnAlipayRefund(AlipayRefund refund, AlipayTradeFastpayRefundQueryResponse queryRefundResponse);

    void refundAlipay(AlipayRefund refund);

    Long sumAmount(AlipayRefund refund);
}

package space.lzhq.ph.service;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.Drug;

import java.util.List;

/**
 * 药品Service接口
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
public interface IDrugService {
    /**
     * 查询药品
     *
     * @param id 药品ID
     * @return 药品
     */
    Drug selectDrugById(String id);

    /**
     * 查询药品列表
     *
     * @param drug 药品
     * @return 药品集合
     */
    List<Drug> selectDrugList(Drug drug);

    /**
     * 新增药品
     *
     * @param drug 药品
     * @return 结果
     */
    int insertDrug(Drug drug);

    /**
     * 批量新增药品
     *
     * @param drugs 药品
     * @return 结果
     */
    int insertDrugs(@Param("drugs") List<Drug> drugs);
    
    /**
     * 修改药品
     *
     * @param drug 药品
     * @return 结果
     */
    int updateDrug(Drug drug);

    /**
     * 批量删除药品
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteDrugByIds(String ids);

    /**
     * 删除药品信息
     *
     * @param id 药品ID
     * @return 结果
     */
    int deleteDrugById(String id);

    /**
     * 删除所有药品
     */
    void clearAll();
}

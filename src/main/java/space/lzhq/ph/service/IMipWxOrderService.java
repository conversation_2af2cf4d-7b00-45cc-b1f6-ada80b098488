package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult;
import space.lzhq.ph.domain.MipWxOrder;

/**
 * 微信医保订单Service接口
 *
 * <AUTHOR>
 * @date 2022-08-11
 */
public interface IMipWxOrderService extends IService<MipWxOrder> {

    MipWxOrder getOneByPayOrderId(String payOrderId);

    MipWxOrder getOneByOutTradeNo(String outTradeNo);

    boolean updateOnRefund(MipWxOrder mipWxOrder, WxInsurancePayRefundResult refundResult);
}

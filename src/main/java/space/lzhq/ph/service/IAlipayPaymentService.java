package space.lzhq.ph.service;

import com.alipay.api.response.AlipayTradeQueryResponse;
import space.lzhq.ph.domain.AlipayPayment;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 支付宝支付Service接口
 *
 * <AUTHOR>
 * @date 2022-09-09
 */
public interface IAlipayPaymentService {
    /**
     * 查询支付宝支付
     *
     * @param id 支付宝支付ID
     * @return 支付宝支付
     */
    AlipayPayment selectAlipayPaymentById(Long id);

    AlipayPayment selectAlipayPaymentByOutTradeNo(String outTradeNo);

    List<AlipayPayment> selectListForRefund(String cardNo, Date minCreateTime, Date maxCreateTime);

    /**
     * 查询支付宝支付列表
     *
     * @param alipayPayment 支付宝支付
     * @return 支付宝支付集合
     */
    List<AlipayPayment> selectAlipayPaymentList(AlipayPayment alipayPayment);

    /**
     * 新增支付宝支付
     *
     * @param alipayPayment 支付宝支付
     * @return 结果
     */
    int insertAlipayPayment(AlipayPayment alipayPayment);

    /**
     * 修改支付宝支付
     *
     * @param alipayPayment 支付宝支付
     * @return 结果
     */
    int updateAlipayPayment(AlipayPayment alipayPayment);

    /**
     * 批量删除支付宝支付
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteAlipayPaymentByIds(String ids);

    /**
     * 删除支付宝支付信息
     *
     * @param id 支付宝支付ID
     * @return 结果
     */
    int deleteAlipayPaymentById(Long id);

    boolean updateOnPay(AlipayPayment payment, AlipayTradeQueryResponse queryOrderResponse);

    Long sumAmount(AlipayPayment payment);

    /**
     * 计算指定日期的支付宝净流入金额，以分为单位
     */
    Long calculateNetInAmount(LocalDate date);

}

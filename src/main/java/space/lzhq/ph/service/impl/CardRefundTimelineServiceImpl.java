package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.CardRefundTimeline;
import space.lzhq.ph.dto.response.CardRefundTimelineDto;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.enums.CardRefundEventType;
import space.lzhq.ph.enums.TimelineOperatorType;
import space.lzhq.ph.exception.TimelineRecordException;
import space.lzhq.ph.mapper.CardRefundTimelineMapper;
import space.lzhq.ph.service.ICardRefundTimelineService;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 余额清退登记时间线服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class CardRefundTimelineServiceImpl
        extends ServiceImpl<CardRefundTimelineMapper, CardRefundTimeline>
        implements ICardRefundTimelineService {

    /**
     * 获取当前代理对象，用于调用事务方法
     */
    private ICardRefundTimelineService getSelf() {
        return (ICardRefundTimelineService) AopContext.currentProxy();
    }

    @Override
    public List<CardRefundTimelineDto> getRegistrationTimeline(String registrationId, Boolean includeDetails) {
        List<CardRefundTimeline> timelines =
                getBaseMapper().selectTimelineByRegistrationId(registrationId, includeDetails);

        return timelines.stream()
                .map(this::convertToDto)
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTimelineEvent(String registrationId, CardRefundEventType eventType,
                                    String eventDescription, TimelineOperatorType operatorType,
                                    String operatorName) {
        CardRefundTimeline timeline = new CardRefundTimeline(
                registrationId, eventType, eventDescription, operatorType, operatorName);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录时间线事件失败，申报ID: {}, 事件类型: {}, 错误信息: {}",
                    registrationId, eventType, e.getMessage(), e);
            throw new TimelineRecordException("记录时间线事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordStatusChangeEvent(String registrationId, CardRefundEventType eventType,
                                        String eventDescription, TimelineOperatorType operatorType,
                                        String operatorName, CardRefundStatus oldStatus,
                                        CardRefundStatus newStatus) {
        CardRefundTimeline timeline = new CardRefundTimeline(
                registrationId, eventType, eventDescription, operatorType, operatorName, oldStatus, newStatus);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录状态变更事件失败，申报ID: {}, 事件类型: {}, 状态变更: {} -> {}, 错误信息: {}",
                    registrationId, eventType, oldStatus, newStatus, e.getMessage(), e);
            throw new TimelineRecordException("记录状态变更事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTimelineEventWithData(String registrationId, CardRefundEventType eventType,
                                            String eventDescription, TimelineOperatorType operatorType,
                                            String operatorName, Map<String, Serializable> additionalData) {
        CardRefundTimeline timeline = new CardRefundTimeline(
                registrationId, eventType, eventDescription, operatorType, operatorName, additionalData);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录带数据的时间线事件失败，申报ID: {}, 事件类型: {}, 附加数据: {}, 错误信息: {}",
                    registrationId, eventType, additionalData, e.getMessage(), e);
            throw new TimelineRecordException("记录时间线事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void recordRegistrationCreated(String registrationId, String operatorName) {
        getSelf().recordTimelineEvent(registrationId, CardRefundEventType.REGISTRATION_CREATED,
                "创建余额清退登记", TimelineOperatorType.APPLICANT, operatorName);
    }

    @Override
    public void recordRegistrationSubmitted(String registrationId, String operatorName,
                                            CardRefundStatus oldStatus) {
        getSelf().recordStatusChangeEvent(registrationId, CardRefundEventType.REGISTRATION_SUBMITTED,
                "提交余额清退登记", TimelineOperatorType.APPLICANT, operatorName,
                oldStatus, CardRefundStatus.SUBMITTED);
    }

    @Override
    public void recordAgentInfoSet(String registrationId, String operatorName, String agentName) {
        Map<String, Serializable> data = new HashMap<>();
        data.put("agentName", agentName);

        getSelf().recordTimelineEventWithData(registrationId, CardRefundEventType.AGENT_INFO_SET,
                "设置代办人信息：" + agentName, TimelineOperatorType.APPLICANT,
                operatorName, data);
    }

    @Override
    public void recordAgentInfoCleared(String registrationId, String operatorName) {
        getSelf().recordTimelineEvent(registrationId, CardRefundEventType.AGENT_INFO_CLEARED,
                "清除代办人信息", TimelineOperatorType.APPLICANT, operatorName);
    }

    @Override
    public void recordBankCardUploaded(String registrationId, String operatorName) {
        getSelf().recordTimelineEvent(registrationId, CardRefundEventType.BANK_CARD_UPLOADED,
                "上传银行卡附件",
                TimelineOperatorType.APPLICANT, operatorName);
    }

    /**
     * 将时间线实体转换为DTO
     */
    private CardRefundTimelineDto convertToDto(CardRefundTimeline timeline) {
        CardRefundTimelineDto dto = new CardRefundTimelineDto();
        dto.setId(timeline.getId());
        dto.setEventType(timeline.getEventType());
        dto.setEventDescription(timeline.getEventDescription());
        dto.setOperatorType(timeline.getOperatorType());
        dto.setOperatorName(timeline.getOperatorName());
        dto.setOldStatus(timeline.getOldStatus());
        dto.setNewStatus(timeline.getNewStatus());
        dto.setEventTime(timeline.getEventTime());
        dto.setAdditionalData(timeline.getAdditionalData());

        // 设置图标和颜色
        dto.setIconAndColor();

        return dto;
    }
}
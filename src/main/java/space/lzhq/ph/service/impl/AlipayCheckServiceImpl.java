package space.lzhq.ph.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.ruoyi.common.core.text.Convert;
import org.mospital.alipay.AlipayBillItem;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.AlipayCheck;
import space.lzhq.ph.mapper.AlipayCheckMapper;
import space.lzhq.ph.service.IAlipayCheckService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付宝对账Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class AlipayCheckServiceImpl implements IAlipayCheckService {

    private final AlipayCheckMapper alipayCheckMapper;

    public AlipayCheckServiceImpl(AlipayCheckMapper alipayCheckMapper) {
        this.alipayCheckMapper = alipayCheckMapper;
    }

    /**
     * 查询支付宝对账
     *
     * @param id 支付宝对账主键
     * @return 支付宝对账
     */
    @Override
    public AlipayCheck selectAlipayCheckById(Long id) {
        return alipayCheckMapper.selectAlipayCheckById(id);
    }

    /**
     * 查询支付宝对账列表
     *
     * @param alipayCheck 支付宝对账
     * @return 支付宝对账
     */
    @Override
    public List<AlipayCheck> selectAlipayCheckList(AlipayCheck alipayCheck) {
        return alipayCheckMapper.selectAlipayCheckList(alipayCheck);
    }

    /**
     * 新增支付宝对账
     *
     * @param alipayCheck 支付宝对账
     * @return 结果
     */
    @Override
    public int insertAlipayCheck(AlipayCheck alipayCheck) {
        return alipayCheckMapper.insertAlipayCheck(alipayCheck);
    }

    /**
     * 修改支付宝对账
     *
     * @param alipayCheck 支付宝对账
     * @return 结果
     */
    @Override
    public int updateAlipayCheck(AlipayCheck alipayCheck) {
        return alipayCheckMapper.updateAlipayCheck(alipayCheck);
    }

    /**
     * 批量删除支付宝对账
     *
     * @param ids 需要删除的支付宝对账主键
     * @return 结果
     */
    @Override
    public int deleteAlipayCheckByIds(String ids) {
        return alipayCheckMapper.deleteAlipayCheckByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除支付宝对账信息
     *
     * @param id 支付宝对账主键
     * @return 结果
     */
    @Override
    public int deleteAlipayCheckById(Long id) {
        return alipayCheckMapper.deleteAlipayCheckById(id);
    }

    @Override
    public AlipayCheck getOrCreate(Long id) {
        AlipayCheck alipayCheck = selectAlipayCheckById(id);
        if (alipayCheck == null) {
            alipayCheck = new AlipayCheck();
            alipayCheck.setId(id);
            insertAlipayCheck(alipayCheck);
        }
        return alipayCheck;
    }

    @Override
    public boolean syncAlipayBill(Long id, List<AlipayBillItem> alipayBillItems) {
        AlipayCheck alipayCheck = getOrCreate(id);
        alipayCheck.setAlipayPayAmount(
                alipayBillItems.stream()
                        .filter(it -> it.isPay() && (it.isMenzhen() || it.isZhyuan()))
                        .map(AlipayBillItem::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        alipayCheck.setAlipayMzPayAmount(
                alipayBillItems.stream()
                        .filter(it -> it.isPay() && it.isMenzhen())
                        .map(AlipayBillItem::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        alipayCheck.setAlipayZyPayAmount(
                alipayBillItems.stream()
                        .filter(it -> it.isPay() && it.isZhyuan())
                        .map(AlipayBillItem::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        alipayCheck.setAlipayRefundAmount(
                alipayBillItems.stream()
                        .filter(it -> it.isRefund() && (it.isMenzhen() || it.isZhyuan()))
                        .map(AlipayBillItem::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .negate()
        );
        alipayCheck.setAlipayMzRefundAmount(
                alipayBillItems.stream()
                        .filter(it -> it.isRefund() && it.isMenzhen())
                        .map(AlipayBillItem::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .negate()
        );
        alipayCheck.setAlipayZyRefundAmount(
                alipayBillItems.stream()
                        .filter(it -> it.isRefund() && it.isZhyuan())
                        .map(AlipayBillItem::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .negate()
        );
        alipayCheck.setAlipayNetIn(alipayCheck.getAlipayPayAmount().subtract(alipayCheck.getAlipayRefundAmount()));
        alipayCheck.setDiffPayAmount(
                NumberUtil.sub(
                        alipayCheck.getAlipayPayAmount(),
                        alipayCheck.getHisPayAmount() == null ? BigDecimal.ZERO : alipayCheck.getHisPayAmount()
                )
        );
        alipayCheck.setDiffRefundAmount(
                NumberUtil.sub(
                        alipayCheck.getAlipayRefundAmount(),
                        alipayCheck.getHisRefundAmount() == null ? BigDecimal.ZERO : alipayCheck.getHisRefundAmount()
                )
        );
        alipayCheck.setPayBalanced(alipayCheck.getDiffPayAmount().compareTo(BigDecimal.ZERO) == 0 ? 1 : 0);
        alipayCheck.setRefundBalanced(alipayCheck.getDiffRefundAmount().compareTo(BigDecimal.ZERO) == 0 ? 1 : 0);
        alipayCheck.setMipPayAmount(
                alipayBillItems.stream()
                        .filter(it -> it.isPay() && it.isYibao())
                        .map(AlipayBillItem::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        alipayCheck.setMipRefundAmount(
                alipayBillItems.stream()
                        .filter(it -> it.isRefund() && it.isYibao())
                        .map(AlipayBillItem::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .negate()
        );
        alipayCheck.setMipNetIn(alipayCheck.getMipPayAmount().subtract(alipayCheck.getMipRefundAmount()));
        alipayCheck.setTotalNetIn(alipayCheck.getAlipayNetIn().add(alipayCheck.getMipNetIn()));

        return updateAlipayCheck(alipayCheck) == 1;
    }

}

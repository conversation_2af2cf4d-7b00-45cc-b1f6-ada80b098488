package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.MedicalRecordApplication;
import space.lzhq.ph.domain.MedicalRecordApplicationStatusEvent;
import space.lzhq.ph.dto.request.MedicalRecordApplicationApprovalDto;
import space.lzhq.ph.dto.request.MedicalRecordApplicationSearchForm;
import space.lzhq.ph.dto.request.MedicalRecordApplicationShippingDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationAdminListResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationDetailResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationStatisticsDto;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.exception.DataUpdateFailedException;
import space.lzhq.ph.exception.EditNotAllowedException;
import space.lzhq.ph.exception.ResourceNotFoundException;
import space.lzhq.ph.mapper.MedicalRecordApplicationMapper;
import space.lzhq.ph.service.IMedicalRecordApplicationAdminService;
import space.lzhq.ph.service.IMedicalRecordApplicationService;
import space.lzhq.ph.service.IMedicalRecordApplicationTimelineService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 病历复印申请后台管理服务实现类
 */
@Service
@Slf4j
@Validated
public class MedicalRecordApplicationAdminServiceImpl implements IMedicalRecordApplicationAdminService {

    private final MedicalRecordApplicationMapper applicationMapper;
    private final IMedicalRecordApplicationService applicationService;
    private final IMedicalRecordApplicationTimelineService timelineService;
    private final ApplicationContext applicationContext;

    @Autowired
    public MedicalRecordApplicationAdminServiceImpl(
            MedicalRecordApplicationMapper applicationMapper,
            IMedicalRecordApplicationService applicationService,
            IMedicalRecordApplicationTimelineService timelineService,
            ApplicationContext applicationContext
    ) {
        this.applicationMapper = applicationMapper;
        this.applicationService = applicationService;
        this.timelineService = timelineService;
        this.applicationContext = applicationContext;
    }

    @Override
    public List<MedicalRecordApplicationAdminListResponseDto> searchApplications(MedicalRecordApplicationSearchForm searchForm) {
        // 构建查询条件
        LambdaQueryWrapper<MedicalRecordApplication> queryWrapper = buildSearchQueryWrapper(searchForm);
        
        // 执行查询
        List<MedicalRecordApplication> applications = applicationMapper.selectList(queryWrapper);
        
        // 转换为响应DTO并脱敏
        return applications.stream()
                .map(this::convertToAdminListResponseDto)
                .toList();
    }

    @Override
    public List<MedicalRecordApplication> exportApplications(MedicalRecordApplicationSearchForm searchForm) {
        // 构建查询条件（用于导出，不限制条数）
        LambdaQueryWrapper<MedicalRecordApplication> queryWrapper = buildSearchQueryWrapper(searchForm);
        
        // 执行查询
        return applicationMapper.selectList(queryWrapper);
    }

    @Override
    public MedicalRecordApplicationDetailResponseDto getAdminDetailById(String id) {
        // 验证记录是否存在
        MedicalRecordApplication application = applicationMapper.selectById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        // 复用用户端的详情获取方法，但以管理员身份获取（不验证openId）
        MedicalRecordApplicationDetailResponseDto detailDto = applicationService.getDetailById(id, application.getOpenId());
        
        // 设置管理员权限字段
        detailDto.setCanApprove(application.getStatus().canApprove());
        detailDto.setCanDelete(application.getStatus().canDelete());
        detailDto.setCanShip(application.getStatus().isPendingShipping());
        
        return detailDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveApplication(String id, MedicalRecordApplicationApprovalDto approvalDto, String approverName) {
        // 1. 验证申请记录存在
        MedicalRecordApplication application = applicationMapper.selectById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        // 2. 验证申请状态是否允许审批
        if (!application.getStatus().canApprove()) {
            throw new EditNotAllowedException(
                    "申请记录当前状态不允许审批：" + application.getStatus().getDescription(),
                    "MedicalRecordApplication",
                    id,
                    application.getStatus().getDescription()
            );
        }

        // 3. 根据审批结果更新申请记录
        if (approvalDto.getStatus() == MedicalRecordApplicationStatus.APPROVED) {
            handleApprovalApproved(application, approvalDto);
        } else if (approvalDto.getStatus() == MedicalRecordApplicationStatus.REJECTED) {
            handleApprovalRejected(application, approvalDto);
        } else {
            throw new IllegalArgumentException("无效的审批状态：" + approvalDto.getStatus());
        }

        // 4. 更新基本信息
        application.setApproveTime(LocalDateTime.now());
        application.setUpdateTime(LocalDateTime.now());

        // 5. 保存更新
        boolean updateSuccess = applicationMapper.updateById(application) == 1;
        if (!updateSuccess) {
            throw new DataUpdateFailedException("审批申请失败，请重试", "审批申请", id);
        }

        // 6. 记录时间线
        safeRecordTimeline(() -> {
            if (approvalDto.getStatus() == MedicalRecordApplicationStatus.APPROVED) {
                timelineService.recordApplicationApproved(id, approverName, MedicalRecordApplicationStatus.SUBMITTED);
            } else {
                timelineService.recordApplicationRejected(id, approverName, MedicalRecordApplicationStatus.SUBMITTED, approvalDto.getRejectReason());
            }
        });

        // 7. 发送微信订阅消息
        applicationContext.publishEvent(new MedicalRecordApplicationStatusEvent(application));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shipApplication(String id, MedicalRecordApplicationShippingDto shippingDto, String operatorName) {
        // 1. 验证申请记录存在
        MedicalRecordApplication application = applicationMapper.selectById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        // 2. 验证申请状态是否允许邮寄（必须是已支付状态）
        if (application.getStatus() != MedicalRecordApplicationStatus.PAID) {
            throw new EditNotAllowedException(
                    "申请记录当前状态不允许邮寄：" + application.getStatus().getDescription(),
                    "MedicalRecordApplication",
                    id,
                    application.getStatus().getDescription()
            );
        }

        // 3. 更新邮寄信息
        application.setTrackingNumber(shippingDto.getTrackingNumber());
        application.setShippingTime(LocalDateTime.now());
        application.setStatus(MedicalRecordApplicationStatus.DELIVERED);
        application.setUpdateTime(LocalDateTime.now());

        // 4. 保存更新
        boolean updateSuccess = applicationMapper.updateById(application) == 1;
        if (!updateSuccess) {
            throw new DataUpdateFailedException("邮寄操作失败，请重试", "邮寄操作", id);
        }

        // 5. 记录时间线
        safeRecordTimeline(() -> timelineService.recordApplicationDelivered(
                id,
                operatorName,
                MedicalRecordApplicationStatus.PAID,
                shippingDto.getTrackingNumber()
        ));

        // 6. 发送微信订阅消息
        applicationContext.publishEvent(new MedicalRecordApplicationStatusEvent(application));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteApplication(String id, String operatorName) {
        // 1. 验证申请记录存在
        MedicalRecordApplication application = applicationMapper.selectById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        // 2. 验证申请状态是否允许删除
        if (!application.getStatus().canDelete()) {
            throw new EditNotAllowedException(
                    "申请记录当前状态不允许删除：" + application.getStatus().getDescription(),
                    "MedicalRecordApplication",
                    id,
                    application.getStatus().getDescription()
            );
        }

        // 3. 记录删除事件到时间线（在删除前记录）
        safeRecordTimeline(() -> timelineService.recordApplicationDeleted(
                id,
                operatorName,
                application.getStatus()
        ));

        // 4. 删除申请记录
        boolean deleteSuccess = applicationMapper.deleteById(id) == 1;
        if (!deleteSuccess) {
            throw new DataUpdateFailedException("删除申请失败，请重试", "删除申请", id);
        }

        return true;
    }

    @Override
    public MedicalRecordApplicationStatisticsDto getStatistics() {
        MedicalRecordApplicationStatisticsDto statistics = new MedicalRecordApplicationStatisticsDto();

        try {
            // 1. 基础统计数据
            populateBasicStatistics(statistics);

            // 2. 状态分布统计
            populateStatusStatistics(statistics);

            // 3. 时间统计
            populateTimeStatistics(statistics);

            // 4. 费用统计
            populateFeeStatistics(statistics);

            // 5. 业务分类统计
            populatePurposeStatistics(statistics);

            // 6. 趋势分析
            populateDailyTrend(statistics);

        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            // 返回默认统计数据，避免页面异常
            return createDefaultStatistics();
        }

        return statistics;
    }

    /**
     * 构建搜索查询条件
     */
    private LambdaQueryWrapper<MedicalRecordApplication> buildSearchQueryWrapper(MedicalRecordApplicationSearchForm searchForm) {
        LambdaQueryWrapper<MedicalRecordApplication> queryWrapper = Wrappers.lambdaQuery();

        // 患者信息搜索
        if (StringUtils.isNotBlank(searchForm.getPatientName())) {
            queryWrapper.like(MedicalRecordApplication::getPatientName, searchForm.getPatientName());
        }
        if (StringUtils.isNotBlank(searchForm.getPatientIdCardNo())) {
            queryWrapper.like(MedicalRecordApplication::getPatientIdCardNo, searchForm.getPatientIdCardNo());
        }
        if (StringUtils.isNotBlank(searchForm.getPatientPhone())) {
            queryWrapper.like(MedicalRecordApplication::getPatientPhone, searchForm.getPatientPhone());
        }

        // 业务信息搜索
        if (StringUtils.isNotBlank(searchForm.getAdmissionId())) {
            queryWrapper.like(MedicalRecordApplication::getAdmissionId, searchForm.getAdmissionId());
        }
        if (searchForm.getStatus() != null) {
            queryWrapper.eq(MedicalRecordApplication::getStatus, searchForm.getStatus());
        }
        if (searchForm.getCopyPurpose() != null) {
            queryWrapper.eq(MedicalRecordApplication::getCopyPurpose, searchForm.getCopyPurpose());
        }

        // 收件人信息搜索
        if (StringUtils.isNotBlank(searchForm.getRecipientName())) {
            queryWrapper.like(MedicalRecordApplication::getRecipientName, searchForm.getRecipientName());
        }
        if (StringUtils.isNotBlank(searchForm.getRecipientMobile())) {
            queryWrapper.like(MedicalRecordApplication::getRecipientMobile, searchForm.getRecipientMobile());
        }

        // 快递单号搜索
        if (StringUtils.isNotBlank(searchForm.getTrackingNumber())) {
            queryWrapper.like(MedicalRecordApplication::getTrackingNumber, searchForm.getTrackingNumber());
        }

        // 时间范围搜索
        if (searchForm.getSubmitDateStart() != null) {
            queryWrapper.ge(MedicalRecordApplication::getSubmitTime, searchForm.getSubmitDateStart().atStartOfDay());
        }
        if (searchForm.getSubmitDateEnd() != null) {
            queryWrapper.le(MedicalRecordApplication::getSubmitTime, searchForm.getSubmitDateEnd().atTime(23, 59, 59));
        }
        if (searchForm.getApproveDateStart() != null) {
            queryWrapper.ge(MedicalRecordApplication::getApproveTime, searchForm.getApproveDateStart().atStartOfDay());
        }
        if (searchForm.getApproveDateEnd() != null) {
            queryWrapper.le(MedicalRecordApplication::getApproveTime, searchForm.getApproveDateEnd().atTime(23, 59, 59));
        }
        if (searchForm.getPayDateStart() != null) {
            queryWrapper.ge(MedicalRecordApplication::getPayTime, searchForm.getPayDateStart().atStartOfDay());
        }
        if (searchForm.getPayDateEnd() != null) {
            queryWrapper.le(MedicalRecordApplication::getPayTime, searchForm.getPayDateEnd().atTime(23, 59, 59));
        }

        // 排序：最新提交的在前
        queryWrapper.orderByDesc(MedicalRecordApplication::getCreateTime);

        return queryWrapper;
    }

    /**
     * 转换为管理端列表响应DTO
     */
    private MedicalRecordApplicationAdminListResponseDto convertToAdminListResponseDto(MedicalRecordApplication application) {
        MedicalRecordApplicationAdminListResponseDto dto = new MedicalRecordApplicationAdminListResponseDto();

        // 基本信息
        dto.setId(application.getId());
        dto.setAdmissionId(application.getAdmissionId());
        dto.setPatientName(application.getPatientName());
        
        // 脱敏信息
        dto.setPatientIdCardNo(maskIdCardNo(application.getPatientIdCardNo()));
        dto.setPatientPhone(maskMobile(application.getPatientPhone()));

        // 业务信息
        dto.setCopyPurpose(application.getCopyPurpose());
        dto.setCopyPurposeName(application.getCopyPurpose() != null ? application.getCopyPurpose().getDescription() : null);

        // 代办人信息
        dto.setAgentFlag(application.getAgentFlag());
        dto.setAgentName(application.getAgentName());
        dto.setAgentIdCardNo(maskIdCardNo(application.getAgentIdCardNo()));
        dto.setAgentMobile(maskMobile(application.getAgentMobile()));
        dto.setAgentRelation(application.getAgentRelation());
        dto.setAgentRelationName(application.getAgentRelation() != null ? application.getAgentRelation().getDescription() : null);

        // 收件人信息
        dto.setRecipientName(application.getRecipientName());
        dto.setRecipientMobile(maskMobile(application.getRecipientMobile()));
        dto.setRecipientAddress(application.getRecipientAddress());

        // 费用信息
        dto.setCopyFee(application.getCopyFee());
        dto.setShippingFee(application.getShippingFee());
        dto.setTotalFee(application.getTotalFee());

        // 快递信息
        dto.setTrackingNumber(application.getTrackingNumber());

        // 状态信息
        dto.setStatus(application.getStatus());
        dto.setStatusName(application.getStatus().getDescription());

        // 时间信息
        dto.setSubmitTime(application.getSubmitTime());
        dto.setApproveTime(application.getApproveTime());
        dto.setPayTime(application.getPayTime());
        dto.setShippingTime(application.getShippingTime());

        // 审批信息
        dto.setRejectReason(application.getRejectReason());
        dto.setCorrectionAdvice(application.getCorrectionAdvice());

        // 系统时间
        dto.setCreateTime(application.getCreateTime());
        dto.setUpdateTime(application.getUpdateTime());

        // 权限标识
        dto.setCanApprove(application.getStatus().canApprove());
        dto.setCanDelete(application.getStatus().canDelete());
        dto.setCanShip(application.getStatus() == MedicalRecordApplicationStatus.PAID);

        return dto;
    }

    /**
     * 处理审批通过逻辑
     */
    private void handleApprovalApproved(MedicalRecordApplication application, MedicalRecordApplicationApprovalDto approvalDto) {
        // 验证费用信息必填
        if (approvalDto.getCopyFee() == null) {
            throw new IllegalArgumentException("审批通过时复印费用不能为空");
        }
        if (approvalDto.getShippingFee() == null) {
            throw new IllegalArgumentException("审批通过时快递费用不能为空");
        }

        // 设置费用信息
        application.setCopyFee(approvalDto.getCopyFee());
        application.setShippingFee(approvalDto.getShippingFee());
        application.setTotalFee(approvalDto.getCopyFee().add(approvalDto.getShippingFee()));

        // 更新状态
        application.setStatus(MedicalRecordApplicationStatus.APPROVED);
        
        // 清空驳回信息
        application.setRejectReason(null);
        application.setCorrectionAdvice(null);
    }

    /**
     * 处理审批驳回逻辑
     */
    private void handleApprovalRejected(MedicalRecordApplication application, MedicalRecordApplicationApprovalDto approvalDto) {
        // 设置驳回信息
        application.setRejectReason(approvalDto.getRejectReason());
        application.setCorrectionAdvice(approvalDto.getCorrectionAdvice());

        // 更新状态
        application.setStatus(MedicalRecordApplicationStatus.REJECTED);

        // 清空费用信息
        application.setCopyFee(null);
        application.setShippingFee(null);
        application.setTotalFee(null);
    }

    /**
     * 基础统计数据
     */
    private void populateBasicStatistics(MedicalRecordApplicationStatisticsDto statistics) {
        // 总数量
        Long totalCount = applicationMapper.selectCount(new QueryWrapper<>());
        statistics.setTotalCount(totalCount);

        // 各状态数量
        for (MedicalRecordApplicationStatus status : MedicalRecordApplicationStatus.values()) {
            Long count = applicationMapper.selectCount(
                    Wrappers.<MedicalRecordApplication>lambdaQuery()
                            .eq(MedicalRecordApplication::getStatus, status)
            );

            switch (status) {
                case DRAFT -> statistics.setDraftCount(count);
                case SUBMITTED -> statistics.setSubmittedCount(count);
                case APPROVED -> statistics.setApprovedCount(count);
                case REJECTED -> statistics.setRejectedCount(count);
                case PAID -> statistics.setPaidCount(count);
                case COMPLETED -> statistics.setCompletedCount(count);
                case DELIVERED -> statistics.setDeliveredCount(count);
            }
        }

        // 待处理数量
        statistics.setPendingApprovalCount(statistics.getSubmittedCount());
        statistics.setPendingPaymentCount(statistics.getApprovedCount());
        statistics.setPendingShippingCount(statistics.getPaidCount());
    }

    /**
     * 状态分布统计
     */
    private void populateStatusStatistics(MedicalRecordApplicationStatisticsDto statistics) {
        Map<String, Long> countByStatus = new HashMap<>();
        
        for (MedicalRecordApplicationStatus status : MedicalRecordApplicationStatus.values()) {
            Long count = applicationMapper.selectCount(
                    Wrappers.<MedicalRecordApplication>lambdaQuery()
                            .eq(MedicalRecordApplication::getStatus, status)
            );
            countByStatus.put(status.getDescription(), count);
        }
        
        statistics.setCountByStatus(countByStatus);
    }

    /**
     * 时间统计
     */
    private void populateTimeStatistics(MedicalRecordApplicationStatisticsDto statistics) {
        LocalDate today = LocalDate.now();
        LocalDate weekStart = today.minusDays(today.getDayOfWeek().getValue() - 1L);
        LocalDate monthStart = today.withDayOfMonth(1);

        // 今日新增
        Long todayCount = applicationMapper.selectCount(
                Wrappers.<MedicalRecordApplication>lambdaQuery()
                        .ge(MedicalRecordApplication::getCreateTime, today.atStartOfDay())
                        .lt(MedicalRecordApplication::getCreateTime, today.plusDays(1).atStartOfDay())
        );
        statistics.setTodayCount(todayCount);

        // 本周新增
        Long thisWeekCount = applicationMapper.selectCount(
                Wrappers.<MedicalRecordApplication>lambdaQuery()
                        .ge(MedicalRecordApplication::getCreateTime, weekStart.atStartOfDay())
        );
        statistics.setThisWeekCount(thisWeekCount);

        // 本月新增
        Long thisMonthCount = applicationMapper.selectCount(
                Wrappers.<MedicalRecordApplication>lambdaQuery()
                        .ge(MedicalRecordApplication::getCreateTime, monthStart.atStartOfDay())
        );
        statistics.setThisMonthCount(thisMonthCount);
    }

    /**
     * 费用统计
     */
    private void populateFeeStatistics(MedicalRecordApplicationStatisticsDto statistics) {
        // 查询所有已支付的申请记录
        List<MedicalRecordApplication> paidApplications = applicationMapper.selectList(
                Wrappers.<MedicalRecordApplication>lambdaQuery()
                        .in(MedicalRecordApplication::getStatus, 
                            Arrays.asList(MedicalRecordApplicationStatus.PAID, 
                                         MedicalRecordApplicationStatus.COMPLETED, 
                                         MedicalRecordApplicationStatus.DELIVERED))
                        .isNotNull(MedicalRecordApplication::getTotalFee)
        );

        if (!paidApplications.isEmpty()) {
            // 总收入
            BigDecimal totalRevenue = paidApplications.stream()
                    .map(MedicalRecordApplication::getTotalFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.setTotalRevenue(totalRevenue);

            // 平均费用
            BigDecimal averageFee = totalRevenue.divide(
                    BigDecimal.valueOf(paidApplications.size()), 
                    2,
                    RoundingMode.HALF_UP
            );
            statistics.setAverageFee(averageFee);
        } else {
            statistics.setTotalRevenue(BigDecimal.ZERO);
            statistics.setAverageFee(BigDecimal.ZERO);
        }
    }

    /**
     * 复印用途统计
     */
    private void populatePurposeStatistics(MedicalRecordApplicationStatisticsDto statistics) {
        // 获取所有复印用途的统计
        List<MedicalRecordApplication> allApplications = applicationMapper.selectList(
                Wrappers.<MedicalRecordApplication>lambdaQuery()
                        .isNotNull(MedicalRecordApplication::getCopyPurpose)
        );

        Map<String, Long> purposeCountMap = allApplications.stream()
                .collect(Collectors.groupingBy(
                        app -> app.getCopyPurpose().getDescription(),
                        Collectors.counting()
                ));

        statistics.setCountByPurpose(purposeCountMap);
    }

    /**
     * 每日趋势统计
     */
    private void populateDailyTrend(MedicalRecordApplicationStatisticsDto statistics) {
        List<MedicalRecordApplicationStatisticsDto.DailyStatistic> dailyTrend = new ArrayList<>();
        
        // 最近7天的统计
        for (int i = 6; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            MedicalRecordApplicationStatisticsDto.DailyStatistic dailyStat = 
                    new MedicalRecordApplicationStatisticsDto.DailyStatistic();
            
            dailyStat.setDate(date);
            
            // 当日申报数量
            Long dailyCount = applicationMapper.selectCount(
                    Wrappers.<MedicalRecordApplication>lambdaQuery()
                            .ge(MedicalRecordApplication::getCreateTime, date.atStartOfDay())
                            .lt(MedicalRecordApplication::getCreateTime, date.plusDays(1).atStartOfDay())
            );
            dailyStat.setCount(dailyCount);

            // 当日通过数量
            Long dailyApproved = applicationMapper.selectCount(
                    Wrappers.<MedicalRecordApplication>lambdaQuery()
                            .eq(MedicalRecordApplication::getStatus, MedicalRecordApplicationStatus.APPROVED)
                            .ge(MedicalRecordApplication::getApproveTime, date.atStartOfDay())
                            .lt(MedicalRecordApplication::getApproveTime, date.plusDays(1).atStartOfDay())
            );
            dailyStat.setApprovedCount(dailyApproved);

            // 当日驳回数量
            Long dailyRejected = applicationMapper.selectCount(
                    Wrappers.<MedicalRecordApplication>lambdaQuery()
                            .eq(MedicalRecordApplication::getStatus, MedicalRecordApplicationStatus.REJECTED)
                            .ge(MedicalRecordApplication::getApproveTime, date.atStartOfDay())
                            .lt(MedicalRecordApplication::getApproveTime, date.plusDays(1).atStartOfDay())
            );
            dailyStat.setRejectedCount(dailyRejected);

            // 当日支付数量
            Long dailyPaid = applicationMapper.selectCount(
                    Wrappers.<MedicalRecordApplication>lambdaQuery()
                            .eq(MedicalRecordApplication::getStatus, MedicalRecordApplicationStatus.PAID)
                            .ge(MedicalRecordApplication::getPayTime, date.atStartOfDay())
                            .lt(MedicalRecordApplication::getPayTime, date.plusDays(1).atStartOfDay())
            );
            dailyStat.setPaidCount(dailyPaid);

            // 当日邮寄数量
            Long dailyShipped = applicationMapper.selectCount(
                    Wrappers.<MedicalRecordApplication>lambdaQuery()
                            .eq(MedicalRecordApplication::getStatus, MedicalRecordApplicationStatus.DELIVERED)
                            .ge(MedicalRecordApplication::getShippingTime, date.atStartOfDay())
                            .lt(MedicalRecordApplication::getShippingTime, date.plusDays(1).atStartOfDay())
            );
            dailyStat.setShippedCount(dailyShipped);

            // 当日收入
            List<MedicalRecordApplication> dailyPaidApps = applicationMapper.selectList(
                    Wrappers.<MedicalRecordApplication>lambdaQuery()
                            .isNotNull(MedicalRecordApplication::getTotalFee)
                            .ge(MedicalRecordApplication::getPayTime, date.atStartOfDay())
                            .lt(MedicalRecordApplication::getPayTime, date.plusDays(1).atStartOfDay())
            );
            
            BigDecimal dailyRevenue = dailyPaidApps.stream()
                    .map(MedicalRecordApplication::getTotalFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dailyStat.setDailyRevenue(dailyRevenue);

            dailyTrend.add(dailyStat);
        }
        
        statistics.setDailyTrend(dailyTrend);
    }

    /**
     * 创建默认统计数据
     */
    private MedicalRecordApplicationStatisticsDto createDefaultStatistics() {
        MedicalRecordApplicationStatisticsDto statistics = new MedicalRecordApplicationStatisticsDto();
        statistics.setTotalCount(0L);
        statistics.setDraftCount(0L);
        statistics.setSubmittedCount(0L);
        statistics.setApprovedCount(0L);
        statistics.setRejectedCount(0L);
        statistics.setPaidCount(0L);
        statistics.setCompletedCount(0L);
        statistics.setDeliveredCount(0L);
        statistics.setPendingApprovalCount(0L);
        statistics.setPendingPaymentCount(0L);
        statistics.setPendingShippingCount(0L);
        statistics.setTodayCount(0L);
        statistics.setThisWeekCount(0L);
        statistics.setThisMonthCount(0L);
        statistics.setTotalRevenue(BigDecimal.ZERO);
        statistics.setAverageFee(BigDecimal.ZERO);
        statistics.setCountByStatus(new HashMap<>());
        statistics.setCountByPurpose(new HashMap<>());
        statistics.setDailyTrend(new ArrayList<>());
        return statistics;
    }

    /**
     * 安全地记录时间线事件，不影响主业务逻辑
     */
    private void safeRecordTimeline(Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            log.warn("记录时间线事件失败，但不影响主业务逻辑: {}", e.getMessage());
        }
    }

    /**
     * 身份证号脱敏
     */
    private String maskIdCardNo(String idCardNo) {
        if (StringUtils.isBlank(idCardNo)) {
            return idCardNo;
        }
        return cn.hutool.core.util.DesensitizedUtil.idCardNum(idCardNo, 4, 4);
    }

    /**
     * 手机号脱敏
     */
    private String maskMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return mobile;
        }
        return cn.hutool.core.util.DesensitizedUtil.mobilePhone(mobile);
    }
} 
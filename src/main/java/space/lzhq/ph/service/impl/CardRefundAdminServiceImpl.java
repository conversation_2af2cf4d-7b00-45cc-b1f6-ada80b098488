package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.Attachment;
import space.lzhq.ph.domain.CardRefund;
import space.lzhq.ph.dto.mapper.AttachmentMapper;
import space.lzhq.ph.dto.request.CardRefundApprovalDto;
import space.lzhq.ph.dto.request.CardRefundSearchForm;
import space.lzhq.ph.dto.response.CardRefundAdminListResponseDto;
import space.lzhq.ph.dto.response.CardRefundDetailResponseDto;
import space.lzhq.ph.dto.response.CardRefundStatisticsDto;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.enums.CardRefundEventType;
import space.lzhq.ph.enums.TimelineOperatorType;
import space.lzhq.ph.exception.*;
import space.lzhq.ph.mapper.CardRefundMapper;
import space.lzhq.ph.service.ICardRefundAdminService;
import space.lzhq.ph.service.IAttachmentService;
import space.lzhq.ph.service.ICardRefundTimelineService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Validated
public class CardRefundAdminServiceImpl
        extends ServiceImpl<CardRefundMapper, CardRefund>
        implements ICardRefundAdminService {

    private final IAttachmentService attachmentService;
    private final ICardRefundTimelineService timelineService;

    public CardRefundAdminServiceImpl(
            IAttachmentService attachmentService,
            ICardRefundTimelineService timelineService
    ) {
        this.attachmentService = attachmentService;
        this.timelineService = timelineService;
    }

    /**
     * 获取当前代理对象，用于调用事务方法
     */
    private CardRefundAdminServiceImpl getSelf() {
        return (CardRefundAdminServiceImpl) AopContext.currentProxy();
    }

    @Override
    @Transactional(readOnly = true)
    public List<CardRefundAdminListResponseDto> searchRegistrations(
            CardRefundSearchForm searchForm) {

        // 构建查询条件
        LambdaQueryWrapper<CardRefund> queryWrapper = buildSearchQueryWrapper(searchForm);

        // 查询申报记录
        List<CardRefund> registrations = this.list(queryWrapper);

        if (registrations.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为DTO并进行数据脱敏
        return registrations.stream()
                .map(registration -> {
                    CardRefundAdminListResponseDto dto = convertToAdminListDto(registration);

                    // 设置是否可审批
                    dto.setCanApprove(registration.getStatus().canApprove());

                    // 设置是否可删除
                    dto.setCanDelete(registration.getStatus().canDelete());

                    return dto;
                })
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public CardRefundDetailResponseDto getAdminDetailById(
            @NotBlank @Size(min = 32, max = 32) String id) {

        // 获取申报记录
        CardRefund registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException("申报记录不存在", "CardRefund", id);
        }

        // 转换为详情DTO
        CardRefundDetailResponseDto detailDto =
                space.lzhq.ph.dto.mapper.CardRefundMapper.INSTANCE.toDetailResponseDto(registration);

        // 填充附件信息
        populateAttachments(registration, detailDto);

        detailDto.setCanApprove(registration.getStatus().canApprove());

        return detailDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardRefund approveRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid CardRefundApprovalDto approvalDto,
            @NotBlank @Size(min = 1, max = 50) String approverName) {
        // 获取申报记录
        CardRefund registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException("申报记录不存在", "CardRefund", id);
        }

        // 验证是否可以审批
        if (!registration.getStatus().canApprove()) {
            throw new ApprovalNotAllowedException(
                    String.format("申报记录状态为 %s，不允许审批", registration.getStatus().getDescription()),
                    registration.getStatus()
            );
        }

        // 验证审批信息
        validateApprovalDto(approvalDto);

        // 更新申报记录
        registration.setStatus(approvalDto.getStatus());
        registration.setApproveTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        // 根据审批结果设置相关字段
        if (approvalDto.getStatus() == CardRefundStatus.REJECTED) {
            registration.setRejectReason(approvalDto.getRejectReason());
            registration.setCorrectionAdvice(approvalDto.getCorrectionAdvice());
        } else if (approvalDto.getStatus() == CardRefundStatus.APPROVED) {
            // 清空之前的驳回信息
            registration.setRejectReason(null);
            registration.setCorrectionAdvice(null);
        }

        // 保存更新
        boolean success = this.updateById(registration);
        if (!success) {
            throw new DataUpdateFailedException("审批失败，数据库更新失败");
        }

        // 记录时间线
        safeRecordTimeline(() -> {
            CardRefundEventType eventType = approvalDto.getStatus() == CardRefundStatus.APPROVED ?
                    CardRefundEventType.REGISTRATION_APPROVED : CardRefundEventType.REGISTRATION_REJECTED;
            String description = String.format("管理员 %s %s申报", approverName, eventType.getDescription());
            if (StringUtils.isNotBlank(approvalDto.getRejectReason())) {
                description += "，驳回原因：" + approvalDto.getRejectReason();
            }
            timelineService.recordTimelineEvent(id, eventType, description, TimelineOperatorType.REVIEWER, approverName);
        });

        return registration;
    }

    @Override
    public int batchApproveRegistrations(
            @NotNull @Size(min = 1) List<@NotBlank @Size(min = 32, max = 32) String> ids,
            @NotNull @Valid CardRefundApprovalDto approvalDto,
            @NotBlank @Size(min = 1, max = 50) String approverName) {
        // 验证审批信息
        validateApprovalDto(approvalDto);

        int successCount = 0;

        for (String id : ids) {
            try {
                getSelf().approveRegistration(id, approvalDto, approverName);
                successCount++;
            } catch (Exception e) {
                log.warn("批量审批中单个记录审批失败，ID: {}, 错误: {}", id, e.getMessage());
            }
        }
        return successCount;
    }

    @Override
    @Transactional(readOnly = true)
    public List<CardRefund> exportRegistrations(
            CardRefundSearchForm searchForm) {
        // 构建查询条件
        LambdaQueryWrapper<CardRefund> queryWrapper = buildSearchQueryWrapper(searchForm);

        // 查询并返回原始记录（用于导出，不进行数据脱敏）
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 1, max = 50) String operatorName) {
        // 获取申报记录
        CardRefund registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException("申报记录不存在", "CardRefund", id);
        }

        // 验证是否可以删除
        if (!registration.getStatus().canDelete()) {
            throw new DeleteNotAllowedException(
                    String.format("申报记录状态为 %s，不允许删除", registration.getStatus().getDescription())
            );
        }

        // 软删除（设置删除标记）
        registration.setDeleteFlag(true);
        registration.setUpdateTime(LocalDateTime.now());

        boolean success = this.updateById(registration);
        if (!success) {
            throw new DataUpdateFailedException("删除失败，数据库更新失败");
        }

        // 记录时间线
        safeRecordTimeline(() -> {
            String description = String.format("管理员 %s 删除申报记录", operatorName);
            timelineService.recordTimelineEvent(id, CardRefundEventType.REGISTRATION_DELETED, description, TimelineOperatorType.ADMIN, operatorName);
        });

        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public CardRefundStatisticsDto getStatistics() {
        // 构建查询条件，包含权限过滤
        LambdaQueryWrapper<CardRefund> queryWrapper = buildSearchQueryWrapper(null);

        // 查询申报记录（已应用权限过滤）
        List<CardRefund> allRegistrations = this.list(queryWrapper);

        CardRefundStatisticsDto statistics = new CardRefundStatisticsDto();

        // 计算各状态统计
        Map<CardRefundStatus, Long> statusCounts = allRegistrations.stream()
                .collect(Collectors.groupingBy(
                        CardRefund::getStatus,
                        Collectors.counting()
                ));

        // 设置总数和基本状态统计
        statistics.setTotalCount((long) allRegistrations.size());
        statistics.setApprovedCount(statusCounts.getOrDefault(CardRefundStatus.APPROVED, 0L));
        statistics.setRejectedCount(statusCounts.getOrDefault(CardRefundStatus.REJECTED, 0L));

        // 设置各个状态的详细统计
        statistics.setDraftCount(statusCounts.getOrDefault(CardRefundStatus.DRAFT, 0L));
        statistics.setSubmittedCount(statusCounts.getOrDefault(CardRefundStatus.SUBMITTED, 0L));
        statistics.setPendingApprovalCount(statusCounts.getOrDefault(CardRefundStatus.SUBMITTED, 0L));

        // 设置状态统计映射
        statistics.setCountByStatus(statusCounts.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().getDescription(),
                        Map.Entry::getValue
                ))
        );

        // 计算时间相关统计
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);

        // 今日统计
        long todayCount = allRegistrations.stream()
                .filter(reg -> !reg.getCreateTime().isBefore(startOfDay) && !reg.getCreateTime().isAfter(endOfDay))
                .count();
        statistics.setTodayCount(todayCount);

        // 本周统计（从周一开始）
        LocalDate mondayOfThisWeek = today.with(java.time.DayOfWeek.MONDAY);
        LocalDateTime startOfWeek = mondayOfThisWeek.atStartOfDay();
        long thisWeekCount = allRegistrations.stream()
                .filter(reg -> !reg.getCreateTime().isBefore(startOfWeek))
                .count();
        statistics.setThisWeekCount(thisWeekCount);

        // 本月统计
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        LocalDateTime startOfMonth = firstDayOfMonth.atStartOfDay();
        long thisMonthCount = allRegistrations.stream()
                .filter(reg -> !reg.getCreateTime().isBefore(startOfMonth))
                .count();
        statistics.setThisMonthCount(thisMonthCount);

        // 计算最近7天的每日统计
        List<CardRefundStatisticsDto.DailyStatistic> dailyStatistics =
                calculateDailyStatistics(allRegistrations);
        statistics.setDailyTrend(dailyStatistics);

        return statistics;
    }

    /**
     * 构建搜索查询条件
     */
    private LambdaQueryWrapper<CardRefund> buildSearchQueryWrapper(
            CardRefundSearchForm searchForm) {

        LambdaQueryWrapper<CardRefund> queryWrapper = Wrappers.lambdaQuery();

        // 按提交时间降序排列，NULL值排在最后
        queryWrapper.last("ORDER BY IFNULL(submit_time, '1970-01-01 00:00:00') DESC");

        if (searchForm != null) {
            // 优先处理有索引的提交时间条件，让数据库优化器优先使用索引
            if (searchForm.getSubmitDateStart() != null) {
                queryWrapper.ge(CardRefund::getSubmitTime, searchForm.getSubmitDateStart().atStartOfDay());
            }
            if (searchForm.getSubmitDateEnd() != null) {
                queryWrapper.le(CardRefund::getSubmitTime, searchForm.getSubmitDateEnd().atTime(LocalTime.MAX));
            }

            // 申报状态（如果有状态索引也应该优先处理）
            if (searchForm.getStatus() != null) {
                queryWrapper.eq(CardRefund::getStatus, searchForm.getStatus());
            }

            // 患者姓名
            if (StringUtils.isNotBlank(searchForm.getPatientName())) {
                queryWrapper.like(CardRefund::getPatientName, searchForm.getPatientName().trim());
            }

            // 身份证号
            if (StringUtils.isNotBlank(searchForm.getPatientIdCardNo())) {
                queryWrapper.like(CardRefund::getPatientIdCardNo, searchForm.getPatientIdCardNo().trim());
            }

            // 手机号
            if (StringUtils.isNotBlank(searchForm.getPatientMobile())) {
                queryWrapper.like(CardRefund::getPatientMobile, searchForm.getPatientMobile().trim());
            }
        }

        // 默认查询未删除的记录（无索引条件放在最后）
        queryWrapper.eq(CardRefund::getDeleteFlag, 0);

        return queryWrapper;
    }

    /**
     * 转换为管理端列表DTO并进行数据脱敏
     */
    private CardRefundAdminListResponseDto convertToAdminListDto(
            CardRefund registration) {

        CardRefundAdminListResponseDto dto = new CardRefundAdminListResponseDto();

        // 基本信息
        dto.setId(registration.getId());
        dto.setPatientName(registration.getPatientName());
        dto.setPatientIdCardNo(registration.getPatientIdCardNo());
        dto.setPatientMobile(registration.getPatientMobile());
        dto.setBankCardNo(registration.getBankCardNo());
        dto.setStatus(registration.getStatus());
        dto.setCreateTime(registration.getCreateTime());
        dto.setSubmitTime(registration.getSubmitTime());
        dto.setUpdateTime(registration.getUpdateTime());
        dto.setApproveTime(registration.getApproveTime());

        // 代办信息
        dto.setAgentFlag(registration.getAgentFlag());
        if (Objects.equals(registration.getAgentFlag(), 1)) {
            dto.setAgentName(registration.getAgentName());
            dto.setAgentIdCardNo(registration.getAgentIdCardNo());
            dto.setAgentMobile(registration.getAgentMobile());
            dto.setAgentRelation(registration.getAgentRelation());
        }

        // 驳回信息
        dto.setRejectReason(registration.getRejectReason());
        dto.setCorrectionAdvice(registration.getCorrectionAdvice());

        return dto;
    }

    /**
     * 填充附件信息到DTO
     */
    private void populateAttachments(CardRefund registration,
                                     CardRefundDetailResponseDto detailDto) {
        // 收集所有附件ID
        List<String> allAttachmentIds = collectAllAttachmentIds(registration);

        if (allAttachmentIds.isEmpty()) {
            return;
        }

        // 批量查询附件并创建映射
        Map<String, Attachment> attachmentMap =
                batchQueryAndMapAttachments(allAttachmentIds);

        // 设置各类附件到DTO
        setAttachmentsToDto(registration, detailDto, attachmentMap);
    }

    /**
     * 收集申报记录中的所有附件ID
     */
    private List<String> collectAllAttachmentIds(CardRefund registration) {
        List<String> allAttachmentIds = new ArrayList<>();

        if (registration.getPatientIdCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getPatientIdCardAttachmentId());
        }
        if (registration.getAgentIdCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getAgentIdCardAttachmentId());
        }
        if (registration.getBankCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getBankCardAttachmentId());
        }

        return allAttachmentIds.stream()
                .distinct()
                .toList();
    }

    /**
     * 批量查询附件并创建ID到附件对象的映射
     */
    private Map<String, Attachment> batchQueryAndMapAttachments(List<String> attachmentIds) {
        List<Attachment> allAttachments = attachmentService.listByIds(attachmentIds);
        return allAttachments.stream()
                .collect(Collectors.toMap(Attachment::getId, Function.identity()));
    }

    /**
     * 设置附件信息到DTO
     */
    private void setAttachmentsToDto(CardRefund registration,
                                     CardRefundDetailResponseDto detailDto,
                                     Map<String, Attachment> attachmentMap) {

        // 设置患者身份证附件
        if (registration.getPatientIdCardAttachmentId() != null) {
            Attachment attachment =
                    attachmentMap.get(registration.getPatientIdCardAttachmentId());
            if (attachment != null) {
                detailDto.setPatientIdCardAttachment(
                        AttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }

        // 设置代办人身份证附件
        if (registration.getAgentIdCardAttachmentId() != null) {
            Attachment attachment =
                    attachmentMap.get(registration.getAgentIdCardAttachmentId());
            if (attachment != null) {
                detailDto.setAgentIdCardAttachment(
                        AttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }

        // 设置银行卡附件
        if (registration.getBankCardAttachmentId() != null) {
            Attachment attachment =
                    attachmentMap.get(registration.getBankCardAttachmentId());
            if (attachment != null) {
                detailDto.setBankCardAttachment(
                        AttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }
    }

    /**
     * 验证审批DTO
     */
    private void validateApprovalDto(CardRefundApprovalDto approvalDto) {
        if (approvalDto.getStatus() == CardRefundStatus.REJECTED && StringUtils.isBlank(approvalDto.getRejectReason())) {
            throw new InvalidApprovalException("驳回申报时必须提供驳回原因");
        }
    }

    /**
     * 安全地记录时间线事件，不影响主业务逻辑
     */
    private void safeRecordTimeline(Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            log.warn("记录时间线事件失败，但不影响主业务逻辑: {}", e.getMessage());
        }
    }

    /**
     * 计算最近N天的每日统计
     */
    private List<CardRefundStatisticsDto.DailyStatistic> calculateDailyStatistics(
            List<CardRefund> allRegistrations
    ) {

        List<CardRefundStatisticsDto.DailyStatistic> dailyStatistics = new ArrayList<>();

        for (int i = 7 - 1; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

            long dayCount = allRegistrations.stream()
                    .filter(reg -> !reg.getCreateTime().isBefore(startOfDay) && !reg.getCreateTime().isAfter(endOfDay))
                    .count();

            CardRefundStatisticsDto.DailyStatistic dayStat =
                    new CardRefundStatisticsDto.DailyStatistic();
            dayStat.setDate(date);
            dayStat.setCount(dayCount);

            dailyStatistics.add(dayStat);
        }

        return dailyStatistics;
    }
}

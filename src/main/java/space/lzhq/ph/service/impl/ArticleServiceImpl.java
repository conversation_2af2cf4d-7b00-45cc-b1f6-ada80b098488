package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.html.EscapeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Article;
import space.lzhq.ph.mapper.ArticleMapper;
import space.lzhq.ph.service.IArticleService;

import java.util.Date;
import java.util.List;

/**
 * 文章Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-05-30
 */
@Service
public class ArticleServiceImpl implements IArticleService {
    @Autowired
    private ArticleMapper articleMapper;

    /**
     * 查询文章
     *
     * @param id 文章ID
     * @return 文章
     */
    @Override
    public Article selectArticleById(Long id) {
        return articleMapper.selectArticleById(id);
    }

    /**
     * 查询文章列表
     *
     * @param article 文章
     * @return 文章
     */
    @Override
    public List<Article> selectArticleList(Article article) {
        return articleMapper.selectArticleList(article);
    }

    private void cleanXss(Article article) {
        article.setTitle(EscapeUtil.clean(article.getTitle()));
        article.setCategoryName(EscapeUtil.clean(article.getCategoryName()));
        article.setSummary(EscapeUtil.clean(article.getSummary()));
    }

    /**
     * 新增文章
     *
     * @param article 文章
     * @return 结果
     */
    @Override
    public int insertArticle(Article article) {
        Date now = new Date();
        article.setCreateTime(now);
        article.setUpdateTime(now);

        String currentUserName = ShiroUtils.getLoginName();
        article.setCreateBy(currentUserName);
        article.setUpdateBy(currentUserName);

        cleanXss(article);
        return articleMapper.insertArticle(article);
    }

    /**
     * 修改文章
     *
     * @param article 文章
     * @return 结果
     */
    @Override
    public int updateArticle(Article article) {
        Date now = new Date();
        article.setCreateTime(now);
        article.setUpdateTime(now);

        String currentUserName = ShiroUtils.getLoginName();
        article.setCreateBy(currentUserName);
        article.setUpdateBy(currentUserName);

        cleanXss(article);
        return articleMapper.updateArticle(article);
    }

    /**
     * 删除文章对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteArticleByIds(String ids) {
        return articleMapper.deleteArticleByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除文章信息
     *
     * @param id 文章ID
     * @return 结果
     */
    @Override
    public int deleteArticleById(Long id) {
        return articleMapper.deleteArticleById(id);
    }
}

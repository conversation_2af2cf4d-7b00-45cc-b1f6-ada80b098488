package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult;
import org.springframework.stereotype.Service;
import space.lzhq.ph.common.Constants;
import space.lzhq.ph.domain.MipWxOrder;
import space.lzhq.ph.mapper.MipWxOrderMapper;
import space.lzhq.ph.service.IMipWxOrderService;

/**
 * 微信医保订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-11
 */
@Service
public class MipWxOrderServiceImpl extends ServiceImpl<MipWxOrderMapper, MipWxOrder> implements IMipWxOrderService {

    @Override
    public MipWxOrder getOneByPayOrderId(String payOrderId) {
        return new LambdaQueryChainWrapper<>(getBaseMapper())
                .eq(MipWxOrder::getPayOrderId, payOrderId)
                .one();
    }

    @Override
    public MipWxOrder getOneByOutTradeNo(String outTradeNo) {
        return new LambdaQueryChainWrapper<>(getBaseMapper())
                .eq(MipWxOrder::getHospitalOutTradeNo, outTradeNo)
                .one();
    }

    @Override
    public boolean updateOnRefund(MipWxOrder mipWxOrder, WxInsurancePayRefundResult refundResult) {
        mipWxOrder.setMedRefundId(refundResult.getMedRefundId());
        mipWxOrder.setCashRefundId(refundResult.getCashRefundId());
        mipWxOrder.setCashPayStatus(Constants.REFUND_OK);
        mipWxOrder.setInsuranceRefundId(refundResult.getInsuranceRefundId());
        return updateById(mipWxOrder);
    }
}

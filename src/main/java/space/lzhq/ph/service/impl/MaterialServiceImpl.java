package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Material;
import space.lzhq.ph.mapper.MaterialMapper;
import space.lzhq.ph.service.IMaterialService;

import java.util.List;

/**
 * 材料Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
@Service
public class MaterialServiceImpl implements IMaterialService {
    @Autowired
    private MaterialMapper materialMapper;

    /**
     * 查询材料
     *
     * @param id 材料ID
     * @return 材料
     */
    @Override
    public Material selectMaterialById(String id) {
        return materialMapper.selectMaterialById(id);
    }

    /**
     * 查询材料列表
     *
     * @param material 材料
     * @return 材料
     */
    @Override
    public List<Material> selectMaterialList(Material material) {
        return materialMapper.selectMaterialList(material);
    }

    /**
     * 新增材料
     *
     * @param material 材料
     * @return 结果
     */
    @Override
    public int insertMaterial(Material material) {
        return materialMapper.insertMaterial(material);
    }

    @Override
    public int insertMaterials(List<Material> materials) {
        int count = 0;
        List<List<Material>> materialChunks = ListUtils.partition(materials, 500);
        for (List<Material> materialChunk : materialChunks) {
            count += materialMapper.insertMaterials(materialChunk);
        }
        return count;
    }

    /**
     * 修改材料
     *
     * @param material 材料
     * @return 结果
     */
    @Override
    public int updateMaterial(Material material) {
        return materialMapper.updateMaterial(material);
    }

    /**
     * 删除材料对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteMaterialByIds(String ids) {
        return materialMapper.deleteMaterialByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除材料信息
     *
     * @param id 材料ID
     * @return 结果
     */
    @Override
    public int deleteMaterialById(String id) {
        return materialMapper.deleteMaterialById(id);
    }

    @Override
    public void clearAll() {
        materialMapper.clearAll();
    }
}

package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.WxSubscribeMessage;
import space.lzhq.ph.mapper.WxSubscribeMessageMapper;
import space.lzhq.ph.service.IWxSubscribeMessageService;

import java.util.List;

/**
 * 微信订阅消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-06-16
 */
@Service
public class WxSubscribeMessageServiceImpl implements IWxSubscribeMessageService {
    @Autowired
    private WxSubscribeMessageMapper wxSubscribeMessageMapper;

    /**
     * 查询微信订阅消息
     *
     * @param id 微信订阅消息ID
     * @return 微信订阅消息
     */
    @Override
    public WxSubscribeMessage selectWxSubscribeMessageById(Long id) {
        return wxSubscribeMessageMapper.selectWxSubscribeMessageById(id);
    }

    /**
     * 查询微信订阅消息列表
     *
     * @param wxSubscribeMessage 微信订阅消息
     * @return 微信订阅消息
     */
    @Override
    public List<WxSubscribeMessage> selectWxSubscribeMessageList(WxSubscribeMessage wxSubscribeMessage) {
        return wxSubscribeMessageMapper.selectWxSubscribeMessageList(wxSubscribeMessage);
    }

    /**
     * 新增微信订阅消息
     *
     * @param wxSubscribeMessage 微信订阅消息
     * @return 结果
     */
    @Override
    public int insertWxSubscribeMessage(WxSubscribeMessage wxSubscribeMessage) {
        return wxSubscribeMessageMapper.insertWxSubscribeMessage(wxSubscribeMessage);
    }

    /**
     * 修改微信订阅消息
     *
     * @param wxSubscribeMessage 微信订阅消息
     * @return 结果
     */
    @Override
    public int updateWxSubscribeMessage(WxSubscribeMessage wxSubscribeMessage) {
        return wxSubscribeMessageMapper.updateWxSubscribeMessage(wxSubscribeMessage);
    }

    /**
     * 删除微信订阅消息对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWxSubscribeMessageByIds(String ids) {
        return wxSubscribeMessageMapper.deleteWxSubscribeMessageByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除微信订阅消息信息
     *
     * @param id 微信订阅消息ID
     * @return 结果
     */
    @Override
    public int deleteWxSubscribeMessageById(Long id) {
        return wxSubscribeMessageMapper.deleteWxSubscribeMessageById(id);
    }
}

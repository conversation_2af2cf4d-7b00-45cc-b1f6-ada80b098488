package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.Attachment;
import space.lzhq.ph.domain.CardRefund;
import space.lzhq.ph.dto.mapper.AttachmentMapper;
import space.lzhq.ph.dto.mapper.CardRefundMapper;
import space.lzhq.ph.dto.request.CardRefundAgentUpdateDto;
import space.lzhq.ph.dto.request.CardRefundBankCardUpdateDto;
import space.lzhq.ph.dto.request.CardRefundPatientFormDto;
import space.lzhq.ph.dto.response.CardRefundDetailResponseDto;
import space.lzhq.ph.dto.response.CardRefundListResponseDto;
import space.lzhq.ph.enums.AttachmentMasterType;
import space.lzhq.ph.enums.AttachmentType;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.exception.*;
import space.lzhq.ph.service.AttachmentFileChecker;
import space.lzhq.ph.service.IAttachmentService;
import space.lzhq.ph.service.ICardRefundService;
import space.lzhq.ph.service.ICardRefundTimelineService;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@Validated
public class CardRefundServiceImpl
        extends ServiceImpl<space.lzhq.ph.mapper.CardRefundMapper, CardRefund>
        implements ICardRefundService {

    private final IAttachmentService attachmentService;
    private final AttachmentFileChecker attachmentFileChecker;
    private final ICardRefundTimelineService timelineService;

    public CardRefundServiceImpl(
            IAttachmentService attachmentService,
            AttachmentFileChecker attachmentFileChecker,
            ICardRefundTimelineService timelineService) {
        this.attachmentService = attachmentService;
        this.attachmentFileChecker = attachmentFileChecker;
        this.timelineService = timelineService;
    }

    @Override
    public CardRefundDetailResponseDto getDetailById(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 验证权限并获取申报记录
        CardRefund registration = validateAndGetRegistration(id, openId);

        // 2. 转换为详情DTO
        CardRefundDetailResponseDto detailDto =
                CardRefundMapper.INSTANCE.toDetailResponseDto(registration);

        // 3. 批量查询并设置附件信息
        populateAttachments(registration, detailDto);

        return detailDto;
    }

    /**
     * 安全地记录时间线事件，不影响主业务逻辑
     *
     * @param action 记录时间线的具体操作
     */
    private void safeRecordTimeline(Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            log.warn("记录时间线事件失败，但不影响主业务逻辑: {}", e.getMessage());
        }
    }

    /**
     * 根据申报记录确定操作者姓名
     *
     * @param registration 申报记录
     * @return 操作者姓名（代办人或患者本人）
     */
    private String getOperatorName(CardRefund registration) {
        // 如果是代办模式且代办人姓名不为空，返回代办人姓名
        if (Objects.equals(registration.getAgentFlag(), 1) && StringUtils.isNotBlank(registration.getAgentName())) {
            return registration.getAgentName();
        }
        // 否则返回患者本人姓名
        return registration.getPatientName();
    }

    /**
     * 验证权限并获取申报记录
     *
     * @param id     申报记录ID
     * @param openId 用户OpenID
     * @return 申报记录，如果不存在或权限不符则返回null
     */
    private CardRefund validateAndGetRegistration(String id, String openId) {
        CardRefund registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "CardRefund",
                    id
            );
        }

        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "CardRefund",
                    id,
                    openId
            );
        }
        return registration;
    }

    /**
     * 批量查询并填充附件信息到DTO
     *
     * @param registration 申报记录
     * @param detailDto    详情DTO
     */
    private void populateAttachments(CardRefund registration,
                                     CardRefundDetailResponseDto detailDto) {
        // 收集所有附件ID
        List<String> allAttachmentIds = collectAllAttachmentIds(registration);

        if (allAttachmentIds.isEmpty()) {
            return;
        }

        // 批量查询附件并创建映射
        Map<String, Attachment> attachmentMap =
                batchQueryAndMapAttachments(allAttachmentIds);

        // 设置各类附件到DTO
        setAttachmentsToDto(registration, detailDto, attachmentMap);
    }

    /**
     * 收集申报记录中的所有附件ID
     *
     * @param registration 申报记录
     * @return 附件ID列表
     */
    private List<String> collectAllAttachmentIds(CardRefund registration) {
        List<String> allAttachmentIds = new ArrayList<>();

        // 收集单个附件ID
        if (registration.getPatientIdCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getPatientIdCardAttachmentId());
        }
        if (registration.getAgentIdCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getAgentIdCardAttachmentId());
        }
        if (registration.getBankCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getBankCardAttachmentId());
        }

        return allAttachmentIds.stream()
                .distinct()
                .toList();
    }

    /**
     * 批量查询附件并创建ID到附件对象的映射
     *
     * @param attachmentIds 附件ID列表
     * @return 附件ID到附件对象的映射
     */
    private Map<String, Attachment> batchQueryAndMapAttachments(List<String> attachmentIds) {
        List<Attachment> allAttachments = attachmentService.listByIds(attachmentIds);
        return allAttachments.stream()
                .collect(Collectors.toMap(Attachment::getId, Function.identity()));
    }

    /**
     * 将附件信息设置到详情DTO中
     *
     * @param registration  申报记录
     * @param detailDto     详情DTO
     * @param attachmentMap 附件映射
     */
    private void setAttachmentsToDto(CardRefund registration,
                                     CardRefundDetailResponseDto detailDto,
                                     Map<String, Attachment> attachmentMap) {
        // 设置患者身份证附件
        setPatientIdCardAttachment(registration, detailDto, attachmentMap);

        // 设置代办人身份证附件
        setAgentIdCardAttachment(registration, detailDto, attachmentMap);

        // 设置银行卡附件
        setBankCardAttachment(registration, detailDto, attachmentMap);
    }

    /**
     * 设置患者身份证附件
     */
    private void setPatientIdCardAttachment(CardRefund registration,
                                            CardRefundDetailResponseDto detailDto,
                                            Map<String, Attachment> attachmentMap) {
        if (registration.getPatientIdCardAttachmentId() != null) {
            Attachment attachment =
                    attachmentMap.get(registration.getPatientIdCardAttachmentId());
            if (attachment != null) {
                detailDto.setPatientIdCardAttachment(
                        AttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }
    }

    /**
     * 设置代办人身份证附件
     */
    private void setAgentIdCardAttachment(CardRefund registration,
                                          CardRefundDetailResponseDto detailDto,
                                          Map<String, Attachment> attachmentMap) {
        if (registration.getAgentIdCardAttachmentId() != null) {
            Attachment attachment =
                    attachmentMap.get(registration.getAgentIdCardAttachmentId());
            if (attachment != null) {
                detailDto.setAgentIdCardAttachment(
                        AttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }
    }

    /**
     * 设置患者医保卡附件
     */
    private void setBankCardAttachment(CardRefund registration,
                                       CardRefundDetailResponseDto detailDto,
                                       Map<String, Attachment> attachmentMap) {
        if (registration.getBankCardAttachmentId() != null) {
            Attachment attachment =
                    attachmentMap.get(registration.getBankCardAttachmentId());
            if (attachment != null) {
                detailDto.setBankCardAttachment(
                        AttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Optional<CardRefund> save(
            @NotNull @Valid CardRefundPatientFormDto dto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) throws AttachmentValidationException {
        // 1. 验证身份证附件
        Attachment idCardAttachment = validateAttachment(
                dto.getPatientIdCardAttachmentId(),
                openId,
                AttachmentType.ID_CARD
        );

        // 2. 使用MapStruct映射DTO到实体
        CardRefund registration = CardRefundMapper.INSTANCE.fromPatientFormDto(dto);

        // 4. 设置必要的字段
        registration.setOpenId(openId);
        registration.setStatus(CardRefundStatus.DRAFT); // 默认草稿状态
        registration.setCreateTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        // 5. 保存申报主记录
        boolean saveSuccess = this.save(registration);
        if (!saveSuccess) {
            throw new DataUpdateFailedException("保存余额清退登记失败", "保存申报记录");
        }

        // 6. 更新附件记录的申报主表ID
        updateAttachmentMasterInfo(idCardAttachment, registration.getId());

        // 7. 记录创建事件到时间线
        safeRecordTimeline(() -> timelineService.recordRegistrationCreated(
                registration.getId(),
                getOperatorName(registration)
        ));

        return Optional.of(registration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardRefund clearAgentFlag(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        CardRefund registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "CardRefund",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "CardRefund",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许用户编辑
        if (!registration.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许修改：" + registration.getStatus().getDescription(),
                    "CardRefund",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 清除代办标志
        boolean updateSuccess = this.getBaseMapper().clearAgent(id) == 1;
        if (!updateSuccess) {
            throw new DataUpdateFailedException("清除代办标志失败，请重试", "清除代办标志", id);
        }

        // 5. 记录清除代办人事件到时间线
        safeRecordTimeline(() -> timelineService.recordAgentInfoCleared(
                id,
                getOperatorName(registration)
        ));

        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardRefund updateAgent(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid CardRefundAgentUpdateDto agentDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        CardRefund registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "CardRefund",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "CardRefund",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许用户编辑
        if (!registration.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许修改：" + registration.getStatus().getDescription(),
                    "CardRefund",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 验证代办人身份证附件
        Attachment agentIdCardAttachment = validateAttachment(
                agentDto.getAgentIdCardAttachmentId(),
                openId,
                AttachmentType.ID_CARD,
                id  // 允许附件已经关联到当前申报记录
        );

        // 5. 更新代办人信息
        registration.setAgentFlag(1);
        registration.setAgentName(agentDto.getAgentName());
        registration.setAgentIdCardNo(agentDto.getAgentIdCardNo());
        registration.setAgentIdCardAttachmentId(agentDto.getAgentIdCardAttachmentId());
        registration.setAgentMobile(agentDto.getAgentMobile());
        registration.setAgentRelation(agentDto.getAgentRelation());
        registration.setUpdateTime(LocalDateTime.now());

        // 6. 保存更新后的记录
        boolean updateSuccess = this.updateById(registration);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新代办人信息失败，请重试", "更新代办人信息", id);
        }

        // 7. 更新附件记录的申报主表ID
        updateAttachmentMasterInfo(agentIdCardAttachment, id);

        // 8. 记录设置代办人事件到时间线
        safeRecordTimeline(() -> timelineService.recordAgentInfoSet(
                id,
                getOperatorName(registration),
                agentDto.getAgentName()
        ));

        return registration;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardRefund updateBankCard(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid CardRefundBankCardUpdateDto bankCardDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        CardRefund registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "CardRefund",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "CardRefund",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许用户编辑
        if (!registration.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许修改：" + registration.getStatus().getDescription(),
                    "CardRefund",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 验证银行卡附件
        Attachment bankCardAttachment = validateAttachment(
                bankCardDto.getBankCardAttachmentId(),
                openId,
                AttachmentType.BANK_CARD,
                id  // 允许附件已经关联到当前申报记录
        );

        // 5. 更新银行卡信息
        registration.setBankCardAttachmentId(bankCardDto.getBankCardAttachmentId());
        registration.setBankCardNo(bankCardDto.getBankCardNo());
        registration.setUpdateTime(LocalDateTime.now());

        // 6. 保存更新后的记录
        boolean updateSuccess = this.updateById(registration);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新银行卡信息失败，请重试", "更新银行卡信息", id);
        }

        // 7. 更新附件记录的申报主表ID
        updateAttachmentMasterInfo(bankCardAttachment, id);

        // 8. 记录上传银行卡事件到时间线
        safeRecordTimeline(() -> timelineService.recordBankCardUploaded(
                id,
                getOperatorName(registration)
        ));

        return registration;
    }

    /**
     * 验证附件是否满足业务规则
     *
     * @param attachmentId 附件ID
     * @param openId       用户OpenID
     * @param expectedType 期望的附件类型
     * @return 附件记录
     * @throws AttachmentValidationException 当附件不满足业务规则时抛出
     */
    private Attachment validateAttachment(
            String attachmentId,
            String openId,
            AttachmentType expectedType
    ) {
        return validateAttachment(attachmentId, openId, expectedType, null);
    }

    /**
     * 验证附件是否满足业务规则
     *
     * @param attachmentId   附件ID
     * @param openId         用户OpenID
     * @param expectedType   期望的附件类型
     * @param registrationId 允许附件已经关联到的申报记录ID
     * @return 附件记录
     * @throws AttachmentValidationException 当附件不满足业务规则时抛出
     */
    private Attachment validateAttachment(
            String attachmentId,
            String openId,
            AttachmentType expectedType,
            String registrationId
    ) {
        String typeName = expectedType.getDescription();

        // 1. 检查附件是否存在
        Attachment attachment = attachmentService.getById(attachmentId);
        if (attachment == null) {
            throw new AttachmentValidationException(
                    typeName + "附件不存在，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 2. 检查附件是否属于当前用户
        if (!openId.equals(attachment.getOpenId())) {
            throw new AttachmentValidationException(
                    typeName + "附件不属于当前用户，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 3. 检查附件类型是否正确
        if (!expectedType.equals(attachment.getType())) {
            throw new AttachmentValidationException(
                    typeName + "附件类型不正确，期望: " + expectedType + "，实际: " + attachment.getType(),
                    attachmentId,
                    typeName
            );
        }

        // 4. 检查附件是否已被使用
        if (attachment.getMasterId() != null && !attachment.getMasterId().equals(registrationId)) {
            throw new AttachmentValidationException(
                    typeName + "附件已被使用，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 5. 检查文件是否真实存在
        if (!attachmentFileChecker.isFileExists(attachment.getFileUrl())) {
            throw new AttachmentValidationException(
                    typeName + "附件对应的文件不存在，文件URL: " + attachment.getFileUrl(),
                    attachmentId,
                    typeName
            );
        }

        return attachment;
    }

    /**
     * 更新附件记录中的申报主表ID
     *
     * @param attachment 附件记录
     * @param masterId   申报主表ID
     */
    private void updateAttachmentMasterInfo(Attachment attachment, String masterId) {
        if (Objects.equals(masterId, attachment.getMasterId())) {
            return;
        }

        boolean updateSuccess = attachmentService.updateMasterInfo(attachment.getId(), masterId, AttachmentMasterType.CARD_REFUND_APPLICATION);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新附件关联的申报ID失败", "更新附件关联", attachment.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardRefund submitRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        CardRefund registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "CardRefund",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "CardRefund",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许提交（仅DRAFT和REJECTED状态可以提交）
        if (registration.getStatus() != CardRefundStatus.DRAFT
                && registration.getStatus() != CardRefundStatus.REJECTED) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许提交：" + registration.getStatus().getDescription(),
                    "CardRefund",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 验证申报信息完整性
        validateRegistrationCompleteness(registration);

        // 5. 记录状态变更前的状态
        CardRefundStatus oldStatus = registration.getStatus();

        // 6. 更新申报状态为SUBMITTED，设置提交时间
        registration.setStatus(CardRefundStatus.SUBMITTED);
        registration.setSubmitTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        // 7. 保存更新后的记录
        boolean updateSuccess = this.updateById(registration);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("提交余额清退失败，请重试", "提交申报记录", id);
        }

        // 8. 记录提交申报事件到时间线
        safeRecordTimeline(() -> timelineService.recordRegistrationSubmitted(
                id,
                getOperatorName(registration),
                oldStatus
        ));

        return registration;
    }

    /**
     * 验证余额清退记录的完整性
     *
     * @param registration 申报记录
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validateRegistrationCompleteness(CardRefund registration) {
        String id = registration.getId();

        // 验证申报人信息是否完整
        validatePatientInfo(registration, id);

        // 验证代办人信息是否完整（如果是代办申请）
        validateAgentInfo(registration, id);

        // 验证银行卡附件
        validateBankCardAttachment(registration, id);
    }

    /**
     * 验证申报人信息完整性
     *
     * @param registration 申报记录
     * @param id           申报记录ID
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validatePatientInfo(CardRefund registration, String id) {
        validateRequiredStringField(registration.getPatientIdCardAttachmentId(), "申报人身份证附件未上传", "patientIdCardAttachmentId", id);
        validateRequiredStringField(registration.getPatientIdCardNo(), "申报人身份证号未填写", "patientIdCardNo", id);
        validateRequiredStringField(registration.getPatientName(), "申报人姓名未填写", "patientName", id);
        validateRequiredStringField(registration.getPatientMobile(), "申报人手机号未填写", "patientMobile", id);
    }

    /**
     * 验证代办人信息完整性（如果是代办申请）
     *
     * @param registration 申报记录
     * @param id           申报记录ID
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validateAgentInfo(CardRefund registration, String id) {
        if (registration.getAgentFlag() != null && registration.getAgentFlag() == 1) {
            validateRequiredStringField(registration.getAgentName(), "代办人姓名未填写", "agentName", id);
            validateRequiredStringField(registration.getAgentIdCardNo(), "代办人身份证号未填写", "agentIdCardNo", id);
            validateRequiredStringField(registration.getAgentIdCardAttachmentId(), "代办人身份证附件未上传", "agentIdCardAttachmentId", id);
            validateRequiredStringField(registration.getAgentMobile(), "代办人手机号未填写", "agentMobile", id);

            if (registration.getAgentRelation() == null) {
                throw new SubmissionValidationException("代办人与患者关系未选择", "agentRelation", id);
            }
        }
    }

    private void validateBankCardAttachment(CardRefund registration, String id) {
        validateRequiredStringField(registration.getBankCardAttachmentId(), "银行卡附件未上传", "bankCardAttachmentId", id);
        validateRequiredStringField(registration.getBankCardNo(), "银行卡号未填写", "bankCardNo", id);
    }

    /**
     * 验证必填字符串字段
     *
     * @param value          字段值
     * @param errorMessage   错误消息
     * @param fieldName      字段名
     * @param registrationId 申报记录ID
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validateRequiredStringField(String value, String errorMessage, String fieldName, String registrationId) {
        if (value == null || value.trim().isEmpty()) {
            throw new SubmissionValidationException(errorMessage, fieldName, registrationId);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<CardRefundListResponseDto> getUserRegistrationList(
            @NotBlank @Size(min = 16, max = 28) String openId,
            CardRefundStatus status
    ) {
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CardRefund> queryWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        queryWrapper.eq(CardRefund::getOpenId, openId);

        if (status != null) {
            queryWrapper.eq(CardRefund::getStatus, status);
        }

        queryWrapper.orderByDesc(CardRefund::getCreateTime);

        List<CardRefund> registrations = this.list(queryWrapper);

        return registrations.stream()
                .map(CardRefundMapper.INSTANCE::toListResponseDto)
                .toList();
    }

}

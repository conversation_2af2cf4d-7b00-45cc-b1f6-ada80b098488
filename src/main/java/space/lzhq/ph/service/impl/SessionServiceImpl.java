package space.lzhq.ph.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Session;
import space.lzhq.ph.mapper.SessionMapper;
import space.lzhq.ph.service.ISessionService;

import java.util.List;

/**
 * 微信会话Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-05-24
 */
@Service
public class SessionServiceImpl implements ISessionService {
    
    @Autowired
    private SessionMapper sessionMapper;

    /**
     * 查询微信会话
     *
     * @param id 微信会话ID
     * @return 微信会话
     */
    @Override
    public Session selectSessionById(String id) {
        return sessionMapper.selectSessionById(id);
    }

    @Override
    public Session selectSessionByOpenId(String openId) {
        Session session = new Session();
        session.setOpenId(openId);
        List<Session> list = selectSessionList(session);
        return CollectionUtil.getFirst(list);
    }

    /**
     * 查询微信会话列表
     *
     * @param Session 微信会话
     * @return 微信会话
     */
    @Override
    public List<Session> selectSessionList(Session session) {
        return sessionMapper.selectSessionList(session);
    }

    /**
     * 新增微信会话
     *
     * @param Session 微信会话
     * @return 结果
     */
    @Override
    public int insertSession(Session session) {
        return sessionMapper.insertSession(session);
    }

    /**
     * 修改微信会话
     *
     * @param Session 微信会话
     * @return 结果
     */
    @Override
    public int updateSession(Session session) {
        return sessionMapper.updateSession(session);
    }

    /**
     * 删除微信会话对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSessionByIds(String ids) {
        return sessionMapper.deleteSessionByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除微信会话信息
     *
     * @param id 微信会话ID
     * @return 结果
     */
    @Override
    public int deleteSessionById(String id) {
        return sessionMapper.deleteSessionById(id);
    }

    @Override
    public int deleteSessionByOpenId(String openId) {
        return sessionMapper.deleteSessionByOpenId(openId);
    }
}
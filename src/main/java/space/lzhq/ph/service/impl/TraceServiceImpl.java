package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Trace;
import space.lzhq.ph.mapper.TraceMapper;
import space.lzhq.ph.service.ITraceService;

import java.time.LocalDateTime;

@Service
public class TraceServiceImpl extends ServiceImpl<TraceMapper, Trace> implements ITraceService {

    @Override
    public void traceMipWxOrder(Long mipWxOrderId, String message) {
        Trace trace = new Trace();
        trace.setTargetId("MipWxOrder#" + mipWxOrderId);
        trace.setMessage(message);
        trace.setCreateTime(LocalDateTime.now());
        save(trace);
    }

    @Override
    public void traceReservation(Long reservationId, String message) {
        Trace trace = new Trace();
        trace.setTargetId("Reservation#" + reservationId);
        trace.setMessage(message);
        trace.setCreateTime(LocalDateTime.now());
        save(trace);
    }

}

package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.MedicalRecordApplicationTimeline;
import space.lzhq.ph.dto.response.MedicalRecordApplicationTimelineDto;
import space.lzhq.ph.enums.MedicalRecordApplicationEventType;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.enums.TimelineOperatorType;
import space.lzhq.ph.exception.TimelineRecordException;
import space.lzhq.ph.mapper.MedicalRecordApplicationTimelineMapper;
import space.lzhq.ph.service.IMedicalRecordApplicationTimelineService;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 病历复印申请时间线服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class MedicalRecordApplicationTimelineServiceImpl
        extends ServiceImpl<MedicalRecordApplicationTimelineMapper, MedicalRecordApplicationTimeline>
        implements IMedicalRecordApplicationTimelineService {

    /**
     * 获取当前代理对象，用于调用事务方法
     */
    private IMedicalRecordApplicationTimelineService getSelf() {
        return (IMedicalRecordApplicationTimelineService) AopContext.currentProxy();
    }

    @Override
    public List<MedicalRecordApplicationTimelineDto> getApplicationTimeline(String applicationId, Boolean includeDetails) {
        List<MedicalRecordApplicationTimeline> timelines =
                getBaseMapper().selectTimelineByApplicationId(applicationId, includeDetails);

        return timelines.stream()
                .map(this::convertToDto)
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTimelineEvent(String applicationId, MedicalRecordApplicationEventType eventType,
                                    String eventDescription, TimelineOperatorType operatorType,
                                    String operatorName) {
        MedicalRecordApplicationTimeline timeline = new MedicalRecordApplicationTimeline(
                applicationId, eventType, eventDescription, operatorType, operatorName);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录时间线事件失败，申请ID: {}, 事件类型: {}, 错误信息: {}",
                    applicationId, eventType, e.getMessage(), e);
            throw new TimelineRecordException("记录时间线事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordStatusChangeEvent(String applicationId, MedicalRecordApplicationEventType eventType,
                                        String eventDescription, TimelineOperatorType operatorType,
                                        String operatorName, MedicalRecordApplicationStatus oldStatus,
                                        MedicalRecordApplicationStatus newStatus) {
        MedicalRecordApplicationTimeline timeline = new MedicalRecordApplicationTimeline(
                applicationId, eventType, eventDescription, operatorType, operatorName, oldStatus, newStatus);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录状态变更事件失败，申请ID: {}, 事件类型: {}, 状态变更: {} -> {}, 错误信息: {}",
                    applicationId, eventType, oldStatus, newStatus, e.getMessage(), e);
            throw new TimelineRecordException("记录状态变更事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTimelineEventWithData(String applicationId, MedicalRecordApplicationEventType eventType,
                                            String eventDescription, TimelineOperatorType operatorType,
                                            String operatorName, Map<String, Serializable> additionalData) {
        MedicalRecordApplicationTimeline timeline = new MedicalRecordApplicationTimeline(
                applicationId, eventType, eventDescription, operatorType, operatorName, additionalData);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录带数据的时间线事件失败，申请ID: {}, 事件类型: {}, 附加数据: {}, 错误信息: {}",
                    applicationId, eventType, additionalData, e.getMessage(), e);
            throw new TimelineRecordException("记录时间线事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void recordApplicationCreated(String applicationId, String operatorName) {
        getSelf().recordTimelineEvent(applicationId, MedicalRecordApplicationEventType.APPLICATION_CREATED,
                "创建病历复印申请", TimelineOperatorType.APPLICANT, operatorName);
    }

    @Override
    public void recordApplicationSubmitted(String applicationId, String operatorName,
                                           MedicalRecordApplicationStatus oldStatus) {
        getSelf().recordStatusChangeEvent(applicationId, MedicalRecordApplicationEventType.APPLICATION_SUBMITTED,
                "提交病历复印申请", TimelineOperatorType.APPLICANT, operatorName,
                oldStatus, MedicalRecordApplicationStatus.SUBMITTED);
    }

    @Override
    public void recordApplicationApproved(String applicationId, String operatorName,
                                          MedicalRecordApplicationStatus oldStatus) {
        getSelf().recordStatusChangeEvent(applicationId, MedicalRecordApplicationEventType.APPLICATION_APPROVED,
                "审核通过病历复印申请", TimelineOperatorType.REVIEWER, operatorName,
                oldStatus, MedicalRecordApplicationStatus.APPROVED);
    }

    @Override
    public void recordApplicationRejected(String applicationId, String operatorName,
                                          MedicalRecordApplicationStatus oldStatus, String rejectReason) {
        Map<String, Serializable> data = new HashMap<>();
        data.put("rejectReason", rejectReason);

        // 同时记录状态变更
        getSelf().recordStatusChangeEvent(applicationId, MedicalRecordApplicationEventType.APPLICATION_REJECTED,
                "驳回病历复印申请", TimelineOperatorType.REVIEWER, operatorName,
                oldStatus, MedicalRecordApplicationStatus.REJECTED);
    }

    @Override
    public void recordApplicationCompleted(String applicationId, String operatorName,
                                           MedicalRecordApplicationStatus oldStatus) {
        getSelf().recordStatusChangeEvent(applicationId, MedicalRecordApplicationEventType.APPLICATION_COMPLETED,
                "完成病历复印申请", TimelineOperatorType.ADMIN, operatorName,
                oldStatus, MedicalRecordApplicationStatus.COMPLETED);
    }

    @Override
    public void recordAttachmentUploaded(String applicationId, String operatorName, String attachmentType) {
        Map<String, Serializable> data = new HashMap<>();
        data.put("attachmentType", attachmentType);

        getSelf().recordTimelineEventWithData(applicationId, MedicalRecordApplicationEventType.ATTACHMENT_UPLOADED,
                "上传附件：" + attachmentType, TimelineOperatorType.APPLICANT,
                operatorName, data);
    }

    @Override
    public void recordMailingInfoSet(String applicationId, String operatorName, String mailingAddress) {
        Map<String, Serializable> data = new HashMap<>();
        data.put("mailingAddress", mailingAddress);

        getSelf().recordTimelineEventWithData(applicationId, MedicalRecordApplicationEventType.MAILING_INFO_SET,
                "设置邮寄地址：" + mailingAddress, TimelineOperatorType.APPLICANT,
                operatorName, data);
    }

    @Override
    public void recordAgentInfoCleared(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName
    ) {
        getSelf().recordTimelineEvent(applicationId, MedicalRecordApplicationEventType.AGENT_INFO_CLEARED,
                "清除代办人信息", TimelineOperatorType.APPLICANT, operatorName);
    }

    @Override
    public void recordAgentInfoSet(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            @NotBlank String agentName
    ) {
        Map<String, Serializable> data = new HashMap<>();
        data.put("agentName", agentName);

        getSelf().recordTimelineEventWithData(applicationId, MedicalRecordApplicationEventType.AGENT_INFO_SET,
                "设置代办人信息：" + agentName, TimelineOperatorType.AGENT,
                operatorName, data);
    }

    /**
     * 将时间线实体转换为DTO
     */
    private MedicalRecordApplicationTimelineDto convertToDto(MedicalRecordApplicationTimeline timeline) {
        MedicalRecordApplicationTimelineDto dto = new MedicalRecordApplicationTimelineDto();
        dto.setId(timeline.getId());
        dto.setEventType(timeline.getEventType());
        dto.setEventDescription(timeline.getEventDescription());
        dto.setOperatorType(timeline.getOperatorType());
        dto.setOperatorName(timeline.getOperatorName());
        dto.setOldStatus(timeline.getOldStatus());
        dto.setNewStatus(timeline.getNewStatus());
        dto.setEventTime(timeline.getEventTime());
        dto.setAdditionalData(timeline.getAdditionalData());

        // 设置图标和颜色
        dto.setIconAndColor();

        return dto;
    }

    @Override
    public void recordApplicationPaid(String applicationId, String operatorName, MedicalRecordApplicationStatus oldStatus) {
        getSelf().recordStatusChangeEvent(
                applicationId,
                MedicalRecordApplicationEventType.APPLICATION_PAID,
                "标记已支付",
                TimelineOperatorType.ADMIN,
                operatorName,
                oldStatus,
                MedicalRecordApplicationStatus.PAID
        );
    }

    @Override
    public void recordApplicationDelivered(String applicationId, String operatorName, 
                                         MedicalRecordApplicationStatus oldStatus, String trackingNumber) {
        getSelf().recordStatusChangeEvent(
                applicationId,
                MedicalRecordApplicationEventType.APPLICATION_DELIVERED,
                "邮寄完成，快递单号：" + trackingNumber,
                TimelineOperatorType.ADMIN,
                operatorName,
                oldStatus,
                MedicalRecordApplicationStatus.DELIVERED
        );
    }

    @Override
    public void recordApplicationDeleted(String applicationId, String operatorName, MedicalRecordApplicationStatus oldStatus) {
        getSelf().recordStatusChangeEvent(
                applicationId,
                MedicalRecordApplicationEventType.APPLICATION_DELETED,
                "删除申请记录",
                TimelineOperatorType.ADMIN,
                operatorName,
                oldStatus,
                null
        );
    }
} 
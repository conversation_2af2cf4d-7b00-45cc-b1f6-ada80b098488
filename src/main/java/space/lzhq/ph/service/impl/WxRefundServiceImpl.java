package space.lzhq.ph.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryResult;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.mospital.dongruan.pay.UnifiedOrderForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.common.Constants;
import space.lzhq.ph.common.PaymentKit;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.WxPayment;
import space.lzhq.ph.domain.WxRefund;
import space.lzhq.ph.domain.ZhuyuanRefundForm;
import space.lzhq.ph.mapper.WxRefundMapper;
import space.lzhq.ph.service.IWxRefundService;

import java.util.Date;
import java.util.List;

@Service
public class WxRefundServiceImpl implements IWxRefundService {

    private final WxRefundMapper wxRefundMapper;

    @Autowired
    public WxRefundServiceImpl(WxRefundMapper wxRefundMapper) {
        this.wxRefundMapper = wxRefundMapper;
    }

    /**
     * 查询退款记录
     *
     * @param id 退款记录ID
     * @return 退款记录
     */
    @Override
    public WxRefund selectWxRefundById(Long id) {
        return wxRefundMapper.selectWxRefundById(id);
    }

    @Override
    public WxRefund selectWxRefundByZyRefundNo(String zyRefundNo) {
        WxRefund wxRefund = new WxRefund();
        wxRefund.setZyRefundNo(zyRefundNo);
        List<WxRefund> list = selectWxRefundList(wxRefund);
        return CollUtil.getFirst(list);
    }

    /**
     * 查询退款记录列表
     *
     * @param wxRefund 退款记录
     * @return 退款记录
     */
    @Override
    public List<WxRefund> selectWxRefundList(WxRefund wxRefund) {
        return wxRefundMapper.selectWxRefundList(wxRefund);
    }

    @Override
    public Long sumAmount(WxRefund wxRefund) {
        return wxRefundMapper.sumAmount(wxRefund);
    }

    /**
     * 新增退款记录
     *
     * @param wxRefund 退款记录
     * @return 结果
     */
    @Override
    public int insertWxRefund(WxRefund wxRefund) {
        wxRefund.setCreateTime(DateUtils.getNowDate());
        return wxRefundMapper.insertWxRefund(wxRefund);
    }

    /**
     * 修改退款记录
     *
     * @param wxRefund 退款记录
     * @return 结果
     */
    @Override
    public int updateWxRefund(WxRefund wxRefund) {
        return wxRefundMapper.updateWxRefund(wxRefund);
    }

    /**
     * 删除退款记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWxRefundByIds(String ids) {
        return wxRefundMapper.deleteWxRefundByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除退款记录信息
     *
     * @param id 退款记录ID
     * @return 结果
     */
    @Override
    public int deleteWxRefundById(Long id) {
        return wxRefundMapper.deleteWxRefundById(id);
    }

    @Override
    public WxRefund createAndSave(WxPayment wxPayment, ZhuyuanRefundForm zhuyuanRefundForm) {
        WxRefund wxRefund = new WxRefund();
        wxRefund.setType(ServiceType.ZY.name());
        wxRefund.setOpenid(wxPayment.getOpenid());
        wxRefund.setPatientNo(wxPayment.getPatientNo());
        wxRefund.setJzCardNo(wxPayment.getJzCardNo());
        wxRefund.setCardNo(wxPayment.getCardNo());
        wxRefund.setIdCardNo(wxPayment.getIdCardNo());
        wxRefund.setZhuyuanNo(wxPayment.getZhuyuanNo());
        wxRefund.setTotalAmount(wxPayment.getAmount());
        wxRefund.setAmount(PaymentKit.INSTANCE.yuanToFen(zhuyuanRefundForm.getRefundAmount()));
        wxRefund.setZyPayNo(wxPayment.getZyPayNo());
        wxRefund.setWxPayNo(wxPayment.getWxPayNo());
        wxRefund.setZyRefundNo(PaymentKit.INSTANCE.newOrderId(ServiceType.ZY));
        wxRefund.setWxRefundNo(null);
        wxRefund.setHisTradeStatus(Constants.REFUND_OK);
        wxRefund.setWxTradeStatus(null);
        wxRefund.setManualRefundState(Constants.MANUAL_INIT);
        wxRefund.setCreateTime(new Date());
        if (insertWxRefund(wxRefund) == 1) {
            return wxRefund;
        } else {
            return null;
        }
    }

    public WxRefund createAndSave(WxPayment wxPayment, long refundAmount) {
        WxRefund wxRefund = new WxRefund();
        wxRefund.setType(wxPayment.getType());
        wxRefund.setOpenid(wxPayment.getOpenid());
        wxRefund.setPatientNo(wxPayment.getPatientNo());
        wxRefund.setJzCardNo(wxPayment.getJzCardNo());
        wxRefund.setCardNo(wxPayment.getCardNo());
        wxRefund.setIdCardNo(wxPayment.getIdCardNo());
        wxRefund.setZhuyuanNo(wxPayment.getZhuyuanNo());
        wxRefund.setTotalAmount(wxPayment.getAmount());
        wxRefund.setAmount(refundAmount);
        wxRefund.setZyPayNo(wxPayment.getZyPayNo());
        wxRefund.setWxPayNo(wxPayment.getWxPayNo());
        wxRefund.setZyRefundNo(UnifiedOrderForm.Companion.newOrderNo());
        wxRefund.setWxRefundNo(null);
        wxRefund.setHisTradeStatus(Constants.REFUND_OK);
        wxRefund.setWxTradeStatus(null);
        wxRefund.setManualRefundState(Constants.MANUAL_INIT);
        wxRefund.setCreateTime(new Date());
        if (insertWxRefund(wxRefund) == 1) {
            return wxRefund;
        } else {
            return null;
        }
    }

    /**
     * 微信退款时，更新退款记录
     *
     * @param wxRefund     退款记录
     * @param refundRecord 微信退款结果
     */
    public boolean updateOnWXRefund(WxRefund wxRefund, WxPayRefundQueryResult.RefundRecord refundRecord) {
        if (wxRefund.getManualRefundState() == Constants.MANUAL_REQUESTED) {
            wxRefund.setManualRefundState(Constants.MANUAL_APPROVED);
        }

        wxRefund.setWxRefundNo(refundRecord.getRefundId());
        wxRefund.setWxTradeStatus("SUCCESS".equals(refundRecord.getRefundStatus()) ? Constants.REFUND_OK :
                refundRecord.getRefundStatus());
        return updateWxRefund(wxRefund) == 1;
    }

}

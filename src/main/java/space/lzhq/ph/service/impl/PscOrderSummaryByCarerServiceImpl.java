package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PscOrderSummaryByCarer;
import space.lzhq.ph.domain.PscOrderSummaryByCarerAndDepartment;
import space.lzhq.ph.mapper.PscOrderSummaryByCarerMapper;
import space.lzhq.ph.service.IPscOrderSummaryByCarerService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PscOrderSummaryByCarerServiceImpl implements IPscOrderSummaryByCarerService {

    private final PscOrderSummaryByCarerMapper mapper;

    public PscOrderSummaryByCarerServiceImpl(PscOrderSummaryByCarerMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<PscOrderSummaryByCarer> summary(LocalDateTime minCreateTime, LocalDateTime maxCreateTime) {
        return mapper.summary(minCreateTime, maxCreateTime);
    }

    @Override
    public List<PscOrderSummaryByCarerAndDepartment> summaryByCarerAndDepartment(
            LocalDateTime minCreateTime,
            LocalDateTime maxCreateTime,
            String carerEmployeeNo
    ) {
        return mapper.summaryByCarerAndDepartment(minCreateTime, maxCreateTime, carerEmployeeNo);
    }
    
}

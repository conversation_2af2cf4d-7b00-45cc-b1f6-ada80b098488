package space.lzhq.ph.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.common.Constants;
import space.lzhq.ph.domain.AlipayPayment;
import space.lzhq.ph.domain.AlipayRefund;
import space.lzhq.ph.mapper.AlipayPaymentMapper;
import space.lzhq.ph.service.IAlipayPaymentService;
import space.lzhq.ph.service.IAlipayRefundService;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付宝支付Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-09-09
 */
@Service
public class AlipayPaymentServiceImpl implements IAlipayPaymentService {
    @Autowired
    private AlipayPaymentMapper alipayPaymentMapper;

    @Autowired
    private IAlipayRefundService alipayRefundService;

    /**
     * 查询支付宝支付
     *
     * @param id 支付宝支付ID
     * @return 支付宝支付
     */
    @Override
    public AlipayPayment selectAlipayPaymentById(Long id) {
        return alipayPaymentMapper.selectAlipayPaymentById(id);
    }

    @Override
    public AlipayPayment selectAlipayPaymentByOutTradeNo(String outTradeNo) {
        return alipayPaymentMapper.selectAlipayPaymentByOutTradeNo(outTradeNo);
    }

    @Override
    public List<AlipayPayment> selectListForRefund(String cardNo, Date minCreateTime, Date maxCreateTime) {
        AlipayPayment payment = new AlipayPayment();
        payment.setCardNo(cardNo);
        payment.setTradeStatus(Constants.TRADE_SUCCESS);
        payment.setHisTradeStatus(Constants.RECHARGE_OK);
        payment.setParams(
                Dict.create()
                        .set("beginCreateTime", DateUtil.formatDateTime(minCreateTime))
                        .set("endCreateTime", DateUtil.formatDateTime(maxCreateTime))
        );
        return selectAlipayPaymentList(payment);
    }

    /**
     * 查询支付宝支付列表
     *
     * @param alipayPayment 支付宝支付
     * @return 支付宝支付
     */
    @Override
    public List<AlipayPayment> selectAlipayPaymentList(AlipayPayment alipayPayment) {
        return alipayPaymentMapper.selectAlipayPaymentList(alipayPayment);
    }

    /**
     * 新增支付宝支付
     *
     * @param alipayPayment 支付宝支付
     * @return 结果
     */
    @Override
    public int insertAlipayPayment(AlipayPayment alipayPayment) {
        alipayPayment.setCreateTime(DateUtils.getNowDate());
        return alipayPaymentMapper.insertAlipayPayment(alipayPayment);
    }

    /**
     * 修改支付宝支付
     *
     * @param alipayPayment 支付宝支付
     * @return 结果
     */
    @Override
    public int updateAlipayPayment(AlipayPayment alipayPayment) {
        return alipayPaymentMapper.updateAlipayPayment(alipayPayment);
    }

    /**
     * 删除支付宝支付对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteAlipayPaymentByIds(String ids) {
        return alipayPaymentMapper.deleteAlipayPaymentByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除支付宝支付信息
     *
     * @param id 支付宝支付ID
     * @return 结果
     */
    @Override
    public int deleteAlipayPaymentById(Long id) {
        return alipayPaymentMapper.deleteAlipayPaymentById(id);
    }

    @Override
    public boolean updateOnPay(AlipayPayment payment, AlipayTradeQueryResponse queryOrderResponse) {
        payment.setTradeNo(queryOrderResponse.getTradeNo());
        payment.setTradeStatus(queryOrderResponse.getTradeStatus());
        return updateAlipayPayment(payment) == 1;
    }

    @Override
    public Long sumAmount(AlipayPayment payment) {
        return alipayPaymentMapper.sumAmount(payment);
    }

    @Override
    public Long calculateNetInAmount(LocalDate date) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTime = date.atStartOfDay().format(dateTimeFormatter);
        String endTime = date.atTime(LocalTime.MAX).format(dateTimeFormatter);

        Map<String, Object> params = new HashMap<>();
        params.put("beginCreateTime", startTime);
        params.put("endCreateTime", endTime);

        AlipayPayment payment = new AlipayPayment();
        payment.setParams(params);
        Long payAmount = sumAmount(payment);
        payAmount = payAmount == null ? 0 : payAmount;

        AlipayRefund refund = new AlipayRefund();
        refund.setParams(params);
        Long refundAmount = alipayRefundService.sumAmount(refund);
        refundAmount = refundAmount == null ? 0 : refundAmount;

        return payAmount - refundAmount;
    }
}

package space.lzhq.ph.service.impl;

import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import space.lzhq.ph.ext.FileUploader;
import space.lzhq.ph.service.AttachmentFileDeleter;

@Slf4j
@Service
public class DefaultAttachmentFileDeleter implements AttachmentFileDeleter {

    @Override
    public boolean deleteFile(@NotBlank String fileUrl) {
        try {
            boolean deleted = FileUploader.INSTANCE.deleteFile(fileUrl);
            if (!deleted) {
                log.warn("Deletion of attachment file [{}] returned false", fileUrl);
            }
            return deleted;
        } catch (Exception ex) {
            log.error("Unexpected error while deleting attachment file [{}]", fileUrl, ex);
            return false;
        }
    }

}

package space.lzhq.ph.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.mospital.dongruan.pay.UnifiedPayOrderQueryResult;
import org.mospital.dongruan.pay.UnifiedPayStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.common.Constants;
import space.lzhq.ph.common.PaymentKit;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.MedicalRecordApplication;
import space.lzhq.ph.domain.Patient;
import space.lzhq.ph.domain.WxPayment;
import space.lzhq.ph.domain.WxRefund;
import space.lzhq.ph.mapper.WxPaymentMapper;
import space.lzhq.ph.service.IWxPaymentService;
import space.lzhq.ph.service.IWxRefundService;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 充值记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
@Service
public class WxPaymentServiceImpl implements IWxPaymentService {

    private final WxPaymentMapper wxPaymentMapper;

    private final IWxRefundService wxRefundService;

    @Autowired
    public WxPaymentServiceImpl(WxPaymentMapper wxPaymentMapper, IWxRefundService wxRefundService) {
        this.wxPaymentMapper = wxPaymentMapper;
        this.wxRefundService = wxRefundService;
    }

    /**
     * 查询充值记录
     *
     * @param id 充值记录ID
     * @return 充值记录
     */
    @Override
    public WxPayment selectWxPaymentById(Long id) {
        return wxPaymentMapper.selectWxPaymentById(id);
    }


    /**
     * 查找充值记录
     *
     * @param zyPayNo 掌医订单号
     */
    @Override
    public WxPayment selectWxPaymentByZyPayNo(String zyPayNo) {
        return wxPaymentMapper.selectWxPaymentByZyPayNo(zyPayNo);
    }

    /**
     * 查询充值记录列表
     *
     * @param wxPayment 充值记录
     * @return 充值记录
     */
    @Override
    public List<WxPayment> selectWxPaymentList(WxPayment wxPayment) {
        return wxPaymentMapper.selectWxPaymentList(wxPayment);
    }

    /**
     * 查询可退款的充值订单
     *
     * @param cardNo        就诊卡号
     * @param minCreateTime 最小下单时间
     * @param maxCreateTime 最大下单时间
     */
    @Override
    public List<WxPayment> selectListForRefund(String cardNo, Date minCreateTime, Date maxCreateTime) {
        WxPayment wxPayment = new WxPayment();
        wxPayment.setCardNo(cardNo);
        wxPayment.setWxTradeStatus(Constants.PAY_OK);
        wxPayment.setHisTradeStatus(Constants.RECHARGE_OK);
        wxPayment.setParams(
                Dict.create()
                        .set("beginCreateTime", DateUtil.formatDateTime(minCreateTime))
                        .set("endCreateTime", DateUtil.formatDateTime(maxCreateTime))
        );
        return wxPaymentMapper.selectWxPaymentList(wxPayment);
    }

    @Override
    public Long sumAmount(WxPayment wxPayment) {
        return wxPaymentMapper.sumAmount(wxPayment);
    }

    /**
     * 新增充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    @Override
    public int insertWxPayment(WxPayment wxPayment) {
        wxPayment.setCreateTime(DateUtils.getNowDate());
        return wxPaymentMapper.insertWxPayment(wxPayment);
    }

    /**
     * 修改充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    @Override
    public int updateWxPayment(WxPayment wxPayment) {
        return wxPaymentMapper.updateWxPayment(wxPayment);
    }

    /**
     * 删除充值记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWxPaymentByIds(String ids) {
        return wxPaymentMapper.deleteWxPaymentByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除充值记录信息
     *
     * @param id 充值记录ID
     * @return 结果
     */
    @Override
    public int deleteWxPaymentById(Long id) {
        return wxPaymentMapper.deleteWxPaymentById(id);
    }


    /**
     * 创建并保存
     *
     * @param serviceType     服务类型
     * @param patient         门诊患者
     * @param outTradeNo      掌医订单号
     * @param totalFee        订单金额
     * @param prescriptionNos 处方号
     */
    @Override
    public boolean createAndSave(ServiceType serviceType, Patient patient, String outTradeNo, Long totalFee, String prescriptionNos) {
        WxPayment payment = new WxPayment();

        payment.setCreateTime(new Date());
        payment.setType(serviceType.name());
        payment.setOpenid(patient.getOpenId());
        payment.setPatientNo(patient.getAccountNo());
        payment.setJzCardNo(patient.getJzCardNo());
        payment.setCardNo(patient.getCardNo());
        payment.setIdCardNo(patient.getIdCardNo());
        payment.setZhuyuanNo(patient.getZhuyuanNo());
        payment.setZyPayNo(outTradeNo);
        payment.setAmount(totalFee);
        payment.setPrescriptionNos(prescriptionNos);
        payment.setClientType(patient.getClientType());
        payment.setPatientName(patient.getName());

        // 已退金额，初始为0
        payment.setExrefund(0L);
        // 未退（可退）金额，初始为null
        payment.setUnrefund(null);

        payment.setWxPayNo("");
        payment.setWxTradeStatus("");
        payment.setHisTradeStatus("");
        payment.setHisSuccessTime(null);
        payment.setManualPayState(Constants.MANUAL_INIT);
        payment.setRemark("");
        return insertWxPayment(payment) == 1;
    }

    @Override
    public boolean createAndSave(MedicalRecordApplication application, Integer clientType) {
        WxPayment payment = new WxPayment();

        payment.setCreateTime(new Date());
        payment.setType(ServiceType.BL.name());
        payment.setOpenid(application.getOpenId());
        payment.setPatientNo("");
        payment.setJzCardNo("");
        payment.setCardNo("");
        payment.setIdCardNo(application.getPatientIdCardNo());
        payment.setZhuyuanNo(application.getAdmissionId());
        payment.setZyPayNo(application.getZyPayNo());
        payment.setAmount(PaymentKit.INSTANCE.yuanToFen(application.getTotalFee()));
        payment.setPrescriptionNos("");
        payment.setClientType(clientType);
        payment.setPatientName(application.getPatientName());

        // 已退金额，初始为0
        payment.setExrefund(0L);
        // 未退（可退）金额，初始为null
        payment.setUnrefund(null);

        payment.setWxPayNo("");
        payment.setWxTradeStatus("");
        payment.setHisTradeStatus("");
        payment.setHisSuccessTime(null);
        payment.setManualPayState(Constants.MANUAL_INIT);
        payment.setRemark("");
        return insertWxPayment(payment) == 1;
    }

    /**
     * 接收到支付通知时，更新充值记录
     *
     * @param wxPayment                  充值记录
     * @param unifiedPayOrderQueryResult 支付通知
     */
    @Override
    public boolean updateOnPay(WxPayment wxPayment, UnifiedPayOrderQueryResult unifiedPayOrderQueryResult) {
        if (unifiedPayOrderQueryResult == null) {
            return false;
        }
        wxPayment.setWxPayNo(unifiedPayOrderQueryResult.getTradeNo());
        wxPayment.setUnrefund(wxPayment.getAmount() - wxPayment.getExrefund());
        String code = unifiedPayOrderQueryResult.getCode();
        if (code == null) {
            code = UnifiedPayStatusEnum.SYSTEMERROR.getCode();
        }
        UnifiedPayStatusEnum statusEnum = UnifiedPayStatusEnum.Companion.getByCode(code);
        if (statusEnum != null) {
            wxPayment.setWxTradeStatus(statusEnum.getMsg());
        }


        return updateWxPayment(wxPayment) == 1;
    }

    @Override
    public boolean updateHisRechargeStatusOnPay(WxPayment wxPayment, String serialNo, String invoiceNo) {
        wxPayment.setHisReceiptNo(invoiceNo);
        wxPayment.setHisTradeStatus(Constants.RECHARGE_OK);
        return updateWxPayment(wxPayment) == 1;
    }

    @Override
    public boolean updateHisPayStatusOnPay(WxPayment wxPayment) {
        wxPayment.setHisTradeStatus(Constants.PAYMENT_OK);
        wxPayment.setHisSuccessTime(new Date());
        return updateWxPayment(wxPayment) == 1;
    }

    /**
     * HIS退款后，更新充值记录
     *
     * @param wxPayment    充值记录
     * @param refundAmount 退款金额，以分为单位
     */
    @Override
    public boolean updateOnRefund(WxPayment wxPayment, Long refundAmount) {
        Long exrefund = wxPayment.getExrefund() == null ? 0L : wxPayment.getExrefund();
        exrefund += refundAmount;
        wxPayment.setExrefund(exrefund);
        wxPayment.setUnrefund(wxPayment.getAmount() - exrefund);
        return updateWxPayment(wxPayment) == 1;
    }

    @Override
    public Long calculateNetInAmount(LocalDate date) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTime = date.atStartOfDay().format(dateTimeFormatter);
        String endTime = date.atTime(LocalTime.MAX).format(dateTimeFormatter);

        Map<String, Object> params = new HashMap<>();
        params.put("beginCreateTime", startTime);
        params.put("endCreateTime", endTime);

        WxPayment wxPayment = new WxPayment();
        wxPayment.setParams(params);
        Long payAmount = sumAmount(wxPayment);
        payAmount = payAmount == null ? 0 : payAmount;

        WxRefund wxRefund = new WxRefund();
        wxRefund.setParams(params);
        Long refundAmount = wxRefundService.sumAmount(wxRefund);
        refundAmount = refundAmount == null ? 0 : refundAmount;

        return payAmount - refundAmount;
    }

    @Override
    public boolean updateWithOptimisticLock(WxPayment wxPayment) {
        wxPayment.setUpdateTime(new Date());
        int affectedRows = wxPaymentMapper.updateWithOptimisticLock(wxPayment);
        return affectedRows == 1; // 返回true表示更新成功，false表示版本冲突
    }
    
    @Override
    public boolean updateOnPayWithOptimisticLock(WxPayment wxPayment, UnifiedPayOrderQueryResult result) {
        if (result == null) {
            return false;
        }
        
        // 设置支付信息
        wxPayment.setWxPayNo(result.getTradeNo());
        wxPayment.setUnrefund(wxPayment.getAmount() - wxPayment.getExrefund());
        
        String code = result.getCode();
        if (code == null) {
            code = UnifiedPayStatusEnum.SYSTEMERROR.getCode();
        }
        
        UnifiedPayStatusEnum statusEnum = UnifiedPayStatusEnum.Companion.getByCode(code);
        if (statusEnum != null) {
            wxPayment.setWxTradeStatus(statusEnum.getMsg());
        }
        
        // 使用乐观锁更新
        return updateWithOptimisticLock(wxPayment);
    }
}

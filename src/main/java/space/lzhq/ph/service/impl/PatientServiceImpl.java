package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Patient;
import space.lzhq.ph.domain.PatientDailyIncrement;
import space.lzhq.ph.mapper.PatientMapper;
import space.lzhq.ph.service.IPatientService;

import java.util.List;

/**
 * 就诊人Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-05-25
 */
@Service
public class PatientServiceImpl implements IPatientService {
    @Autowired
    private PatientMapper patientMapper;

    /**
     * 查询就诊人
     *
     * @param id 就诊人ID
     * @return 就诊人
     */
    @Override
    public Patient selectPatientById(Long id) {
        return patientMapper.selectPatientById(id);
    }

    @Override
    public Patient selectActivePatientByOpenId(String openId) {
        return patientMapper.selectActivePatientByOpenId(openId);
    }

    /**
     * 查询就诊人列表
     *
     * @param patient 就诊人
     * @return 就诊人
     */
    @Override
    public List<Patient> selectPatientList(Patient patient) {
        return patientMapper.selectPatientList(patient);
    }

    @Override
    public List<PatientDailyIncrement> statisticsByDay(PatientDailyIncrement patientDailyIncrement) {
        return patientMapper.statisticsByDay(patientDailyIncrement);
    }

    @Override
    public List<Patient> selectPatientListByOpenId(String openId) {
        Patient patient = new Patient();
        patient.setOpenId(openId);
        return selectPatientList(patient);
    }

    @Override
    public List<Patient> selectPatientListByIdCard(String idCardNo) {
        Patient patient = new Patient();
        patient.setIdCardNo(idCardNo);
        return selectPatientList(patient);
    }

    @Override
    public List<Patient> selectPatientListByPatientNo(String patientNo) {
        Patient patient = new Patient();
        patient.setAccountNo(patientNo);
        return selectPatientList(patient);
    }

    @Override
    public Patient selectPatientByIdCard(String idCardNo) {
        List<Patient> patients = selectPatientListByIdCard(idCardNo);
        return patients.isEmpty() ? null : patients.getFirst();
    }

    @Override
    public Patient selectPatientByJzCardNoAndOpenId(String jzCardNo, String openId) {
        Patient patient = new Patient();
        patient.setJzCardNo(jzCardNo);
        patient.setOpenId(openId);
        List<Patient> result = selectPatientList(patient);
        return result.isEmpty() ? null : result.getFirst();
    }

    @Override
    public List<Patient> selectZhuyuanPatient() {
        return patientMapper.selectZhuyuanPatient();
    }

    @Override
    public List<Patient> selectZaiyuanPatient() {
        return patientMapper.selectZaiyuanPatient();
    }

    /**
     * 新增就诊人
     *
     * @param patient 就诊人
     * @return 结果
     */
    @Override
    public int insertPatient(Patient patient) {
        patient.setCreateTime(DateUtils.getNowDate());
        return patientMapper.insertPatient(patient);
    }

    /**
     * 修改就诊人
     *
     * @param patient 就诊人
     * @return 结果
     */
    @Override
    public int updatePatient(Patient patient) {
        return patientMapper.updatePatient(patient);
    }

    /**
     * 删除就诊人对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePatientByIds(String ids) {
        return patientMapper.deletePatientByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除就诊人信息
     *
     * @param id 就诊人ID
     * @return 结果
     */
    @Override
    public int deletePatientById(Long id) {
        return patientMapper.deletePatientById(id);
    }

    @Override
    public int switchActivePatient(String jzCardNo, String openId) {
        return patientMapper.switchActivePatient(jzCardNo, openId);
    }
}

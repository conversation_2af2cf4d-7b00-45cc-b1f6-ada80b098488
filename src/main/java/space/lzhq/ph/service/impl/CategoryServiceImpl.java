package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Category;
import space.lzhq.ph.mapper.CategoryMapper;
import space.lzhq.ph.service.ICategoryService;

import java.util.Date;
import java.util.List;

/**
 * 栏目Service业务层处理
 *
 * @date 2020-05-30
 */
@Service
public class CategoryServiceImpl implements ICategoryService {
    @Autowired
    private CategoryMapper categoryMapper;

    /**
     * 查询栏目
     *
     * @param id 栏目ID
     * @return 栏目
     */
    @Override
    public Category selectCategoryById(Long id) {
        return categoryMapper.selectCategoryById(id);
    }

    @Override
    public List<Category> selectCategoryAll() {
        return categoryMapper.selectCategoryAll();
    }

    /**
     * 查询栏目列表
     *
     * @param category 栏目
     * @return 栏目
     */
    @Override
    public List<Category> selectCategoryList(Category category) {
        return categoryMapper.selectCategoryList(category);
    }

    /**
     * 新增栏目
     *
     * @param category 栏目
     * @return 结果
     */
    @Override
    public int insertCategory(Category category) {
        Date now = new Date();
        category.setCreateTime(now);
        category.setUpdateTime(now);

        String currentUserName = ShiroUtils.getLoginName();
        category.setCreateBy(currentUserName);
        category.setUpdateBy(currentUserName);

        return categoryMapper.insertCategory(category);
    }

    /**
     * 修改栏目
     *
     * @param category 栏目
     * @return 结果
     */
    @Override
    public int updateCategory(Category category) {
        Date now = new Date();
        category.setCreateTime(now);
        category.setUpdateTime(now);

        String currentUserName = ShiroUtils.getLoginName();
        category.setCreateBy(currentUserName);
        category.setUpdateBy(currentUserName);

        return categoryMapper.updateCategory(category);
    }

    /**
     * 删除栏目对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteCategoryByIds(String ids) {
        return categoryMapper.deleteCategoryByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除栏目信息
     *
     * @param id 栏目ID
     * @return 结果
     */
    @Override
    public int deleteCategoryById(Long id) {
        return categoryMapper.deleteCategoryById(id);
    }
}

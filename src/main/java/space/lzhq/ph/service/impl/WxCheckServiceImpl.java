package space.lzhq.ph.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayBillInfo;
import com.github.binarywang.wxpay.bean.result.WxPayBillResult;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.mospital.common.IdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.common.PaymentKit;
import space.lzhq.ph.domain.WxCheck;
import space.lzhq.ph.ext.WeixinExtKt;
import space.lzhq.ph.mapper.WxCheckMapper;
import space.lzhq.ph.service.IWxCheckService;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 对账Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class WxCheckServiceImpl implements IWxCheckService {

    private final WxCheckMapper wxCheckMapper;

    @Autowired
    public WxCheckServiceImpl(WxCheckMapper wxCheckMapper) {
        this.wxCheckMapper = wxCheckMapper;
    }

    /**
     * 查询对账
     *
     * @param id 对账ID
     * @return 对账
     */
    @Override
    public WxCheck selectWxCheckById(Long id) {
        return wxCheckMapper.selectWxCheckById(id);
    }

    /**
     * 根据账单日查询对账记录
     *
     * @param date 账单日
     */
    @Override
    public WxCheck selectWxCheckByDate(LocalDate date) {
        return selectWxCheckById(IdUtil.INSTANCE.localDateToId(date));
    }

    /**
     * 根据账单日获取或创建对账记录
     *
     * @param date 账单日
     */
    @Override
    public WxCheck getOrCreate(LocalDate date) {
        WxCheck wxCheck = selectWxCheckByDate(date);
        if (wxCheck == null) {
            wxCheck = create(date);
        }
        return wxCheck;
    }

    /**
     * 查询对账列表
     *
     * @param wxCheck 对账
     * @return 对账
     */
    @Override
    public List<WxCheck> selectWxCheckList(WxCheck wxCheck) {
        return wxCheckMapper.selectWxCheckList(wxCheck);
    }

    /**
     * 新增对账
     *
     * @param wxCheck 对账
     * @return 结果
     */
    @Override
    public int insertWxCheck(WxCheck wxCheck) {
        return wxCheckMapper.insertWxCheck(wxCheck);
    }

    /**
     * 创建对账记录
     *
     * @param billDate 账单日
     */
    @Override
    public WxCheck create(LocalDate billDate) {
        WxCheck wxCheck = new WxCheck();
        wxCheck.setId(IdUtil.INSTANCE.localDateToId(billDate));
        return insertWxCheck(wxCheck) == 1 ? wxCheck : null;
    }

    /**
     * 修改对账
     *
     * @param wxCheck 对账
     * @return 结果
     */
    @Override
    public int updateWxCheck(WxCheck wxCheck) {
        return wxCheckMapper.updateWxCheck(wxCheck);
    }

    /**
     * 删除对账对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWxCheckByIds(String ids) {
        return wxCheckMapper.deleteWxCheckByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除对账信息
     *
     * @param id 对账ID
     * @return 结果
     */
    @Override
    public int deleteWxCheckById(Long id) {
        return wxCheckMapper.deleteWxCheckById(id);
    }

    /**
     * 同步建行账单
     *
     * @param billDate        账单日
     * @param wxPayBillResult 建行账单
     */
    @Override
    public boolean syncWxBill(LocalDate billDate, WxPayBillResult wxPayBillResult) {
        if (wxPayBillResult == null) {
            return true;
        }

        WxCheck wxCheck = getOrCreate(billDate);
        if (wxCheck == null) {
            throw new IllegalStateException("获取或创建对账记录失败");
        }

        String appId = SpringUtils.getBean(WxPayService.class).getConfig().getAppId();
        wxCheck.setWxPayAmount(WeixinExtKt.calculateTotalPayAmount(wxPayBillResult, appId));
        wxCheck.setWxMzPayAmount(WeixinExtKt.calculateMenzhenPayAmount(wxPayBillResult, appId));
        wxCheck.setWxZyPayAmount(WeixinExtKt.calculateZhuyuanPayAmount(wxPayBillResult, appId));
        wxCheck.setWxRefundAmount(WeixinExtKt.calculateTotalRefundAmount(wxPayBillResult, appId));
        wxCheck.setWxMzRefundAmount(WeixinExtKt.calculateMenzhenRefundAmount(wxPayBillResult, appId));
        wxCheck.setWxZyRefundAmount(WeixinExtKt.calculateZhuyuanRefundAmount(wxPayBillResult, appId));
        wxCheck.setWxNetIn(NumberUtil.sub(wxCheck.getWxPayAmount(), wxCheck.getWxRefundAmount()));
        wxCheck.setTotalNetIn(wxCheck.getWxNetIn().add(wxCheck.getMipNetIn() == null ? BigDecimal.ZERO : wxCheck.getMipNetIn()));

        wxCheck.setDiffPayAmount(NumberUtil.sub(
                wxCheck.getWxPayAmount(),
                wxCheck.getHisPayAmount() == null ? Double.valueOf(0.00D) : wxCheck.getHisPayAmount()
        ));
        wxCheck.setPayBalanced(PaymentKit.INSTANCE.yuanToFen(wxCheck.getDiffPayAmount()) == 0L ? 1 : 0);

        wxCheck.setDiffRefundAmount(NumberUtil.sub(
                wxCheck.getWxRefundAmount(),
                wxCheck.getHisRefundAmount() == null ? Double.valueOf(0.00D) : wxCheck.getHisRefundAmount()
        ));
        wxCheck.setRefundBalanced(PaymentKit.INSTANCE.yuanToFen(wxCheck.getDiffRefundAmount()) == 0L ? 1 : 0);
        return updateWxCheck(wxCheck) == 1;
    }

    @Override
    public boolean syncWxMipBill(LocalDate billDate, File billFile) {
        if (billFile == null) {
            return false;
        }

        WxCheck wxCheck = getOrCreate(billDate);
        if (wxCheck == null) {
            throw new IllegalStateException("获取或创建对账记录失败");
        }

        String rawBillResultString = FileUtil.readUtf8String(billFile);
        List<WxInsurancePayBillInfo> bills = WxInsurancePayBillInfo.Companion.fromRawBillResultString(rawBillResultString, "ALL");

        BigDecimal mipPayAmount = bills.stream().map(WxInsurancePayBillInfo::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal mipRefundAmount = bills.stream().map(WxInsurancePayBillInfo::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal mipNetIn = mipPayAmount.subtract(mipRefundAmount);
        wxCheck.setMipPayAmount(mipPayAmount);
        wxCheck.setMipRefundAmount(mipRefundAmount);
        wxCheck.setMipNetIn(mipNetIn);
        wxCheck.setTotalNetIn(mipNetIn.add(wxCheck.getWxNetIn() == null ? BigDecimal.ZERO : wxCheck.getWxNetIn()));

        return updateWxCheck(wxCheck) == 1;
    }

}

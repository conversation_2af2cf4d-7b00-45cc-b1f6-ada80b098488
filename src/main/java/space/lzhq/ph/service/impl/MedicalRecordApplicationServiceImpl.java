package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mospital.common.TokenManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.Attachment;
import space.lzhq.ph.domain.MedicalRecordApplication;
import space.lzhq.ph.dto.InpatientInfo;
import space.lzhq.ph.dto.mapper.AttachmentMapper;
import space.lzhq.ph.dto.mapper.MedicalRecordApplicationMapper;
import space.lzhq.ph.dto.request.MedicalRecordApplicationAgentUpdateDto;
import space.lzhq.ph.dto.request.MedicalRecordApplicationPatientFormDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationDetailResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationListResponseDto;
import space.lzhq.ph.dto.response.attachment.AttachmentDto;
import space.lzhq.ph.enums.AttachmentMasterType;
import space.lzhq.ph.enums.AttachmentType;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.exception.AttachmentValidationException;
import space.lzhq.ph.exception.DataUpdateFailedException;
import space.lzhq.ph.exception.EditNotAllowedException;
import space.lzhq.ph.exception.ResourceNotFoundException;
import space.lzhq.ph.exception.UnauthorizedResourceAccessException;
import space.lzhq.ph.ext.HisExt;
import space.lzhq.ph.service.AttachmentFileChecker;
import space.lzhq.ph.service.IAttachmentService;
import space.lzhq.ph.service.IMedicalRecordApplicationService;
import space.lzhq.ph.service.IMedicalRecordApplicationTimelineService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@Validated
public class MedicalRecordApplicationServiceImpl
        extends ServiceImpl<space.lzhq.ph.mapper.MedicalRecordApplicationMapper, MedicalRecordApplication>
        implements IMedicalRecordApplicationService {

    private final IAttachmentService attachmentService;
    private final AttachmentFileChecker attachmentFileChecker;
    private final IMedicalRecordApplicationTimelineService timelineService;

    public MedicalRecordApplicationServiceImpl(
            IAttachmentService attachmentService,
            AttachmentFileChecker attachmentFileChecker,
            IMedicalRecordApplicationTimelineService timelineService
    ) {
        this.attachmentService = attachmentService;
        this.attachmentFileChecker = attachmentFileChecker;
        this.timelineService = timelineService;
    }

    @Override
    public MedicalRecordApplicationDetailResponseDto getDetailById(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 验证权限并获取申报记录
        MedicalRecordApplication application = validateAndGetApplication(id, openId);

        // 2. 转换为详情DTO
        MedicalRecordApplicationDetailResponseDto detailDto =
                MedicalRecordApplicationMapper.INSTANCE.toDetailResponseDto(application);

        // 3. 批量查询并设置附件信息
        populateAttachments(application, detailDto);

        return detailDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Optional<MedicalRecordApplication> save(
            @NotNull @Valid MedicalRecordApplicationPatientFormDto dto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) throws AttachmentValidationException {
        // 验证身份证附件
        Attachment idCardAttachment = validateAttachmentIfPresent(
                dto.getPatientIdCardAttachmentId(),
                openId,
                AttachmentType.ID_CARD
        );

        // 验证出生证附件
        Attachment birthCertificateAttachment = validateAttachmentIfPresent(
                dto.getPatientBirthCertificateAttachmentId(),
                openId,
                AttachmentType.BIRTH_CERTIFICATE
        );

        // 验证医保卡附件
        Attachment medicalInsuranceCardAttachment = validateAttachmentIfPresent(
                dto.getPatientMedicalInsuranceCardAttachmentId(),
                openId,
                AttachmentType.MEDICAL_INSURANCE_CARD
        );

        // 验证户口本附件
        Attachment householdRegisterAttachment = validateAttachmentIfPresent(
                dto.getPatientHouseholdRegisterAttachmentId(),
                openId,
                AttachmentType.HOUSEHOLD_REGISTER
        );

        // 解析患者信息
        InpatientInfo patientInfo = TokenManager.INSTANCE.parseToken(dto.getPatientToken(), new TypeReference<>() {
        });
        if (patientInfo == null) {
            throw new IllegalArgumentException("参数 patientToken 无效");
        }

        if (StringUtils.isNotBlank(dto.getPatientIdCardNo()) && !Objects.equals(patientInfo.getIdCardNo(), dto.getPatientIdCardNo())) {
            throw new IllegalArgumentException("患者身份证号不正确");
        }
        if (StringUtils.isNotBlank(dto.getPatientName()) && !HisExt.INSTANCE.matchesChinese(dto.getPatientName(), patientInfo.getName())) {
            throw new IllegalArgumentException("患者姓名不正确");
        }

        MedicalRecordApplication application = new MedicalRecordApplication(dto, patientInfo, openId);

        // 保存申报主记录
        boolean saveSuccess = this.save(application);
        if (!saveSuccess) {
            throw new DataUpdateFailedException("保存病历复印申请失败", "保存申报记录");
        }

        // 更新附件记录的申报主表ID
        Stream.of(idCardAttachment, birthCertificateAttachment, medicalInsuranceCardAttachment, householdRegisterAttachment)
                .filter(Objects::nonNull)
                .forEach(attachment -> updateAttachmentMasterInfo(attachment, application.getId()));

        // 7. 记录创建事件到时间线
        safeRecordTimeline(() -> timelineService.recordApplicationCreated(
                application.getId(),
                getOperatorName(application)
        ));

        return Optional.of(application);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MedicalRecordApplication clearAgentFlag(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        MedicalRecordApplication application = this.getById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(application.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申请记录不属于当前用户",
                    "MedicalRecordApplication",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许用户编辑
        if (!application.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申请记录当前状态不允许修改：" + application.getStatus().getDescription(),
                    "MedicalRecordApplication",
                    id,
                    application.getStatus().getDescription()
            );
        }

        // 4. 清除代办标志
        boolean updateSuccess = this.getBaseMapper().clearAgent(id) == 1;
        if (!updateSuccess) {
            throw new DataUpdateFailedException("清除代办标志失败，请重试", "清除代办标志", id);
        }

        // 5. 获取更新后的记录
        MedicalRecordApplication updatedApplication = getById(id);

        // 6. 记录清除代办人事件到时间线（使用更新后的记录）
        safeRecordTimeline(() -> timelineService.recordAgentInfoCleared(
                id,
                getOperatorName(updatedApplication)
        ));

        return updatedApplication;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MedicalRecordApplication updateAgent(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid MedicalRecordApplicationAgentUpdateDto agentDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申请记录
        MedicalRecordApplication application = this.getById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        // 2. 验证申请记录的openId是否与当前用户的openId一致
        if (!openId.equals(application.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申请记录不属于当前用户",
                    "MedicalRecordApplication",
                    id,
                    openId
            );
        }

        // 3. 验证申请记录状态是否允许用户编辑
        if (!application.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申请记录当前状态不允许修改：" + application.getStatus().getDescription(),
                    "MedicalRecordApplication",
                    id,
                    application.getStatus().getDescription()
            );
        }

        // 4. 验证代办人身份证附件
        Attachment agentIdCardAttachment = validateAttachment(
                agentDto.getAgentIdCardAttachmentId(),
                openId,
                AttachmentType.ID_CARD,
                id  // 允许附件已经关联到当前申报记录
        );

        // 5. 更新代办人信息
        application.setAgentFlag(1);
        application.setAgentName(agentDto.getAgentName());
        application.setAgentIdCardNo(agentDto.getAgentIdCardNo());
        application.setAgentIdCardAttachmentId(agentDto.getAgentIdCardAttachmentId());
        application.setAgentMobile(agentDto.getAgentMobile());
        application.setAgentRelation(agentDto.getAgentRelation());
        application.setUpdateTime(LocalDateTime.now());

        // 6. 保存更新后的记录
        boolean updateSuccess = this.updateById(application);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新代办人信息失败，请重试", "更新代办人信息", id);
        }

        // 7. 更新附件记录的申报主表ID
        updateAttachmentMasterInfo(agentIdCardAttachment, id);

        // 8. 记录设置代办人事件到时间线
        safeRecordTimeline(() -> timelineService.recordAgentInfoSet(
                id,
                getOperatorName(application),
                agentDto.getAgentName()
        ));

        return application;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MedicalRecordApplication updateMailingInfo(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid space.lzhq.ph.dto.request.MedicalRecordApplicationMailingUpdateDto mailingDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申请记录
        MedicalRecordApplication application = this.getById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        // 2. 验证申请记录的openId是否与当前用户的openId一致
        if (!openId.equals(application.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申请记录不属于当前用户",
                    "MedicalRecordApplication",
                    id,
                    openId
            );
        }

        // 3. 验证申请记录状态是否允许用户编辑
        if (!application.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申请记录当前状态不允许修改：" + application.getStatus().getDescription(),
                    "MedicalRecordApplication",
                    id,
                    application.getStatus().getDescription()
            );
        }

        // 4. 更新邮寄信息
        application.setRecipientName(mailingDto.getRecipientName());
        application.setRecipientMobile(mailingDto.getRecipientMobile());
        application.setRecipientAddress(mailingDto.getRecipientAddress());
        application.setUpdateTime(LocalDateTime.now());

        // 5. 保存更新后的记录
        boolean updateSuccess = this.updateById(application);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新邮寄信息失败，请重试", "更新邮寄信息", id);
        }

        // 6. 记录设置邮寄信息事件到时间线
        safeRecordTimeline(() -> timelineService.recordMailingInfoSet(
                id,
                getOperatorName(application),
                mailingDto.getRecipientAddress()
        ));

        return application;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MedicalRecordApplication submitApplication(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申请记录
        MedicalRecordApplication application = this.getById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        // 2. 验证申请记录的openId是否与当前用户的openId一致
        if (!openId.equals(application.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申请记录不属于当前用户",
                    "MedicalRecordApplication",
                    id,
                    openId
            );
        }

        // 3. 验证申请记录状态是否允许提交（必须是DRAFT状态）
        if (!application.getStatus().equals(MedicalRecordApplicationStatus.DRAFT)) {
            throw new EditNotAllowedException(
                    "申请记录当前状态不允许提交：" + application.getStatus().getDescription(),
                    "MedicalRecordApplication",
                    id,
                    application.getStatus().getDescription()
            );
        }

        // 4. 验证申请信息完整性
        validateApplicationCompleteness(application);

        // 5. 更新申请状态和提交时间
        application.setStatus(MedicalRecordApplicationStatus.SUBMITTED);
        application.setSubmitTime(LocalDateTime.now());
        application.setUpdateTime(LocalDateTime.now());

        // 6. 保存更新后的记录
        boolean updateSuccess = this.updateById(application);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("提交申请失败，请重试", "提交申请", id);
        }

        // 7. 记录申请提交事件到时间线
        safeRecordTimeline(() -> timelineService.recordApplicationSubmitted(
                id,
                getOperatorName(application),
                MedicalRecordApplicationStatus.DRAFT
        ));

        return application;
    }

    /**
     * 验证申请信息完整性
     *
     * @param application 申请记录
     * @throws IllegalArgumentException 当申请信息不完整时
     */
    private void validateApplicationCompleteness(MedicalRecordApplication application) {
        // 验证患者基本信息
        if (StringUtils.isBlank(application.getPatientName())) {
            throw new IllegalArgumentException("患者姓名不能为空");
        }
        if (StringUtils.isBlank(application.getPatientGender())) {
            throw new IllegalArgumentException("患者性别不能为空");
        }
        if (application.getPatientBirthDate() == null) {
            throw new IllegalArgumentException("患者出生日期不能为空");
        }
        if (application.getCopyPurpose() == null) {
            throw new IllegalArgumentException("复印用途不能为空");
        }

        // 验证身份证明文件（身份证、出生证、医保卡、户口本至少有一个）
        if (Stream.of(application.getPatientIdCardAttachmentId(),
                        application.getPatientBirthCertificateAttachmentId(),
                        application.getPatientMedicalInsuranceCardAttachmentId(),
                        application.getPatientHouseholdRegisterAttachmentId())
                .allMatch(StringUtils::isBlank)) {
            throw new IllegalArgumentException("患者身份证明文件不能为空，请上传身份证、出生证、医保卡或户口本");
        }

        // 如果是代办，验证代办人信息完整性
        if (Objects.equals(application.getAgentFlag(), 1)) {
            if (StringUtils.isBlank(application.getAgentName())) {
                throw new IllegalArgumentException("代办人姓名不能为空");
            }
            if (StringUtils.isBlank(application.getAgentIdCardNo())) {
                throw new IllegalArgumentException("代办人身份证号不能为空");
            }
            if (StringUtils.isBlank(application.getAgentIdCardAttachmentId())) {
                throw new IllegalArgumentException("代办人身份证附件不能为空");
            }
            if (StringUtils.isBlank(application.getAgentMobile())) {
                throw new IllegalArgumentException("代办人手机号不能为空");
            }
            if (application.getAgentRelation() == null) {
                throw new IllegalArgumentException("代办人与患者关系不能为空");
            }
        }

        // 验证邮寄信息完整性
        if (StringUtils.isBlank(application.getRecipientName())) {
            throw new IllegalArgumentException("收件人姓名不能为空");
        }
        if (StringUtils.isBlank(application.getRecipientMobile())) {
            throw new IllegalArgumentException("收件人手机号不能为空");
        }
        if (StringUtils.isBlank(application.getRecipientAddress())) {
            throw new IllegalArgumentException("收件人地址不能为空");
        }
    }

    /**
     * 如果附件ID存在则验证附件，否则返回null
     * 消除重复的附件验证逻辑
     *
     * @param attachmentId 附件ID，可能为null或空字符串
     * @param openId       用户OpenID
     * @param expectedType 期望的附件类型
     * @return 验证通过的附件对象，如果ID为空则返回null
     */
    private Attachment validateAttachmentIfPresent(
            String attachmentId,
            String openId,
            AttachmentType expectedType
    ) {
        if (StringUtils.isNotBlank(attachmentId)) {
            return validateAttachment(attachmentId, openId, expectedType);
        }
        return null;
    }

    /**
     * 验证附件是否满足业务规则
     *
     * @param attachmentId 附件ID
     * @param openId       用户OpenID
     * @param expectedType 期望的附件类型
     * @return 附件记录
     * @throws AttachmentValidationException 当附件不满足业务规则时抛出
     */
    private Attachment validateAttachment(
            String attachmentId,
            String openId,
            AttachmentType expectedType
    ) {
        return validateAttachment(attachmentId, openId, expectedType, null);
    }

    /**
     * 验证附件是否满足业务规则
     *
     * @param attachmentId  附件ID
     * @param openId        用户OpenID
     * @param expectedType  期望的附件类型
     * @param applicationId 允许附件已经关联到的申报记录ID
     * @return 附件记录
     * @throws AttachmentValidationException 当附件不满足业务规则时抛出
     */
    private Attachment validateAttachment(
            String attachmentId,
            String openId,
            AttachmentType expectedType,
            String applicationId
    ) {
        String typeName = expectedType.getDescription();

        // 1. 检查附件是否存在
        Attachment attachment = attachmentService.getById(attachmentId);
        if (attachment == null) {
            throw new AttachmentValidationException(
                    typeName + "附件不存在，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 2. 检查附件是否属于当前用户
        if (!openId.equals(attachment.getOpenId())) {
            throw new AttachmentValidationException(
                    typeName + "附件不属于当前用户，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 3. 检查附件类型是否正确
        if (!expectedType.equals(attachment.getType())) {
            throw new AttachmentValidationException(
                    typeName + "附件类型不正确，期望: " + expectedType + "，实际: " + attachment.getType(),
                    attachmentId,
                    typeName
            );
        }

        // 4. 检查附件是否已被使用
        if (attachment.getMasterId() != null && !attachment.getMasterId().equals(applicationId)) {
            throw new AttachmentValidationException(
                    typeName + "附件已被使用，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 5. 检查文件是否真实存在
        if (!attachmentFileChecker.isFileExists(attachment.getFileUrl())) {
            throw new AttachmentValidationException(
                    typeName + "附件对应的文件不存在，文件URL: " + attachment.getFileUrl(),
                    attachmentId,
                    typeName
            );
        }

        return attachment;
    }

    /**
     * 更新附件记录中的申报主表ID
     *
     * @param attachment 附件记录
     * @param masterId   申报主表ID
     */
    private void updateAttachmentMasterInfo(Attachment attachment, String masterId) {
        if (Objects.equals(masterId, attachment.getMasterId())) {
            return;
        }

        boolean updateSuccess = attachmentService.updateMasterInfo(attachment.getId(), masterId, AttachmentMasterType.MEDICAL_RECORD_APPLICATION);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新附件关联的申报ID失败", "更新附件关联", attachment.getId());
        }
    }

    /**
     * 安全地记录时间线事件，不影响主业务逻辑
     *
     * @param action 记录时间线的具体操作
     */
    private void safeRecordTimeline(Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            log.warn("记录时间线事件失败，但不影响主业务逻辑: {}", e.getMessage());
        }
    }

    /**
     * 根据申报记录确定操作者姓名
     *
     * @param application 申报记录
     * @return 操作者姓名（代办人或患者本人）
     */
    private String getOperatorName(MedicalRecordApplication application) {
        // 如果是代办模式且代办人姓名不为空，返回代办人姓名
        if (Objects.equals(application.getAgentFlag(), 1) && StringUtils.isNotBlank(application.getAgentName())) {
            return application.getAgentName();
        }
        // 否则返回患者本人姓名
        return application.getPatientName();
    }

    /**
     * 验证权限并获取申报记录
     *
     * @param id     申报记录ID
     * @param openId 用户OpenID
     * @return 申报记录，如果不存在或权限不符则返回null
     */
    private MedicalRecordApplication validateAndGetApplication(String id, String openId) {
        MedicalRecordApplication application = this.getById(id);
        if (application == null) {
            throw new ResourceNotFoundException(
                    "申请记录不存在",
                    "MedicalRecordApplication",
                    id
            );
        }

        if (!openId.equals(application.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申请记录不属于当前用户",
                    "MedicalRecordApplication",
                    id,
                    openId
            );
        }
        return application;
    }

    /**
     * 批量查询并填充附件信息到DTO
     *
     * @param application 申报记录
     * @param detailDto   详情DTO
     */
    private void populateAttachments(MedicalRecordApplication application,
                                     MedicalRecordApplicationDetailResponseDto detailDto) {
        // 收集所有附件ID
        List<String> allAttachmentIds = collectAllAttachmentIds(application);

        if (allAttachmentIds.isEmpty()) {
            return;
        }

        // 批量查询附件并创建映射
        Map<String, Attachment> attachmentMap =
                batchQueryAndMapAttachments(allAttachmentIds);

        // 设置各类附件到DTO
        setAttachmentsToDto(application, detailDto, attachmentMap);
    }

    /**
     * 收集申报记录中的所有附件ID
     *
     * @param application 申报记录
     * @return 附件ID列表
     */
    private List<String> collectAllAttachmentIds(MedicalRecordApplication application) {
        return Stream.of(
                        application.getPatientIdCardAttachmentId(),
                        application.getPatientBirthCertificateAttachmentId(),
                        application.getPatientMedicalInsuranceCardAttachmentId(),
                        application.getPatientHouseholdRegisterAttachmentId(),
                        application.getAgentIdCardAttachmentId()
                ).filter(Objects::nonNull)
                .distinct()
                .toList();
    }

    /**
     * 批量查询附件并创建ID到附件对象的映射
     *
     * @param attachmentIds 附件ID列表
     * @return 附件ID到附件对象的映射
     */
    private Map<String, Attachment> batchQueryAndMapAttachments(List<String> attachmentIds) {
        List<Attachment> allAttachments = attachmentService.listByIds(attachmentIds);
        return allAttachments.stream()
                .collect(Collectors.toMap(Attachment::getId, Function.identity()));
    }

    /**
     * 将附件信息设置到详情DTO中
     *
     * @param application   申报记录
     * @param detailDto     详情DTO
     * @param attachmentMap 附件映射
     */
    private void setAttachmentsToDto(
            MedicalRecordApplication application,
            MedicalRecordApplicationDetailResponseDto detailDto,
            Map<String, Attachment> attachmentMap
    ) {
        // 使用通用方法设置各种附件
        setAttachmentIfExists(
                application.getPatientIdCardAttachmentId(),
                attachmentMap,
                detailDto::setPatientIdCardAttachment
        );

        setAttachmentIfExists(
                application.getPatientBirthCertificateAttachmentId(),
                attachmentMap,
                detailDto::setPatientBirthCertificateAttachment
        );

        setAttachmentIfExists(
                application.getPatientMedicalInsuranceCardAttachmentId(),
                attachmentMap,
                detailDto::setPatientMedicalInsuranceCardAttachment
        );

        setAttachmentIfExists(
                application.getPatientHouseholdRegisterAttachmentId(),
                attachmentMap,
                detailDto::setPatientHouseholdRegisterAttachment
        );

        setAttachmentIfExists(
                application.getAgentIdCardAttachmentId(),
                attachmentMap,
                detailDto::setAgentIdCardAttachment
        );
    }

    /**
     * 通用的附件设置方法
     *
     * @param attachmentId  附件ID
     * @param attachmentMap 附件映射
     * @param setter        设置器函数
     */
    private void setAttachmentIfExists(
            String attachmentId,
            Map<String, Attachment> attachmentMap,
            java.util.function.Consumer<AttachmentDto> setter
    ) {
        if (attachmentId != null) {
            Attachment attachment = attachmentMap.get(attachmentId);
            if (attachment != null) {
                setter.accept(AttachmentMapper.INSTANCE.toDto(attachment));
            }
        }
    }

    @Override
    public List<MedicalRecordApplicationListResponseDto> getUserApplicationList(
            @NotBlank @Size(min = 16, max = 28) String openId,
            MedicalRecordApplicationStatus status
    ) {
        log.debug("获取用户申请列表，用户ID: {}, 状态: {}", openId, status);

        // 构建查询条件
        LambdaQueryWrapper<MedicalRecordApplication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicalRecordApplication::getOpenId, openId);

        if (status != null) {
            queryWrapper.eq(MedicalRecordApplication::getStatus, status);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(MedicalRecordApplication::getCreateTime);

        // 查询申请记录
        List<MedicalRecordApplication> applications = list(queryWrapper);
        log.debug("找到 {} 条申请记录", applications.size());

        // 转换为响应DTO
        return applications.stream()
                .map(MedicalRecordApplicationMapper.INSTANCE::toListResponseDto)
                .collect(Collectors.toList());
    }
}

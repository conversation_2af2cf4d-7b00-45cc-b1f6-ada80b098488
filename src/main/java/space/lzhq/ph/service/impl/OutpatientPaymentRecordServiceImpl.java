package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.OutpatientPaymentRecord;
import space.lzhq.ph.mapper.OutpatientPaymentRecordMapper;
import space.lzhq.ph.service.IOutpatientPaymentRecordService;

import java.util.Date;
import java.util.List;

/**
 * 门诊缴费记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class OutpatientPaymentRecordServiceImpl implements IOutpatientPaymentRecordService {
    @Autowired
    private OutpatientPaymentRecordMapper outpatientPaymentRecordMapper;

    /**
     * 查询门诊缴费记录
     *
     * @param id 门诊缴费记录ID
     * @return 门诊缴费记录
     */
    @Override
    public OutpatientPaymentRecord selectOutpatientPaymentRecordById(Long id) {
        return outpatientPaymentRecordMapper.selectOutpatientPaymentRecordById(id);
    }

    /**
     * 查询门诊缴费记录列表
     *
     * @param outpatientPaymentRecord 门诊缴费记录
     * @return 门诊缴费记录
     */
    @Override
    public List<OutpatientPaymentRecord> selectOutpatientPaymentRecordList(OutpatientPaymentRecord outpatientPaymentRecord) {
        return outpatientPaymentRecordMapper.selectOutpatientPaymentRecordList(outpatientPaymentRecord);
    }

    /**
     * 根据患者卡号查询门诊缴费记录列表
     *
     * @param patientCardNo 患者卡号
     * @return 门诊缴费记录集合
     */
    @Override
    public List<OutpatientPaymentRecord> selectOutpatientPaymentRecordListByPatientCardNo(String patientCardNo) {
        return outpatientPaymentRecordMapper.selectOutpatientPaymentRecordListByPatientCardNo(patientCardNo);
    }

    /**
     * 新增门诊缴费记录
     *
     * @param outpatientPaymentRecord 门诊缴费记录
     * @return 结果
     */
    @Override
    public int insertOutpatientPaymentRecord(OutpatientPaymentRecord outpatientPaymentRecord) {
        Date now = DateUtils.getNowDate();
        outpatientPaymentRecord.setCreatedTime(now);
        outpatientPaymentRecord.setUpdatedTime(now);
        return outpatientPaymentRecordMapper.insertOutpatientPaymentRecord(outpatientPaymentRecord);
    }

    /**
     * 修改门诊缴费记录
     *
     * @param outpatientPaymentRecord 门诊缴费记录
     * @return 结果
     */
    @Override
    public int updateOutpatientPaymentRecord(OutpatientPaymentRecord outpatientPaymentRecord) {
        outpatientPaymentRecord.setUpdatedTime(DateUtils.getNowDate());
        return outpatientPaymentRecordMapper.updateOutpatientPaymentRecord(outpatientPaymentRecord);
    }

    // 缴费记录作为重要审计数据，不提供删除功能
    // 如需删除请联系系统管理员手动处理
} 
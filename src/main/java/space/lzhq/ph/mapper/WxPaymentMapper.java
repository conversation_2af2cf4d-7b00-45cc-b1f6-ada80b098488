package space.lzhq.ph.mapper;

import space.lzhq.ph.domain.WxPayment;

import java.util.List;

/**
 * 充值记录Mapper接口
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
public interface WxPaymentMapper {
    /**
     * 查询充值记录
     *
     * @param id 充值记录ID
     * @return 充值记录
     */
    WxPayment selectWxPaymentById(Long id);

    /**
     * 查询充值记录列表
     *
     * @param wxPayment 充值记录
     * @return 充值记录集合
     */
    List<WxPayment> selectWxPaymentList(WxPayment wxPayment);

    /**
     * 新增充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    int insertWxPayment(WxPayment wxPayment);

    /**
     * 修改充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    int updateWxPayment(WxPayment wxPayment);

    /**
     * 删除充值记录
     *
     * @param id 充值记录ID
     * @return 结果
     */
    int deleteWxPaymentById(Long id);

    /**
     * 批量删除充值记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWxPaymentByIds(String[] ids);

    /**
     * 查找充值记录
     *
     * @param zyPayNo 掌医订单号
     */
    WxPayment selectWxPaymentByZyPayNo(String zyPayNo);

    Long sumAmount(WxPayment wxPayment);

    /**
     * 使用乐观锁更新充值记录
     *
     * @param wxPayment 充值记录（必须包含版本号）
     * @return 受影响的行数
     */
    int updateWithOptimisticLock(WxPayment wxPayment);
}

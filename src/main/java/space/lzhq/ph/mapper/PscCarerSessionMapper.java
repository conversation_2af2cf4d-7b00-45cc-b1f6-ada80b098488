package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.PscCarerSession;

import java.util.List;

/**
 * 陪检会话Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-12
 */
@Repository
public interface PscCarerSessionMapper {
    /**
     * 查询陪检会话
     *
     * @param id 陪检会话ID
     * @return 陪检会话
     */
    PscCarerSession selectPscCarerSessionById(Long id);

    /**
     * 查询陪检会话
     *
     * @param employeeNo 工号
     * @param clientType 客户端类型
     * @return 陪检会话
     */
    PscCarerSession selectPscCarerSessionByEmployeeNoAndClientType(
            @Param("employeeNo") String employeeNo,
            @Param("clientType") Integer clientType
    );

    /**
     * 查询陪检会话
     *
     * @param openId openId
     * @return 陪检会话
     */
    PscCarerSession selectPscCarerSessionByOpenId(@Param("openId") String openId);

    /**
     * 是否存在指定工号和客户端类型的陪检会话
     *
     * @param employeeNo 工号
     * @param clientType 客户端类型
     * @return 是否存在
     */
    boolean existsByEmployeeNoAndClientType(
            @Param("employeeNo") String employeeNo,
            @Param("clientType") Integer clientType
    );

    /**
     * 查询陪检会话列表
     *
     * @param pscCarerSession 陪检会话
     * @return 陪检会话集合
     */
    List<PscCarerSession> selectPscCarerSessionList(PscCarerSession pscCarerSession);

    /**
     * 新增陪检会话
     *
     * @param pscCarerSession 陪检会话
     * @return 结果
     */
    int insertPscCarerSession(PscCarerSession pscCarerSession);

    /**
     * 修改陪检会话
     *
     * @param pscCarerSession 陪检会话
     * @return 结果
     */
    int updatePscCarerSession(PscCarerSession pscCarerSession);

    /**
     * 删除陪检会话
     *
     * @param id 陪检会话ID
     * @return 结果
     */
    int deletePscCarerSessionById(Long id);

    /**
     * 批量删除陪检会话
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePscCarerSessionByIds(String[] ids);

    /**
     * 删除陪检会话
     *
     * @param openId openId
     * @return 删除成功的记录数
     */
    int deletePscCarerSessionByOpenId(@Param("openId") String openId);
}

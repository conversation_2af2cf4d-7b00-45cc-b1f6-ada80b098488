package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.CardRefundTimeline;
import space.lzhq.ph.enums.CardRefundEventType;

import java.util.List;

/**
 * 余额清退登记时间线Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CardRefundTimelineMapper extends BaseMapper<CardRefundTimeline> {

    /**
     * 根据申报记录ID查询时间线事件列表
     *
     * @param registrationId 申报记录ID
     * @param includeDetails 是否包含详细操作（false时只返回重要事件）
     * @return 时间线事件列表，按事件时间倒序排列
     */
    List<CardRefundTimeline> selectTimelineByRegistrationId(
            @Param("registrationId") String registrationId,
            @Param("includeDetails") Boolean includeDetails
    );

    /**
     * 根据申报记录ID和事件类型查询最新的时间线事件
     *
     * @param registrationId 申报记录ID
     * @param eventType      事件类型
     * @return 最新的时间线事件
     */
    CardRefundTimeline selectLatestByRegistrationIdAndEventType(
            @Param("registrationId") String registrationId,
            @Param("eventType") CardRefundEventType eventType
    );

    /**
     * 批量插入时间线事件
     *
     * @param timelines 时间线事件列表
     * @return 插入的记录数
     */
    int insertBatch(@Param("timelines") List<CardRefundTimeline> timelines);
} 
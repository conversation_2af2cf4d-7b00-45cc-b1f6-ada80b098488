package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.Session;

import java.util.List;

/**
 * 会话Mapper接口
 *
 * <AUTHOR>
 * @date 2020-05-24
 */
@Repository
public interface SessionMapper {
    /**
     * 查询会话
     *
     * @param id 会话ID
     * @return 会话
     */
    Session selectSessionById(String id);

    /**
     * 查询会话列表
     *
     * @param wxSession 会话
     * @return 会话集合
     */
    List<Session> selectSessionList(Session wxSession);

    /**
     * 新增会话
     *
     * @param wxSession 会话
     * @return 结果
     */
    int insertSession(Session wxSession);

    /**
     * 修改会话
     *
     * @param wxSession 会话
     * @return 结果
     */
    int updateSession(Session wxSession);

    /**
     * 删除会话
     *
     * @param id 会话ID
     * @return 结果
     */
    int deleteSessionById(String id);

    /**
     * 删除会话
     *
     * @param openId openid
     * @return 结果
     */
    int deleteSessionByOpenId(String openId);

    /**
     * 批量删除会话
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteSessionByIds(String[] ids);
}
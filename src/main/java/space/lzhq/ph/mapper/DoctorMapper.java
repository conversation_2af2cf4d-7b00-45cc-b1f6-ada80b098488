package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.Doctor;

import java.util.List;

/**
 * 医生Mapper接口
 *
 * @date 2020-05-27
 */
@Repository
public interface DoctorMapper {
    /**
     * 查询医生
     *
     * @param id 医生ID
     * @return 医生
     */
    Doctor selectDoctorById(String id);

    /**
     * 查询医生列表
     *
     * @param doctor 医生
     * @return 医生集合
     */
    List<Doctor> selectDoctorList(Doctor doctor);

    /**
     * 查询所有医生的列表
     *
     * @return 医生集合
     */
    List<Doctor> selectDoctorAll();

    /**
     * 新增医生
     *
     * @param doctor 医生
     * @return 结果
     */
    int insertDoctor(Doctor doctor);

    /**
     * 修改医生
     *
     * @param doctor 医生
     * @return 结果
     */
    int updateDoctor(Doctor doctor);

    /**
     * 删除医生
     *
     * @param id 医生ID
     * @return 结果
     */
    int deleteDoctorById(String id);

    /**
     * 批量删除医生
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteDoctorByIds(String[] ids);

    /**
     * 批量插入科室
     */
    boolean insertDoctors(List<Doctor> doctors);

    /**
     * 批量更新科室
     */
    boolean updateDoctors(List<Doctor> doctors);

    void truncateTable();
}
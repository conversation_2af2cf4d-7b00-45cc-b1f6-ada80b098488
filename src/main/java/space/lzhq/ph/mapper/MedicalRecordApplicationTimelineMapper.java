package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.MedicalRecordApplicationTimeline;
import space.lzhq.ph.enums.MedicalRecordApplicationEventType;

import java.util.List;

/**
 * 病历复印申请时间线Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicalRecordApplicationTimelineMapper extends BaseMapper<MedicalRecordApplicationTimeline> {

    /**
     * 根据申请记录ID查询时间线事件列表
     *
     * @param applicationId  申请记录ID
     * @param includeDetails 是否包含详细操作（false时只返回重要事件）
     * @return 时间线事件列表，按事件时间倒序排列
     */
    List<MedicalRecordApplicationTimeline> selectTimelineByApplicationId(
            @Param("applicationId") String applicationId,
            @Param("includeDetails") Boolean includeDetails
    );

    /**
     * 根据申请记录ID和事件类型查询最新的时间线事件
     *
     * @param applicationId 申请记录ID
     * @param eventType     事件类型
     * @return 最新的时间线事件
     */
    MedicalRecordApplicationTimeline selectLatestByApplicationIdAndEventType(
            @Param("applicationId") String applicationId,
            @Param("eventType") MedicalRecordApplicationEventType eventType
    );

    /**
     * 批量插入时间线事件
     *
     * @param timelines 时间线事件列表
     * @return 插入的记录数
     */
    int insertBatch(@Param("timelines") List<MedicalRecordApplicationTimeline> timelines);
} 
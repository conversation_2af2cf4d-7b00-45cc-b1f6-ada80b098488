package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.AlipayUserInfo;

import java.util.List;

/**
 * 支付宝用户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-08
 */
@Repository
public interface AlipayUserInfoMapper {
    /**
     * 查询支付宝用户信息
     *
     * @param id 支付宝用户信息ID
     * @return 支付宝用户信息
     */
    AlipayUserInfo selectAlipayUserInfoById(String id);

    /**
     * 查询支付宝用户信息列表
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 支付宝用户信息集合
     */
    List<AlipayUserInfo> selectAlipayUserInfoList(AlipayUserInfo alipayUserInfo);

    /**
     * 新增支付宝用户信息
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 结果
     */
    int insertAlipayUserInfo(AlipayUserInfo alipayUserInfo);

    /**
     * 修改支付宝用户信息
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 结果
     */
    int updateAlipayUserInfo(AlipayUserInfo alipayUserInfo);

    /**
     * 删除支付宝用户信息
     *
     * @param id 支付宝用户信息ID
     * @return 结果
     */
    int deleteAlipayUserInfoById(String id);

    /**
     * 批量删除支付宝用户信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteAlipayUserInfoByIds(String[] ids);
}

package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.PscCarer;

import java.util.List;

/**
 * 陪检Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-11
 */
@Repository
public interface PscCarerMapper {
    /**
     * 查询陪检
     *
     * @param id 陪检ID
     * @return 陪检
     */
    PscCarer selectPscCarerById(Long id);

    /**
     * 查询护士
     *
     * @param employeeNo 工号
     * @return 护士
     */
    PscCarer selectPscCarerByEmployeeNo(@Param("employeeNo") String employeeNo);

    /**
     * 查询陪检列表
     *
     * @param pscCarer 陪检
     * @return 陪检集合
     */
    List<PscCarer> selectPscCarerList(PscCarer pscCarer);

    /**
     * 检查工号是否唯一
     *
     * @param pscNurse 陪检
     * @return 工号是否唯一
     */
    boolean isEmployeeNoUnique(PscCarer pscCarer);

    /**
     * 检查手机号是否唯一
     *
     * @param pscNurse 陪检
     * @return 手机号是否唯一
     */
    boolean isMobileNoUnique(PscCarer pscCarer);

    /**
     * 新增陪检
     *
     * @param pscCarer 陪检
     * @return 结果
     */
    int insertPscCarer(PscCarer pscCarer);

    /**
     * 修改陪检
     *
     * @param pscCarer 陪检
     * @return 结果
     */
    int updatePscCarer(PscCarer pscCarer);

    /**
     * 删除陪检
     *
     * @param id 陪检ID
     * @return 结果
     */
    int deletePscCarerById(Long id);

    /**
     * 批量删除陪检
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePscCarerByIds(String[] ids);
}

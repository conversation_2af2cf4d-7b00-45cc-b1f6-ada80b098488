package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;
import space.lzhq.ph.domain.CardRefund;

@Mapper
public interface CardRefundMapper extends BaseMapper<CardRefund> {

    @Update("""
            UPDATE card_refund
            SET agent_flag = 0,
            agent_name = NULL,
            agent_id_card_no = NULL,
            agent_id_card_attachment_id = NULL,
            agent_mobile = NULL,
            agent_relation = NULL,
            update_time = NOW()
            WHERE id = #{id}
            """)
    int clearAgent(String id);

}
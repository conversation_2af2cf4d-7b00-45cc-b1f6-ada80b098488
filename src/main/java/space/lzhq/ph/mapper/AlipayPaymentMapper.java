package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.AlipayPayment;

import java.util.List;

/**
 * 支付宝支付Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-09
 */
@Repository
public interface AlipayPaymentMapper {
    /**
     * 查询支付宝支付
     *
     * @param id 支付宝支付ID
     * @return 支付宝支付
     */
    AlipayPayment selectAlipayPaymentById(Long id);

    AlipayPayment selectAlipayPaymentByOutTradeNo(@Param("outTradeNo") String outTradeNo);

    /**
     * 查询支付宝支付列表
     *
     * @param alipayPayment 支付宝支付
     * @return 支付宝支付集合
     */
    List<AlipayPayment> selectAlipayPaymentList(AlipayPayment alipayPayment);

    /**
     * 新增支付宝支付
     *
     * @param alipayPayment 支付宝支付
     * @return 结果
     */
    int insertAlipayPayment(AlipayPayment alipayPayment);

    /**
     * 修改支付宝支付
     *
     * @param alipayPayment 支付宝支付
     * @return 结果
     */
    int updateAlipayPayment(AlipayPayment alipayPayment);

    /**
     * 删除支付宝支付
     *
     * @param id 支付宝支付ID
     * @return 结果
     */
    int deleteAlipayPaymentById(Long id);

    /**
     * 批量删除支付宝支付
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteAlipayPaymentByIds(String[] ids);

    Long sumAmount(AlipayPayment payment);
}

package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.Department;

import java.util.List;

/**
 * 科室Mapper接口
 *
 * @date 2020-05-24
 */
@Repository
public interface DepartmentMapper {
    /**
     * 查询科室
     *
     * @param id 科室ID
     * @return 科室
     */
    Department selectDepartmentById(String id);

    List<Department> selectDepartmentAll();

    List<Department> selectDepartmentListByKeyword(@Param("keyword") String keyword);

    /**
     * 查询科室列表
     *
     * @param department 科室
     * @return 科室集合
     */
    List<Department> selectDepartmentList(Department department);

    /**
     * 新增科室
     *
     * @param department 科室
     * @return 结果
     */
    int insertDepartment(Department department);

    /**
     * 修改科室
     *
     * @param department 科室
     * @return 结果
     */
    int updateDepartment(Department department);

    /**
     * 删除科室
     *
     * @param id 科室ID
     * @return 结果
     */
    int deleteDepartmentById(String id);

    /**
     * 批量删除科室
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteDepartmentByIds(String[] ids);

    int deleteByType(@Param("type") String type);

    /**
     * 批量插入科室
     */
    boolean insertDepartments(List<Department> departments);

    /**
     * 批量更新科室
     */
    boolean updateDepartments(List<Department> departments);

    void truncateTable();

}

package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.PscOrderSummaryByCarer;
import space.lzhq.ph.domain.PscOrderSummaryByCarerAndDepartment;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface PscOrderSummaryByCarerMapper {

    /**
     * 汇总
     *
     * @param minCreateTime 最小创建时间
     * @param maxCreateTime 最大创建时间
     * @return 汇总结果
     */
    List<PscOrderSummaryByCarer> summary(
            @Param("minCreateTime") LocalDateTime minCreateTime,
            @Param("maxCreateTime") LocalDateTime maxCreateTime
    );

    List<PscOrderSummaryByCarerAndDepartment> summaryByCarerAndDepartment(
            @Param("minCreateTime") LocalDateTime minCreateTime,
            @Param("maxCreateTime") LocalDateTime maxCreateTime,
            @Param("carerEmployeeNo") String carerEmployeeNo
    );

}

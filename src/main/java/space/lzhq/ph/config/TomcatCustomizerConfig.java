package space.lzhq.ph.config;

import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Tomcat自定义配置，添加请求日志记录Valve
 */
@Configuration
public class TomcatCustomizerConfig {

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            factory.addEngineValves(new TomcatRequestLoggingValve());

            // 可以添加更多自定义配置
            factory.addConnectorCustomizers(connector -> {
                // 设置更详细的连接器属性
                connector.setProperty("maxHttpHeaderSize", "10240"); // 10KB
                connector.setProperty("maxHttpRequestHeaderSize", "10240"); // 10KB

                // 启用更详细的日志
                connector.setProperty("logLevel", "INFO");
            });
        };
    }
}

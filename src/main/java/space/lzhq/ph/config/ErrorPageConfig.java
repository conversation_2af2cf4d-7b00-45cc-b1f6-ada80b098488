package space.lzhq.ph.config;

import org.springframework.boot.web.server.ErrorPage;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

@Configuration
public class ErrorPageConfig {

    @Bean
    public WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> webServerFactoryCustomizer() {
        return factory -> {
            // 拦截 400 Bad Request 错误
            ErrorPage errorPage400 = new ErrorPage(HttpStatus.BAD_REQUEST, "/error");
            // 拦截 404 Not Found 错误
            ErrorPage errorPage404 = new ErrorPage(HttpStatus.NOT_FOUND, "/error");
            // 拦截 500 Internal Server Error 错误
            ErrorPage errorPage500 = new ErrorPage(HttpStatus.INTERNAL_SERVER_ERROR, "/error");
            
            factory.addErrorPages(errorPage400, errorPage404, errorPage500);
        };
    }
}

package space.lzhq.ph.config;

import jakarta.servlet.ServletException;
import org.apache.catalina.connector.Request;
import org.apache.catalina.connector.Response;
import org.apache.catalina.valves.ValveBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Enumeration;

/**
 * 自定义Tomcat Valve，用于记录HTTP请求头过大等低层次错误
 * 这个Valve在Tomcat Connector层面工作，可以捕获到Spring Boot无法处理的错误
 */
public class TomcatRequestLoggingValve extends ValveBase {

    private static final Logger log = LoggerFactory.getLogger(TomcatRequestLoggingValve.class);

    @Override
    public void invoke(Request request, Response response) throws IOException, ServletException {
        long startTime = System.currentTimeMillis();

        // 记录请求开始信息
        logRequestStart(request);

        try {
            // 继续处理请求
            getNext().invoke(request, response);
        } catch (Exception e) {
            // 捕获处理过程中的异常
            logRequestError(request, response, e);
            throw e;
        } finally {
            // 记录请求结束信息
            logRequestEnd(request, response, startTime);
        }
    }

    private void logRequestStart(Request request) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("====== Tomcat Valve 请求开始 ======\n");
            sb.append("请求URI: ").append(request.getRequestURI()).append("\n");
            sb.append("请求方法: ").append(request.getMethod()).append("\n");
            sb.append("客户端IP: ").append(request.getRemoteAddr()).append("\n");
            sb.append("User-Agent: ").append(request.getHeader("User-Agent")).append("\n");

            // 计算请求头总大小
            int totalHeaderSize = calculateRequestHeaderSize(request);
            sb.append("请求头总大小: ").append(totalHeaderSize).append(" 字节\n");

            // 如果请求头接近限制，记录警告
            if (totalHeaderSize > 1800) { // 接近2KB限制
                sb.append("⚠️ 警告：请求头大小接近限制！\n");
                logAllHeaders(request, sb);
            }

            log.info(sb.toString());
        } catch (Exception e) {
            log.error("记录请求开始信息时出错", e);
        }
    }

    private void logRequestEnd(Request request, Response response, long startTime) {
        try {
            long duration = System.currentTimeMillis() - startTime;
            int statusCode = response.getStatus();

            StringBuilder sb = new StringBuilder();
            sb.append("====== Tomcat Valve 请求结束 ======\n");
            sb.append("状态码: ").append(statusCode).append("\n");
            sb.append("处理时间: ").append(duration).append(" ms\n");
            sb.append("响应大小: ").append(response.getContentLength()).append(" 字节\n");

            // 如果是400错误，记录详细信息
            if (statusCode == 400) {
                sb.append("❌ 400错误详细信息：\n");
                sb.append("可能原因：HTTP请求头过大或格式错误\n");
                logAllHeaders(request, sb);
            }

            log.info(sb.toString());
        } catch (Exception e) {
            log.error("记录请求结束信息时出错", e);
        }
    }

    private void logRequestError(Request request, Response response, Exception e) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("====== Tomcat Valve 请求异常 ======\n");
            sb.append("异常类型: ").append(e.getClass().getName()).append("\n");
            sb.append("异常信息: ").append(e.getMessage()).append("\n");
            sb.append("请求URI: ").append(request.getRequestURI()).append("\n");

            log.error(sb.toString(), e);
        } catch (Exception ex) {
            log.error("记录请求异常信息时出错", ex);
        }
    }

    private int calculateRequestHeaderSize(Request request) {
        try {
            int totalSize = 0;

            // 请求行大小
            String requestLine = request.getMethod() + " " + request.getRequestURI();
            if (request.getQueryString() != null) {
                requestLine += "?" + request.getQueryString();
            }
            requestLine += " HTTP/1.1";
            totalSize += requestLine.length();

            // 所有请求头大小
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                totalSize += headerName.length() + headerValue.length() + 4; // +4 for ": " and "\r\n"
            }

            return totalSize;
        } catch (Exception e) {
            log.error("计算请求头大小时出错", e);
            return 0;
        }
    }

    private void logAllHeaders(Request request, StringBuilder sb) {
        try {
            sb.append("------ 所有请求头 ------\n");
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                sb.append(headerName).append(": ");

                // 如果头部值太长，截断显示
                if (headerValue.length() > 200) {
                    sb.append(headerValue.substring(0, 200)).append("...[截断]");
                } else {
                    sb.append(headerValue);
                }
                sb.append(" (").append(headerValue.length()).append(" 字节)\n");
            }
            sb.append("------------------------\n");
        } catch (Exception e) {
            sb.append("记录请求头时出错: ").append(e.getMessage()).append("\n");
        }
    }
}

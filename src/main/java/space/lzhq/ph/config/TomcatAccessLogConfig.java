package space.lzhq.ph.config;

import org.apache.catalina.valves.AccessLogValve;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Tomcat访问日志配置
 * 确保记录真实的客户端IP地址
 */
@Configuration
public class TomcatAccessLogConfig {

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> accessLogCustomizer() {
        return factory -> {
            // 创建自定义的访问日志Valve
            AccessLogValve accessLogValve = new AccessLogValve();

            // 设置日志目录
            accessLogValve.setDirectory("logs");

            // 设置日志文件前缀
            accessLogValve.setPrefix("access_log");

            // 设置日志文件后缀
            accessLogValve.setSuffix(".log");

            // 设置日期格式
            accessLogValve.setFileDateFormat(".yyyy-MM-dd");

            // 设置自定义日志格式，优先使用X-Real-IP
            // 格式说明：
            // %{X-Real-IP}i 头部值（真实客户端IP）
            // %a - 远程IP地址（如果没有代理，这是真实IP）
            // %l - 远程逻辑用户名
            // %u - 远程用户名
            // %t - 时间戳
            // %r - 请求行
            // %s - HTTP状态码
            // %b - 响应字节数
            // %D - 处理时间（微秒）
            // %{Referer}i - Referer头部
            // %{User-Agent}i - User-Agent头部

            String pattern = "%{X-Real-IP}i %a %l %u %t \"%r\" %s %b \"%{Referer}i\" \"%{User-Agent}i\" %D";
            accessLogValve.setPattern(pattern);

            // 启用访问日志
            accessLogValve.setEnabled(true);

            // 添加到Tomcat
            factory.addEngineValves(accessLogValve);
        };
    }
}

package space.lzhq.ph;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(
        scanBasePackages = {"com.ruoyi", "space.lzhq.ph", "space.lzhq.ph.webservice"},
        exclude = {DataSourceAutoConfiguration.class}
)
@MapperScan(value = {"com.ruoyi.*.mapper", "space.lzhq.ph.mapper"})
public class ZhangyiApplication {

    private static final Logger log = LoggerFactory.getLogger(ZhangyiApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(ZhangyiApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  若依启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " .-------.       ____     __        \n" +
                " |  _ _   \\      \\   \\   /  /    \n" +
                " | ( ' )  |       \\  _. /  '       \n" +
                " |(_ o _) /        _( )_ .'         \n" +
                " | (_,_).' __  ___(_ o _)'          \n" +
                " |  |\\ \\  |  ||   |(_,_)'         \n" +
                " |  | \\ `'   /|   `-'  /           \n" +
                " |  |  \\    /  \\      /           \n" +
                " ''-'   `'-'    `-..-'              ");
    }

}

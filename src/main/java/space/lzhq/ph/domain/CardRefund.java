package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.xss.Xss;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.data.PhoneUtil;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.enums.Relationship;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName(value = "card_refund", autoResultMap = true)
public class CardRefund implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String openId;

    @NotBlank(message = "申报患者身份证附件ID不能为空")
    private String patientIdCardAttachmentId;

    /**
     * 申报患者身份证号
     */
    @NotBlank(message = "申报患者身份证号不能为空")
    private String patientIdCardNo;

    /**
     * 申报患者姓名
     */
    @NotBlank(message = "申报患者姓名不能为空")
    @Xss(message = "申报患者姓名不能包含特殊字符")
    private String patientName;

    /**
     * 申报患者性别
     */
    private String patientGender;

    /**
     * 申报患者出生日期
     */
    private LocalDate patientBirthDate;

    /**
     * 申报患者手机号
     */
    @NotBlank(message = "申报患者手机号不能为空")
    private String patientMobile;

    /**
     * 是否代办 0-否 1-是
     */
    @Excel(name = "是否代办", readConverterExp = "0=否,1=是")
    @NotNull(message = "是否代办不能为空")
    private Integer agentFlag;

    /**
     * 代办人姓名
     */
    @Excel(name = "代办人姓名")
    private String agentName;

    /**
     * 代办人身份证号
     */
    @Excel(name = "代办人身份证号")
    private String agentIdCardNo;

    /**
     * 代办人身份证附件ID
     */
    @Excel(name = "代办人身份证附件ID")
    private String agentIdCardAttachmentId;

    /**
     * 代办人手机号
     */
    @Excel(name = "代办人手机号")
    private String agentMobile;

    /**
     * 代办人与患者关系
     */
    @Excel(name = "代办人与患者关系")
    private Relationship agentRelation;

    /**
     * 银行卡附件ID
     */
    @NotBlank(message = "银行卡附件ID不能为空")
    private String bankCardAttachmentId;

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空")
    private String bankCardNo;

    /**
     * 申报状态
     * DRAFT - 草稿
     * SUBMITTED - 已提交
     * APPROVED - 已通过
     * REJECTED - 已驳回
     */
    @Excel(name = "申报状态")
    @NotNull(message = "申报状态不能为空")
    @EnumValue
    private CardRefundStatus status;

    /**
     * 提交时间
     */
    @Excel(name = "提交时间")
    private LocalDateTime submitTime;

    /**
     * 审批时间
     */
    @Excel(name = "审批时间")
    private LocalDateTime approveTime;

    /**
     * 驳回原因
     */
    @Excel(name = "驳回原因")
    private String rejectReason;

    /**
     * 整改意见
     */
    @Excel(name = "整改意见")
    private String correctionAdvice;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记：0-未删除 1-已删除
     */
    private Boolean deleteFlag = false;

    /**
     * 验证代办人相关字段
     * 当agentFlag为1时，代办人相关字段不能为空
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "当选择代办时，代办人姓名、身份证号、身份证附件ID、手机号和与患者关系不能为空")
    public boolean isAgentFieldsValid() {
        // 如果agentFlag为null或0，不需要验证代办人字段
        if (agentFlag == null || agentFlag == 0) {
            return true;
        }

        // 如果agentFlag为1，验证代办人相关字段不能为空
        return agentName != null && !agentName.trim().isEmpty()
                && agentIdCardNo != null && !agentIdCardNo.trim().isEmpty()
                && agentIdCardAttachmentId != null && !agentIdCardAttachmentId.trim().isEmpty()
                && agentMobile != null && !agentMobile.trim().isEmpty()
                && agentRelation != null;
    }

    /**
     * 验证患者身份证号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "患者身份证号格式不正确")
    public boolean isPatientIdCardValid() {
        return patientIdCardNo == null || IdcardUtil.isValidCard(patientIdCardNo);
    }

    /**
     * 验证患者手机号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "患者手机号格式不正确")
    public boolean isPatientMobileValid() {
        return patientMobile == null || PhoneUtil.isMobile(patientMobile);
    }

    /**
     * 验证代办人身份证号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "代办人身份证号格式不正确")
    public boolean isAgentIdCardValid() {
        // 如果agentFlag为null或0，不需要验证代办人身份证号
        if (agentFlag == null || agentFlag == 0) {
            return true;
        }
        return agentIdCardNo == null || IdcardUtil.isValidCard(agentIdCardNo);
    }

    /**
     * 验证代办人手机号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "代办人手机号格式不正确")
    public boolean isAgentMobileValid() {
        // 如果agentFlag为null或0，不需要验证代办人手机号
        if (agentFlag == null || agentFlag == 0) {
            return true;
        }
        return agentMobile == null || PhoneUtil.isMobile(agentMobile);
    }

    /**
     * 判断当前余额清退登记是否可以被用户（申请人或代理人）修改
     *
     * @return 如果记录的状态允许用户修改则返回true，否则返回false
     */
    public boolean canBeModifiedByUser() {
        return status != null && status.isEditableByApplicantOrAgent();
    }

}

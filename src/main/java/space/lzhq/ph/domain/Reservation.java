package space.lzhq.ph.domain;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 预约记录对象 ph_reservation
 *
 * <AUTHOR>
 * @date 2022-11-06
 */
@TableName(value = "ph_reservation")
public class Reservation implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 预约
     */
    @TableField(exist = false)
    public static final int OPERATION_TYPE_RESERVE = 0;

    /**
     * 取消预约
     */
    @TableField(exist = false)
    public static final int OPERATION_TYPE_CANCEL = 1;

    /**
     * 操作成功
     */
    @TableField(exist = false)
    public static final int OPERATION_RESULT_SUCCESS = 0;

    /**
     * 操作失败
     */
    @TableField(exist = false)
    public static final int OPERATION_RESULT_FAIL = 1;


    /**
     * 预约成功
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_INIT = 0;

    /**
     * 取消预约
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_CANCELED = 9;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作类型
     */
    @Excel(name = "操作类型", dictType = "reservation_operation_type")
    private Integer operationType;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationTime;

    private String openid;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    @Excel(name = "实体卡号")
    private String jzCardNo;

    /**
     * 虚拟卡号，相当于 cardNo
     */
    @Excel(name = "虚拟卡号")
    private String patientId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * 就诊日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "就诊日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date jzDate;

    /**
     * 就诊时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "就诊时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date jzTime;

    private String ampm;

    /**
     * 挂号类型
     */
    @Excel(name = "挂号类型")
    private String reservationType;

    /**
     * 科室编码
     */
    @Excel(name = "科室编码")
    private String departmentCode;

    /**
     * 科室名称
     */
    @Excel(name = "科室名称")
    private String departmentName;

    /**
     * 科室位置
     */
    @Excel(name = "科室位置")
    private String departmentLocation;

    /**
     * 医生编码
     */
    @Excel(name = "医生编码")
    private String doctorCode;

    /**
     * 医生姓名
     */
    @Excel(name = "医生姓名")
    private String doctorName;

    /**
     * 排班号
     */
    @Excel(name = "排班号")
    private String schid;

    private String bookIndexId;

    /**
     * 挂号费
     */
    @Excel(name = "挂号费")
    private BigDecimal registerFee;

    /**
     * 顺序号
     */
    @Excel(name = "顺序号")
    private String visitNumber;

    /**
     * 门诊流水号
     */
    @Excel(name = "门诊流水号")
    private String clinicNo;

    /**
     * 操作结果
     */
    @Excel(name = "操作结果")
    private Integer operationResult;

    /**
     * 操作描述
     */
    @Excel(name = "操作描述")
    private String operationDesc;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * 支付宝医疗订单ID
     */
    private String alipayHospitalOrderId;

    /**
     * 就诊状态
     * 0:未就诊 1:已就诊
     */
    @TableField(exist = false)
    private String clinicStatus;

    public Reservation() {
    }

    public Reservation(Patient patient) {
        this.operationType = OPERATION_TYPE_RESERVE;
        this.operationTime = DateUtil.date().setField(DateField.MILLISECOND, 0);
        this.openid = patient.getOpenId();
        this.idCardNo = patient.getIdCardNo();
        this.jzCardNo = patient.getJzCardNo();
        this.patientId = patient.getCardNo();
        this.name = patient.getName();
        this.mobile = patient.getMobile();
    }


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public String getJzCardNo() {
        return jzCardNo;
    }

    public void setJzCardNo(String jzCardNo) {
        this.jzCardNo = jzCardNo;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setJzDate(Date jzDate) {
        this.jzDate = jzDate;
    }

    public Date getJzDate() {
        return jzDate;
    }

    public void setJzTime(Date jzTime) {
        this.jzTime = jzTime;
    }

    public Date getJzTime() {
        return jzTime;
    }

    public String getAmpm() {
        return ampm;
    }

    public void setAmpm(String ampm) {
        this.ampm = ampm;
    }

    public String getReservationType() {
        return reservationType;
    }

    public void setReservationType(String reservationType) {
        this.reservationType = reservationType;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentLocation(String departmentLocation) {
        this.departmentLocation = departmentLocation;
    }

    public String getDepartmentLocation() {
        return departmentLocation;
    }

    public void setDoctorCode(String doctorCode) {
        this.doctorCode = doctorCode;
    }

    public String getDoctorCode() {
        return doctorCode;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public String getSchid() {
        return schid;
    }

    public void setSchid(String schid) {
        this.schid = schid;
    }

    public String getBookIndexId() {
        return bookIndexId;
    }

    public void setBookIndexId(String bookIndexId) {
        this.bookIndexId = bookIndexId;
    }

    public BigDecimal getRegisterFee() {
        return registerFee;
    }

    public void setRegisterFee(BigDecimal registerFee) {
        this.registerFee = registerFee;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public void setVisitNumber(String visitNumber) {
        this.visitNumber = visitNumber;
    }

    public String getVisitNumber() {
        return visitNumber;
    }

    public void setClinicNo(String clinicNo) {
        this.clinicNo = clinicNo;
    }

    public String getClinicNo() {
        return clinicNo;
    }

    public void setOperationResult(Integer operationResult) {
        this.operationResult = operationResult;
    }

    public Integer getOperationResult() {
        return operationResult;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAlipayHospitalOrderId() {
        return alipayHospitalOrderId;
    }

    public String getClinicStatus() {
        return clinicStatus;
    }

    public void setClinicStatus(String clinicStatus) {
        this.clinicStatus = clinicStatus;
    }

    public void setAlipayHospitalOrderId(String alipayHospitalOrderId) {
        this.alipayHospitalOrderId = alipayHospitalOrderId;
    }

    public boolean isCanCancel() {
        return this.operationType == OPERATION_TYPE_RESERVE
                && this.operationResult == OPERATION_RESULT_SUCCESS
                && Objects.equals(this.status, RESERVATION_STATUS_INIT)
                && DateTime.now().isBefore(this.getJzDate());
    }

    public Reservation toCancel() {
        Reservation cancel = new Reservation();
        cancel.setOperationType(OPERATION_TYPE_CANCEL);
        cancel.setOperationTime(DateUtil.date().setField(DateField.MILLISECOND, 0));
        cancel.setOpenid(this.openid);
        cancel.setIdCardNo(this.idCardNo);
        cancel.setJzCardNo(this.jzCardNo);
        cancel.setPatientId(this.patientId);
        cancel.setName(this.name);
        cancel.setMobile(this.mobile);
        cancel.setJzDate(this.jzDate);
        cancel.setJzTime(this.jzTime);
        cancel.setAmpm(this.ampm);
        cancel.setSchid(this.schid);
        cancel.setBookIndexId(this.bookIndexId);
        cancel.setRegisterFee(this.registerFee);
        cancel.setReservationType(this.reservationType);
        cancel.setDepartmentCode(this.departmentCode);
        cancel.setDepartmentName(this.departmentName);
        cancel.setDoctorCode(this.doctorCode);
        cancel.setDoctorName(this.doctorName);
        cancel.setDepartmentLocation(this.departmentLocation);
        cancel.setVisitNumber(this.visitNumber);
        cancel.setClinicNo(this.clinicNo);
        cancel.setStatus(RESERVATION_STATUS_CANCELED);
        return cancel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("operationType", getOperationType())
                .append("operationTime", getOperationTime())
                .append("idCardNo", getIdCardNo())
                .append("jzCardNo", getPatientId())
                .append("name", getName())
                .append("mobile", getMobile())
                .append("jzDate", getJzDate())
                .append("jzTime", getJzTime())
                .append("departmentCode", getDepartmentCode())
                .append("departmentName", getDepartmentName())
                .append("departmentLocation", getDepartmentLocation())
                .append("doctorCode", getDoctorCode())
                .append("visitNumber", getVisitNumber())
                .append("reservationNumber", getClinicNo())
                .append("operationResult", getOperationResult())
                .append("operationDesc", getOperationDesc())
                .toString();
    }
}

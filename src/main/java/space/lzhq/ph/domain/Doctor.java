package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excels;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.jetbrains.annotations.NotNull;

/**
 * 医生对象 ph_doctor
 *
 * @date 2020-05-27
 */
public class Doctor extends BaseEntity implements Comparable<Doctor> {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 科室编码
     */
    @Excel(name = "科室编码", type = Excel.Type.IMPORT)
    private String departmentId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private Integer sex;

    /**
     * 职称
     */
    @Excel(name = "职称")
    private String title;

    @Excel(name = "职务")
    private String position;

    @Excel(name = "挂号类别")
    private String regType;

    @Excel(name = "挂号费")
    private Double regFee;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 擅长
     */
    @Excel(name = "擅长")
    private String expertise;

    /**
     * 简介
     */
    @Excel(name = "简介")
    private String intro;

    /**
     * 照片
     */
    @Excel(name = "照片")
    private String photo;

    /**
     * 排序号
     */
    @Excel(name = "排序号")
    private Long sortNo;

    /**
     * null
     */
    @Excel(name = "null")
    private String simplePinyin;

    /**
     * null
     */
    @Excel(name = "null")
    private String fullPinyin;

    @Excels({
            @Excel(name = "科室名称名称", targetAttr = "name", type = Excel.Type.EXPORT)
    })
    private Department department;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentId() {
        return departmentId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Integer getSex() {
        return sex;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getRegType() {
        return regType;
    }

    public void setRegType(String regType) {
        this.regType = regType;
    }

    public Double getRegFee() {
        return regFee;
    }

    public void setRegFee(Double regFee) {
        this.regFee = regFee;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public void setExpertise(String expertise) {
        this.expertise = expertise;
    }

    public String getExpertise() {
        return expertise;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getIntro() {
        return intro;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getPhoto() {
        return photo;
    }

    public void setSortNo(Long sortNo) {
        this.sortNo = sortNo;
    }

    public Long getSortNo() {
        return sortNo;
    }

    public void setSimplePinyin(String simplePinyin) {
        this.simplePinyin = simplePinyin;
    }

    public String getSimplePinyin() {
        return simplePinyin;
    }

    public void setFullPinyin(String fullPinyin) {
        this.fullPinyin = fullPinyin;
    }

    public String getFullPinyin() {
        return fullPinyin;
    }

    public Department getDepartment() {
        if (department == null) {
            return new Department();
        } else {
            return department;
        }
    }

    public void setDepartment(Department department) {
        this.department = department;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("departmentId", getDepartmentId())
                .append("name", getName())
                .append("sex", getSex())
                .append("title", getTitle())
                .append("expertise", getExpertise())
                .append("intro", getIntro())
                .append("photo", getPhoto())
                .append("sortNo", getSortNo())
                .append("simplePinyin", getSimplePinyin())
                .append("fullPinyin", getFullPinyin())
                .append("department", getDepartment())
                .toString();
    }

    @Override
    public int compareTo(@NotNull Doctor other) {
        int diffSortNo = other.sortNo.compareTo(this.sortNo);
        if (diffSortNo != 0) {
            return diffSortNo;
        }

        return this.simplePinyin.compareTo(other.simplePinyin);
    }
}
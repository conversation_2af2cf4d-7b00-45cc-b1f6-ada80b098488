package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excels;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import space.lzhq.ph.enums.Relationship;

import java.util.Date;

/**
 * 电子陪护证对象 ph_nurse_card
 *
 * <AUTHOR>
 * @date 2022-05-11
 */
public class NurseCard extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 病区
     */
    @Excel(name = "病区")
    private String department;

    /**
     * 床号
     */
    @Excel(name = "床号")
    private String bed;

    /**
     * 入院时间
     */
    @Excel(name = "入院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ruyuanTime;

    /**
     * 出院时间
     */
    @Excel(name = "出院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date chuyuanTime;

    /**
     * 患者姓名
     */
    @Excel(name = "患者姓名")
    private String patientName;

    /**
     * 患者ID
     */
    @Excel(name = "患者ID")
    private String patientId;

    /**
     * 患者ID
     */
    @Excel(name = "患者身份证号")
    private String patientIdCardNo;

    /**
     * 患者手机号
     */
    @Excel(name = "患者手机号")
    private String patientMobile;

    /**
     * 陪护人姓名
     */
    @Excel(name = "陪护人姓名")
    private String nurseName;

    /**
     * 陪护人身份证号
     */
    @Excel(name = "陪护人身份证号")
    private String nurseIdCardNo;

    /**
     * 陪护人手机号
     */
    @Excel(name = "陪护人手机号")
    private String nurseMobile;

    /**
     * 是患者的
     */
    @Excels({
            @Excel(name = "是患者的", targetAttr = "description", type = Excel.Type.EXPORT)
    })
    private Relationship relationship;

    /**
     * 陪护人核酸报告截图
     */
    private String nurseNatImage;

    /**
     * 陪护人照片
     */
    private String nursePhoto;

    /**
     * 取消时间
     */
    @Excel(name = "取消时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    /**
     * 审核时间
     */
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 生效时间
     */
    @Excel(name = "生效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveTime;

    /**
     * 作废时间
     */
    @Excel(name = "作废时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date invalidTime;

    /**
     * 失效时间
     */
    @Excel(name = "失效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expiredTime;

    /**
     * 状态
     */
    @Excels({
            @Excel(name = "状态", targetAttr = "description", type = Excel.Type.EXPORT)
    })
    private NurseCardStatus status;

    /**
     * 审核意见
     */
    @Excel(name = "审核意见")
    private String auditRemark;

    public NurseCard() {

    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartment() {
        return department;
    }

    public void setBed(String bed) {
        this.bed = bed;
    }

    public String getBed() {
        return bed;
    }

    public void setRuyuanTime(Date ruyuanTime) {
        this.ruyuanTime = ruyuanTime;
    }

    public Date getRuyuanTime() {
        return ruyuanTime;
    }

    public Date getChuyuanTime() {
        return chuyuanTime;
    }

    public void setChuyuanTime(Date chuyuanTime) {
        this.chuyuanTime = chuyuanTime;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientId() {
        return patientId;
    }

    public String getPatientIdCardNo() {
        return patientIdCardNo;
    }

    public void setPatientIdCardNo(String patientIdCardNo) {
        this.patientIdCardNo = patientIdCardNo;
    }

    public void setPatientMobile(String patientMobile) {
        this.patientMobile = patientMobile;
    }

    public String getPatientMobile() {
        return patientMobile;
    }

    public void setNurseName(String nurseName) {
        this.nurseName = nurseName;
    }

    public String getNurseName() {
        return nurseName;
    }

    public void setNurseIdCardNo(String nurseIdCardNo) {
        this.nurseIdCardNo = nurseIdCardNo;
    }

    public String getNurseIdCardNo() {
        return nurseIdCardNo;
    }

    public void setNurseMobile(String nurseMobile) {
        this.nurseMobile = nurseMobile;
    }

    public String getNurseMobile() {
        return nurseMobile;
    }

    public Relationship getRelationship() {
        return relationship;
    }

    public void setRelationship(Relationship relationship) {
        this.relationship = relationship;
    }

    public String getNurseNatImage() {
        return nurseNatImage;
    }

    public void setNurseNatImage(String nurseNatImage) {
        this.nurseNatImage = nurseNatImage;
    }

    public String getNursePhoto() {
        return nursePhoto;
    }

    public void setNursePhoto(String nursePhoto) {
        this.nursePhoto = nursePhoto;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    public Date getCancelTime() {
        return cancelTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setInvalidTime(Date invalidTime) {
        this.invalidTime = invalidTime;
    }

    public Date getInvalidTime() {
        return invalidTime;
    }

    public void setExpiredTime(Date expiredTime) {
        this.expiredTime = expiredTime;
    }

    public Date getExpiredTime() {
        return expiredTime;
    }

    public void setStatus(NurseCardStatus status) {
        this.status = status;
    }

    public NurseCardStatus getStatus() {
        return status;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("zhuyuanNo", getZhuyuanNo())
                .append("department", getDepartment())
                .append("bed", getBed())
                .append("ruyuanTime", getRuyuanTime())
                .append("patientName", getPatientName())
                .append("patientId", getPatientId())
                .append("patientMobile", getPatientMobile())
                .append("nurseName", getNurseName())
                .append("nurseIdCardNo", getNurseIdCardNo())
                .append("nurseMobile", getNurseMobile())
                .append("createTime", getCreateTime())
                .append("cancelTime", getCancelTime())
                .append("auditTime", getAuditTime())
                .append("effectiveTime", getEffectiveTime())
                .append("invalidTime", getInvalidTime())
                .append("expiredTime", getExpiredTime())
                .append("status", getStatus())
                .append("auditRemark", getAuditRemark())
                .toString();
    }
}

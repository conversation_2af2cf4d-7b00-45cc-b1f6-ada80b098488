package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

/**
 * 支付宝对账对象
 */
@ExcelIgnoreUnannotated
public class AlipayCheck extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    @ExcelProperty("日期")
    private Long id;

    /**
     * 掌医支付总金额
     */
    @ExcelProperty(value = {"充值", "掌医"})
    private BigDecimal alipayPayAmount;

    /**
     * 门诊预交金充值总金额
     */
    private BigDecimal alipayMzPayAmount;

    /**
     * 住院预交金充值总金额
     */
    private BigDecimal alipayZyPayAmount;

    /**
     * HIS充值总金额
     */
    @ExcelProperty(value = {"充值", "HIS"})
    private BigDecimal hisPayAmount;

    /**
     * 支付差额
     */
    @ExcelProperty(value = {"充值", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffPayAmount;

    /**
     * 支付平账？
     */
    @ExcelProperty(value = {"充值", "平账？"})
    private Integer payBalanced;

    /**
     * 掌医退款总金额
     */
    @ExcelProperty(value = {"退款", "掌医"})
    private BigDecimal alipayRefundAmount;

    /**
     * 门诊预交金退款总金额
     */
    private BigDecimal alipayMzRefundAmount;

    /**
     * 住院预交金退款总金额
     */
    private BigDecimal alipayZyRefundAmount;

    /**
     * HIS退款总金额
     */
    @ExcelProperty(value = {"退款", "HIS"})
    private BigDecimal hisRefundAmount;

    /**
     * 退款差额
     */
    @ExcelProperty(value = {"退款", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffRefundAmount;

    /**
     * 退款平账？
     */
    @ExcelProperty(value = {"退款", "平账？"})
    private Integer refundBalanced;

    /**
     * 医保支付总金额
     */
    @ExcelProperty(value = {"医保", "支付"})
    private BigDecimal mipPayAmount;

    /**
     * 医保退款总金额
     */
    @ExcelProperty(value = {"医保", "退款"})
    private BigDecimal mipRefundAmount;

    /**
     * 掌医净流入
     */
    @ExcelProperty(value = {"净流入", "掌医"})
    private BigDecimal alipayNetIn;

    /**
     * HIS净流入
     */
    private BigDecimal hisNetIn;

    /**
     * 医保净流入
     */
    @ExcelProperty(value = {"净流入", "医保"})
    private BigDecimal mipNetIn;

    /**
     * 总净流入
     */
    @ExcelProperty(value = {"净流入", "掌医+医保"})
    @ColumnWidth(12)
    private BigDecimal totalNetIn;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setAlipayPayAmount(BigDecimal alipayPayAmount) {
        this.alipayPayAmount = alipayPayAmount;
    }

    public BigDecimal getAlipayPayAmount() {
        return alipayPayAmount;
    }

    public BigDecimal getAlipayMzPayAmount() {
        return alipayMzPayAmount;
    }

    public void setAlipayMzPayAmount(BigDecimal alipayMzPayAmount) {
        this.alipayMzPayAmount = alipayMzPayAmount;
    }

    public BigDecimal getAlipayZyPayAmount() {
        return alipayZyPayAmount;
    }

    public void setAlipayZyPayAmount(BigDecimal alipayZyPayAmount) {
        this.alipayZyPayAmount = alipayZyPayAmount;
    }

    public void setHisPayAmount(BigDecimal hisPayAmount) {
        this.hisPayAmount = hisPayAmount;
    }

    public BigDecimal getHisPayAmount() {
        return hisPayAmount;
    }

    public void setDiffPayAmount(BigDecimal diffPayAmount) {
        this.diffPayAmount = diffPayAmount;
    }

    public BigDecimal getDiffPayAmount() {
        return diffPayAmount;
    }

    public void setPayBalanced(Integer payBalanced) {
        this.payBalanced = payBalanced;
    }

    public Integer getPayBalanced() {
        return payBalanced;
    }

    public void setAlipayRefundAmount(BigDecimal alipayRefundAmount) {
        this.alipayRefundAmount = alipayRefundAmount;
    }

    public BigDecimal getAlipayRefundAmount() {
        return alipayRefundAmount;
    }

    public BigDecimal getAlipayMzRefundAmount() {
        return alipayMzRefundAmount;
    }

    public void setAlipayMzRefundAmount(BigDecimal alipayMzRefundAmount) {
        this.alipayMzRefundAmount = alipayMzRefundAmount;
    }

    public BigDecimal getAlipayZyRefundAmount() {
        return alipayZyRefundAmount;
    }

    public void setAlipayZyRefundAmount(BigDecimal alipayZyRefundAmount) {
        this.alipayZyRefundAmount = alipayZyRefundAmount;
    }

    public void setHisRefundAmount(BigDecimal hisRefundAmount) {
        this.hisRefundAmount = hisRefundAmount;
    }

    public BigDecimal getHisRefundAmount() {
        return hisRefundAmount;
    }

    public void setDiffRefundAmount(BigDecimal diffRefundAmount) {
        this.diffRefundAmount = diffRefundAmount;
    }

    public BigDecimal getDiffRefundAmount() {
        return diffRefundAmount;
    }

    public void setRefundBalanced(Integer refundBalanced) {
        this.refundBalanced = refundBalanced;
    }

    public Integer getRefundBalanced() {
        return refundBalanced;
    }

    public void setAlipayNetIn(BigDecimal alipayNetIn) {
        this.alipayNetIn = alipayNetIn;
    }

    public BigDecimal getAlipayNetIn() {
        return alipayNetIn;
    }

    public void setHisNetIn(BigDecimal hisNetIn) {
        this.hisNetIn = hisNetIn;
    }

    public BigDecimal getHisNetIn() {
        return hisNetIn;
    }

    public BigDecimal getMipPayAmount() {
        return mipPayAmount;
    }

    public void setMipPayAmount(BigDecimal mipPayAmount) {
        this.mipPayAmount = mipPayAmount;
    }

    public BigDecimal getMipRefundAmount() {
        return mipRefundAmount;
    }

    public void setMipRefundAmount(BigDecimal mipRefundAmount) {
        this.mipRefundAmount = mipRefundAmount;
    }

    public BigDecimal getMipNetIn() {
        return mipNetIn;
    }

    public void setMipNetIn(BigDecimal mipNetIn) {
        this.mipNetIn = mipNetIn;
    }

    public BigDecimal getTotalNetIn() {
        return totalNetIn;
    }

    public void setTotalNetIn(BigDecimal totalNetIn) {
        this.totalNetIn = totalNetIn;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", id)
                .append("alipayPayAmount", alipayPayAmount)
                .append("alipayMzPayAmount", alipayMzPayAmount)
                .append("alipayZyPayAmount", alipayZyPayAmount)
                .append("hisPayAmount", hisPayAmount)
                .append("diffPayAmount", diffPayAmount)
                .append("payBalanced", payBalanced)
                .append("alipayRefundAmount", alipayRefundAmount)
                .append("alipayMzRefundAmount", alipayMzRefundAmount)
                .append("alipayZyRefundAmount", alipayZyRefundAmount)
                .append("hisRefundAmount", hisRefundAmount)
                .append("diffRefundAmount", diffRefundAmount)
                .append("refundBalanced", refundBalanced)
                .append("alipayNetIn", alipayNetIn)
                .append("hisNetIn", hisNetIn)
                .append("mipPayAmount", mipPayAmount)
                .append("mipRefundAmount", mipRefundAmount)
                .append("mipNetIn", mipNetIn)
                .append("totalNetIn", totalNetIn)
                .toString();
    }
}

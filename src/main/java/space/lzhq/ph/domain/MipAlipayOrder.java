package space.lzhq.ph.domain;

import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "mip_alipay_order")
public class MipAlipayOrder implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = -8850129385384585974L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 授权码
     */
    private String authCode;

    /**
     * 支付宝用户ID
     */
    private String userId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 证件类型
     */
    private String certType;

    /**
     * 参保地
     */
    private String cityId;

    /**
     * 授权Token
     */
    private String accessToken;

    /**
     * 移动支付中心返回授权编号
     */
    private String payAuthNo;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 支付订单号
     */
    @Excel(name = "支付订单号")
    private String payOrderId;

    private String payToken;

    private String clinicNo;

    private String feesJson;

    /**
     * 费用总额
     */
    @Excel(name = "费用总额")
    private BigDecimal feeSumAmount;

    /**
     * 现金支付金额
     */
    @Excel(name = "现金支付金额")
    private BigDecimal ownPayAmount;

    /**
     * 个人账户支出
     */
    @Excel(name = "个人账户支出")
    private BigDecimal personalAccountPayAmount;

    /**
     * 医保基金支出
     */
    @Excel(name = "医保基金支出")
    private BigDecimal fundPayAmount;

    /**
     * 医保电子凭证机构号，获取线上医保业务授权接口返回此参数
     */
    private String medicalCardInstId;

    /**
     * 医保电子凭证授权码，获取线上医保业务授权接口返回此参数
     */
    private String medicalCardId;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 医疗机构订单号
     */
    private String medOrgOrd;

    /**
     * 支付宝订单号
     */
    private String tradeNo;

    /**
     * 交易状态
     */
    private String tradeStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 交易时间
     */
    private Date tradeTime;

    private String hisInvoiceNo;

    private Date hisSettleTime;

    private String hisSettleMessage;

    private String siSettleId;

    private Date siSettleTime;

    private String alipayOutRequestNo;

    private String alipayRefundStatus;

    public MipAlipayOrder() {
    }

    public MipAlipayOrder(String authCode, String accessToken, AlipayUserInfoShareResponse infoShareResponse) {
        this.authCode = authCode;
        this.accessToken = accessToken;
        this.userId = infoShareResponse.getUserId();
        this.userName = infoShareResponse.getUserName();
        this.certNo = infoShareResponse.getCertNo();
        this.certType = infoShareResponse.getCertType();
        this.createTime = new Date();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getPayAuthNo() {
        return payAuthNo;
    }

    public void setPayAuthNo(String payAuthNo) {
        this.payAuthNo = payAuthNo;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getPayOrderId() {
        return payOrderId;
    }

    public void setPayOrderId(String payOrderId) {
        this.payOrderId = payOrderId;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getClinicNo() {
        return clinicNo;
    }

    public void setClinicNo(String clinicNo) {
        this.clinicNo = clinicNo;
    }

    public String getFeesJson() {
        return feesJson;
    }

    public void setFeesJson(String feesJson) {
        this.feesJson = feesJson;
    }

    public BigDecimal getFeeSumAmount() {
        return feeSumAmount;
    }

    public void setFeeSumAmount(BigDecimal feeSumAmount) {
        this.feeSumAmount = feeSumAmount;
    }

    public BigDecimal getOwnPayAmount() {
        return ownPayAmount;
    }

    public void setOwnPayAmount(BigDecimal ownPayAmount) {
        this.ownPayAmount = ownPayAmount;
    }

    public BigDecimal getPersonalAccountPayAmount() {
        return personalAccountPayAmount;
    }

    public void setPersonalAccountPayAmount(BigDecimal personalAccountPayAmount) {
        this.personalAccountPayAmount = personalAccountPayAmount;
    }

    public BigDecimal getFundPayAmount() {
        return fundPayAmount;
    }

    public void setFundPayAmount(BigDecimal fundPayAmount) {
        this.fundPayAmount = fundPayAmount;
    }

    public String getMedicalCardInstId() {
        return medicalCardInstId;
    }

    public void setMedicalCardInstId(String medicalCardInstId) {
        this.medicalCardInstId = medicalCardInstId;
    }

    public String getMedicalCardId() {
        return medicalCardId;
    }

    public void setMedicalCardId(String medicalCardId) {
        this.medicalCardId = medicalCardId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getMedOrgOrd() {
        return medOrgOrd;
    }

    public void setMedOrgOrd(String medOrgOrd) {
        this.medOrgOrd = medOrgOrd;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(String tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(Date tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getHisInvoiceNo() {
        return hisInvoiceNo;
    }

    public void setHisInvoiceNo(String hisInvoiceNo) {
        this.hisInvoiceNo = hisInvoiceNo;
    }

    public Date getHisSettleTime() {
        return hisSettleTime;
    }

    public void setHisSettleTime(Date hisSettleTime) {
        this.hisSettleTime = hisSettleTime;
    }

    public String getHisSettleMessage() {
        return hisSettleMessage;
    }

    public void setHisSettleMessage(String hisSettleMessage) {
        this.hisSettleMessage = hisSettleMessage;
    }

    public String getSiSettleId() {
        return siSettleId;
    }

    public void setSiSettleId(String siSettleId) {
        this.siSettleId = siSettleId;
    }

    public Date getSiSettleTime() {
        return siSettleTime;
    }

    public void setSiSettleTime(Date siSettleTime) {
        this.siSettleTime = siSettleTime;
    }

    public String getAlipayOutRequestNo() {
        return alipayOutRequestNo;
    }

    public void setAlipayOutRequestNo(String alipayOutRequestNo) {
        this.alipayOutRequestNo = alipayOutRequestNo;
    }

    public String getAlipayRefundStatus() {
        return alipayRefundStatus;
    }

    public void setAlipayRefundStatus(String alipayRefundStatus) {
        this.alipayRefundStatus = alipayRefundStatus;
    }
}

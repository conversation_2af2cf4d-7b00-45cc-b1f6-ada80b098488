package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 对账对象 ph_wx_check
 *
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
public class WxCheck extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    @ExcelProperty("日期")
    private Long id;

    /**
     * 掌医支付总金额
     */
    @ExcelProperty(value = {"充值", "掌医"})
    private BigDecimal wxPayAmount;

    private BigDecimal wxMzPayAmount;

    private BigDecimal wxZyPayAmount;

    /**
     * HIS充值总金额
     */
    @ExcelProperty(value = {"充值", "HIS"})
    private BigDecimal hisPayAmount;

    /**
     * 支付差额
     */
    @ExcelProperty(value = {"充值", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffPayAmount;

    /**
     * 支付平账？
     */
    @ExcelProperty(value = {"充值", "平账？"})
    private Integer payBalanced;

    /**
     * 掌医退款总金额
     */
    @ExcelProperty(value = {"退款", "掌医"})
    private BigDecimal wxRefundAmount;

    private BigDecimal wxMzRefundAmount;

    private BigDecimal wxZyRefundAmount;

    /**
     * HIS退款总金额
     */
    @ExcelProperty(value = {"退款", "HIS"})
    private BigDecimal hisRefundAmount;

    /**
     * 退款差额
     */
    @ExcelProperty(value = {"退款", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffRefundAmount;

    /**
     * 退款平账？
     */
    @ExcelProperty(value = {"退款", "平账？"})
    private Integer refundBalanced;

    /**
     * 医保支付总金额
     */
    @ExcelProperty(value = {"医保", "支付"})
    private BigDecimal mipPayAmount;

    /**
     * 医保退款总金额
     */
    @ExcelProperty(value = {"医保", "退款"})
    private BigDecimal mipRefundAmount;
    /**
     * 掌医净流入
     */
    @ExcelProperty(value = {"净流入", "掌医"})
    private BigDecimal wxNetIn;

    /**
     * HIS净流入
     */
    private BigDecimal hisNetIn;


    /**
     * 医保净流入
     */
    @ExcelProperty(value = {"净流入", "医保"})
    private BigDecimal mipNetIn;

    /**
     * 总净流入
     */
    @ExcelProperty(value = {"净流入", "掌医+医保"})
    @ColumnWidth(12)
    private BigDecimal totalNetIn;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setWxPayAmount(BigDecimal wxPayAmount) {
        this.wxPayAmount = wxPayAmount;
    }

    public BigDecimal getWxPayAmount() {
        return wxPayAmount;
    }

    public BigDecimal getWxMzPayAmount() {
        return wxMzPayAmount;
    }

    public void setWxMzPayAmount(BigDecimal wxMzPayAmount) {
        this.wxMzPayAmount = wxMzPayAmount;
    }

    public BigDecimal getWxZyPayAmount() {
        return wxZyPayAmount;
    }

    public void setWxZyPayAmount(BigDecimal wxZyPayAmount) {
        this.wxZyPayAmount = wxZyPayAmount;
    }

    public void setHisPayAmount(BigDecimal hisPayAmount) {
        this.hisPayAmount = hisPayAmount;
    }

    public BigDecimal getHisPayAmount() {
        return hisPayAmount;
    }

    public void setDiffPayAmount(BigDecimal diffPayAmount) {
        this.diffPayAmount = diffPayAmount;
    }

    public BigDecimal getDiffPayAmount() {
        return diffPayAmount;
    }

    public void setPayBalanced(Integer payBalanced) {
        this.payBalanced = payBalanced;
    }

    public Integer getPayBalanced() {
        return payBalanced;
    }

    public void setWxRefundAmount(BigDecimal wxRefundAmount) {
        this.wxRefundAmount = wxRefundAmount;
    }

    public BigDecimal getWxRefundAmount() {
        return wxRefundAmount;
    }

    public BigDecimal getWxMzRefundAmount() {
        return wxMzRefundAmount;
    }

    public void setWxMzRefundAmount(BigDecimal wxMzRefundAmount) {
        this.wxMzRefundAmount = wxMzRefundAmount;
    }

    public BigDecimal getWxZyRefundAmount() {
        return wxZyRefundAmount;
    }

    public void setWxZyRefundAmount(BigDecimal wxZyRefundAmount) {
        this.wxZyRefundAmount = wxZyRefundAmount;
    }

    public void setHisRefundAmount(BigDecimal hisRefundAmount) {
        this.hisRefundAmount = hisRefundAmount;
    }

    public BigDecimal getHisRefundAmount() {
        return hisRefundAmount;
    }

    public void setDiffRefundAmount(BigDecimal diffRefundAmount) {
        this.diffRefundAmount = diffRefundAmount;
    }

    public BigDecimal getDiffRefundAmount() {
        return diffRefundAmount;
    }

    public void setRefundBalanced(Integer refundBalanced) {
        this.refundBalanced = refundBalanced;
    }

    public Integer getRefundBalanced() {
        return refundBalanced;
    }

    public void setWxNetIn(BigDecimal wxNetIn) {
        this.wxNetIn = wxNetIn;
    }

    public BigDecimal getWxNetIn() {
        return wxNetIn;
    }

    public void setHisNetIn(BigDecimal hisNetIn) {
        this.hisNetIn = hisNetIn;
    }

    public BigDecimal getHisNetIn() {
        return hisNetIn;
    }

    public BigDecimal getMipPayAmount() {
        return mipPayAmount;
    }

    public void setMipPayAmount(BigDecimal mipPayAmount) {
        this.mipPayAmount = mipPayAmount;
    }

    public BigDecimal getMipRefundAmount() {
        return mipRefundAmount;
    }

    public void setMipRefundAmount(BigDecimal mipRefundAmount) {
        this.mipRefundAmount = mipRefundAmount;
    }

    public BigDecimal getMipNetIn() {
        return mipNetIn;
    }

    public void setMipNetIn(BigDecimal mipNetIn) {
        this.mipNetIn = mipNetIn;
    }

    public BigDecimal getTotalNetIn() {
        return totalNetIn;
    }

    public void setTotalNetIn(BigDecimal totalNetIn) {
        this.totalNetIn = totalNetIn;
    }

}

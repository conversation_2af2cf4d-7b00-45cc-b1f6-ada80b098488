package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelProperty;
import space.lzhq.ph.common.NumberKitKt;

import java.io.Serializable;
import java.math.BigDecimal;

public class WxCheckReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "日期")
    private Long id;

    @ExcelProperty(value = {"门诊", "预交金", "充值"})
    private BigDecimal mzPayAmount;

    @ExcelProperty(value = {"门诊", "预交金", "退款"})
    private BigDecimal mzRefundAmount;

    @ExcelProperty(value = {"门诊", "预交金", "净流入"})
    private BigDecimal mzNetInAmount;

    @ExcelProperty(value = {"门诊", "医保移动支付", "充值"})
    private BigDecimal mipPayAmount;

    @ExcelProperty(value = {"门诊", "医保移动支付", "退款"})
    private BigDecimal mipRefundAmount;

    @ExcelProperty(value = {"门诊", "医保移动支付", "净流入"})
    private BigDecimal mipNetInAmount;

    @ExcelProperty(value = {"门诊", "合计", "充值"})
    private BigDecimal mzTotalPayAmount;

    @ExcelProperty(value = {"门诊", "合计", "退款"})
    private BigDecimal mzTotalRefundAmount;

    @ExcelProperty(value = {"门诊", "合计", "净流入"})
    private BigDecimal mzTotalNetInAmount;

    @ExcelProperty(value = {"住院", "充值"})
    private BigDecimal zyPayAmount;

    @ExcelProperty(value = {"住院", "退款"})
    private BigDecimal zyRefundAmount;

    @ExcelProperty(value = {"住院", "净流入"})
    private BigDecimal zyNetInAmount;

    @ExcelProperty(value = {"合计", "充值"})
    private BigDecimal totalPayAmount;

    @ExcelProperty(value = {"合计", "退款"})
    private BigDecimal totalRefundAmount;

    @ExcelProperty(value = {"合计", "净流入"})
    private BigDecimal totalNetInAmount;

    public WxCheckReport(WxCheck check) {
        this.id = check.getId();
        this.mzPayAmount = check.getWxMzPayAmount();
        this.mzRefundAmount = check.getWxMzRefundAmount();
        this.mzNetInAmount = NumberKitKt.subtract(this.mzPayAmount, this.mzRefundAmount);
        this.mipPayAmount = check.getMipPayAmount();
        this.mipRefundAmount = check.getMipRefundAmount();
        this.mipNetInAmount = check.getMipNetIn();
        this.mzTotalPayAmount = NumberKitKt.add(this.mzPayAmount, this.mipPayAmount);
        this.mzTotalRefundAmount = NumberKitKt.add(this.mzRefundAmount, this.mipRefundAmount);
        this.mzTotalNetInAmount = NumberKitKt.subtract(this.mzTotalPayAmount, this.mzTotalRefundAmount);
        this.zyPayAmount = check.getWxZyPayAmount();
        this.zyRefundAmount = check.getWxZyRefundAmount();
        this.zyNetInAmount = NumberKitKt.subtract(this.zyPayAmount, this.zyRefundAmount);
        this.totalPayAmount = NumberKitKt.add(this.mzTotalPayAmount, this.zyPayAmount);
        this.totalRefundAmount = NumberKitKt.add(this.mzTotalRefundAmount, this.zyRefundAmount);
        this.totalNetInAmount = NumberKitKt.subtract(this.totalPayAmount, this.totalRefundAmount);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getMzPayAmount() {
        return mzPayAmount;
    }

    public void setMzPayAmount(BigDecimal mzPayAmount) {
        this.mzPayAmount = mzPayAmount;
    }

    public BigDecimal getMzRefundAmount() {
        return mzRefundAmount;
    }

    public void setMzRefundAmount(BigDecimal mzRefundAmount) {
        this.mzRefundAmount = mzRefundAmount;
    }

    public BigDecimal getMzNetInAmount() {
        return mzNetInAmount;
    }

    public void setMzNetInAmount(BigDecimal mzNetInAmount) {
        this.mzNetInAmount = mzNetInAmount;
    }

    public BigDecimal getMipPayAmount() {
        return mipPayAmount;
    }

    public void setMipPayAmount(BigDecimal mipPayAmount) {
        this.mipPayAmount = mipPayAmount;
    }

    public BigDecimal getMipRefundAmount() {
        return mipRefundAmount;
    }

    public void setMipRefundAmount(BigDecimal mipRefundAmount) {
        this.mipRefundAmount = mipRefundAmount;
    }

    public BigDecimal getMipNetInAmount() {
        return mipNetInAmount;
    }

    public void setMipNetInAmount(BigDecimal mipNetInAmount) {
        this.mipNetInAmount = mipNetInAmount;
    }

    public BigDecimal getMzTotalPayAmount() {
        return mzTotalPayAmount;
    }

    public void setMzTotalPayAmount(BigDecimal mzTotalPayAmount) {
        this.mzTotalPayAmount = mzTotalPayAmount;
    }

    public BigDecimal getMzTotalRefundAmount() {
        return mzTotalRefundAmount;
    }

    public void setMzTotalRefundAmount(BigDecimal mzTotalRefundAmount) {
        this.mzTotalRefundAmount = mzTotalRefundAmount;
    }

    public BigDecimal getMzTotalNetInAmount() {
        return mzTotalNetInAmount;
    }

    public void setMzTotalNetInAmount(BigDecimal mzTotalNetInAmount) {
        this.mzTotalNetInAmount = mzTotalNetInAmount;
    }

    public BigDecimal getZyPayAmount() {
        return zyPayAmount;
    }

    public void setZyPayAmount(BigDecimal zyPayAmount) {
        this.zyPayAmount = zyPayAmount;
    }

    public BigDecimal getZyRefundAmount() {
        return zyRefundAmount;
    }

    public void setZyRefundAmount(BigDecimal zyRefundAmount) {
        this.zyRefundAmount = zyRefundAmount;
    }

    public BigDecimal getZyNetInAmount() {
        return zyNetInAmount;
    }

    public void setZyNetInAmount(BigDecimal zyNetInAmount) {
        this.zyNetInAmount = zyNetInAmount;
    }

    public BigDecimal getTotalPayAmount() {
        return totalPayAmount;
    }

    public void setTotalPayAmount(BigDecimal totalPayAmount) {
        this.totalPayAmount = totalPayAmount;
    }

    public BigDecimal getTotalRefundAmount() {
        return totalRefundAmount;
    }

    public void setTotalRefundAmount(BigDecimal totalRefundAmount) {
        this.totalRefundAmount = totalRefundAmount;
    }

    public BigDecimal getTotalNetInAmount() {
        return totalNetInAmount;
    }

    public void setTotalNetInAmount(BigDecimal totalNetInAmount) {
        this.totalNetInAmount = totalNetInAmount;
    }
}

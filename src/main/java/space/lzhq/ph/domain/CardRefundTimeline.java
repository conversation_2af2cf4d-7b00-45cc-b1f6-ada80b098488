package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.enums.CardRefundEventType;
import space.lzhq.ph.enums.TimelineOperatorType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 余额清退时间线
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName(value = "card_refund_timeline", autoResultMap = true)
public class CardRefundTimeline implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 申报记录ID
     */
    @NotBlank(message = "申报记录ID不能为空")
    private String registrationId;

    /**
     * 事件类型
     */
    @NotNull(message = "事件类型不能为空")
    private CardRefundEventType eventType;

    /**
     * 事件描述
     */
    private String eventDescription;

    /**
     * 操作者类型
     */
    @NotNull(message = "操作者类型不能为空")
    private TimelineOperatorType operatorType;

    /**
     * 操作者姓名
     */
    private String operatorName;

    /**
     * 变更前状态
     */
    @EnumValue
    private CardRefundStatus oldStatus;

    /**
     * 变更后状态
     */
    @EnumValue
    private CardRefundStatus newStatus;

    /**
     * 事件时间
     */
    @NotNull(message = "事件时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    /**
     * 额外数据（JSON格式）
     * 如驳回原因、附件信息等
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Serializable> additionalData;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 构造函数 - 创建基础时间线事件
     */
    public CardRefundTimeline(
            String registrationId,
            CardRefundEventType eventType,
            String eventDescription,
            TimelineOperatorType operatorType,
            String operatorName
    ) {
        this.registrationId = registrationId;
        this.eventType = eventType;
        this.eventDescription = eventDescription;
        this.operatorType = operatorType;
        this.operatorName = operatorName;
        this.eventTime = LocalDateTime.now();
    }

    /**
     * 构造函数 - 创建状态变更时间线事件
     */
    public CardRefundTimeline(
            String registrationId,
            CardRefundEventType eventType,
            String eventDescription,
            TimelineOperatorType operatorType,
            String operatorName,
            CardRefundStatus oldStatus,
            CardRefundStatus newStatus
    ) {
        this(registrationId, eventType, eventDescription, operatorType, operatorName);
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
    }

    /**
     * 构造函数 - 创建包含额外数据的时间线事件
     */
    public CardRefundTimeline(
            String registrationId,
            CardRefundEventType eventType,
            String eventDescription,
            TimelineOperatorType operatorType,
            String operatorName,
            Map<String, Serializable> additionalData
    ) {
        this(registrationId, eventType, eventDescription, operatorType, operatorName);
        this.additionalData = additionalData;
    }
}
package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 微信订阅消息对象 ph_wx_subscribe_message
 *
 * <AUTHOR>
 * @date 2020-06-16
 */
public class WxSubscribeMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 模板ID
     */
    @Excel(name = "模板ID")
    private String templateId;

    /**
     * openid
     */
    @Excel(name = "openid")
    private String openid;

    /**
     * 消息内容
     */
    @Excel(name = "消息内容")
    private String data;

    /**
     * 消息类型
     */
    @Excel(name = "消息类型")
    private String type;

    /**
     * 关联ID
     */
    @Excel(name = "关联ID")
    private String companionId;

    /**
     * 发送时间
     */
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sendTime;

    /**
     * 发送状态
     */
    @Excel(name = "发送状态")
    private Integer sendStatus;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息")
    private String error;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getData() {
        return data;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setCompanionId(String companionId) {
        this.companionId = companionId;
    }

    public String getCompanionId() {
        return companionId;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getError() {
        return error;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("templateId", getTemplateId())
                .append("openid", getOpenid())
                .append("data", getData())
                .append("type", getType())
                .append("companionId", getCompanionId())
                .append("sendTime", getSendTime())
                .append("sendStatus", getSendStatus())
                .append("error", getError())
                .toString();
    }
}

package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonAlias;

import java.io.Serializable;
import java.math.BigDecimal;

public class ZhuyuanRefundForm implements Serializable {

    private static final long serialVersionUID = -7749871240492941039L;

    @JsonAlias({"zhuyuanNo", "InpatientNo"})
    private String zhuyuanNo;

    @JsonAlias({"zyPayNo", "BankTransNo"})
    private String zyPayNo;

    @JsonAlias({"refundAmount", "RefundAmount"})
    private BigDecimal refundAmount;

    public ZhuyuanRefundForm() {

    }

    public ZhuyuanRefundForm(String zhuyuanNo, String zyPayNo, BigDecimal refundAmount) {
        this.zhuyuanNo = zhuyuanNo;
        this.zyPayNo = zyPayNo;
        this.refundAmount = refundAmount;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZyPayNo() {
        return zyPayNo;
    }

    public void setZyPayNo(String zyPayNo) {
        this.zyPayNo = zyPayNo;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ZhuyuanRefundForm)) return false;

        ZhuyuanRefundForm that = (ZhuyuanRefundForm) o;

        if (!getZhuyuanNo().equals(that.getZhuyuanNo())) return false;
        if (!getZyPayNo().equals(that.getZyPayNo())) return false;
        if (!getRefundAmount().equals(that.getRefundAmount())) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = getZhuyuanNo().hashCode();
        result = 31 * result + getZyPayNo().hashCode();
        result = 31 * result + getRefundAmount().hashCode();
        return result;
    }
}

package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 门诊缴费记录对象 outpatient_payment_record
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class OutpatientPaymentRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 患者卡号
     */
    @Excel(name = "患者卡号")
    private String patientCardNo;

    /**
     * 患者姓名
     */
    @Excel(name = "患者姓名")
    private String patientName;

    /**
     * 完整费用信息JSON
     */
    @Excel(name = "费用详情")
    private String feeDetails;

    /**
     * 缴费总额
     */
    @Excel(name = "缴费总额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalAmount;

    /**
     * 支付方式：CARD_BALANCE
     */
    @Excel(name = "支付方式")
    private String paymentMethod;

    /**
     * HIS系统交易号
     */
    @Excel(name = "HIS交易号")
    private String hisTradeNo;

    /**
     * HIS响应代码
     */
    @Excel(name = "HIS响应代码")
    private String hisResponseCode;

    /**
     * HIS响应消息
     */
    @Excel(name = "HIS响应消息")
    private String hisResponseMessage;

    /**
     * HIS响应完整数据
     */
    @Excel(name = "HIS响应数据")
    private String hisResponseData;

    /**
     * 错误详情
     */
    @Excel(name = "错误详情")
    private String errorDetails;

    /**
     * 状态：1=成功，2=失败
     */
    @Excel(name = "状态", readConverterExp = "1=成功,2=失败")
    private Integer status;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setPatientCardNo(String patientCardNo) {
        this.patientCardNo = patientCardNo;
    }

    public String getPatientCardNo() {
        return patientCardNo;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setFeeDetails(String feeDetails) {
        this.feeDetails = feeDetails;
    }

    public String getFeeDetails() {
        return feeDetails;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setHisTradeNo(String hisTradeNo) {
        this.hisTradeNo = hisTradeNo;
    }

    public String getHisTradeNo() {
        return hisTradeNo;
    }

    public void setHisResponseCode(String hisResponseCode) {
        this.hisResponseCode = hisResponseCode;
    }

    public String getHisResponseCode() {
        return hisResponseCode;
    }

    public void setHisResponseMessage(String hisResponseMessage) {
        this.hisResponseMessage = hisResponseMessage;
    }

    public String getHisResponseMessage() {
        return hisResponseMessage;
    }

    public void setHisResponseData(String hisResponseData) {
        this.hisResponseData = hisResponseData;
    }

    public String getHisResponseData() {
        return hisResponseData;
    }

    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }

    public String getErrorDetails() {
        return errorDetails;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("patientCardNo", getPatientCardNo())
                .append("patientName", getPatientName())
                .append("feeDetails", getFeeDetails())
                .append("totalAmount", getTotalAmount())
                .append("paymentMethod", getPaymentMethod())
                .append("hisTradeNo", getHisTradeNo())
                .append("hisResponseCode", getHisResponseCode())
                .append("hisResponseMessage", getHisResponseMessage())
                .append("hisResponseData", getHisResponseData())
                .append("errorDetails", getErrorDetails())
                .append("status", getStatus())
                .append("createdTime", getCreatedTime())
                .append("updatedTime", getUpdatedTime())
                .toString();
    }
} 
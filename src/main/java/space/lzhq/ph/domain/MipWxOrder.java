package space.lzhq.ph.domain;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import org.mospital.dongruan.yibao.bean.CreateOrderResult;
import org.mospital.jackson.JacksonKit;
import org.mospital.wecity.mip.ClientType;
import org.mospital.wecity.mip.UserQueryResult;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@TableName(value = "mip_wx_order")
public class MipWxOrder implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public MipWxOrder() {
    }

    public MipWxOrder(UserQueryResult userQueryResult, Patient patient, CreateOrderResult.PreSettlement preSettlement) {
        this.createTime = LocalDateTime.now();
        this.openid = patient.getOpenId();
        this.clientType = switch (patient.getClientTypeEnum()) {
            case WEIXIN_MA -> ClientType.MA;
            case WEIXIN_MP -> ClientType.MP;
            default -> throw new RuntimeException("不支持的客户端类型");
        };

        this.userCardType = CharSequenceUtil.nullToDefault(userQueryResult.getUserCardType(), "1");
        this.userName = userQueryResult.getUserName();
        this.userCardNo = userQueryResult.getUserCardNo();
        this.patientName = patient.getName();
        this.patientIdCardNo = patient.getIdCardNo();
        if (userQueryResult.isFamilyPay()) {
            this.familyPay = 1;
            this.payAuthNo = userQueryResult.getFamilyPayAuthNo();
        } else {
            this.familyPay = 0;
            this.payAuthNo = userQueryResult.getPayAuthNo();
        }

        this.longitudeLatitude = userQueryResult.getLongitudeLatitude() == null ? "" : userQueryResult.getLongitudeLatitude().toString();
        this.payOrderId = preSettlement.getPayOrderId();
        this.orderStatus = "";
        this.feeSumAmount = preSettlement.getFeeSumamt();
        this.ownPayAmount = preSettlement.getOwnPayAmt();
        this.personalAccountPayAmount = preSettlement.getPsnAcctPay();
        this.fundPayAmount = preSettlement.getFundPay();
        this.hospitalOutTradeNo = "";
        this.medTransactionId = "";
        this.hospOutRefundNo = "";
        this.medRefundId = "";
        this.cashRefundId = "";
        this.cashPayStatus = "";
        this.insuranceRefundId = "";
    }

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String openid;

    /**
     * 用户姓名
     */
    @Excel(name = "用户姓名")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String userName;

    /**
     * 用户证件号码
     */
    @Excel(name = "用户证件号码")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String userCardNo;

    private String patientName;

    private String patientIdCardNo;

    /**
     * 是否亲属医保移动支付：0-否，1-是
     */
    private Integer familyPay;

    /**
     * 用户证件类型
     */
    @Excel(name = "用户证件类型")
    private String userCardType;

    /**
     * 医保授权码
     */
    @Excel(name = "医保授权码")
    private String payAuthNo;

    /**
     * 经纬度
     */
    @Excel(name = "经纬度")
    private String longitudeLatitude;

    /**
     * 支付订单号
     */
    @Excel(name = "支付订单号")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String payOrderId;

    /**
     * HIS订单号
     */
    @Excel(name = "支付订单号")
    private String hisOrderId;

    /**
     * 订单状态
     */
    @Excel(name = "订单状态")
    private String orderStatus;

    /**
     * 费用总额
     */
    @Excel(name = "费用总额")
    private BigDecimal feeSumAmount;

    /**
     * 现金支付金额
     */
    @Excel(name = "现金支付金额")
    private BigDecimal ownPayAmount;

    /**
     * 个人账户支出
     */
    @Excel(name = "个人账户支出")
    private BigDecimal personalAccountPayAmount;

    /**
     * 医保基金支出
     */
    @Excel(name = "医保基金支出")
    private BigDecimal fundPayAmount;

    private String payToken;

    private String medOrgOrd;

    private String clinicNo;

    private String feesJson;

    /**
     * 第三方服务商订单号
     */
    @Excel(name = "第三方服务商订单号")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalOutTradeNo;

    /**
     * 诊疗单ID
     */
    @Excel(name = "诊疗单ID")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String medTransactionId;

    private String hisInvoiceNo;

    private Date hisSettleTime;

    private String hisSettleMessage;

    private String siSettleId;

    private Date siSettleTime;

    /**
     * 医院退款订单号
     */
    @Excel(name = "医院退款订单号")
    private String hospOutRefundNo;

    /**
     * 微信生成的退款医疗订单号
     */
    @Excel(name = "微信生成的退款医疗订单号")
    private String medRefundId;

    /**
     * 微信医保支付生成的微信支付退款订单号
     */
    @Excel(name = "微信医保支付生成的微信支付退款订单号")
    private String cashRefundId;

    /**
     * 现金支付状态
     */
    @Excel(name = "现金支付状态")
    private String cashPayStatus;

    /**
     * 微信医保支付生成的医保退款单号
     */
    @Excel(name = "微信医保支付生成的医保退款单号")
    private String insuranceRefundId;

    private ClientType clientType;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserCardNo(String userCardNo) {
        this.userCardNo = userCardNo;
    }

    public String getUserCardNo() {
        return userCardNo;
    }

    public void setUserCardType(String userCardType) {
        this.userCardType = userCardType;
    }

    public String getUserCardType() {
        return userCardType;
    }

    public void setPayAuthNo(String payAuthNo) {
        this.payAuthNo = payAuthNo;
    }

    public String getPayAuthNo() {
        return payAuthNo;
    }

    public void setLongitudeLatitude(String longitudeLatitude) {
        this.longitudeLatitude = longitudeLatitude;
    }

    public String getLongitudeLatitude() {
        return longitudeLatitude;
    }

    public void setPayOrderId(String payOrderId) {
        this.payOrderId = payOrderId;
    }

    public String getPayOrderId() {
        return payOrderId;
    }

    public String getHisOrderId() {
        return hisOrderId;
    }

    public void setHisOrderId(String hisOrderId) {
        this.hisOrderId = hisOrderId;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setFeeSumAmount(BigDecimal feeSumAmount) {
        this.feeSumAmount = feeSumAmount;
    }

    public BigDecimal getFeeSumAmount() {
        return feeSumAmount;
    }

    public void setOwnPayAmount(BigDecimal ownPayAmount) {
        this.ownPayAmount = ownPayAmount;
    }

    public BigDecimal getOwnPayAmount() {
        return ownPayAmount;
    }

    public void setPersonalAccountPayAmount(BigDecimal personalAccountPayAmount) {
        this.personalAccountPayAmount = personalAccountPayAmount;
    }

    public BigDecimal getPersonalAccountPayAmount() {
        return personalAccountPayAmount;
    }

    public void setFundPayAmount(BigDecimal fundPayAmount) {
        this.fundPayAmount = fundPayAmount;
    }

    public BigDecimal getFundPayAmount() {
        return fundPayAmount;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getMedOrgOrd() {
        return medOrgOrd;
    }

    public void setMedOrgOrd(String medOrgOrd) {
        this.medOrgOrd = medOrgOrd;
    }

    public String getClinicNo() {
        return clinicNo;
    }

    public void setClinicNo(String clinicNo) {
        this.clinicNo = clinicNo;
    }

    public String getFeesJson() {
        return feesJson;
    }

    public void setFeesJson(String feesJson) {
        this.feesJson = feesJson;
    }

    public void setHospitalOutTradeNo(String hospitalOutTradeNo) {
        this.hospitalOutTradeNo = hospitalOutTradeNo;
    }

    public String getHospitalOutTradeNo() {
        return hospitalOutTradeNo;
    }

    public void setMedTransactionId(String medTransactionId) {
        this.medTransactionId = medTransactionId;
    }

    public String getMedTransactionId() {
        return medTransactionId;
    }


    public String getHisInvoiceNo() {
        return hisInvoiceNo;
    }

    public void setHisInvoiceNo(String hisInvoiceNo) {
        this.hisInvoiceNo = hisInvoiceNo;
    }

    public Date getHisSettleTime() {
        return hisSettleTime;
    }

    public void setHisSettleTime(Date hisSettleTime) {
        this.hisSettleTime = hisSettleTime;
    }

    public String getHisSettleMessage() {
        return hisSettleMessage;
    }

    public void setHisSettleMessage(String hisSettleMessage) {
        this.hisSettleMessage = hisSettleMessage;
    }

    public String getSiSettleId() {
        return siSettleId;
    }

    public void setSiSettleId(String siSettleId) {
        this.siSettleId = siSettleId;
    }

    public Date getSiSettleTime() {
        return siSettleTime;
    }

    public void setSiSettleTime(Date siSettleTime) {
        this.siSettleTime = siSettleTime;
    }

    public String getHospOutRefundNo() {
        return hospOutRefundNo;
    }

    public void setHospOutRefundNo(String hospOutRefundNo) {
        this.hospOutRefundNo = hospOutRefundNo;
    }

    public String getMedRefundId() {
        return medRefundId;
    }

    public void setMedRefundId(String medRefundId) {
        this.medRefundId = medRefundId;
    }

    public String getCashRefundId() {
        return cashRefundId;
    }

    public void setCashRefundId(String cashRefundId) {
        this.cashRefundId = cashRefundId;
    }

    public String getInsuranceRefundId() {
        return insuranceRefundId;
    }

    public void setInsuranceRefundId(String insuranceRefundId) {
        this.insuranceRefundId = insuranceRefundId;
    }

    public String getCashPayStatus() {
        return cashPayStatus;
    }

    public void setCashPayStatus(String cashPayStatus) {
        this.cashPayStatus = cashPayStatus;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientIdCardNo() {
        return patientIdCardNo;
    }

    public void setPatientIdCardNo(String patientIdCardNo) {
        this.patientIdCardNo = patientIdCardNo;
    }

    public Integer getFamilyPay() {
        return familyPay;
    }

    public void setFamilyPay(Integer familyPay) {
        this.familyPay = familyPay;
    }

    public boolean isSelfPay() {
        return familyPay != null && familyPay == 0;
    }

    public ClientType getClientType() {
        return clientType;
    }

    public void setClientType(ClientType clientType) {
        this.clientType = clientType;
    }

    @JsonIgnore
    public String getEnv() {
        return switch (clientType) {
            case MA -> "WeixinXiaochengxu";
            case MP -> "WeixinGongzhonghao";
        };
    }

    @Override
    public String toString() {
        return JacksonKit.INSTANCE.writeValueAsString(this);
    }
}

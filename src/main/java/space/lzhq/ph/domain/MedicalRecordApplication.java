package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.xss.Xss;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.data.PhoneUtil;
import space.lzhq.ph.annotation.ValidGender;
import space.lzhq.ph.dto.InpatientInfo;
import space.lzhq.ph.dto.request.MedicalRecordApplicationPatientFormDto;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.enums.MedicalRecordCopyPurpose;
import space.lzhq.ph.enums.Relationship;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName(value = "medical_record_application", autoResultMap = true)
public class MedicalRecordApplication implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String openId;

    /**
     * 住院号
     */
    private String admissionId;

    /**
     * 身份证附件ID
     */
    private String patientIdCardAttachmentId;

    /**
     * 患者身份证号
     */
    private String patientIdCardNo;

    /**
     * 患者出生证附件ID
     */
    private String patientBirthCertificateAttachmentId;

    /**
     * 患者医保卡附件ID
     */
    private String patientMedicalInsuranceCardAttachmentId;

    /**
     * 患者户口本附件ID
     */
    private String patientHouseholdRegisterAttachmentId;

    /**
     * 申报患者姓名
     */
    @NotBlank(message = "申报患者姓名不能为空")
    @Xss(message = "申报患者姓名不能包含特殊字符")
    private String patientName;

    /**
     * 患者性别
     */
    @NotBlank(message = "患者性别不能为空")
    @ValidGender
    private String patientGender;

    /**
     * 患者出生日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @NotNull(message = "患者出生日期不能为空")
    private LocalDate patientBirthDate;

    /**
     * 患者手机号
     */
    private String patientPhone;

    /**
     * 入院时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime admissionTime;

    /**
     * 出院时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dischargeTime;

    /**
     * 复印用途
     */
    @NotNull(message = "复印用途不能为空")
    @EnumValue
    private MedicalRecordCopyPurpose copyPurpose;

    /**
     * 复印份数
     */
    @NotNull(message = "复印份数不能为空")
    @Min(value = 1, message = "复印份数不能少于1份")
    @Max(value = 10, message = "复印份数不能超过10份")
    private Integer copyCount;

    /**
     * 病区
     */
    private String ward;

    /**
     * 是否代办 0-否 1-是
     */
    @Excel(name = "是否代办", readConverterExp = "0=否,1=是")
    private Integer agentFlag;

    /**
     * 代办人姓名
     */
    @Excel(name = "代办人姓名")
    private String agentName;

    /**
     * 代办人身份证号
     */
    @Excel(name = "代办人身份证号")
    private String agentIdCardNo;

    /**
     * 代办人身份证附件ID
     */
    @Excel(name = "代办人身份证附件ID")
    private String agentIdCardAttachmentId;

    /**
     * 代办人手机号
     */
    @Excel(name = "代办人手机号")
    private String agentMobile;

    /**
     * 代办人与患者关系
     */
    @Excel(name = "代办人与患者关系")
    @EnumValue
    private Relationship agentRelation;

    /**
     * 申报状态
     * DRAFT - 草稿
     * SUBMITTED - 已提交
     * APPROVED - 已通过
     * REJECTED - 已驳回
     */
    @Excel(name = "申报状态")
    @NotNull(message = "申报状态不能为空")
    @EnumValue
    private MedicalRecordApplicationStatus status;

    /**
     * 提交时间
     */
    @Excel(name = "提交时间")
    private LocalDateTime submitTime;

    /**
     * 审批时间
     */
    @Excel(name = "审批时间")
    private LocalDateTime approveTime;

    /**
     * 驳回原因
     */
    @Excel(name = "驳回原因")
    private String rejectReason;

    /**
     * 整改意见
     */
    @Excel(name = "整改意见")
    private String correctionAdvice;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记：0-未删除 1-已删除
     */
    private Boolean deleteFlag = false;

    /**
     * 收件人姓名
     */
    @Excel(name = "收件人姓名")
    private String recipientName;

    /**
     * 收件人手机号
     */
    @Excel(name = "收件人手机号")
    private String recipientMobile;

    /**
     * 收件人地址
     */
    @Excel(name = "收件人地址")
    private String recipientAddress;

    /**
     * 复印费用（元）
     */
    @Excel(name = "复印费用")
    private java.math.BigDecimal copyFee;

    /**
     * 快递费用（元）
     */
    @Excel(name = "快递费用")
    private java.math.BigDecimal shippingFee;

    /**
     * 总费用（元）
     */
    @Excel(name = "总费用")
    private java.math.BigDecimal totalFee;

    /**
     * 快递单号
     */
    @Excel(name = "快递单号")
    private String trackingNumber;

    /**
     * 支付时间
     */
    @Excel(name = "支付时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    /**
     * 邮寄时间
     */
    @Excel(name = "邮寄时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippingTime;

    /**
     * 掌医支付订单号
     */
    @Excel(name = "掌医支付订单号")
    private String zyPayNo;

    /**
     * 微信支付订单号
     */
    @Excel(name = "微信支付订单号")
    private String wxPayNo;

    public MedicalRecordApplication(MedicalRecordApplicationPatientFormDto formDto, InpatientInfo inpatientInfo, String openId) {
        this.openId = openId;
        this.admissionId = inpatientInfo.getAdmissionId();
        this.patientIdCardAttachmentId = formDto.getPatientIdCardAttachmentId();
        this.patientIdCardNo = formDto.getPatientIdCardNo();
        this.patientBirthCertificateAttachmentId = formDto.getPatientBirthCertificateAttachmentId();
        this.patientMedicalInsuranceCardAttachmentId = formDto.getPatientMedicalInsuranceCardAttachmentId();
        this.patientHouseholdRegisterAttachmentId = formDto.getPatientHouseholdRegisterAttachmentId();
        this.patientName = formDto.getPatientName();
        this.patientGender = inpatientInfo.getSex();
        this.patientBirthDate = inpatientInfo.getBirthday();
        this.patientPhone = inpatientInfo.getPhone();
        this.admissionTime = inpatientInfo.getAdmissionTime();
        this.dischargeTime = inpatientInfo.getDischargeTime();
        this.ward = inpatientInfo.getWard();
        this.copyPurpose = formDto.getCopyPurpose();
        this.copyCount = formDto.getCopyCount();
        this.status = MedicalRecordApplicationStatus.DRAFT;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 验证患者身份证号格式
     * 注意：只有在身份证号不为空时才验证格式，空值验证由 @NotBlank 处理
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "患者身份证号不正确")
    public boolean isPatientIdCardValid() {
        if (StringUtils.isBlank(patientIdCardNo)) {
            return StringUtils.isBlank(patientIdCardAttachmentId);
        } else {
            return IdcardUtil.isValidCard(patientIdCardNo);
        }
    }

    @JsonIgnore
    @AssertTrue(message = "患者出生证附件ID和身份证附件ID不能同时为空")
    public boolean isPatientAttachmentIdValid() {
        return StringUtils.isNotBlank(patientIdCardAttachmentId) || StringUtils.isNotBlank(patientBirthCertificateAttachmentId);
    }

    /**
     * 验证代办人相关字段
     * 当agentFlag为1时，代办人相关字段不能为空
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "当选择代办时，代办人姓名、身份证号、身份证附件ID、手机号和与患者关系不能为空")
    public boolean isAgentFieldsValid() {
        // 如果agentFlag为null或0，不需要验证代办人字段
        if (agentFlag == null || agentFlag == 0) {
            return true;
        }

        // 如果agentFlag为1，验证代办人相关字段不能为空
        return agentName != null && !agentName.trim().isEmpty()
                && agentIdCardNo != null && !agentIdCardNo.trim().isEmpty()
                && agentIdCardAttachmentId != null && !agentIdCardAttachmentId.trim().isEmpty()
                && agentMobile != null && !agentMobile.trim().isEmpty()
                && agentRelation != null;
    }

    /**
     * 验证代办人身份证号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "代办人身份证号格式不正确")
    public boolean isAgentIdCardValid() {
        // 如果agentFlag为null或0，不需要验证代办人身份证号
        if (agentFlag == null || agentFlag == 0) {
            return true;
        }
        return agentIdCardNo == null || IdcardUtil.isValidCard(agentIdCardNo);
    }

    /**
     * 验证代办人手机号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "代办人手机号格式不正确")
    public boolean isAgentMobileValid() {
        // 如果agentFlag为null或0，不需要验证代办人手机号
        if (agentFlag == null || agentFlag == 0) {
            return true;
        }
        return agentMobile == null || PhoneUtil.isMobile(agentMobile);
    }

    /**
     * 判断当前余额清退登记是否可以被用户（申请人或代理人）修改
     *
     * @return 如果记录的状态允许用户修改则返回true，否则返回false
     */
    public boolean canBeModifiedByUser() {
        return status != null && status.isEditableByApplicantOrAgent();
    }
}

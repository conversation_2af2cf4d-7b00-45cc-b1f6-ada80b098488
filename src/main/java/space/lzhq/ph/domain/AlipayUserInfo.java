package space.lzhq.ph.domain;

import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class AlipayUserInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Excel(name = "ID")
    private String id;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 是否通过实名认证
     */
    @Excel(name = "是否通过实名认证")
    private String isCertified;

    /**
     * 证件类型
     */
    @Excel(name = "证件类型")
    private String certType;

    /**
     * 证件号码
     */
    @Excel(name = "证件号码")
    private String certNo;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String userName;

    /**
     * 昵称
     */
    @Excel(name = "昵称")
    private String nickName;

    /**
     * 头像地址
     */
    @Excel(name = "头像地址")
    private String avatar;

    /**
     * 电子邮件
     */
    @Excel(name = "电子邮件")
    private String email;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * 性别
     */
    @Excel(name = "性别")
    private String gender;

    /**
     * 省份
     */
    @Excel(name = "省份")
    private String province;

    /**
     * 市
     */
    @Excel(name = "市")
    private String city;

    /**
     * 区县
     */
    @Excel(name = "区县")
    private String area;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String address;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    public AlipayUserInfo() {

    }

    public AlipayUserInfo(AlipayUserInfoShareResponse infoShareResponse) {
        this.id = infoShareResponse.getUserId();
        this.type = infoShareResponse.getUserType();
        this.isCertified = infoShareResponse.getIsCertified();
        this.certType = infoShareResponse.getCertType();
        this.certNo = infoShareResponse.getCertNo();
        this.userName = infoShareResponse.getUserName();
        this.nickName = infoShareResponse.getNickName();
        this.avatar = infoShareResponse.getAvatar();
        this.email = infoShareResponse.getEmail();
        this.mobile = infoShareResponse.getMobile();
        this.gender = infoShareResponse.getGender();
        this.province = infoShareResponse.getProvince();
        this.city = infoShareResponse.getCity();
        this.area = infoShareResponse.getArea();
        this.address = infoShareResponse.getAddress();
        this.status = infoShareResponse.getUserStatus();
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setIsCertified(String isCertified) {
        this.isCertified = isCertified;
    }

    public String getIsCertified() {
        return isCertified;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserName() {
        return userName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGender() {
        return gender;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvince() {
        return province;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCity() {
        return city;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getArea() {
        return area;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("type", getType())
                .append("isCertified", getIsCertified())
                .append("certType", getCertType())
                .append("certNo", getCertNo())
                .append("userName", getUserName())
                .append("nickName", getNickName())
                .append("avatar", getAvatar())
                .append("email", getEmail())
                .append("mobile", getMobile())
                .append("gender", getGender())
                .append("province", getProvince())
                .append("city", getCity())
                .append("area", getArea())
                .append("address", getAddress())
                .append("status", getStatus())
                .toString();
    }
}

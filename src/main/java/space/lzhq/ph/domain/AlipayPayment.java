package space.lzhq.ph.domain;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import space.lzhq.ph.common.Constants;
import space.lzhq.ph.common.ServiceType;

import java.util.Date;

/**
 * 支付宝支付对象 alipay_payment
 *
 * <AUTHOR>
 * @date 2022-09-09
 */
public class AlipayPayment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 类型
     */
    @Excel(name = "类型", dictType = "service_type")
    private Integer type;

    /**
     * openid
     */
    @Excel(name = "支付宝用户ID")
    private String openid;

    /**
     * 患者ID
     */
    @Excel(name = "患者索引号")
    private String patientId;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    private String cardNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 商户订单号
     */
    @Excel(name = "掌医订单号")
    private String outTradeNo;

    /**
     * 支付宝订单号
     */
    @Excel(name = "支付宝订单号")
    private String tradeNo;

    /**
     * 支付宝交易状态描述
     */
    @Excel(name = "支付宝交易状态描述")
    private String tradeStatus;

    /**
     * HIS收据号
     */
    @Excel(name = "HIS收据号")
    private String hisReceiptNo;

    /**
     * HIS交易状态描述
     */
    @Excel(name = "HIS交易状态描述")
    private String hisTradeStatus;

    /**
     * 充值到账时间
     */
    @Excel(name = "充值到账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date hisSuccessTime;

    /**
     * 订单金额/分
     */
    @Excel(name = "订单金额/分", cellType = Excel.ColumnType.NUMERIC)
    private Long totalAmount;

    /**
     * 已退款/分
     */
    @Excel(name = "已退费/分", cellType = Excel.ColumnType.NUMERIC)
    private Long exrefund;

    /**
     * 未退款/分
     */
    @Excel(name = "未退费/分", cellType = Excel.ColumnType.NUMERIC)
    private Long unrefund;

    /**
     * 手动充值状态
     */
    @Excel(name = "手动充值状态", dictType = "manual_state")
    private Integer manualRechargeState;

    public boolean isMenzhen() {
        return ObjectUtil.equals(getType(), ServiceType.MZ.getCode());
    }

    public boolean isZhuyuan() {
        return ObjectUtil.equals(getType(), ServiceType.ZY.getCode());
    }

    public AlipayPayment() {
    }

    public AlipayPayment(Patient patient, ServiceType serviceType, String outTradeNo, Long totalAmount, String remark) {
        this.openid = patient.getOpenId();
        this.patientId = patient.getAccountNo();
        this.jzCardNo = patient.getJzCardNo();
        this.cardNo = patient.getCardNo();
        this.zhuyuanNo = patient.getZhuyuanNo();
        this.idCardNo = patient.getIdCardNo();
        this.setCreateTime(new Date());
        this.type = serviceType.getCode();
        this.outTradeNo = outTradeNo;
        this.totalAmount = totalAmount;
        this.tradeNo = "";
        this.tradeStatus = "";
        this.exrefund = 0L;
        this.unrefund = totalAmount;
        this.setRemark(remark);
        this.hisReceiptNo = "";
        this.hisSuccessTime = null;
        this.hisTradeStatus = "";
        this.manualRechargeState = Constants.MANUAL_INIT;

    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setJzCardNo(String jzCardNo) {
        this.jzCardNo = jzCardNo;
    }

    public String getJzCardNo() {
        return jzCardNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setTotalAmount(Long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getTotalAmount() {
        return totalAmount;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeStatus(String tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public String getTradeStatus() {
        return tradeStatus;
    }

    public void setHisReceiptNo(String hisReceiptNo) {
        this.hisReceiptNo = hisReceiptNo;
    }

    public String getHisReceiptNo() {
        return hisReceiptNo;
    }

    public void setHisTradeStatus(String hisTradeStatus) {
        this.hisTradeStatus = hisTradeStatus;
    }

    public String getHisTradeStatus() {
        return hisTradeStatus;
    }

    public void setHisSuccessTime(Date hisSuccessTime) {
        this.hisSuccessTime = hisSuccessTime;
    }

    public Date getHisSuccessTime() {
        return hisSuccessTime;
    }

    public void setExrefund(Long exrefund) {
        this.exrefund = exrefund;
    }

    public Long getExrefund() {
        return exrefund;
    }

    public void setUnrefund(Long unrefund) {
        this.unrefund = unrefund;
    }

    public Long getUnrefund() {
        return unrefund;
    }

    public void setManualRechargeState(Integer manualRechargeState) {
        this.manualRechargeState = manualRechargeState;
    }

    public Integer getManualRechargeState() {
        return manualRechargeState;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("createTime",
                getCreateTime()).append("type", getType()).append("openid", getOpenid()).append("patientId",
                getPatientId()).append("idCardNo", getIdCardNo()).append("jzCardNo", getJzCardNo()).append("zhuyuanNo"
                , getZhuyuanNo()).append("outTradeNo", getOutTradeNo()).append("totalAmount", getTotalAmount()).append("tradeNo", getTradeNo()).append("tradeStatus", getTradeStatus()).append("hisReceiptNo", getHisReceiptNo()).append("hisTradeStatus", getHisTradeStatus()).append("hisSuccessTime", getHisSuccessTime()).append("exrefund", getExrefund()).append("unrefund", getUnrefund()).append("manualRechargeState", getManualRechargeState()).append("remark", getRemark()).toString();
    }
}

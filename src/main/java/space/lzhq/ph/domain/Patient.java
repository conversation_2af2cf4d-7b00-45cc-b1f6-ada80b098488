package space.lzhq.ph.domain;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.mospital.witontek.QueryMenzhenPatientResponse;
import space.lzhq.ph.common.ClientType;

import java.util.Date;

public class Patient extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 客户端
     */
    private Integer clientType;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 账号
     */
    @Excel(name = "患者索引号")
    private String accountNo;

    /**
     * 实体卡号，对应 markNo
     */
    @Excel(name = "实体卡号")
    private String jzCardNo;

    /**
     * 虚拟卡号
     */
    @Excel(name = "虚拟卡号")
    private String cardNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别：0=男，1=女
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private Integer gender;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    private String nation;

    /**
     * $column.columnComment
     */
    @Excel(name = "openid")
    private String openId;

    /**
     * $column.columnComment
     */
    @Excel(name = "unionid")
    private String unionId;

    /**
     * 是否默认卡
     */
    @Excel(name = "是否默认卡", readConverterExp = "1=是,0=否")
    private Integer active;

    /**
     * 电子健康卡主索引号
     */
    @Excel(name = "电子健康卡主索引号")
    private String empi;

    /**
     * 电子健康卡号
     */
    @Excel(name = "电子健康卡号")
    private String erhcCardNo;

    /**
     * 入院时间
     */
    @Excel(name = "入院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ruyuanTime;

    /**
     * 出院时间
     */
    @Excel(name = "出院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date chuyuanTime;

    /**
     * 门诊余额
     */
    private Double menzhenBalance;

    /**
     * 住院余额
     */
    private Double zhuyuanBalance;

    public Patient() {

    }

    public Patient(Session session, QueryMenzhenPatientResponse queryMenzhenPatientResponse, String mobile) {
        this.clientType = session.getClientType();
        this.idCardNo = queryMenzhenPatientResponse.getIdCardNo();
        this.accountNo = queryMenzhenPatientResponse.getHisNo();
        this.cardNo = queryMenzhenPatientResponse.getHisNo();
        this.zhuyuanNo = "";
        this.name = queryMenzhenPatientResponse.getName().trim();
        this.gender = "男".equals(queryMenzhenPatientResponse.getSex()) ? 0 : 1;
        this.mobile = StrUtil.trim(StrUtil.nullToDefault(queryMenzhenPatientResponse.getMobile(), mobile));
        this.nation = "";
        this.openId = session.getOpenId();
        this.unionId = session.getUnionId();
        this.active = 0;
        this.menzhenBalance = queryMenzhenPatientResponse.getBalance().doubleValue();
        this.zhuyuanBalance = null;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public Integer getClientType() {
        return clientType;
    }

    public ClientType getClientTypeEnum() {
        if (getClientType() == null) {
            return ClientType.WEIXIN_MP;
        } else {
            return ClientType.Companion.fromCode(getClientType());
        }
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setJzCardNo(String jzCardNo) {
        this.jzCardNo = jzCardNo;
    }

    public String getJzCardNo() {
        return jzCardNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getGender() {
        return gender;
    }

    public Boolean isMale() {
        return !StrUtil.isBlank(idCardNo) && IdcardUtil.getGenderByIdCard(getIdCardNo()) == 1;
    }

    public Boolean isFemale() {
        return !StrUtil.isBlank(idCardNo) && IdcardUtil.getGenderByIdCard(getIdCardNo()) == 0;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public Integer getActive() {
        return active;
    }

    public void setEmpi(String empi) {
        this.empi = empi;
    }

    public String getEmpi() {
        return empi;
    }

    public void setErhcCardNo(String erhcCardNo) {
        this.erhcCardNo = erhcCardNo;
    }

    public String getErhcCardNo() {
        return erhcCardNo;
    }

    public Date getRuyuanTime() {
        return ruyuanTime;
    }

    public void setRuyuanTime(Date ruyuanTime) {
        this.ruyuanTime = ruyuanTime;
    }

    public Date getChuyuanTime() {
        return chuyuanTime;
    }

    public void setChuyuanTime(Date chuyuanTime) {
        this.chuyuanTime = chuyuanTime;
    }

    public void setMenzhenBalance(Double menzhenBalance) {
        this.menzhenBalance = menzhenBalance;
    }

    public Double getMenzhenBalance() {
        return this.menzhenBalance;
    }

    public Double getZhuyuanBalance() {
        return zhuyuanBalance;
    }

    public void setZhuyuanBalance(Double zhuyuanBalance) {
        this.zhuyuanBalance = zhuyuanBalance;
    }

    public boolean hasErhcCard() {
        return !erhcCardNo.isEmpty() || jzCardNo.length() == 64;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("clientType", getClientType())
                .append("idCardNo", getIdCardNo())
                .append("accountNo", getAccountNo())
                .append("jzCardNo", getJzCardNo())
                .append("cardNo", getCardNo())
                .append("zhuyuanNo", getZhuyuanNo())
                .append("name", getName())
                .append("gender", getGender())
                .append("mobile", getMobile())
                .append("openId", getOpenId())
                .append("unionId", getUnionId())
                .append("createTime", getCreateTime())
                .append("active", getActive())
                .append("empi", getEmpi())
                .append("erhcCardNo", getErhcCardNo())
                .toString();
    }
}

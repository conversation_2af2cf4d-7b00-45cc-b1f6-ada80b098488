package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class PscOrderSummaryByCarer implements Serializable {
    private static final long serialVersionUID = 4220022089814891818L;

    @ExcelProperty(value = "工号")
    @ColumnWidth(15)
    private String carerEmployeeNo;

    @ExcelProperty(value = "姓名")
    @ColumnWidth(15)
    private String carerName;

    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String carerMobile;

    @ExcelProperty(value = {"床", "人数"})
    private Integer sickbedOrderCount;

    @ExcelProperty(value = {"床", "项目"})
    private Integer sickbedItemCount;

    @ExcelProperty(value = {"轮椅", "人数"})
    private Integer wheelchairOrderCount;

    @ExcelProperty(value = {"轮椅", "项目"})
    private Integer wheelchairItemCount;

    @ExcelProperty(value = {"陪检", "人数"})
    private Integer guideOrderCount;

    @ExcelProperty(value = {"陪检", "项目"})
    private Integer guideItemCount;

    @ExcelProperty(value = {"手术", "人数"})
    private Integer surgeryOrderCount;

    @ExcelProperty(value = {"手术", "项目"})
    private Integer surgeryItemCount;

    @ExcelProperty(value = {"总合计", "总人数"})
    private Integer totalOrderCount;

    @ExcelProperty(value = {"总合计", "总项目"})
    private Integer totalItemCount;

    public String getCarerEmployeeNo() {
        return carerEmployeeNo;
    }

    public void setCarerEmployeeNo(String carerEmployeeNo) {
        this.carerEmployeeNo = carerEmployeeNo;
    }

    public String getCarerName() {
        return carerName;
    }

    public void setCarerName(String carerName) {
        this.carerName = carerName;
    }

    public String getCarerMobile() {
        return carerMobile;
    }

    public void setCarerMobile(String carerMobile) {
        this.carerMobile = carerMobile;
    }

    public Integer getSickbedOrderCount() {
        return sickbedOrderCount;
    }

    public void setSickbedOrderCount(Integer sickbedOrderCount) {
        this.sickbedOrderCount = sickbedOrderCount;
    }

    public Integer getSickbedItemCount() {
        return sickbedItemCount;
    }

    public void setSickbedItemCount(Integer sickbedItemCount) {
        this.sickbedItemCount = sickbedItemCount;
    }

    public Integer getWheelchairOrderCount() {
        return wheelchairOrderCount;
    }

    public void setWheelchairOrderCount(Integer wheelchairOrderCount) {
        this.wheelchairOrderCount = wheelchairOrderCount;
    }

    public Integer getWheelchairItemCount() {
        return wheelchairItemCount;
    }

    public void setWheelchairItemCount(Integer wheelchairItemCount) {
        this.wheelchairItemCount = wheelchairItemCount;
    }

    public Integer getGuideOrderCount() {
        return guideOrderCount;
    }

    public void setGuideOrderCount(Integer guideOrderCount) {
        this.guideOrderCount = guideOrderCount;
    }

    public Integer getGuideItemCount() {
        return guideItemCount;
    }

    public void setGuideItemCount(Integer guideItemCount) {
        this.guideItemCount = guideItemCount;
    }

    public Integer getSurgeryOrderCount() {
        return surgeryOrderCount;
    }

    public void setSurgeryOrderCount(Integer surgeryOrderCount) {
        this.surgeryOrderCount = surgeryOrderCount;
    }

    public Integer getSurgeryItemCount() {
        return surgeryItemCount;
    }

    public void setSurgeryItemCount(Integer surgeryItemCount) {
        this.surgeryItemCount = surgeryItemCount;
    }

    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    public void setTotalOrderCount(Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    public Integer getTotalItemCount() {
        return totalItemCount;
    }

    public void setTotalItemCount(Integer totalItemCount) {
        this.totalItemCount = totalItemCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PscOrderSummaryByCarer)) return false;

        PscOrderSummaryByCarer that = (PscOrderSummaryByCarer) o;

        if (!getCarerEmployeeNo().equals(that.getCarerEmployeeNo())) return false;
        if (!getCarerName().equals(that.getCarerName())) return false;
        if (!getCarerMobile().equals(that.getCarerMobile())) return false;
        if (!getSickbedOrderCount().equals(that.getSickbedOrderCount())) return false;
        if (!getSickbedItemCount().equals(that.getSickbedItemCount())) return false;
        if (!getWheelchairOrderCount().equals(that.getWheelchairOrderCount())) return false;
        if (!getWheelchairItemCount().equals(that.getWheelchairItemCount())) return false;
        if (!getGuideOrderCount().equals(that.getGuideOrderCount())) return false;
        if (!getGuideItemCount().equals(that.getGuideItemCount())) return false;
        if (!getSurgeryOrderCount().equals(that.getSurgeryOrderCount())) return false;
        if (!getSurgeryItemCount().equals(that.getSurgeryItemCount())) return false;
        if (!getTotalOrderCount().equals(that.getTotalOrderCount())) return false;
        return getTotalItemCount().equals(that.getTotalItemCount());
    }

    @Override
    public int hashCode() {
        int result = getCarerEmployeeNo().hashCode();
        result = 31 * result + getCarerName().hashCode();
        result = 31 * result + getCarerMobile().hashCode();
        result = 31 * result + getSickbedOrderCount().hashCode();
        result = 31 * result + getSickbedItemCount().hashCode();
        result = 31 * result + getWheelchairOrderCount().hashCode();
        result = 31 * result + getWheelchairItemCount().hashCode();
        result = 31 * result + getGuideOrderCount().hashCode();
        result = 31 * result + getGuideItemCount().hashCode();
        result = 31 * result + getSurgeryOrderCount().hashCode();
        result = 31 * result + getSurgeryItemCount().hashCode();
        result = 31 * result + getTotalOrderCount().hashCode();
        result = 31 * result + getTotalItemCount().hashCode();
        return result;
    }
}

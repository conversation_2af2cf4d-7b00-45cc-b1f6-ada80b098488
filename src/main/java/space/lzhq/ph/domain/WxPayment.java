package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import space.lzhq.ph.common.ServiceType;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 充值记录对象 ph_wx_payment
 *
 * <AUTHOR>
 */
public class WxPayment extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付类型：1=旧版充值，2=智能缴费
     */
    public static final int PAYMENT_TYPE_OLD = 1;
    public static final int PAYMENT_TYPE_INTELLIGENT = 2;

    /**
     * ID
     */
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型", dictType = "mz_zy")
    private String type;

    /**
     * openid
     */
    @Excel(name = "微信用户ID")
    private String openid;

    @Excel(name = "患者索引号")
    private String patientNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 虚拟卡号
     */
    private String cardNo;

    private String idCardNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 掌医订单号
     */
    @Excel(name = "掌医订单号")
    private String zyPayNo;

    /**
     * 微信订单号
     */
    @Excel(name = "微信订单号")
    private String wxPayNo;

    /**
     * 微信交易状态
     */
    @Excel(name = "微信交易状态")
    private String wxTradeStatus;

    /**
     * HIS交易状态
     */
    @Excel(name = "HIS交易状态")
    private String hisTradeStatus;

    /**
     * 充值到账时间
     */
    @Excel(name = "充值到账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date hisSuccessTime;

    @Excel(name = "HIS收据号")
    private String hisReceiptNo;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额/分", cellType = Excel.ColumnType.NUMERIC, isStatistics = true)
    private Long amount;

    /**
     * 已退金额
     * 1. 初始为0
     */
    @Excel(name = "已退金额/分", cellType = Excel.ColumnType.NUMERIC, isStatistics = true)
    private Long exrefund;

    /**
     * 未退金额
     * 1. 初始为null
     * 2. 收到支付成功的通知时，如当前值为初始值，则更新为订单金额
     */
    @Excel(name = "未退金额/分", cellType = Excel.ColumnType.NUMERIC, isStatistics = true)
    private Long unrefund;

    private BigDecimal balance;

    /**
     * 处方号
     */
    private String prescriptionNos;

    /**
     * 人工充值状态:0=未发起,1=待放行,2=已放行
     */
    @Excel(name = "手动充值状态", dictType = "manual_state")
    private Integer manualPayState;

    /**
     * 服务类型
     * @see space.lzhq.ph.common.ClientType
     */
    private Integer clientType;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 费用详情JSON
     */
    private String feeDetails;

    /**
     * 费用总额
     */
    private BigDecimal totalFee;

    /**
     * 下单时卡余额
     */
    private BigDecimal cardBalanceAtTime;

    /**
     * 支付类型：1=旧版充值，2=智能缴费
     */
    private Integer paymentType;

    /**
     * 版本号（乐观锁）
     */
    private Long version = 0L;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return openid;
    }

    public String getPatientNo() {
        return patientNo;
    }

    public void setPatientNo(String patientNo) {
        this.patientNo = patientNo;
    }

    public void setJzCardNo(String jzCardNo) {
        this.jzCardNo = jzCardNo;
    }

    public String getJzCardNo() {
        return jzCardNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setZyPayNo(String zyPayNo) {
        this.zyPayNo = zyPayNo;
    }

    public String getZyPayNo() {
        return zyPayNo;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getAmount() {
        return amount;
    }

    public void setExrefund(Long exrefund) {
        this.exrefund = exrefund;
    }

    public Long getExrefund() {
        return exrefund;
    }

    public void setUnrefund(Long unrefund) {
        this.unrefund = unrefund;
    }

    public Long getUnrefund() {
        return unrefund;
    }

    public void setWxPayNo(String wxPayNo) {
        this.wxPayNo = wxPayNo;
    }

    public String getWxPayNo() {
        return wxPayNo;
    }

    public void setWxTradeStatus(String wxTradeStatus) {
        this.wxTradeStatus = wxTradeStatus;
    }

    public String getWxTradeStatus() {
        return wxTradeStatus;
    }

    public void setHisTradeStatus(String hisTradeStatus) {
        this.hisTradeStatus = hisTradeStatus;
    }

    public String getHisTradeStatus() {
        return hisTradeStatus;
    }

    public Date getHisSuccessTime() {
        return hisSuccessTime;
    }

    public void setHisSuccessTime(Date hisSuccessTime) {
        this.hisSuccessTime = hisSuccessTime;
    }

    public String getHisReceiptNo() {
        return hisReceiptNo;
    }

    public void setHisReceiptNo(String hisReceiptNo) {
        this.hisReceiptNo = hisReceiptNo;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public Integer getManualPayState() {
        return manualPayState;
    }

    public void setManualPayState(Integer manualPayState) {
        this.manualPayState = manualPayState;
    }

    public boolean isMenzhen() {
        return ServiceType.MZ.name().equals(getType());
    }

    public boolean isZhuyuan() {
        return ServiceType.ZY.name().equals(getType());
    }

    public boolean isBingli() {
        return ServiceType.BL.name().equals(getType());
    }

    public String getPrescriptionNos() {
        return prescriptionNos;
    }

    public void setPrescriptionNos(String prescriptionNos) {
        this.prescriptionNos = prescriptionNos;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getFeeDetails() {
        return feeDetails;
    }

    public void setFeeDetails(String feeDetails) {
        this.feeDetails = feeDetails;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public BigDecimal getCardBalanceAtTime() {
        return cardBalanceAtTime;
    }

    public void setCardBalanceAtTime(BigDecimal cardBalanceAtTime) {
        this.cardBalanceAtTime = cardBalanceAtTime;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("clientType", getClientType())
                .append("createTime", getCreateTime())
                .append("type", getType())
                .append("openid", getOpenid())
                .append("patientNo", getPatientNo())
                .append("jzCardNo", getJzCardNo())
                .append("zhuyuanNo", getZhuyuanNo())
                .append("zyPayNo", getZyPayNo())
                .append("amount", getAmount())
                .append("exrefund", getExrefund())
                .append("unrefund", getUnrefund())
                .append("wxPayNo", getWxPayNo())
                .append("wxTradeStatus", getWxTradeStatus())
                .append("hisTradeStatus", getHisTradeStatus())
                .append("hisReceiptNo", getHisReceiptNo())
                .append("prescriptionNos", getPrescriptionNos())
                .append("balance", getBalance())
                .append("manualPayState", getManualPayState())
                .append("remark", getRemark())
                .toString();
    }
}

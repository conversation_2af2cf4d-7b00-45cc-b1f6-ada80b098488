package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import space.lzhq.ph.common.Constants;

import java.util.Date;

/**
 * 支付宝退款记录对象 alipay_refund
 *
 * <AUTHOR>
 * @date 2022-09-10
 */
public class AlipayRefund extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 支付宝用户ID
     */
    private String openid;

    /**
     * 患者索引号
     */
    @Excel(name = "患者索引号")
    private String patientId;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号", width = 30)
    private String jzCardNo;

    private String idCardNo;

    private String cardNo;

    /**
     * 住院号
     */
    private String zhuyuanNo;

    /**
     * 总金额
     */
    @Excel(name = "总金额/分")
    private Long totalAmount;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额/分")
    private Long amount;

    /**
     * 掌医充值订单号
     */
    @Excel(name = "掌医充值订单号", width = 30)
    private String outTradeNo;

    /**
     * 支付宝充值订单号
     */
    @Excel(name = "支付宝充值订单号", width = 30)
    private String tradeNo;

    /**
     * 退款订单号
     */
    @Excel(name = "退款订单号", width = 30)
    private String outRefundNo;

    /**
     * HIS交易状态
     */
    @Excel(name = "HIS交易状态")
    private String hisTradeStatus;

    /**
     * HIS交易单号
     */
    @Excel(name = "HIS交易单号")
    private String hisTradeNo;

    /**
     * 支付宝交易状态
     */
    @Excel(name = "支付宝交易状态")
    private String alipayTradeStatus;

    /**
     * 支付宝交易时间
     */
    @Excel(name = "支付宝交易时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date alipayTradeTime;

    /**
     * 人工退款状态:0=未发起,1=待放行,2=已放行
     */
    @Excel(name = "人工退款状态", dictType = "manual_state")
    private Integer manualRefundState;

    public AlipayRefund() {

    }

    public AlipayRefund(AlipayPayment payment, Long refundAmount, String hisTradeStatus) {
        this.createTime = new Date();
        this.type = payment.getType();
        this.openid = payment.getOpenid();
        this.patientId = payment.getPatientId();
        this.jzCardNo = payment.getJzCardNo();
        this.idCardNo = payment.getIdCardNo();
        this.cardNo = payment.getCardNo();
        this.zhuyuanNo = payment.getZhuyuanNo();
        this.totalAmount = payment.getTotalAmount();
        this.amount = refundAmount;
        this.outTradeNo = payment.getOutTradeNo();
        this.tradeNo = payment.getTradeNo();
        this.outRefundNo = "";
        this.hisTradeStatus = hisTradeStatus;
        this.alipayTradeStatus = "";
        this.alipayTradeTime = null;
        this.manualRefundState = Constants.MANUAL_INIT;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setJzCardNo(String jzCardNo) {
        this.jzCardNo = jzCardNo;
    }

    public String getJzCardNo() {
        return jzCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setTotalAmount(Long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getTotalAmount() {
        return totalAmount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getAmount() {
        return amount;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setHisTradeStatus(String hisTradeStatus) {
        this.hisTradeStatus = hisTradeStatus;
    }

    public String getHisTradeStatus() {
        return hisTradeStatus;
    }

    public void setHisTradeNo(String hisTradeNo) {
        this.hisTradeNo = hisTradeNo;
    }

    public String getHisTradeNo() {
        return hisTradeNo;
    }

    public void setAlipayTradeStatus(String alipayTradeStatus) {
        this.alipayTradeStatus = alipayTradeStatus;
    }

    public String getAlipayTradeStatus() {
        return alipayTradeStatus;
    }

    public void setAlipayTradeTime(Date alipayTradeTime) {
        this.alipayTradeTime = alipayTradeTime;
    }

    public Date getAlipayTradeTime() {
        return alipayTradeTime;
    }

    public void setManualRefundState(Integer manualRefundState) {
        this.manualRefundState = manualRefundState;
    }

    public Integer getManualRefundState() {
        return manualRefundState;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("createTime", getCreateTime())
                .append("type", getType())
                .append("openid", getOpenid())
                .append("patientId", getPatientId())
                .append("jzCardNo", getJzCardNo())
                .append("zhuyuanNo", getZhuyuanNo())
                .append("totalAmount", getTotalAmount())
                .append("amount", getAmount())
                .append("outTradeNo", getOutTradeNo())
                .append("tradeNo", getTradeNo())
                .append("outRefundNo", getOutRefundNo())
                .append("hisTradeStatus", getHisTradeStatus())
                .append("hisTradeNo", getHisTradeNo())
                .append("alipayTradeStatus", getAlipayTradeStatus())
                .append("alipayTradeTime", getAlipayTradeTime())
                .append("manualRefundState", getManualRefundState())
                .toString();
    }
}

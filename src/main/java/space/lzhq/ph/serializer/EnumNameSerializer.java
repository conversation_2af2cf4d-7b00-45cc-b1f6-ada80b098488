package space.lzhq.ph.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * 通用枚举序列化器，将枚举序列化为 name 值
 * 使用方式：@JsonSerialize(using = EnumNameSerializer.class)
 * 
 * @param <E> 枚举类型
 */
public class EnumNameSerializer<E extends Enum<E>> extends JsonSerializer<E> {
    @Override
    public void serialize(E value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            gen.writeString(value.name());
        } else {
            gen.writeNull();
        }
    }
}

package space.lzhq.ph.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import space.lzhq.ph.dto.MipOrderCreationForm;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 处方号不重复验证注解
 * 验证费用列表中的处方号不能重复
 */
@Constraint(validatedBy = NoDuplicatePrescriptionNo.NoDuplicatePrescriptionNoValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface NoDuplicatePrescriptionNo {

    String message() default "费用明细中不能存在重复的处方号";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class NoDuplicatePrescriptionNoValidator implements ConstraintValidator<NoDuplicatePrescriptionNo, List<MipOrderCreationForm.Fee>> {

        @Override
        public boolean isValid(List<MipOrderCreationForm.Fee> feeList, ConstraintValidatorContext context) {
            if (feeList == null || feeList.isEmpty()) {
                return true; // 空列表由@NotEmpty处理
            }

            Set<String> prescriptionNos = new HashSet<>();
            for (MipOrderCreationForm.Fee fee : feeList) {
                if (fee != null) {
                    String prescriptionNo = fee.getPrescriptionNo().trim();
                    if (!prescriptionNos.add(prescriptionNo)) {
                        // 如果添加失败，说明已存在相同的处方号
                        context.disableDefaultConstraintViolation();
                        context.buildConstraintViolationWithTemplate("处方号 '" + prescriptionNo + "' 在费用明细中重复出现")
                                .addConstraintViolation();
                        return false;
                    }
                }
            }
            return true;
        }
    }
} 
package space.lzhq.ph.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.regex.Pattern;

/**
 * 排班ID验证注解
 * 验证排班ID为6位数字
 */
@Constraint(validatedBy = ValidScheduleId.ScheduleIdValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidScheduleId {

    String message() default "排班ID必须为6位数字";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class ScheduleIdValidator implements ConstraintValidator<ValidScheduleId, String> {

        private static final Pattern SCHEDULE_ID_PATTERN = Pattern.compile("^\\d{6}$");

        @Override
        public boolean isValid(String scheduleId, ConstraintValidatorContext context) {
            if (scheduleId == null) {
                return false;
            }
            return SCHEDULE_ID_PATTERN.matcher(scheduleId).matches();
        }
    }
} 
package space.lzhq.ph.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.mospital.dongruan.pay.UnifiedOrderForm;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 订单号验证注解
 * 验证订单号格式是否正确
 */
@Constraint(validatedBy = ValidOrderNo.OrderNoValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidOrderNo {

    String message() default "订单号格式不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class OrderNoValidator implements ConstraintValidator<ValidOrderNo, String> {

        @Override
        public boolean isValid(String orderNo, ConstraintValidatorContext context) {
            if (orderNo == null) {
                return false;
            }
            return UnifiedOrderForm.isValidOrderNo(orderNo);
        }
    }

}
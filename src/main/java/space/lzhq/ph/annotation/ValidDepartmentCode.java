package space.lzhq.ph.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.regex.Pattern;

/**
 * 科室编码验证注解
 * 验证科室编码为2-6位数字
 */
@Constraint(validatedBy = ValidDepartmentCode.DepartmentCodeValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidDepartmentCode {

    String message() default "科室编码必须为2-6位数字";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class DepartmentCodeValidator implements ConstraintValidator<ValidDepartmentCode, String> {

        private static final Pattern DEPARTMENT_CODE_PATTERN = Pattern.compile("^\\d{2,6}$");

        @Override
        public boolean isValid(String departmentCode, ConstraintValidatorContext context) {
            if (departmentCode == null) {
                return false;
            }
            return DEPARTMENT_CODE_PATTERN.matcher(departmentCode).matches();
        }
    }
}

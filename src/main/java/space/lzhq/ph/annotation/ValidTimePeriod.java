package space.lzhq.ph.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.List;

/**
 * 时段验证注解
 * 验证时段为"上午"、"下午"或"晚上"
 */
@Constraint(validatedBy = ValidTimePeriod.TimePeriodValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidTimePeriod {

    String message() default "时段必须为\"上午\"、\"下午\"或\"晚上\"";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class TimePeriodValidator implements ConstraintValidator<ValidTimePeriod, String> {

        private static final List<String> VALID_TIME_PERIOD_VALUES = Arrays.asList("上午", "下午", "晚上");

        @Override
        public boolean isValid(String timePeriod, ConstraintValidatorContext context) {
            if (timePeriod == null) {
                return false;
            }
            return VALID_TIME_PERIOD_VALUES.contains(timePeriod);
        }
    }
} 
package space.lzhq.ph.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.regex.Pattern;

/**
 * 医生编码验证注解
 * 验证医生编码为"None"或6位数字
 */
@Constraint(validatedBy = ValidDoctorCode.DoctorCodeValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidDoctorCode {

    String message() default "医生编码必须为\"None\"或6位数字";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class DoctorCodeValidator implements ConstraintValidator<ValidDoctorCode, String> {

        private static final Pattern DOCTOR_CODE_PATTERN = Pattern.compile("^\\d{6}$");

        @Override
        public boolean isValid(String doctorCode, ConstraintValidatorContext context) {
            if (doctorCode == null) {
                return false;
            }
            // 允许"None"或6位数字
            return "None".equals(doctorCode) || DOCTOR_CODE_PATTERN.matcher(doctorCode).matches();
        }
    }
}
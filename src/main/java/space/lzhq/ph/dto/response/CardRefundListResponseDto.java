package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.enums.Relationship;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 余额清退列表响应数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardRefundListResponseDto {

    /**
     * 申报ID
     */
    private String id;

    /**
     * 用户OpenID
     */
    private String openId;

    /**
     * 申报患者身份证附件ID
     */
    private String patientIdCardAttachmentId;

    /**
     * 申报患者身份证号
     */
    private String patientIdCardNo;

    /**
     * 申报患者姓名
     */
    private String patientName;

    /**
     * 申报患者性别
     */
    private String patientGender;

    /**
     * 申报患者出生日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate patientBirthDate;

    /**
     * 申报患者手机号
     */
    private String patientMobile;

    /**
     * 是否代办
     */
    private Integer agentFlag;

    /**
     * 代办人姓名
     */
    private String agentName;

    /**
     * 代办人身份证号
     */
    private String agentIdCardNo;

    /**
     * 代办人身份证附件ID
     */
    private String agentIdCardAttachmentId;

    /**
     * 代办人手机号
     */
    private String agentMobile;

    /**
     * 代办人与患者关系
     */
    private Relationship agentRelation;

    /**
     * 银行卡附件ID
     */
    private String bankCardAttachmentId;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 申报状态
     */
    private CardRefundStatus status;

    /**
     * 提交时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 审批时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 整改意见
     */
    private String correctionAdvice;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
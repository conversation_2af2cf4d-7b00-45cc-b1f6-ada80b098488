package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.enums.MedicalRecordCopyPurpose;
import space.lzhq.ph.enums.Relationship;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 病历复印申请记录管理端列表响应DTO
 */
@Data
@NoArgsConstructor
public class MedicalRecordApplicationAdminListResponseDto {

    /**
     * 主键
     */
    private String id;

    /**
     * 住院号
     */
    private String admissionId;

    /**
     * 申报患者姓名
     */
    private String patientName;

    /**
     * 申报患者身份证号（脱敏）
     */
    private String patientIdCardNo;

    /**
     * 申报患者手机号（脱敏）
     */
    private String patientPhone;

    /**
     * 复印用途
     */
    private MedicalRecordCopyPurpose copyPurpose;

    /**
     * 复印用途名称
     */
    private String copyPurposeName;

    /**
     * 是否代办 0-否 1-是
     */
    private Integer agentFlag;

    /**
     * 代办人姓名
     */
    private String agentName;

    /**
     * 代办人身份证号（脱敏）
     */
    private String agentIdCardNo;

    /**
     * 代办人手机号（脱敏）
     */
    private String agentMobile;

    /**
     * 代办人与患者关系
     */
    private Relationship agentRelation;

    /**
     * 代办人与患者关系名称
     */
    private String agentRelationName;

    /**
     * 收件人姓名
     */
    private String recipientName;

    /**
     * 收件人手机号（脱敏）
     */
    private String recipientMobile;

    /**
     * 收件人地址
     */
    private String recipientAddress;

    /**
     * 复印费用（元）
     */
    private BigDecimal copyFee;

    /**
     * 快递费用（元）
     */
    private BigDecimal shippingFee;

    /**
     * 总费用（元）
     */
    private BigDecimal totalFee;

    /**
     * 快递单号
     */
    private String trackingNumber;

    /**
     * 申报状态
     */
    private MedicalRecordApplicationStatus status;

    /**
     * 申报状态名称
     */
    private String statusName;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    /**
     * 邮寄时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippingTime;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 整改意见
     */
    private String correctionAdvice;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否可以审批
     */
    private Boolean canApprove;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    /**
     * 是否可以邮寄
     */
    private Boolean canShip;

} 
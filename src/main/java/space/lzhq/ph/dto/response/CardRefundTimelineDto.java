package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.enums.CardRefundEventType;
import space.lzhq.ph.enums.TimelineOperatorType;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 时间线事件响应DTO
 *
 * <AUTHOR>
 */
@Data
public class CardRefundTimelineDto {

    /**
     * 事件ID
     */
    private String id;

    /**
     * 事件类型
     */
    private CardRefundEventType eventType;

    /**
     * 事件描述
     */
    private String eventDescription;

    /**
     * 操作者类型
     */
    private TimelineOperatorType operatorType;

    /**
     * 操作者姓名
     */
    private String operatorName;

    /**
     * 变更前状态
     */
    private CardRefundStatus oldStatus;

    /**
     * 变更后状态
     */
    private CardRefundStatus newStatus;

    /**
     * 事件时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    /**
     * 额外数据
     */
    private Map<String, Serializable> additionalData;

    /**
     * 是否为重要事件
     */
    private Boolean isImportant;

    /**
     * 事件图标
     */
    private String icon;

    /**
     * 事件颜色
     */
    private String color;

    /**
     * 根据事件类型设置图标和颜色
     */
    public void setIconAndColor() {
        if (eventType == null) {
            return;
        }

        this.isImportant = eventType.isImportantEvent();

        switch (eventType) {
            case REGISTRATION_CREATED:
                this.icon = "create";
                this.color = "blue";
                break;
            case REGISTRATION_SUBMITTED, REGISTRATION_RESUBMITTED:
                this.icon = "submit";
                this.color = "orange";
                break;
            case REGISTRATION_APPROVED:
                this.icon = "check";
                this.color = "green";
                break;
            case REGISTRATION_REJECTED:
                this.icon = "close";
                this.color = "red";
                break;
            case PATIENT_INFO_UPDATED, AGENT_INFO_UPDATED, BANK_CARD_UPDATED:
                this.icon = "edit";
                this.color = "gray";
                break;
            case AGENT_INFO_SET:
                this.icon = "user-add";
                this.color = "blue";
                break;
            case AGENT_INFO_CLEARED:
                this.icon = "user-delete";
                this.color = "orange";
                break;
            case BANK_CARD_UPLOADED:
                this.icon = "upload";
                this.color = "cyan";
                break;
            default:
                this.icon = "info";
                this.color = "gray";
                break;
        }
    }
} 
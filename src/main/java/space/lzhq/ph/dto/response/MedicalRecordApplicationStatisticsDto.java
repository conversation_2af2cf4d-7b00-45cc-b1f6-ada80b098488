package space.lzhq.ph.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class MedicalRecordApplicationStatisticsDto {

    /**
     * 总申报数量
     */
    private Long totalCount;

    /**
     * 草稿状态数量
     */
    private Long draftCount;

    /**
     * 已提交数量
     */
    private Long submittedCount;

    /**
     * 已通过数量
     */
    private Long approvedCount;

    /**
     * 已驳回数量
     */
    private Long rejectedCount;

    /**
     * 已支付数量
     */
    private Long paidCount;

    /**
     * 已完成数量
     */
    private Long completedCount;

    /**
     * 已邮寄数量
     */
    private Long deliveredCount;

    /**
     * 待审批数量（已提交状态）
     */
    private Long pendingApprovalCount;

    /**
     * 待支付数量（已通过状态）
     */
    private Long pendingPaymentCount;

    /**
     * 待邮寄数量（已支付状态）
     */
    private Long pendingShippingCount;

    /**
     * 今日新增申报数量
     */
    private Long todayCount;

    /**
     * 本周新增申报数量
     */
    private Long thisWeekCount;

    /**
     * 本月新增申报数量
     */
    private Long thisMonthCount;

    /**
     * 总费用统计（已支付的申请）
     */
    private BigDecimal totalRevenue;

    /**
     * 平均费用
     */
    private BigDecimal averageFee;

    /**
     * 按状态统计
     */
    private Map<String, Long> countByStatus;

    /**
     * 按复印用途统计
     */
    private Map<String, Long> countByPurpose;

    /**
     * 最近7天每日申报趋势
     */
    private List<DailyStatistic> dailyTrend;

    /**
     * 每日统计数据
     */
    @Data
    @NoArgsConstructor
    public static class DailyStatistic {
        /**
         * 日期 (yyyy-MM-dd)
         */
        private LocalDate date;

        /**
         * 当日申报数量
         */
        private Long count;

        /**
         * 当日通过数量
         */
        private Long approvedCount;

        /**
         * 当日驳回数量
         */
        private Long rejectedCount;

        /**
         * 当日支付数量
         */
        private Long paidCount;

        /**
         * 当日邮寄数量
         */
        private Long shippedCount;

        /**
         * 当日收入金额
         */
        private BigDecimal dailyRevenue;
    }

} 
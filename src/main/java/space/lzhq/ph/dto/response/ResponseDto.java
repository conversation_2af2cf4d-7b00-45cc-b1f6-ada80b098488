package space.lzhq.ph.dto.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * 通用响应数据结构
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ResponseDto<T> extends BaseResponseDto {

    private T data;

    public ResponseDto(T data) {
        super(0, "操作成功");
        this.data = data;
    }

    public ResponseDto(T data, int code, String msg) {
        super(code, msg);
        this.data = data;
    }

    public static <T> ResponseDto<T> success(T data) {
        return new ResponseDto<>(data);
    }

    public static ResponseDto<?> error(HttpStatus httpStatus, String msg) {
        return new ResponseDto<>(null, httpStatus.value(), msg);
    }

    public static <T> ResponseDto<T> error(String msg) {
        return new ResponseDto<>(null, HttpStatus.INTERNAL_SERVER_ERROR.value(), msg);
    }

} 
package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.enums.CardRefundStatus;
import space.lzhq.ph.enums.Relationship;

import java.time.LocalDateTime;

/**
 * 余额清退登记记录管理端列表响应DTO
 */
@Data
@NoArgsConstructor
public class CardRefundAdminListResponseDto {

    /**
     * 主键
     */
    private String id;

    /**
     * 申报患者姓名
     */
    private String patientName;

    /**
     * 申报患者身份证号
     */
    private String patientIdCardNo;

    /**
     * 申报患者手机号
     */
    private String patientMobile;

    /**
     * 是否代办 0-否 1-是
     */
    private Integer agentFlag;

    /**
     * 代办人姓名
     */
    private String agentName;

    /**
     * 代办人身份证号（脱敏）
     */
    private String agentIdCardNo;

    /**
     * 代办人手机号（脱敏）
     */
    private String agentMobile;

    /**
     * 代办人与患者关系
     */
    private Relationship agentRelation;

    /**
     * 代办人与患者关系名称
     */
    private String agentRelationName;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 申报状态
     */
    private CardRefundStatus status;

    /**
     * 申报状态名称
     */
    private String statusName;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 整改意见
     */
    private String correctionAdvice;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否可以审批
     */
    private Boolean canApprove;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

} 
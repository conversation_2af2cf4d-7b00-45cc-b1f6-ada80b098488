package space.lzhq.ph.dto.response.attachment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 身份证OCR识别结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdCardOcrResultDto {

    /**
     * 身份证类型
     * Front: 正面
     * Back: 背面
     */
    private String type;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号码
     */
    private String id;

    /**
     * 地址
     */
    private String addr;

    /**
     * 性别
     */
    private String gender;

    /**
     * 民族
     */
    private String nationality;

    /**
     * 有效期
     */
    private String validDate;

} 
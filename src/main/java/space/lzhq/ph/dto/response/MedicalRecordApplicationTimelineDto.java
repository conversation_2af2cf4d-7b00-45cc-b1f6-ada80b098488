package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import space.lzhq.ph.enums.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 时间线事件响应DTO
 *
 * <AUTHOR>
 */
@Data
public class MedicalRecordApplicationTimelineDto {

    /**
     * 事件ID
     */
    private String id;

    /**
     * 事件类型
     */
    private MedicalRecordApplicationEventType eventType;

    /**
     * 事件描述
     */
    private String eventDescription;

    /**
     * 操作者类型
     */
    private TimelineOperatorType operatorType;

    /**
     * 操作者姓名
     */
    private String operatorName;

    /**
     * 变更前状态
     */
    private MedicalRecordApplicationStatus oldStatus;

    /**
     * 变更后状态
     */
    private MedicalRecordApplicationStatus newStatus;

    /**
     * 事件时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    /**
     * 额外数据
     */
    private Map<String, Serializable> additionalData;

    /**
     * 是否为重要事件
     */
    private Boolean isImportant;

    /**
     * 事件图标
     */
    private String icon;

    /**
     * 事件颜色
     */
    private String color;

    /**
     * 根据事件类型设置图标和颜色
     */
    public void setIconAndColor() {
        if (eventType == null) {
            return;
        }

        this.isImportant = eventType.isImportantEvent();

        switch (eventType) {
            case APPLICATION_CREATED:
                this.icon = "create";
                this.color = "blue";
                break;
            case APPLICATION_SUBMITTED, APPLICATION_RESUBMITTED:
                this.icon = "submit";
                this.color = "orange";
                break;
            case APPLICATION_APPROVED:
                this.icon = "check";
                this.color = "green";
                break;
            case APPLICATION_REJECTED:
                this.icon = "close";
                this.color = "red";
                break;
            case APPLICATION_COMPLETED:
                this.icon = "check-circle";
                this.color = "green";
                break;
            case APPLICATION_PAID:
                this.icon = "money";
                this.color = "blue";
                break;
            case APPLICATION_DELIVERED:
                this.icon = "truck";
                this.color = "green";
                break;
            case PATIENT_INFO_UPDATED, AGENT_INFO_UPDATED, MEDICAL_INFO_UPDATED, ADDRESS_INFO_UPDATED:
                this.icon = "edit";
                this.color = "gray";
                break;
            case AGENT_INFO_SET:
                this.icon = "user-add";
                this.color = "blue";
                break;
            case AGENT_INFO_CLEARED:
                this.icon = "user-delete";
                this.color = "orange";
                break;
            case ATTACHMENT_UPLOADED:
                this.icon = "upload";
                this.color = "blue";
                break;
            case MAILING_INFO_SET:
                this.icon = "location";
                this.color = "blue";
                break;
            default:
                this.icon = "info";
                this.color = "gray";
                break;
        }
    }
} 
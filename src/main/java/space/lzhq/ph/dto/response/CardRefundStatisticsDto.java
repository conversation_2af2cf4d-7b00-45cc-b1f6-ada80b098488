package space.lzhq.ph.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class CardRefundStatisticsDto {

    /**
     * 总申报数量
     */
    private Long totalCount;

    /**
     * 草稿状态数量
     */
    private Long draftCount;

    /**
     * 已提交数量
     */
    private Long submittedCount;

    /**
     * 已通过数量
     */
    private Long approvedCount;

    /**
     * 已驳回数量
     */
    private Long rejectedCount;

    /**
     * 已重新提交数量
     */
    private Long resubmittedCount;

    /**
     * 待审批数量（已提交 + 已重新提交）
     */
    private Long pendingApprovalCount;

    /**
     * 今日新增申报数量
     */
    private Long todayCount;

    /**
     * 本周新增申报数量
     */
    private Long thisWeekCount;

    /**
     * 本月新增申报数量
     */
    private Long thisMonthCount;

    /**
     * 按状态统计
     */
    private Map<String, Long> countByStatus;

    /**
     * 最近7天每日申报趋势
     */
    private List<DailyStatistic> dailyTrend;

    /**
     * 每日统计数据
     */
    @Data
    @NoArgsConstructor
    public static class DailyStatistic {
        /**
         * 日期 (yyyy-MM-dd)
         */
        private LocalDate date;

        /**
         * 当日申报数量
         */
        private Long count;

        /**
         * 当日通过数量
         */
        private Long approvedCount;

        /**
         * 当日驳回数量
         */
        private Long rejectedCount;
    }

}

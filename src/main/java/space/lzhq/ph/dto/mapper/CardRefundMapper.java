package space.lzhq.ph.dto.mapper;

import org.dromara.hutool.core.data.IdcardUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import space.lzhq.ph.domain.CardRefund;
import space.lzhq.ph.dto.request.CardRefundPatientFormDto;
import space.lzhq.ph.dto.response.CardRefundDetailResponseDto;
import space.lzhq.ph.dto.response.CardRefundListResponseDto;

import java.time.LocalDate;

/**
 * 余额清退映射器
 * 负责实体类与DTO之间的转换
 */
@Mapper
public interface CardRefundMapper {

    CardRefundMapper INSTANCE = Mappers.getMapper(CardRefundMapper.class);

    @Mapping(target = "patientGender", source = "patientIdCardNo", qualifiedByName = "extractGenderFromIdCard")
    @Mapping(target = "patientBirthDate", source = "patientIdCardNo", qualifiedByName = "extractBirthDateFromIdCard")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "openId", ignore = true)
    @Mapping(target = "agentFlag", ignore = true)
    @Mapping(target = "agentName", ignore = true)
    @Mapping(target = "agentIdCardNo", ignore = true)
    @Mapping(target = "agentIdCardAttachmentId", ignore = true)
    @Mapping(target = "agentMobile", ignore = true)
    @Mapping(target = "agentRelation", ignore = true)
    @Mapping(target = "bankCardNo", ignore = true)
    @Mapping(target = "bankCardAttachmentId", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "submitTime", ignore = true)
    @Mapping(target = "approveTime", ignore = true)
    @Mapping(target = "rejectReason", ignore = true)
    @Mapping(target = "correctionAdvice", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "deleteFlag", constant = "false")
    CardRefund fromPatientFormDto(CardRefundPatientFormDto dto);

    /**
     * 将余额清退实体转换为列表响应DTO
     *
     * @param registration 余额清退实体
     * @return 余额清退列表响应DTO
     */
    CardRefundListResponseDto toListResponseDto(CardRefund registration);

    /**
     * 将余额清退实体转换为详情响应DTO
     * 注意：此方法不会自动映射附件对象，需要手动设置附件信息
     *
     * @param registration 余额清退实体
     * @return 余额清退详情响应DTO
     */
    @Mapping(target = "patientIdCardAttachment", ignore = true)
    @Mapping(target = "agentIdCardAttachment", ignore = true)
    @Mapping(target = "bankCardAttachment", ignore = true)
    @Mapping(target = "canApprove", ignore = true)
    CardRefundDetailResponseDto toDetailResponseDto(CardRefund registration);

    /**
     * 从身份证号提取性别
     *
     * @param idCardNo 身份证号
     * @return 性别 (男/女)
     */
    @Named("extractGenderFromIdCard")
    default String extractGenderFromIdCard(String idCardNo) {
        if (!IdcardUtil.isValidCard(idCardNo)) {
            return null;
        }
        return IdcardUtil.getGender(idCardNo) == 1 ? "男" : "女";
    }

    /**
     * 从身份证号提取出生日期
     *
     * @param idCardNo 身份证号
     * @return 出生日期
     */
    @Named("extractBirthDateFromIdCard")
    default LocalDate extractBirthDateFromIdCard(String idCardNo) {
        if (!IdcardUtil.isValidCard(idCardNo)) {
            return null;
        }
        var birthDate = IdcardUtil.getBirthDate(idCardNo);
        return birthDate != null ? birthDate.toLocalDateTime().toLocalDate() : null;
    }
}
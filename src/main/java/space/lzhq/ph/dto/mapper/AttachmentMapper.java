package space.lzhq.ph.dto.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import space.lzhq.ph.domain.Attachment;
import space.lzhq.ph.dto.response.attachment.AttachmentDto;

import java.util.List;

@Mapper
public interface AttachmentMapper {

    AttachmentMapper INSTANCE = Mappers.getMapper(AttachmentMapper.class);

    /**
     * 将附件实体转换为附件DTO
     *
     * @param attachment 附件实体
     * @return 附件DTO
     */
    AttachmentDto toDto(Attachment attachment);

    /**
     * 将附件实体列表转换为附件DTO列表
     *
     * @param attachments 附件实体列表
     * @return 附件DTO列表
     */
    List<AttachmentDto> toDtoList(List<Attachment> attachments);

}
package space.lzhq.ph.dto.mapper;

import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import space.lzhq.ph.dto.response.attachment.IdCardOcrResultDto;

@Mapper
public interface IdCardOcrResultMapper {

    IdCardOcrResultMapper INSTANCE = Mappers.getMapper(IdCardOcrResultMapper.class);

    IdCardOcrResultDto toDto(WxOcrIdCardResult wxOcrIdCardResult);

}

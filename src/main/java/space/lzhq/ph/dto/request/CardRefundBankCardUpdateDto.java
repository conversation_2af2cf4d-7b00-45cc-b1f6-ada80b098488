package space.lzhq.ph.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class CardRefundBankCardUpdateDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "银行卡附件ID不能为空")
    private String bankCardAttachmentId;

    @NotBlank(message = "银行卡号不能为空")
    private String bankCardNo;

}

package space.lzhq.ph.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.annotation.ApprovalRequestDto;
import space.lzhq.ph.annotation.ValidRejectReason;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;

import java.math.BigDecimal;

/**
 * 病历复印申请审批请求DTO
 */
@Data
@NoArgsConstructor
@ValidRejectReason
public class MedicalRecordApplicationApprovalDto implements ApprovalRequestDto {

    /**
     * 审批状态 (APPROVED - 已通过, REJECTED - 已驳回)
     */
    @NotNull(message = "审批状态不能为空")
    private MedicalRecordApplicationStatus status;

    /**
     * 驳回原因 (当状态为REJECTED时必填)
     */
    @Size(max = 500, message = "驳回原因不能超过500个字符")
    private String rejectReason;

    /**
     * 整改意见 (当状态为REJECTED时可选)
     */
    @Size(max = 1000, message = "整改意见不能超过1000个字符")
    private String correctionAdvice;

    /**
     * 复印费用（元）- 审批通过时必填
     */
    @DecimalMin(value = "0.00", message = "复印费用不能为负数")
    @DecimalMax(value = "9999.99", message = "复印费用不能超过9999.99元")
    @Digits(integer = 4, fraction = 2, message = "复印费用格式不正确，最多4位整数2位小数")
    private BigDecimal copyFee;

    /**
     * 快递费用（元）- 审批通过时必填
     */
    @DecimalMin(value = "0.00", message = "快递费用不能为负数")
    @DecimalMax(value = "999.99", message = "快递费用不能超过999.99元")
    @Digits(integer = 3, fraction = 2, message = "快递费用格式不正确，最多3位整数2位小数")
    private BigDecimal shippingFee;

    @Override
    public boolean isRejectedStatus() {
        return status == MedicalRecordApplicationStatus.REJECTED;
    }
} 
package space.lzhq.ph.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.enums.CardRefundStatus;

import java.time.LocalDate;

/**
 * 余额清退登记搜索表单
 */
@Data
@NoArgsConstructor
public class CardRefundSearchForm {

    /**
     * 申报患者姓名
     */
    private String patientName;

    /**
     * 申报患者身份证号
     */
    private String patientIdCardNo;

    /**
     * 申报患者手机号
     */
    private String patientMobile;

    /**
     * 申报状态
     */
    private CardRefundStatus status;

    /**
     * 提交开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate submitDateStart;

    /**
     * 提交结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate submitDateEnd;

} 
package space.lzhq.ph.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.xss.Xss;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.data.PhoneUtil;
import space.lzhq.ph.enums.Relationship;

import java.io.Serial;
import java.io.Serializable;

/**
 * 余额清退代办人更新请求DTO
 */
@Data
public class CardRefundAgentUpdateDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 代办人姓名
     */
    @NotBlank(message = "代办人姓名不能为空")
    @Xss(message = "代办人姓名不能包含特殊字符")
    private String agentName;

    /**
     * 代办人身份证号
     */
    @NotBlank(message = "代办人身份证号不能为空")
    private String agentIdCardNo;

    /**
     * 代办人身份证附件ID
     */
    @NotBlank(message = "代办人身份证附件ID不能为空")
    private String agentIdCardAttachmentId;

    /**
     * 代办人手机号
     */
    @NotBlank(message = "代办人手机号不能为空")
    private String agentMobile;

    /**
     * 代办人与患者关系
     */
    @NotNull(message = "代办人与患者关系不能为空")
    @JsonSerialize(using = space.lzhq.ph.serializer.EnumNameSerializer.class)
    private Relationship agentRelation;

    /**
     * 验证代办人身份证号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "代办人身份证号格式不正确")
    public boolean isAgentIdCardValid() {
        // 如果身份证号为空，跳过格式验证（由 @NotBlank 处理空值）
        if (StringUtils.isBlank(agentIdCardNo)) {
            return true;
        }
        return IdcardUtil.isValidCard(agentIdCardNo);
    }

    /**
     * 验证代办人手机号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "代办人手机号格式不正确")
    public boolean isAgentMobileValid() {
        // 如果手机号为空，跳过格式验证（由 @NotBlank 处理空值）
        if (StringUtils.isBlank(agentMobile)) {
            return true;
        }
        return PhoneUtil.isMobile(agentMobile);
    }
} 
package space.lzhq.ph.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.xss.Xss;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.data.PhoneUtil;

import java.io.Serial;
import java.io.Serializable;

/**
 * 余额清退申报人认证表单请求DTO
 */
@Data
public class CardRefundPatientFormDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 申报患者身份证附件ID
     */
    @NotBlank(message = "申报患者身份证附件ID不能为空")
    private String patientIdCardAttachmentId;

    /**
     * 申报患者身份证号
     */
    @NotBlank(message = "申报患者身份证号不能为空")
    private String patientIdCardNo;

    /**
     * 申报患者姓名
     */
    @NotBlank(message = "申报患者姓名不能为空")
    @Xss(message = "申报患者姓名不能包含特殊字符")
    private String patientName;

    /**
     * 申报患者手机号
     */
    @NotBlank(message = "申报患者手机号不能为空")
    private String patientMobile;

    /**
     * 验证患者身份证号格式
     * 注意：只有在身份证号不为空时才验证格式，空值验证由 @NotBlank 处理
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "申报患者身份证号格式不正确")
    public boolean isPatientIdCardValid() {
        // 如果身份证号为空，跳过格式验证（由 @NotBlank 处理空值）
        if (StringUtils.isBlank(patientIdCardNo)) {
            return true;
        }
        return IdcardUtil.isValidCard(patientIdCardNo);
    }

    /**
     * 验证患者手机号格式
     * 注意：只有在手机号不为空时才验证格式，空值验证由 @NotBlank 处理
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "申报患者手机号格式不正确")
    public boolean isPatientMobileValid() {
        // 如果手机号为空，跳过格式验证（由 @NotBlank 处理空值）
        if (StringUtils.isBlank(patientMobile)) {
            return true;
        }
        return PhoneUtil.isMobile(patientMobile);
    }
}
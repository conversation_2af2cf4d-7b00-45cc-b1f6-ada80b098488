package space.lzhq.ph.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.enums.MedicalRecordCopyPurpose;

import java.time.LocalDate;

/**
 * 病历复印申请搜索表单
 */
@Data
@NoArgsConstructor
public class MedicalRecordApplicationSearchForm {

    /**
     * 申报患者姓名
     */
    private String patientName;

    /**
     * 申报患者身份证号
     */
    private String patientIdCardNo;

    /**
     * 申报患者手机号
     */
    private String patientPhone;

    /**
     * 住院号
     */
    private String admissionId;

    /**
     * 申报状态
     */
    private MedicalRecordApplicationStatus status;

    /**
     * 复印用途
     */
    private MedicalRecordCopyPurpose copyPurpose;

    /**
     * 收件人姓名
     */
    private String recipientName;

    /**
     * 收件人手机号
     */
    private String recipientMobile;

    /**
     * 快递单号
     */
    private String trackingNumber;

    /**
     * 提交开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate submitDateStart;

    /**
     * 提交结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate submitDateEnd;

    /**
     * 审批开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate approveDateStart;

    /**
     * 审批结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate approveDateEnd;

    /**
     * 支付开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate payDateStart;

    /**
     * 支付结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate payDateEnd;

} 
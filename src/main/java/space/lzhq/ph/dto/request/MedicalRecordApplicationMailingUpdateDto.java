package space.lzhq.ph.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.xss.Xss;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.data.PhoneUtil;

import java.io.Serial;
import java.io.Serializable;

/**
 * 病历复印申请邮寄信息更新请求DTO
 */
@Data
public class MedicalRecordApplicationMailingUpdateDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收件人姓名
     */
    @NotBlank(message = "收件人姓名不能为空")
    @Xss(message = "收件人姓名不能包含特殊字符")
    private String recipientName;

    /**
     * 收件人手机号
     */
    @NotBlank(message = "收件人手机号不能为空")
    private String recipientMobile;

    /**
     * 收件人地址
     */
    @NotBlank(message = "收件人地址不能为空")
    @Xss(message = "收件人地址不能包含特殊字符")
    private String recipientAddress;

    /**
     * 验证收件人手机号格式
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "收件人手机号格式不正确")
    public boolean isRecipientMobileValid() {
        return StringUtils.isBlank(recipientMobile) || PhoneUtil.isMobile(recipientMobile);
    }
} 
package space.lzhq.ph.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.annotation.ApprovalRequestDto;
import space.lzhq.ph.annotation.ValidRejectReason;
import space.lzhq.ph.enums.CardRefundStatus;

/**
 * 余额清退登记审批请求DTO
 */
@Data
@NoArgsConstructor
@ValidRejectReason
public class CardRefundApprovalDto implements ApprovalRequestDto {

    /**
     * 审批状态 (APPROVED - 已通过, REJECTED - 已驳回)
     */
    @NotNull(message = "审批状态不能为空")
    private CardRefundStatus status;

    /**
     * 驳回原因 (当状态为REJECTED时必填)
     */
    @Size(max = 500, message = "驳回原因不能超过500个字符")
    private String rejectReason;

    /**
     * 整改意见 (当状态为REJECTED时可选)
     */
    @Size(max = 1000, message = "整改意见不能超过1000个字符")
    private String correctionAdvice;

    @Override
    public boolean isRejectedStatus() {
        return status == CardRefundStatus.REJECTED;
    }
} 
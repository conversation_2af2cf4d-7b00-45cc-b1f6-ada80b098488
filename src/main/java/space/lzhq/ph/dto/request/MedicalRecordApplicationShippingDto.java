package space.lzhq.ph.dto.request;

import com.ruoyi.common.xss.Xss;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 病历复印申请邮寄操作请求DTO
 */
@Data
@NoArgsConstructor
public class MedicalRecordApplicationShippingDto {

    /**
     * 快递单号
     */
    @NotBlank(message = "快递单号不能为空")
    @Size(min = 8, max = 50, message = "快递单号长度必须在8-50个字符之间")
    @Xss(message = "快递单号不能包含特殊字符")
    private String trackingNumber;

    /**
     * 邮寄备注
     */
    @Size(max = 200, message = "邮寄备注不能超过200个字符")
    @Xss(message = "邮寄备注不能包含特殊字符")
    private String shippingNote;

} 
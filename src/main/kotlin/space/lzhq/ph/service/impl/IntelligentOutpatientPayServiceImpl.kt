package space.lzhq.ph.service.impl

import cn.hutool.core.util.NumberUtil
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.dongruan.pay.DongruanPayService
import org.mospital.dongruan.pay.UnifiedOrderForm
import org.mospital.jackson.JacksonKit
import org.mospital.witontek.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.OutpatientPaymentRecord
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.dto.FeeCalculationResult
import space.lzhq.ph.dto.IntelligentPayResult
import space.lzhq.ph.dto.PaymentResult
import space.lzhq.ph.dto.WxPaymentResult
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IIntelligentOutpatientPayService
import space.lzhq.ph.service.IOutpatientPaymentRecordService
import space.lzhq.ph.service.IWxPaymentService
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 智能门诊缴费服务实现
 */
@Service
@Transactional
class IntelligentOutpatientPayServiceImpl : IIntelligentOutpatientPayService {

    private val logger = LoggerFactory.getLogger(IntelligentOutpatientPayServiceImpl::class.java)

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var outpatientPaymentRecordService: IOutpatientPaymentRecordService

    override fun processIntelligentPayment(patient: Patient, feeNos: List<String>): IntelligentPayResult {
        
        // 1. 验证并计算费用
        val feeResult = validateAndCalculateFees(patient, feeNos)
        if (feeResult.totalAmount <= BigDecimal.ZERO) {
            return IntelligentPayResult.Error("未查询到有效的待缴费用")
        }
        
        // 2. 查询就诊卡余额
        val cardBalance = queryCardBalance(patient)
        
        // 3. 判断是否可以直接缴费
        return if (cardBalance >= feeResult.totalAmount) {
            // 余额充足，直接缴费
            val paymentResult = payDirectlyFromCard(patient, feeResult)
            if (paymentResult.success) {
                IntelligentPayResult.DirectPaySuccess(paymentResult.paymentRecord!!)
            } else {
                IntelligentPayResult.Error(paymentResult.errorMessage ?: "缴费失败")
            }
        } else {
            // 余额不足，创建充值订单
            val shortfall = feeResult.totalAmount - cardBalance
            val wxPaymentResult = createRechargeOrder(patient, feeResult, shortfall)
            if (wxPaymentResult.success) {
                IntelligentPayResult.NeedRecharge(wxPaymentResult.wxPayment!!, wxPaymentResult.wxPayParams!!)
            } else {
                IntelligentPayResult.Error(wxPaymentResult.errorMessage ?: "创建支付订单失败")
            }
        }
    }

    override fun validateAndCalculateFees(patient: Patient, feeNos: List<String>): FeeCalculationResult {
        // 1. 调用HIS接口查询可缴费项目（参考 /api/outpatient/payments）
        val availablePayments = queryAvailablePayments(patient)
        
        // 2. 验证费用单号有效性
        val validItems = availablePayments.filter { feeNos.contains(it.prescriptionNo) }
        
        if (validItems.isEmpty()) {
            throw RuntimeException("未查询到有效的待缴费用")
        }
        
        // 3. 计算总费用
        val totalAmount = validItems.sumOf { it.orderFee?.toBigDecimal() ?: BigDecimal.ZERO }
        
        return FeeCalculationResult(feeNos, totalAmount, validItems)
    }

    override fun queryCardBalance(patient: Patient): BigDecimal {
        val resp = WitontekService.me.queryMenzhenPatient(QueryMenzhenPatientRequest().apply {
            this.jzCardNo = patient.jzCardNo
        })
        
        if (!resp.isOk() || !resp.isValid()) {
            throw RuntimeException("查询就诊卡信息失败：${resp.message}")
        }
        
        return resp.balance
    }

    override fun payDirectlyFromCard(patient: Patient, feeDetails: FeeCalculationResult): PaymentResult {
        var outpatientPayResult: OutpatientPayResponse? = null
        var exception: Exception? = null
        
        try {
            // 调用HIS门诊缴费接口
            outpatientPayResult = WitontekService.me.outpatientPay(
                OutpatientPayRequest().apply {
                    this.prescriptionNo = feeDetails.feeNos.joinToString(",")
                    this.realName = patient.name
                    this.patientCard = patient.cardNo
                    this.payType = "eCardPay"
                    this.tradeNo = UnifiedOrderForm.newOrderNo()
                    this.feeType = "门诊缴费"
                    this.orderFee = feeDetails.totalAmount.toPlainString()
                    this.payedTime = LocalDateTime.now()
                })
        } catch (e: Exception) {
            logger.error("调用HIS缴费接口异常", e)
            exception = e
        }
        
        // 无论成功失败都保存缴费记录
        val isSuccess = outpatientPayResult?.isOk() == true
        val paymentRecord = savePaymentRecord(patient, feeDetails, outpatientPayResult, exception, isSuccess)
        
        return if (isSuccess) {
            PaymentResult(true, paymentRecord = paymentRecord)
        } else {
            val errorMessage = outpatientPayResult?.message ?: exception?.message ?: "缴费处理异常"
            PaymentResult(false, errorMessage = errorMessage)
        }
    }

    override fun createRechargeOrder(patient: Patient, feeDetails: FeeCalculationResult, shortfall: BigDecimal): WxPaymentResult {
        try {
            // 参考原 /api/outpatient/wxpay 接口逻辑
            val session = getCurrentSession()
            val ip = getCurrentClientIP()
            
            val outTradeNo = UnifiedOrderForm.newOrderNo()
            val form = UnifiedOrderForm(openid = patient.openId)
                .orderNo(outTradeNo)
                .orderType(2)
                .payWayType(if (session?.clientType == ClientType.WEIXIN_MP.code) 32 else 35)
                .subject("${outTradeNo}-${patient.jzCardNo}-门诊缴费")
                .amount(shortfall)
                .notifyUrl("https://xjzybyy.xjyqtl.cn/open/wx/onDongruanPay")
                .clientIp(ip)
            form.sign()
            
            val result = runBlocking {
                DongruanPayService.me.createUnifiedOrder(form)
            }
            
            return if (result.isOk()) {
                // 保存微信支付订单（增强版本）
                val wxPayment = saveEnhancedWxPayment(patient, outTradeNo, shortfall, feeDetails)
                WxPaymentResult(true, wxPayment = wxPayment, wxPayParams = result.data)
            } else {
                WxPaymentResult(false, errorMessage = result.message)
            }
            
        } catch (e: Exception) {
            logger.error("创建充值订单失败", e)
            return WxPaymentResult(false, errorMessage = "创建支付订单异常")
        }
    }

    /**
     * 查询可缴费项目（参考 /api/outpatient/payments）
     */
    private fun queryAvailablePayments(patient: Patient): List<QueryOutpatientPayResponse.OutpatientPayItem> {
        val availableRegistrationResponse: QueryClinicNoResponse =
            WitontekService.me.queryClinicNo(QueryClinicNoRequest().apply {
                this.realName = patient.name
                this.patientCard = patient.cardNo
            })
        
        if (!availableRegistrationResponse.isOk()) {
            throw RuntimeException("查询挂号信息失败：${availableRegistrationResponse.message}")
        }

        val prescriptions = mutableListOf<QueryOutpatientPayResponse.OutpatientPayItem>()
        
        runBlocking {
            flow {
                availableRegistrationResponse.results.forEach { registration ->
                    val prescriptionResponse: QueryOutpatientPayResponse =
                        WitontekService.me.queryOutpatientPay(
                            QueryOutpatientPayRequest().apply {
                                this.patientCard = patient.cardNo
                                this.clinicNo = registration.clinicNo
                                this.realName = patient.name
                            }
                        )
                    if (!prescriptionResponse.isOk()) {
                        logger.error("Failed to query prescriptions for clinicNo=${registration.clinicNo}")
                        return@forEach
                    }
                    prescriptionResponse.results.forEach { prescription ->
                        // 返回未收费、非慢病、非急诊、非零的费用
                        if (!prescription.isCharged() &&
                            !prescription.isChronic() &&
                            !prescription.isEmergency() &&
                            NumberUtil.isNumber(prescription.orderFee) &&
                            NumberUtil.parseDouble(prescription.orderFee) > 0
                        ) {
                            emit(prescription)
                        }
                    }
                }
            }.toList(prescriptions)
        }
        
        return prescriptions
    }

    /**
     * 保存增强版微信支付订单
     */
    private fun saveEnhancedWxPayment(
        patient: Patient, 
        outTradeNo: String, 
        amount: BigDecimal, 
        feeDetails: FeeCalculationResult
    ): WxPayment {
        val wxPayment = WxPayment().apply {
            this.openid = patient.openId
            this.zyPayNo = outTradeNo
            this.amount = (amount * BigDecimal(100)).toLong()  // 转为分
            this.type = ServiceType.MZ.name
            this.hisTradeStatus = ""
            this.prescriptionNos = feeDetails.feeNos.joinToString(",")
            this.patientNo = patient.accountNo
            this.jzCardNo = patient.jzCardNo
            this.cardNo = patient.cardNo
            this.idCardNo = patient.idCardNo
            this.zhuyuanNo = patient.zhuyuanNo ?: ""
            this.clientType = patient.clientType
            this.patientName = patient.name
            
            // 新增字段
            this.feeDetails = JacksonKit.writeValueAsString(mapOf(
                "feeNos" to feeDetails.feeNos,
                "feeItems" to feeDetails.validPrescriptions,
                "totalAmount" to feeDetails.totalAmount,
                "calculatedAt" to LocalDateTime.now()
            ))
            this.totalFee = feeDetails.totalAmount
            this.cardBalanceAtTime = queryCardBalance(patient)
            this.paymentType = WxPayment.PAYMENT_TYPE_INTELLIGENT  // 智能缴费
            
            // 已退金额，初始为0
            this.exrefund = 0L
            // 未退（可退）金额，初始为null
            this.unrefund = null

            this.wxPayNo = ""
            this.wxTradeStatus = ""
            this.hisSuccessTime = null
            this.manualPayState = space.lzhq.ph.common.Constants.MANUAL_INIT
            this.remark = ""
        }
        
        return wxPaymentService.insertWxPayment(wxPayment).let {
            if (it == 1) wxPayment else throw RuntimeException("保存微信支付订单失败")
        }
    }

    /**
     * 保存缴费记录
     */
    private fun savePaymentRecord(
        patient: Patient, 
        feeDetails: FeeCalculationResult, 
        hisResponse: OutpatientPayResponse?, 
        exception: Exception?, 
        isSuccess: Boolean
    ): OutpatientPaymentRecord {
        val paymentRecord = OutpatientPaymentRecord().apply {
            this.patientCardNo = patient.cardNo  // 使用患者卡号
            this.patientName = patient.name      // 使用患者姓名
            this.feeDetails = JacksonKit.writeValueAsString(mapOf(
                "feeNos" to feeDetails.feeNos,
                "feeItems" to feeDetails.validPrescriptions,
                "totalAmount" to feeDetails.totalAmount,
                "paymentTime" to LocalDateTime.now()
            ))
            this.totalAmount = feeDetails.totalAmount
            this.paymentMethod = "CARD_BALANCE"
            this.status = if (isSuccess) 1 else 2  // 1=成功，2=失败
            
            // 记录HIS响应信息
            if (hisResponse != null) {
                this.hisTradeNo = hisResponse.invoiceNo ?: ""
                this.hisResponseCode = hisResponse.result
                this.hisResponseMessage = hisResponse.message
                this.hisResponseData = JacksonKit.writeValueAsString(hisResponse)
            }
            
            // 记录异常信息
            if (exception != null) {
                this.errorDetails = "${exception.javaClass.simpleName}: ${exception.message}\n${exception.stackTrace.take(5).joinToString("\n")}"
            }
        }
        
        return outpatientPaymentRecordService.insertOutpatientPaymentRecord(paymentRecord).let {
            if (it == 1) paymentRecord else throw RuntimeException("保存缴费记录失败")
        }
    }

    /**
     * 获取当前会话
     */
    private fun getCurrentSession() = try {
        val request = (RequestContextHolder.getRequestAttributes() as ServletRequestAttributes).request
        request.getClientSession()
    } catch (_: Exception) {
        null
    }

    /**
     * 获取当前客户端IP
     */
    private fun getCurrentClientIP() = try {
        val request = (RequestContextHolder.getRequestAttributes() as ServletRequestAttributes).request
        ServletUtil.getClientIP(request)
    } catch (_: Exception) {
        "127.0.0.1"
    }
} 
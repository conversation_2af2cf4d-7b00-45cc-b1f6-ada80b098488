package space.lzhq.ph.service.impl

import org.mospital.dongruan.pay.DongruanPayService
import org.mospital.dongruan.pay.UnifiedPayOrderQueryRequest
import org.mospital.dongruan.pay.UnifiedPayStatusEnum
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import space.lzhq.ph.common.Constants
import space.lzhq.ph.common.OptimisticLockException
import space.lzhq.ph.domain.*
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.service.IOrderQueryService
import space.lzhq.ph.service.IWxPaymentService

/**
 * 订单查询服务实现
 */
@Service
class OrderQueryServiceImpl : IOrderQueryService {

    private val logger = LoggerFactory.getLogger(OrderQueryServiceImpl::class.java)

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    override suspend fun queryAndUpdateOrder(
        payment: WxPayment,
        openid: String?,
        options: Set<OrderQueryOptions>
    ): OrderQueryResult {

        val safeMode = OrderQueryOptions.SAFE_MODE in options
        val withRecharge = OrderQueryOptions.WITH_RECHARGE in options
        val skipStatusCheck = OrderQueryOptions.SKIP_STATUS_CHECK in options

        try {
            // 状态检查（除非跳过）
            if (!skipStatusCheck && isOrderAlreadyProcessed(payment)) {
                return OrderQueryResult.Success("订单状态已更新", payment, null)
            }

            // 确定使用的openid
            val effectiveOpenid = openid ?: payment.openid
            if (effectiveOpenid.isNullOrBlank()) {
                return OrderQueryResult.Failure("openid为空")
            }

            // 创建查询请求
            val form = UnifiedPayOrderQueryRequest(openid = effectiveOpenid)
                .orderNo(payment.zyPayNo)
            form.sign()

            // 执行查询
            val queryOrderResult = DongruanPayService.me.queryOrder(form)

            // 检查查询结果
            if (!queryOrderResult.isOk() || queryOrderResult.data == null) {
                return OrderQueryResult.Failure(queryOrderResult.message)
            }

            // 使用乐观锁更新订单状态
            val updateSuccess = wxPaymentService.updateOnPayWithOptimisticLock(payment, queryOrderResult.data)
            if (!updateSuccess) {
                // 乐观锁冲突，抛出特定异常让Controller重试
                throw OptimisticLockException("订单状态已被其他操作更新，请重试")
            }

            // 获取更新后的支付记录
            var updatedPayment = wxPaymentService.selectWxPaymentById(payment.id)

            // 检查更新后的支付记录是否存在
            if (updatedPayment == null) {
                logger.error("更新后未找到支付记录，支付ID: ${payment.id}")
                return OrderQueryResult.Failure("更新后未找到支付记录")
            }

            // 如果需要充值，直接调用 HisExt.recharge
            if (withRecharge) {
                try {
                    val rechargeResult = HisExt.recharge(payment.zyPayNo)
                    when (rechargeResult) {
                        is RechargeResult.Success -> {
                            // 充值成功，使用返回的最新支付记录
                            updatedPayment = rechargeResult.updatedPayment
                            logger.info("充值成功: ${rechargeResult.message}")
                        }

                        is RechargeResult.Skipped -> {
                            // 跳过充值，使用当前支付记录
                            updatedPayment = rechargeResult.currentPayment
                            logger.info("跳过充值: ${rechargeResult.message}")
                        }

                        is RechargeResult.Failure -> {
                            logger.warn("充值调用失败: ${rechargeResult.message}")
                            // 充值失败不影响订单查询结果，但记录警告日志
                        }
                    }
                } catch (e: Exception) {
                    logger.warn("充值调用异常: ${e.message}", e)
                    // 充值失败不影响订单查询结果
                }
            }

            return OrderQueryResult.Success("查询并更新成功", updatedPayment, queryOrderResult.data)

        } catch (e: Exception) {
            val errorMessage = "查询订单状态失败: ${e.message}"
            if (safeMode) {
                logger.error(errorMessage, e)
                return OrderQueryResult.Failure(errorMessage)
            } else {
                throw e
            }
        }
    }

    /**
     * 检查订单是否已经处理过
     */
    private fun isOrderAlreadyProcessed(payment: WxPayment): Boolean {
        // 检查微信支付状态
        if (payment.wxTradeStatus == UnifiedPayStatusEnum.SUCCESS.msg) {
            return true
        }

        // 检查是否已有微信支付单号
        if (!payment.wxPayNo.isNullOrBlank()) {
            return true
        }

        return false
    }

    override suspend fun confirmPaymentAndRecharge(
        outOrderNo: String,
        patient: Patient
    ): OrderQueryResult {
        try {
            // 1. 查询支付记录
            val payment: WxPayment = wxPaymentService.selectWxPaymentByZyPayNo(outOrderNo)
                ?: return OrderQueryResult.Failure("订单不存在[$outOrderNo]")

            // 2. 验证订单类型是否为智能缴费（paymentType = 2）
            if (payment.paymentType != WxPayment.PAYMENT_TYPE_INTELLIGENT) {
                return OrderQueryResult.Failure("订单类型不正确，仅支持智能缴费订单")
            }

            // 3. 快速检查：如果订单已完成充值，直接返回
            if (!payment.wxPayNo.isNullOrBlank() && payment.hisTradeStatus == Constants.RECHARGE_OK) {
                return OrderQueryResult.Success("订单已充值完成", payment, null)
            }

            // 4. 查询并更新订单状态，带充值处理
            val queryResult = queryAndUpdateOrder(payment, patient.openId, setOf(OrderQueryOptions.WITH_RECHARGE))

            return when (queryResult) {
                is OrderQueryResult.Failure -> {
                    OrderQueryResult.Failure("查询订单状态失败: ${queryResult.message}")
                }

                is OrderQueryResult.Success -> {
                    val updatedPayment = queryResult.updatedPayment
                    logger.info("智能缴费充值确认完成，orderId=${updatedPayment.id}, wxTradeStatus=${updatedPayment.wxTradeStatus}, hisTradeStatus=${updatedPayment.hisTradeStatus}")

                    // 5. 检查充值是否成功
                    when {
                        updatedPayment.hisTradeStatus == Constants.RECHARGE_OK -> {
                            OrderQueryResult.Success("充值成功", updatedPayment, queryResult.queryResult)
                        }

                        updatedPayment.wxTradeStatus == UnifiedPayStatusEnum.SUCCESS.msg -> {
                            OrderQueryResult.Failure("微信支付成功但充值失败，请联系客服处理")
                        }

                        else -> {
                            OrderQueryResult.Failure("支付尚未完成")
                        }
                    }
                }
            }
        } catch (_: OptimisticLockException) {
            return OrderQueryResult.Failure("订单状态冲突，请重试")
        } catch (e: Exception) {
            logger.error("处理智能缴费充值确认异常, outOrderNo=$outOrderNo", e)
            return OrderQueryResult.Failure("充值确认处理异常: ${e.message}")
        }
    }
}

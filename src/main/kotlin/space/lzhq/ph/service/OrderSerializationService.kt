package space.lzhq.ph.service

import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Duration

/**
 * 订单操作串行化服务
 * 使用协程Mutex提供基于订单号的分段锁机制
 * 
 * 注意：从Guava Striped改为协程Mutex是为了解决以下问题：
 * 1. Guava Lock需要在同一线程获取和释放，但协程可能在不同线程间切换
 * 2. 协程Mutex是suspend函数友好的，支持协程的挂起和恢复
 * 3. 避免线程阻塞，提高并发性能
 * 
 * 内存管理：
 * 使用Caffeine Cache替代ConcurrentHashMap，实现自动过期和清理机制：
 * - 写入后30分钟自动过期，防止内存泄漏
 * - 最大缓存大小10000个Mutex，超出时使用LRU驱逐策略
 * - 自动清理不再使用的Mutex对象
 */
@Service
class OrderSerializationService {

    private val logger = LoggerFactory.getLogger(OrderSerializationService::class.java)

    /**
     * 使用Caffeine Cache存储每个订单号对应的Mutex
     * 特性：
     * - expireAfterWrite: 写入后30分钟自动过期，确保长时间不使用的Mutex被清理
     * - maximumSize: 最大缓存10000个Mutex，防止在极高并发下无限增长
     * - LRU驱逐: 当达到最大大小时，优先清理最近最少使用的Mutex
     */
    private val orderMutexCache: Cache<String, Mutex> = Caffeine.newBuilder()
        .expireAfterWrite(Duration.ofMinutes(30))
        .maximumSize(10_000)
        .recordStats()
        .build()

    /**
     * 串行执行订单操作（协程版本）
     * @param orderNo 订单号
     * @param operation 需要串行执行的suspend操作
     */
    suspend fun <T> executeWithOrderLock(orderNo: String, operation: suspend () -> T): T {
        // 获取或创建对应订单的Mutex，使用Caffeine的线程安全get方法
        val mutex = orderMutexCache.get(orderNo) { Mutex() }
        
        logger.debug("获取订单锁: $orderNo, mutex=${mutex.hashCode()}, 当前缓存大小=${getMutexCount()}")

        return mutex.withLock {
            logger.debug("执行订单操作: $orderNo")
            try {
                operation()
            } finally {
                logger.debug("释放订单锁: $orderNo")
            }
        }
    }

    /**
     * 同步版本的锁执行（兼容非协程代码）
     * @param orderNo 订单号  
     * @param operation 需要串行执行的操作
     */
    fun <T> executeWithOrderLockBlocking(orderNo: String, operation: () -> T): T {
        // 获取或创建对应订单的Mutex
        val mutex = orderMutexCache.get(orderNo) { Mutex() }
        
        logger.debug("获取订单锁(阻塞): $orderNo, mutex=${mutex.hashCode()}, 当前缓存大小=${getMutexCount()}")

        // 使用runBlocking包装，但这会阻塞当前线程
        return kotlinx.coroutines.runBlocking {
            mutex.withLock {
                logger.debug("执行订单操作(阻塞): $orderNo")
                try {
                    operation()
                } finally {
                    logger.debug("释放订单锁(阻塞): $orderNo")
                }
            }
        }
    }

    /**
     * 检查订单是否正在处理中（非阻塞）
     * @param orderNo 订单号
     * @return true表示正在处理，false表示未在处理
     */
    fun isOrderProcessing(orderNo: String): Boolean {
        val mutex = orderMutexCache.getIfPresent(orderNo)
        val isLocked = mutex?.isLocked ?: false
        logger.debug("检查订单处理状态: $orderNo, isLocked=$isLocked")
        return isLocked
    }

    /**
     * 尝试获取锁并执行操作（协程非阻塞版本）
     * 如果锁已被占用，立即返回null而不等待
     * @param orderNo 订单号
     * @param operation 需要执行的suspend操作
     * @return 操作结果，如果锁被占用则返回null
     */
    suspend fun <T> tryExecuteWithOrderLock(orderNo: String, operation: suspend () -> T): T? {
        val mutex = orderMutexCache.get(orderNo) { Mutex() }
        
        logger.debug("尝试获取订单锁: $orderNo, mutex=${mutex.hashCode()}")
        
        return if (mutex.tryLock()) {
            try {
                logger.debug("成功获取订单锁，执行操作: $orderNo")
                operation()
            } finally {
                mutex.unlock()
                logger.debug("释放订单锁: $orderNo")
            }
        } else {
            logger.debug("订单锁被占用，跳过执行: $orderNo")
            null
        }
    }

    /**
     * 尝试获取锁并执行操作（同步非阻塞版本）  
     * 如果锁已被占用，立即返回null而不等待
     * @param orderNo 订单号
     * @param operation 需要执行的操作
     * @return 操作结果，如果锁被占用则返回null
     */
    fun <T> tryExecuteWithOrderLockBlocking(orderNo: String, operation: () -> T): T? {
        val mutex = orderMutexCache.get(orderNo) { Mutex() }
        
        logger.debug("尝试获取订单锁(阻塞): $orderNo, mutex=${mutex.hashCode()}")
        
        return kotlinx.coroutines.runBlocking {
            if (mutex.tryLock()) {
                try {
                    logger.debug("成功获取订单锁，执行操作(阻塞): $orderNo")
                    operation()
                } finally {
                    mutex.unlock()
                    logger.debug("释放订单锁(阻塞): $orderNo")
                }
            } else {
                logger.debug("订单锁被占用，跳过执行(阻塞): $orderNo")
                null
            }
        }
    }

    /**
     * 手动清理特定订单的Mutex（可选，通常不需要手动调用）
     * 注意：只有在确定订单不会再被使用时才调用
     */
    fun cleanupOrderMutex(orderNo: String) {
        orderMutexCache.invalidate(orderNo)
        logger.debug("手动清理订单锁: $orderNo, 当前缓存大小=${getMutexCount()}")
    }

    /**
     * 清理所有过期的Mutex（触发Caffeine的清理操作）
     */
    fun cleanupExpiredMutexes() {
        val sizeBefore = getMutexCount()
        orderMutexCache.cleanUp()
        val sizeAfter = getMutexCount()
        logger.info("清理过期Mutex完成，清理前：$sizeBefore，清理后：$sizeAfter，清理数量：${sizeBefore - sizeAfter}")
    }

    /**
     * 获取当前缓存中Mutex的数量（用于监控）
     */
    fun getMutexCount(): Long = orderMutexCache.estimatedSize()

    /**
     * 获取缓存统计信息（用于监控和调试）
     */
    fun getCacheStats(): String {
        val stats = orderMutexCache.stats()
        return "CacheStats{" +
                "hitCount=${stats.hitCount()}, " +
                "missCount=${stats.missCount()}, " +
                "hitRate=${String.format("%.2f%%", stats.hitRate() * 100)}, " +
                "evictionCount=${stats.evictionCount()}, " +
                "size=${orderMutexCache.estimatedSize()}" +
                "}"
    }
} 
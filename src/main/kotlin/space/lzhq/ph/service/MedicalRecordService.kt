package space.lzhq.ph.service

import org.dromara.hutool.http.webservice.SoapClient
import org.dromara.hutool.core.cache.CacheUtil
import org.dromara.hutool.core.data.id.IdUtil
import org.dromara.hutool.core.xml.XPathUtil
import org.dromara.hutool.core.xml.XmlUtil
import org.dromara.hutool.log.LogFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.util.Assert

@Component
class MedicalRecordService(
    @Value("\${mhd.serviceUrl}")
    private val serviceUrl: String
) {

    private val log = LogFactory.getLog(MedicalRecordService::class.java)

    /**
     * 缓存 10 分钟
     */
    private val cache = CacheUtil.newTimedCache<String, List<PmrInformation>>(10 * 60 * 1000)

    private val visitRecordCache = CacheUtil.newTimedCache<String, List<Map<String, String>>>(10 * 60 * 1000)

    fun downloadPdf(inPatientNo: String): List<PmrInformation> {
        val traceId = IdUtil.fastSimpleUUID()

        // 发起请求
        val client = SoapClient.of(serviceUrl)
            .setMethod("tem:PmrMedicalRecordPDF", "http://tempuri.org/")
            .setParam("inPatientNo", inPatientNo)
        log.debug("Request#$traceId: url=$serviceUrl, body=${client.getMsgStr(false)}")
        val responseString = client.send().bodyText
        log.debug("Response#$traceId: $responseString")

        // 解析并校验
        val document = XmlUtil.readXml(responseString)
        val pmrInfomations = XPathUtil.getNodeByXPath("//PmrInfomations", document)
        Assert.isTrue(pmrInfomations.hasChildNodes(), "该住院号查询不到病例内容")

        // 数据转换
        val pmrInfomationMap: Map<String, Any> = XmlUtil.xmlToMap(pmrInfomations)
        val infomations = PmrInformation(
            pmrInfomationMap["RecordType"]?.toString() ?: "",
            pmrInfomationMap["RecordName"]?.toString() ?: "",
            pmrInfomationMap["RecordContent"]?.toString() ?: ""
        )
        return listOf(infomations)
    }

    fun downloadWithCache(inPatientNo: String): List<PmrInformation> {
        return cache.get(inPatientNo) { downloadPdf(inPatientNo) }
    }

    fun queryInpatientVisitRecord(patientNo: String, patientName: String, idenNo: String): List<Map<String, String>> {
        val traceId = IdUtil.fastSimpleUUID()

        // 发起请求
        val client = SoapClient.of(serviceUrl)
            .setMethod("tem:InpatientVisitRecord", "http://tempuri.org/")
            .setParam("patientNo", patientNo)
            .setParam("patientName", patientName)
            .setParam("idenNo", idenNo)
        log.debug("Request#$traceId: url=$serviceUrl, body=${client.getMsgStr(false)}")
        val responseString = client.send().bodyText
        log.debug("Response#$traceId: $responseString")

        // 解析并校验
        val document = XmlUtil.readXml(responseString)
        val inpatientInfomations = XPathUtil.getNodeByXPath("//InpatientInfomations", document)
        Assert.isTrue(inpatientInfomations.hasChildNodes(), "查询不到病例内容")

        // 数据转换
        @Suppress("UNCHECKED_CAST")
        val inpatientInformationMap: Map<String, Map<String, String>> =
            XmlUtil.xmlToMap(inpatientInfomations) as Map<String, Map<String, String>>
        return inpatientInformationMap.entries.map { it.value }
    }

    fun queryInpatientVisitRecordWithCache(
        patientNo: String,
        patientName: String,
        idenNo: String
    ): List<Map<String, String>> {
        return visitRecordCache.get(patientNo) { queryInpatientVisitRecord(patientNo, patientName, idenNo) }
    }

}

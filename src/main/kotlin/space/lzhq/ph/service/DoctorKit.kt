package space.lzhq.ph.service

import com.ruoyi.common.utils.spring.SpringUtils
import org.mospital.witontek.DoctorResponse
import org.mospital.witontek.WitontekService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import space.lzhq.ph.common.PinyinKit
import space.lzhq.ph.common.Values
import space.lzhq.ph.domain.Doctor

object DoctorKit {
    private val log: Logger = LoggerFactory.getLogger(DepartmentKit::class.java)

    fun sync() {
        val doctorService: IDoctorService = SpringUtils.getBean(IDoctorService::class.java)
        try {
            val doctorResp: DoctorResponse = WitontekService.me.getDoctors()
            if (!doctorResp.isOk()) {
                return
            }

            doctorService.truncateTable()
            val oldDoctorMap = doctorService.selectDoctorAll().associateBy { it.id }
            val newDoctors = mutableListOf<Doctor>()
            val newDoctorIds = mutableSetOf<String>()
            val hisDoctors = doctorResp.doctors
            hisDoctors.forEach {
                if (newDoctorIds.contains(it.code)) {
                    return@forEach
                }

                var newDoctor = oldDoctorMap[it.code]
                if (newDoctor != null) {
                    newDoctor.name = it.name
                    newDoctor.simplePinyin = PinyinKit.getSimplePinyin(it.name, "", true)
                    newDoctor.fullPinyin = PinyinKit.getFullPinyin(it.name, "", true)
                    newDoctor.departmentId = it.departmentCode
                    newDoctor.intro = it.summary
                    newDoctor.expertise = it.specialty
                    newDoctor.title = it.jobTitle
                    newDoctor.position = it.position
                } else {
                    newDoctor = Doctor()
                    newDoctor.id = it.code
                    newDoctor.name = it.name
                    newDoctor.simplePinyin = PinyinKit.getSimplePinyin(it.name, "", true)
                    newDoctor.fullPinyin = PinyinKit.getFullPinyin(it.name, "", true)
                    newDoctor.departmentId = it.departmentCode
                    newDoctor.intro = it.summary
                    newDoctor.expertise = it.specialty
                    newDoctor.title = it.jobTitle
                    newDoctor.position = it.position
                    newDoctor.keyword = ""
                    newDoctor.sortNo = 0
                    newDoctor.sex = 2
                    newDoctor.regType = ""
                    newDoctor.regFee = 0.0
                }
                newDoctors.add(newDoctor)
                newDoctorIds.add(it.code)
            }

            newDoctors.addAll(oldDoctorMap.values.filter { !newDoctorIds.contains(it.id) })
            doctorService.insertDoctors(newDoctors)
            buildDoctorsCache()
        } catch (e: Exception) {
            log.debug("同步医生出错", e)
        }
    }

    fun buildDoctorsCache() {
        val doctorService: IDoctorService = SpringUtils.getBean(IDoctorService::class.java)
        val doctors = doctorService.selectDoctorAll()
        Values.CACHED_DOCTORS = doctors.associateBy { it.id }
    }
}
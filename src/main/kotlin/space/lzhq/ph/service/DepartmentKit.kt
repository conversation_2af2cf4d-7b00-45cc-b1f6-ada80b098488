package space.lzhq.ph.service

import com.ruoyi.common.utils.spring.SpringUtils
import org.mospital.witontek.DepartmentResponse
import org.mospital.witontek.WitontekService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import space.lzhq.ph.common.PinyinKit
import space.lzhq.ph.common.Values
import space.lzhq.ph.domain.Department
import java.text.Collator
import java.util.*

object DepartmentKit {

    private val log: Logger = LoggerFactory.getLogger(DepartmentKit::class.java)

    fun getDepartmentsFromHis(): List<Department> {
        val departmentResponse: DepartmentResponse = WitontekService.me.getDepartments()
        if (!departmentResponse.isOk()) {
            return emptyList()
        }

        val departments: MutableMap<String, Department> = mutableMapOf()
        departmentResponse.departments.filter { it.isValid() }.forEach {
            val department = Department().apply {
                this.id = it.code
                this.name = it.name
                this.type = ""
                this.branch = ""
                this.parentId = ""
                this.parentName = ""
                this.intro = it.summary
                this.address = it.address
                this.logo = ""
                this.telephone = ""
                this.keyword = ""
                this.sortNo = 0
                this.simplePinyin = PinyinKit.getSimplePinyin(it.name, "", true)
                this.fullPinyin = PinyinKit.getFullPinyin(it.name, "", true)
            }
            departments[department.id] = department
        }

        return departments.values.toList()
    }

    fun sync() {
        val departmentService: IDepartmentService = SpringUtils.getBean(IDepartmentService::class.java)
        try {
            val departments = getDepartmentsFromHis()
            if (departments.isEmpty()) {
                return
            }

            departmentService.truncateTable()
            departmentService.insertDepartments(departments)
            Values.INDEXED_DEPARTMENTS = indexDepartments(departmentService.selectDepartmentAll())
        } catch (e: Exception) {
            log.debug("同步科室出错", e);
        }
    }

    fun indexDepartments(departments: List<Department>): List<Map<String, Any>> {
        return departments.groupBy {
            it.simplePinyin.first().uppercase()
        }.map {
            mapOf(
                "index" to it.key,
                "departments" to it.value.sortedWith { d1: Department, d2: Department ->
                    Collator.getInstance(Locale.SIMPLIFIED_CHINESE).compare(d1.name, d2.name)
                }
            )
        }
            .sortedWith { o1: Map<String, Any>, o2: Map<String, Any> -> (o1["index"] as String).compareTo(o2["index"] as String) }
    }

}

package space.lzhq.ph.service

import space.lzhq.ph.domain.Patient
import space.lzhq.ph.dto.FeeCalculationResult
import space.lzhq.ph.dto.IntelligentPayResult
import space.lzhq.ph.dto.PaymentResult
import space.lzhq.ph.dto.WxPaymentResult
import java.math.BigDecimal

/**
 * 智能门诊缴费服务
 */
interface IIntelligentOutpatientPayService {
    
    /**
     * 智能缴费处理
     * @param patient 患者信息
     * @param feeNos 费用单号集合
     * @return 处理结果
     */
    fun processIntelligentPayment(patient: Patient, feeNos: List<String>): IntelligentPayResult
    
    /**
     * 验证并计算费用
     */
    fun validateAndCalculateFees(patient: Patient, feeNos: List<String>): FeeCalculationResult
    
    /**
     * 查询就诊卡余额
     */
    fun queryCardBalance(patient: Patient): BigDecimal
    
    /**
     * 直接从卡余额缴费
     */
    fun payDirectlyFromCard(patient: Patient, feeDetails: FeeCalculationResult): PaymentResult
    
    /**
     * 创建充值订单
     */
    fun createRechargeOrder(patient: Patient, feeDetails: FeeCalculationResult, shortfall: BigDecimal): WxPaymentResult
} 
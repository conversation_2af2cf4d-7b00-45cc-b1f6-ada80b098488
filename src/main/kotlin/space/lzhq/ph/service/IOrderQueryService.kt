package space.lzhq.ph.service

import space.lzhq.ph.domain.OrderQueryOptions
import space.lzhq.ph.domain.OrderQueryResult
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.WxPayment

/**
 * 订单查询服务接口
 * 用于统一处理订单状态查询和更新逻辑
 */
interface IOrderQueryService {
    
    /**
     * 查询并更新订单状态
     * 
     * @param payment 支付记录
     * @param openid 用户openid，如果为null则使用payment中的openid
     * @param options 查询选项
     * @return 查询结果
     */
    suspend fun queryAndUpdateOrder(
        payment: WxPayment,
        openid: String? = null,
        options: Set<OrderQueryOptions> = emptySet()
    ): OrderQueryResult
    
    /**
     * 查询并更新订单状态（带充值）
     * 
     * @param payment 支付记录
     * @param openid 用户openid，如果为null则使用payment中的openid
     * @return 查询结果
     */
    suspend fun queryAndUpdateOrderWithRecharge(
        payment: WxPayment,
        openid: String? = null
    ): OrderQueryResult {
        return queryAndUpdateOrder(payment, openid, setOf(OrderQueryOptions.WITH_RECHARGE))
    }
    
    /**
     * 安全模式查询并更新订单状态（捕获异常）
     * 
     * @param payment 支付记录
     * @param openid 用户openid，如果为null则使用payment中的openid
     * @return 查询结果
     */
    suspend fun queryAndUpdateOrderSafely(
        payment: WxPayment,
        openid: String? = null
    ): OrderQueryResult {
        return queryAndUpdateOrder(payment, openid, setOf(OrderQueryOptions.SAFE_MODE))
    }
    
    /**
     * 智能缴费模式下的充值确认
     * 只处理订单查询和充值，不进行门诊缴费
     * 
     * @param outOrderNo 订单号
     * @param patient 患者信息
     * @return 充值结果
     */
    suspend fun confirmPaymentAndRecharge(
        outOrderNo: String,
        patient: Patient
    ): OrderQueryResult
}

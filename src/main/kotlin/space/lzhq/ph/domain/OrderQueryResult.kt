package space.lzhq.ph.domain

import org.mospital.dongruan.pay.UnifiedPayOrderQueryResult

/**
 * 订单查询结果 - 密封类层次结构
 * 使用类型安全的方式表示成功和失败状态，避免非法状态组合
 */
sealed class OrderQueryResult {
    
    /**
     * 查询成功结果
     */
    data class Success(
        /**
         * 结果消息
         */
        val message: String,
        
        /**
         * 更新后的支付记录（非空）
         */
        val updatedPayment: WxPayment,
        
        /**
         * 原始查询结果（可选）
         */
        val queryResult: UnifiedPayOrderQueryResult?
    ) : OrderQueryResult()
    
    /**
     * 查询失败结果
     */
    data class Failure(
        /**
         * 失败消息
         */
        val message: String
    ) : OrderQueryResult()
}

/**
 * 订单查询选项
 */
enum class OrderQueryOptions {
    /**
     * 查询后调用充值
     */
    WITH_RECHARGE,
    
    /**
     * 异常安全模式（捕获异常并记录日志）
     */
    SAFE_MODE,
    
    /**
     * 跳过状态检查（强制查询）
     */
    SKIP_STATUS_CHECK
}

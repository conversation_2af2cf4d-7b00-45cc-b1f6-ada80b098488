package space.lzhq.ph.domain

import org.mospital.witontek.Response

/**
 * 充值操作结果 - 密封类层次结构
 * 使用类型安全的方式表示不同的充值结果状态，避免非法状态组合
 * 
 * <AUTHOR>
 * @date 2024-12-21
 */
sealed class RechargeResult {
    
    /**
     * 充值成功结果
     */
    data class Success(
        /**
         * 结果消息
         */
        val message: String,
        
        /**
         * 更新后的支付记录（非空）
         */
        val updatedPayment: WxPayment,
        
        /**
         * HIS系统响应（可选）
         */
        val hisResponse: Response? = null
    ) : RechargeResult()
    
    /**
     * 充值失败结果
     */
    data class Failure(
        /**
         * 失败消息
         */
        val message: String,
        
        /**
         * HIS系统响应（用于错误分析）
         */
        val hisResponse: Response? = null
    ) : RechargeResult()
    
    /**
     * 跳过充值结果（无需操作）
     */
         data class Skipped(
         /**
          * 跳过原因
          */
         val message: String,
         
         /**
          * 当前支付记录
          */
         val currentPayment: WxPayment
     ) : RechargeResult()
}

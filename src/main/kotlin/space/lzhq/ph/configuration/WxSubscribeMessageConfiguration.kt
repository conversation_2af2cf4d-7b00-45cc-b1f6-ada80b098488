package space.lzhq.ph.configuration

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@ConfigurationProperties(prefix = "wx.subscribe-message")
@Configuration
open class WxSubscribeMessageConfiguration {
    // 小程序
    var ma: MA = MA()

    // 公众号
    var mp: MP = MP()

    class MA {
        lateinit var hospitalizedDayExpandId: String
        lateinit var hospitalizedDayExpandPage: String

        lateinit var hospitalizedArrearsId: String
        lateinit var hospitalizedArrearsPage: String
    }

    class MP {
        lateinit var hospitalizedDayExpandId: String
        lateinit var hospitalizedDayExpandPage: String

        lateinit var hospitalizedArrearsId: String
        lateinit var hospitalizedArrearsPage: String
    }

}
package space.lzhq.ph.configuration

import com.ruoyi.framework.config.ShiroConfig
import org.springframework.context.annotation.Configuration


@Configuration
class CustomShiroConfig : ShiroConfig() {
    override fun loadCustomFilterChainDefinitions(filterChainDefinitionMap: java.util.LinkedHashMap<String, String>) {
        filterChainDefinitionMap["/**/*.ico"] = "xFrameOptions,anon"
        filterChainDefinitionMap["/login"] = "xFrameOptions,anon,captchaValidate"
        filterChainDefinitionMap["/open/**"] = "xFrameOptions,anon"
        filterChainDefinitionMap["/api/**"] = "xFrameOptions,anon"
        filterChainDefinitionMap["/ws/**"] = "xFrameOptions,anon"
    }
}
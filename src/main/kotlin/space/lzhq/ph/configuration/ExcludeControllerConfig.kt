package space.lzhq.ph.configuration

import org.springframework.beans.factory.config.ConfigurableListableBeanFactory
import org.springframework.beans.factory.support.BeanDefinitionRegistry
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ExcludeControllerConfig {

    @Bean
    fun beanDefinitionRegistryPostProcessor(): BeanDefinitionRegistryPostProcessor =
        object : BeanDefinitionRegistryPostProcessor {
            override fun postProcessBeanFactory(beanFactory: ConfigurableListableBeanFactory) {
                // Do nothing
            }

            override fun postProcessBeanDefinitionRegistry(registry: BeanDefinitionRegistry) {
                // remove sysLoginController bean
                if (registry.containsBeanDefinition("sysLoginController")) {
                    registry.removeBeanDefinition("sysLoginController")
                }

                // remove sysIndexController bean
                if (registry.containsBeanDefinition("sysIndexController")) {
                    registry.removeBeanDefinition("sysIndexController")
                }
            }
        }

}
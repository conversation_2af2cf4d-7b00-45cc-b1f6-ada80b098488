package space.lzhq.ph.common

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUtil
import cn.hutool.core.math.Money
import cn.hutool.core.util.RandomUtil
import cn.hutool.core.util.StrUtil
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.atomic.AtomicInteger

object PaymentKit {
    // 微信支付统一下单接口的商户订单号，最大长度为32，参见：https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_1
    // 建设银行聚合支付统一下单接口的商户订单号，最大长度为30，参见：cn.doit.zhangyi.pay.ccb.UnifiedOrderRequest
    fun newOrderId(type: ServiceType): String =
            type.name + DateUtil.format(Date(), DatePattern.PURE_DATETIME_PATTERN) + RandomUtil.randomNumbers(4)

    // 微信支付申请退款接口的商户退款单号，最大长度为64，参见：https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_4
    fun newRefundNo(type: ServiceType, receiptNo: String): String =
            type.name +
                    DateUtil.format(Date(), DatePattern.PURE_DATE_PATTERN) +
                    RandomUtil.randomNumbers(2) +
                    StrUtil.padAfter(receiptNo, 8, "0")

    fun fenToYuan(fen: Long): BigDecimal {
        return Money(BigDecimal.valueOf(fen / 100.0)).amount
    }

    fun yuanToFen(yuan: BigDecimal): Long {
        return Money(yuan.setScale(2)).cent
    }

    fun yuanToFen(yuan: Double): Long {
        return yuanToFen(yuan.toBigDecimal())
    }

    fun diffInFen(yuan1: BigDecimal, yuan2: BigDecimal): Long {
        return yuanToFen(yuan1) - yuanToFen(yuan2)
    }

    /**
     * HIS充值退款服务连续失败的次数
     */
    val CONTINUOUS_HIS_FAILURES: AtomicInteger = AtomicInteger(0)

}
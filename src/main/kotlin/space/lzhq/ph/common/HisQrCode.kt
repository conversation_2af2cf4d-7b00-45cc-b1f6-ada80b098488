package space.lzhq.ph.common

import org.mospital.common.PatientDataToken

/**
 * Utility class for generating QR codes using the Hospital Information System (HIS).
 *
 * The QR code is generated using the patient card and the expiration timestamp.
 *
 * @param patientCard The patient card number.
 * @param expiredTimestamp The expiration timestamp for the QR code. Defaults to 5 minutes from now. This value is in milliseconds.
 */
data class HisQrCode(
    val patientCard: String,
    val expiredTimestamp: Long = System.currentTimeMillis() + 300000
) {
    /**
     * Generates a token for the current QR code instance.
     *
     * The token is generated using the patient card as the patient ID
     * and the expiration timestamp as the data ID.
     *
     * @return A string representation of the generated token.
     */
    fun getToken(): String {
        val patientDataToken = PatientDataToken(
            patientId = patientCard,
            dataId = expiredTimestamp.toString()
        )
        return patientDataToken.generateToken()
    }

    /**
     * Checks if the QR code has expired.
     *
     * The QR code is considered expired if the current time is greater
     * than the expiration timestamp.
     *
     * @return true if the QR code has expired, false otherwise.
     */
    fun isExpired(): <PERSON><PERSON>an {
        return expiredTimestamp < System.currentTimeMillis()
    }

    companion object {
        /**
         * Parses a token string and returns the corresponding [HisQrCode] instance.
         *
         * The token string is expected to be a valid [PatientDataToken] that can be parsed.
         *
         * @param token The token string to parse.
         * @return The parsed [HisQrCode] instance, or throws an [IllegalArgumentException] if the token is invalid.
         */
        fun parse(token: String): HisQrCode {
            val patientDataToken = PatientDataToken.parse(token) ?: throw IllegalArgumentException("Invalid token")
            return HisQrCode(
                patientCard = patientDataToken.patientId,
                expiredTimestamp = patientDataToken.dataId.toLong()
            )
        }
    }
}

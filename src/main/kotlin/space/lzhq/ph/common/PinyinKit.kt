package space.lzhq.ph.common

import cn.hutool.extra.pinyin.PinyinUtil

object PinyinKit {

    private val NOT_LETTER = "[^a-zA-Z]".toRegex()

    fun getSimplePinyin(str: String?, separator: String?, onlyPinyin: Boolean): String {
        val result = PinyinUtil.getFirstLetter(str, separator)
        return if (onlyPinyin) {
            result.replace(NOT_LETTER, "")
        } else {
            result
        }
    }

    fun getFullPinyin(str: String?, separator: String?, onlyPinyin: Boolean): String {
        val result = PinyinUtil.getPinyin(str, separator)
        return if (onlyPinyin) {
            result.replace(NOT_LETTER, "")
        } else {
            result
        }
    }
}
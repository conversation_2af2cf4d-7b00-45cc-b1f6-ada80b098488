package space.lzhq.ph.webservice

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage
import https.xjzybyy_xjyqtl_cn.ws.hospitalized.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import org.mospital.witontek.InpatientInfoRequest
import org.mospital.witontek.WitontekService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.ws.server.endpoint.annotation.Endpoint
import org.springframework.ws.server.endpoint.annotation.PayloadRoot
import org.springframework.ws.server.endpoint.annotation.RequestPayload
import org.springframework.ws.server.endpoint.annotation.ResponsePayload
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IPatientService


const val NAMESPACE_URI = "https://xjzybyy.xjyqtl.cn/ws/hospitalized"

@Endpoint
class HospitalizedWsEndpoint {

    protected val logger: Logger = LoggerFactory.getLogger(this.javaClass)

    @Autowired
    private lateinit var wxSubscribeMessageConfiguration: WxSubscribeMessageConfiguration

    @Autowired
    private lateinit var patientService: IPatientService


    // 推送住院一日清到微信
    @PayloadRoot(namespace = NAMESPACE_URI, localPart = "PushHospitalizedDayExpenseRequest")
    @ResponsePayload
    fun pushDayExpenseToWeChat(@RequestPayload pushHospitalizedDayExpenseRequest: PushHospitalizedDayExpenseRequest?): PushHospitalizedDayExpenseResponse {

        val patientList = patientService.selectPatientListByIdCard(pushHospitalizedDayExpenseRequest?.data?.idCard)
        if (patientList.isNullOrEmpty()) {
            logger.warn("未查询到患者信息")
            return PushHospitalizedDayExpenseResponse().apply {
                result = "FAIL"
                message = "未查询到患者信息" }
        }

        if (pushHospitalizedDayExpenseRequest?.data?.serialNumber.isNullOrBlank()) {
            logger.warn("HIS返回的住院号为空。patientCard: ${pushHospitalizedDayExpenseRequest?.data?.patientCard}")
            return PushHospitalizedDayExpenseResponse().apply {
                result = "FAIL"
                message = "住院号为空"
            }
        }

        // 可能同时用了微信和公众号，这里默认取第一个
        val patient = patientList[0]

        CoroutineScope(Dispatchers.IO).launch {
            if (patient.clientType == ClientType.WEIXIN_MA.code) {
                WeixinExt.sendSubscribeMessage(
                    wxSubscribeMessageConfiguration.ma.hospitalizedDayExpandId,
                    wxSubscribeMessageConfiguration.ma.hospitalizedDayExpandPage,
                    listOf(
                        WxMaSubscribeMessage.MsgData(
                            "thing1",
                            "您好，${pushHospitalizedDayExpenseRequest?.data?.feeDate}的住院清单已生成"
                        ),
                        WxMaSubscribeMessage.MsgData(
                            "character_string2",
                            pushHospitalizedDayExpenseRequest?.data?.serialNumber
                        ),
                        WxMaSubscribeMessage.MsgData("phrase3", pushHospitalizedDayExpenseRequest?.data?.realName),
                        WxMaSubscribeMessage.MsgData("date4", pushHospitalizedDayExpenseRequest?.data?.feeDate),
                    ),
                    patient.openId,
                    patient.zhuyuanNo
                )
            } else {
                // TODO 公众号的推送还需要模板ID
            }

        }


        return PushHospitalizedDayExpenseResponse().apply {
            result = "SUCCESS"
            message = "推送成功"
        }
    }

    // 推送待缴费住院费用到微信
    @PayloadRoot(namespace = NAMESPACE_URI, localPart = "PushHospitalizedArrearsRequest")
    @ResponsePayload
    fun pushArrearsToWeChat(@RequestPayload pushHospitalizedArrearsRequest: PushHospitalizedArrearsRequest): PushHospitalizedArrearsResponse {
        val arrearsData: ArrearsData = pushHospitalizedArrearsRequest.data ?: return PushHospitalizedArrearsResponse().apply {
            result = "FAIL"
            message = "参数 data 不能为空"
        }
        val patientList = patientService.selectPatientListByIdCard(arrearsData.idCard)
        if (patientList.isNullOrEmpty()) {
            return PushHospitalizedArrearsResponse().apply {
                result = "FAIL"
                message = "未查询到患者信息"
            }
        }

        if (pushHospitalizedArrearsRequest.data?.serialNumber.isNullOrBlank()) {
            return PushHospitalizedArrearsResponse().apply {
                result = "FAIL"
                message = "住院号为空"
            }
        }

        // 可能同时用了微信和公众号，这里默认取第一个
        val patient = patientList[0]

        val resp = WitontekService.me.queryInpatient(InpatientInfoRequest().apply {
            this.serialNumber = arrearsData.serialNumber
        })

        if (!resp.isOk()) {
            return PushHospitalizedArrearsResponse().apply {
                result = "FAIL"
                message = "查询住院信息失败"
            }
        }

        CoroutineScope(Dispatchers.IO).launch {
            if (patient.clientType == ClientType.WEIXIN_MA.code) {
                WeixinExt.sendSubscribeMessage(
                    wxSubscribeMessageConfiguration.ma.hospitalizedArrearsId,
                    wxSubscribeMessageConfiguration.ma.hospitalizedArrearsPage,
                    listOf(
                        WxMaSubscribeMessage.MsgData("phone_number3", pushHospitalizedArrearsRequest.data?.serialNumber),
                        WxMaSubscribeMessage.MsgData("thing4", resp.departmentCode),
                        WxMaSubscribeMessage.MsgData("date5", resp.inHospitalDate),
                        WxMaSubscribeMessage.MsgData("amount6", pushHospitalizedArrearsRequest.data?.totalAmount),
                        WxMaSubscribeMessage.MsgData("amount8", pushHospitalizedArrearsRequest.data?.remainAmount),
                    ),
                    patient.openId,
                    patient.zhuyuanNo
                )
            } else {
                // TODO 公众号的推送还需要模板ID
            }
        }


        return PushHospitalizedArrearsResponse().apply {
            result = "SUCCESS"
            message = "推送成功"
        }
    }

}
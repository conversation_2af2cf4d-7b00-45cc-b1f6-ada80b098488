package space.lzhq.ph.ext

import cn.binarywang.wx.miniapp.api.WxMaService
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage
import cn.hutool.core.date.DatePattern
import cn.hutool.core.io.FileUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.http.HttpUtil
import com.alibaba.fastjson.JSON
import com.github.binarywang.wxpay.bean.request.WxInsurancePayDownloadBillRequest
import com.github.binarywang.wxpay.bean.request.WxInsurancePayRefundRequest
import com.github.binarywang.wxpay.bean.request.WxPayDownloadBillRequest
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult
import com.github.binarywang.wxpay.bean.result.WxPayBillInfo
import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import com.github.binarywang.wxpay.config.WxPayConfig
import com.github.binarywang.wxpay.service.WxInsurancePayService
import com.github.binarywang.wxpay.service.WxPayService
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl
import com.ruoyi.common.utils.spring.SpringUtils
import me.chanjar.weixin.common.bean.ocr.WxOcrBankCardResult
import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult
import me.chanjar.weixin.common.service.WxOcrService
import me.chanjar.weixin.mp.api.WxMpService
import org.mospital.dongruan.pay.DongruanPayService
import org.mospital.dongruan.pay.RefundForm
import org.mospital.wecity.mip.ClientType
import org.mospital.wecity.mip.WeixinMipSetting
import space.lzhq.ph.common.Constants
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.common.StringKit
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration
import space.lzhq.ph.domain.MipWxOrder
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.domain.WxRefund
import space.lzhq.ph.domain.WxSubscribeMessage
import space.lzhq.ph.service.IMipWxOrderService
import space.lzhq.ph.service.IWxPaymentService
import space.lzhq.ph.service.IWxRefundService
import space.lzhq.ph.service.IWxSubscribeMessageService
import java.io.File
import java.math.BigDecimal
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*

object WeixinExt {

    private fun downloadAndSaveMipBill(billDate: LocalDate): File? {
        return try {
            val wxInsurancePayDownloadBillResult = getWxMPInsurancePayService().downloadBill(
                WxInsurancePayDownloadBillRequest(
                    billDate = billDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    billType = "ALL"
                )
            )
            val path = buildMipBillPath(billDate)
            HttpUtil.downloadFile(wxInsurancePayDownloadBillResult.downloadUrl, path)
            File(path)
        } catch (e: Exception) {
            null
        }
    }

    fun getOrDownloadMipBillFile(billDate: LocalDate): File {
        val path: String = buildMipBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        downloadAndSaveMipBill(billDate)
        return File(path)
    }

    /**
     * 发送订阅消息
     */
    fun sendSubscribeMessage(
        messageId: String?,
        messagePage: String?,
        messageDataList: List<WxMaSubscribeMessage.MsgData>,
        openid: String,
        companionId: String,
    ) {
        if (messageId.isNullOrBlank()) {
            return
        }

        val wxSubscribeMessageConfiguration = SpringUtils.getBean(WxSubscribeMessageConfiguration::class.java)
        val type: String = when (messageId) {
            wxSubscribeMessageConfiguration.ma.hospitalizedDayExpandId, wxSubscribeMessageConfiguration.mp.hospitalizedDayExpandId -> "HospitalizedDayExpand"
            wxSubscribeMessageConfiguration.ma.hospitalizedArrearsId, wxSubscribeMessageConfiguration.mp.hospitalizedArrearsId -> "HospitalizedArrears"
            else -> "Unknown"
        }

        val wxSubscribeMessageService = SpringUtils.getBean(IWxSubscribeMessageService::class.java)
        val wxSubscribeMessages = wxSubscribeMessageService.selectWxSubscribeMessageList(WxSubscribeMessage().apply {
            this.templateId = messageId
            this.openid = openid
            this.type = type
            this.companionId = companionId
            this.sendStatus = 0
        })
        if (!wxSubscribeMessages.isNullOrEmpty()) {
            return
        }

        val subscribeMessage: WxMaSubscribeMessage = WxMaSubscribeMessage.builder().apply {
            if (!messagePage.isNullOrBlank()) {
                this.page(messagePage)
            }

            this.templateId(messageId)
            this.toUser(openid)
            this.data(messageDataList)
        }.build()
        val now = Date()
        val data = JSON.toJSONString(subscribeMessage.data)
        try {
            val wxMaService = SpringUtils.getBean(WxMaService::class.java)
            wxMaService.msgService.sendSubscribeMsg(subscribeMessage)
            val wxSubscribeMessage = WxSubscribeMessage().apply {
                this.templateId = subscribeMessage.templateId
                this.openid = subscribeMessage.toUser
                this.data = data
                this.type = type
                this.companionId = companionId
                this.sendTime = now
                this.sendStatus = 0
                this.error = "OK"
            }

            wxSubscribeMessageService.insertWxSubscribeMessage(wxSubscribeMessage)
        } catch (e: Exception) {
            val wxSubscribeMessage = WxSubscribeMessage().apply {
                this.templateId = subscribeMessage.templateId
                this.openid = subscribeMessage.toUser
                this.data = data
                this.type = type
                this.companionId = companionId
                this.sendTime = now
                this.sendStatus = 1
                this.error = e.message ?: "未知错误"
            }

            wxSubscribeMessageService.insertWxSubscribeMessage(wxSubscribeMessage)
        }
    }

    fun newWxMaSubscribeMessagePhraseData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, StringKit.removeIfNotChinese(value).take(5))
    }

    fun newWxMaSubscribeMessageThingData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, value.take(20))
    }

    fun newWxMaSubscribeMessageNameData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, StringKit.removeIfNotChinese(value).take(10))
    }

    private var wxMaInsurancePayService: WxInsurancePayService? = null
    private var wxMpInsurancePayService: WxInsurancePayService? = null

    @Synchronized
    fun getWxMAInsurancePayService(): WxInsurancePayService {
        if (wxMaInsurancePayService == null) {
            val wxPayService: WxPayService = SpringUtils.getBean(WxPayService::class.java)
            val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
            val weixinMipSetting = WeixinMipSetting.getInstance(ClientType.MA)
            val wxPayConfig: WxPayConfig = WxPayConfig().apply {
                val originalPayConfig: WxPayConfig = wxPayService.config
                this.appId = weixinMipSetting.appId
                this.mchId = originalPayConfig.mchId
                this.mchKey = weixinMipSetting.mipKey
                this.keyPath = originalPayConfig.keyPath
            }
            wxMaInsurancePayService = WxInsurancePayService(wxPayConfig, { refresh -> wxMaService.getAccessToken(refresh) }, wxPayService)
        }
        return wxMaInsurancePayService!!
    }


    @Synchronized
    fun getWxMPInsurancePayService(): WxInsurancePayService {
        if (wxMpInsurancePayService == null) {
            val wxPayService: WxPayService = SpringUtils.getBean(WxPayService::class.java)
            val wxMpService: WxMpService = SpringUtils.getBean(WxMpService::class.java)
            val weixinMipSetting = WeixinMipSetting.getInstance(ClientType.MP)
            val wxPayConfig: WxPayConfig = WxPayConfig().apply {
                val originalPayConfig: WxPayConfig = wxPayService.config
                this.appId = weixinMipSetting.appId
                this.mchId = originalPayConfig.mchId
                this.mchKey = weixinMipSetting.mipKey
                this.keyPath = originalPayConfig.keyPath
            }
            wxMpInsurancePayService = WxInsurancePayService(wxPayConfig, { refresh -> wxMpService.getAccessToken(refresh) }, wxPayService)
        }
        return wxMpInsurancePayService!!
    }

    fun getWxInsurancePayServiceByClientType(clientType: ClientType): WxInsurancePayService = when (clientType) {
        ClientType.MP -> getWxMPInsurancePayService()
        ClientType.MA -> getWxMAInsurancePayService()
    }

    fun refundWeixin(mipWxOrder: MipWxOrder): WxInsurancePayRefundResult {
        check(mipWxOrder.ownPayAmount > BigDecimal.ZERO) { "微信支付金额为0，无需退微信" }
        check(mipWxOrder.hospOutRefundNo.isNullOrBlank()) { "订单已退款" }
        check(!mipWxOrder.medTransactionId.isNullOrBlank()) { "订单未支付" }
        mipWxOrder.hospOutRefundNo = mipWxOrder.hospitalOutTradeNo + "#R"
        val refundRequest: WxInsurancePayRefundRequest = WxInsurancePayRefundRequest().apply {
            this.medTransId = mipWxOrder.medTransactionId
            this.hospOutRefundNo = mipWxOrder.hospOutRefundNo
            this.partRefundType = "CASH_ONLY"
            this.payOrderId = mipWxOrder.payOrderId
            this.refReason = "申请退款"
        }
        val refundResult: WxInsurancePayRefundResult = WeixinExt.getWxInsurancePayServiceByClientType(mipWxOrder.clientType).refund(refundRequest)
        mipWxOrder.medRefundId = refundResult.medRefundId
        mipWxOrder.cashRefundId = refundResult.cashRefundId
        mipWxOrder.insuranceRefundId = refundResult.insuranceRefundId

        val mipWxOrderService: IMipWxOrderService = SpringUtils.getBean(IMipWxOrderService::class.java)
        mipWxOrderService.updateById(mipWxOrder)

        return refundResult
    }

    fun refundWeixin(paymentId: Long, amount: BigDecimal): WxRefund {
        val wxPaymentService: IWxPaymentService = SpringUtils.getBean(IWxPaymentService::class.java)
        val wxRefundService: IWxRefundService = SpringUtils.getBean(IWxRefundService::class.java)

        val payment: WxPayment = wxPaymentService.selectWxPaymentById(paymentId)
            ?: throw IllegalArgumentException("订单不存在")
        val refundAmountCents = PaymentKit.yuanToFen(amount)
        check(refundAmountCents > 0) { "退款金额必须大于0" }
        check(refundAmountCents <= payment.unrefund) { "退款金额超出订单限制" }

        val refund: WxRefund = wxRefundService.createAndSave(payment, refundAmountCents)
        wxPaymentService.updateOnRefund(payment, refundAmountCents)

        // 调用退款接口
        val refundResponse = DongruanPayService.refund(
            form = RefundForm()
                .orderNo(refund.zyPayNo)
                .tradeNo(refund.wxPayNo)
                .refundNo(refund.zyRefundNo)
                .refundAmount(PaymentKit.fenToYuan(refund.amount))
                .refundReason("申请退款")
        )
        if (refundResponse.isOk()) {
            refund.wxRefundNo = refundResponse.data!!.tradeNo
            refund.wxTradeStatus = Constants.REFUND_OK
        } else {
            refund.wxTradeStatus = refundResponse.message
        }
        wxRefundService.updateWxRefund(refund)

        return refund
    }

    fun idCardOcr(file: File): WxOcrIdCardResult {
        val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
        val wxMaOcrService: WxOcrService = wxMaService.ocrService
        return wxMaOcrService.idCard(file)
    }

    fun bankCardOcr(file: File): WxOcrBankCardResult {
        val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
        val wxMaOcrService: WxOcrService = wxMaService.ocrService
        return wxMaOcrService.bankCard(file)
    }

}

private fun buildBillPath(billDate: LocalDate): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_wxbill.csv"

private fun buildMipBillPath(billDate: LocalDate): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_mipbill.csv"

private fun formatWxBillDate(billDate: LocalDate): String =
    billDate.format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN))

private fun buildWxPayDownloadRequest(
    billDate: LocalDate,
    billType: String = "ALL",
    tarType: String? = null,
    deviceInfo: String? = null,
): WxPayDownloadBillRequest {
    val wxPayDownloadBillRequest = WxPayDownloadBillRequest()
    wxPayDownloadBillRequest.billType = billType
    wxPayDownloadBillRequest.billDate = formatWxBillDate(billDate)
    wxPayDownloadBillRequest.tarType = tarType
    wxPayDownloadBillRequest.deviceInfo = deviceInfo
    return wxPayDownloadBillRequest
}

fun WxPayServiceImpl.downloadAndSaveBill(
    billDate: LocalDate,
    billType: String = "ALL",
    tarType: String? = null,
    deviceInfo: String? = null,
): WxPayBillResult? {
    val wxPayDownloadBillRequest = buildWxPayDownloadRequest(billDate, billType, tarType, deviceInfo)
    val responseContent = this.downloadRawBill(wxPayDownloadBillRequest)
    FileUtil.writeUtf8String(responseContent, buildBillPath(billDate))
    return if (StrUtil.isEmpty(responseContent)) {
        null
    } else {
        WxPayBillResult.fromRawBillResultString(responseContent, wxPayDownloadBillRequest.billType)
    }
}

fun WxPayBillInfo.isMenzhen(): Boolean = outTradeNo.startsWith(ServiceType.MZ.name)
fun WxPayBillInfo.isZhuyuan(): Boolean = outTradeNo.startsWith(ServiceType.ZY.name)
fun WxPayBillInfo.isPay(): Boolean = this.refundChannel.isBlank()
fun WxPayBillInfo.isRefund(): Boolean = !this.isPay()
fun WxPayBillResult.calculateTotalPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() }.map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateMenzhenPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() && it.isMenzhen() }
        .map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateZhuyuanPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() && it.isZhuyuan() }
        .map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateTotalRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateMenzhenRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() && it.isMenzhen() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateZhuyuanRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() && it.isZhuyuan() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

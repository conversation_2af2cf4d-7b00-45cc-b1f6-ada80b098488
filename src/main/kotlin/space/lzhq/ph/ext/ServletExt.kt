package space.lzhq.ph.ext

import com.ruoyi.common.utils.spring.SpringUtils
import jakarta.servlet.http.HttpServletRequest
import org.springframework.http.HttpHeaders
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.Values.CLIENT_SESSION
import space.lzhq.ph.common.Values.CLIENT_SESSION_LOADED
import space.lzhq.ph.common.Values.CURRENT_PATIENT
import space.lzhq.ph.common.Values.CURRENT_PSC_CARER
import space.lzhq.ph.common.Values.CURRENT_PSC_NURSE
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.PscCarerSession
import space.lzhq.ph.domain.PscNurseSession
import space.lzhq.ph.domain.Session
import space.lzhq.ph.service.ISessionService

fun HttpServletRequest.getClientSession(): Session? {
    var session: Session? = this.getAttribute(CLIENT_SESSION) as Session?
    val sessionLoaded: Boolean = this.getAttribute(CLIENT_SESSION_LOADED) as Boolean? ?: false
    if (session == null && !sessionLoaded) {
        val sid: String? = this.getHeader(HttpHeaders.AUTHORIZATION)
        if (sid != null) {
            val wxSessionService = SpringUtils.getBean(ISessionService::class.java)
            session = wxSessionService.selectSessionById(sid)
        }

        this.setAttribute(CLIENT_SESSION_LOADED, true)
        this.setAttribute(CLIENT_SESSION, session)
    }

    return session
}

fun HttpServletRequest.getCurrentPatient(): Patient = getAttribute(CURRENT_PATIENT) as Patient
fun HttpServletRequest.getCurrentPscNurse(): PscNurseSession = getAttribute(CURRENT_PSC_NURSE) as PscNurseSession
fun HttpServletRequest.getCurrentPscCarer(): PscCarerSession = getAttribute(CURRENT_PSC_CARER) as PscCarerSession
fun HttpServletRequest.getClientType(): ClientType = getClientSession()!!.clientTypeEnum
fun HttpServletRequest.getMipClientType(): org.mospital.wecity.mip.ClientType = when (getClientType()) {
    space.lzhq.ph.common.ClientType.WEIXIN_MA -> org.mospital.wecity.mip.ClientType.MA
    space.lzhq.ph.common.ClientType.WEIXIN_MP -> org.mospital.wecity.mip.ClientType.MP
    else -> throw RuntimeException("不支持的客户端类型")
}

package space.lzhq.ph.ext

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUtil
import com.alipay.api.response.AlipayTradeQueryResponse
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult
import com.github.binarywang.wxpay.service.WxInsurancePayService
import com.ruoyi.common.utils.spring.SpringUtils
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.mospital.dongruan.pay.UnifiedOrderForm
import org.mospital.dongruan.pay.UnifiedPayStatusEnum
import org.mospital.dongruan.yibao.DongruanYibaoService
import org.mospital.dongruan.yibao.bean.Request
import org.mospital.dongruan.yibao.bean.SettleForm
import org.mospital.dongruan.yibao.bean.SettleResult
import org.mospital.dongruan.yibao.bean.ThirdType
import org.mospital.jackson.JacksonKit
import org.mospital.wecity.mip.ClientType
import org.mospital.wecity.mip.WeixinMipSetting
import org.mospital.witontek.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import space.lzhq.ph.common.Constants
import space.lzhq.ph.domain.*
import space.lzhq.ph.enums.MedicalRecordApplicationStatus
import space.lzhq.ph.service.IMedicalRecordApplicationService
import space.lzhq.ph.service.IMipAlipayOrderService
import space.lzhq.ph.service.IMipWxOrderService
import space.lzhq.ph.service.IWxPaymentService
import java.math.BigDecimal
import java.math.RoundingMode
import java.net.URL
import java.time.Duration
import java.time.LocalDateTime
import java.util.*

object HisExt {

    private val NOT_CHINESE_REGEXP = Regex("[^一-\u9FFF]")

    private val logger: Logger = LoggerFactory.getLogger(HisExt::class.java)

    /**
     * HisExt 内部维护的充值操作锁缓存
     * 用于确保同一订单号的充值操作串行化
     */
    private val rechargeMutexCache: Cache<String, Mutex> = Caffeine.newBuilder()
        .expireAfterWrite(Duration.ofMinutes(15))
        .maximumSize(5_000)
        .build()

    fun matchesChinese(s1: String, s2: String): Boolean {
        return s1.replace(NOT_CHINESE_REGEXP, "") == s2.replace(NOT_CHINESE_REGEXP, "")
    }

    fun getMipEnvByClientType(clientType: ClientType): String = when (clientType) {
        ClientType.MP -> "WeixinGongzhonghao"
        ClientType.MA -> "WeixinXiaochengxu"
    }

    const val ALIPAY_MIP_ENV = "AlipayXiaochengxu"

    /**
     * 充值操作 - 内部使用独立的锁机制
     * 注意：这个方法现在不再依赖外部的 OrderSerializationService
     * 而是使用内部的 rechargeMutexCache 来确保同一订单号的充值操作串行化
     */
    suspend fun recharge(zyPayNo: String): RechargeResult {
        val wxPaymentService: IWxPaymentService = SpringUtils.getBean(IWxPaymentService::class.java)

        // 使用内部的充值锁机制
        val mutex = rechargeMutexCache.get(zyPayNo) { Mutex() }

        return mutex.withLock {
            logger.debug("开始充值操作: $zyPayNo")

            // 重新查询最新的支付记录，防止状态已被其他线程更新
            val currentPayment: WxPayment? = wxPaymentService.selectWxPaymentByZyPayNo(zyPayNo)

            // 检查支付记录是否存在
            if (currentPayment == null) {
                return@withLock RechargeResult.Failure("未找到订单号为 $zyPayNo 的支付记录")
            }

            try {
                when {
                    currentPayment.isMenzhen -> processMenzhenRecharge(currentPayment, wxPaymentService)
                    currentPayment.isZhuyuan -> processZhuyuanRecharge(currentPayment, wxPaymentService)
                    currentPayment.isBingli -> processBingliRecharge(currentPayment, wxPaymentService)
                    else -> RechargeResult.Failure("未知的支付类型：${currentPayment.paymentType}")
                }
            } finally {
                logger.debug("充值操作完成: $zyPayNo")
            }
        }
    }

    /**
     * 处理门诊充值流程
     */
    private fun processMenzhenRecharge(payment: WxPayment, wxPaymentService: IWxPaymentService): RechargeResult {
        // 检查是否为门诊充值
        if (!payment.isMenzhen) {
            return RechargeResult.Skipped("非门诊订单，无需充值", payment)
        }

        // 检查是否已支付
        if (payment.wxTradeStatus != UnifiedPayStatusEnum.SUCCESS.msg) {
            return RechargeResult.Skipped("订单未支付", payment)
        }

        // 检查是否已充值
        if (payment.hisTradeStatus == Constants.RECHARGE_OK && payment.paymentType == WxPayment.PAYMENT_TYPE_INTELLIGENT) {
            return RechargeResult.Skipped("门诊充值已完成", payment)
        }

        // 检查是否已完成缴费
        if (payment.hisTradeStatus == Constants.PAYMENT_OK && payment.paymentType == WxPayment.PAYMENT_TYPE_OLD) {
            return RechargeResult.Skipped("门诊缴费已完成", payment)
        }

        // 检查是否需要充值
        if (payment.hisTradeStatus != Constants.RECHARGE_OK && payment.hisTradeStatus != Constants.PAYMENT_OK) {
            // 发起院内账户充值
            val rechargeResult = WitontekService.me.outpatientRecharge(OutpatientRechargeRequest().apply {
                this.realName = payment.patientName
                this.patientCard = payment.cardNo
                this.amount = BigDecimal(payment.amount).setScale(2, RoundingMode.HALF_DOWN).divide(BigDecimal(100))
                    .toPlainString()
                this.payType = "weChat"
                this.orderNo = payment.zyPayNo
                this.tradeNo = payment.wxPayNo
                this.nonceStr = "witon"
                this.payedTime = DateUtil.toLocalDateTime(payment.createTime)
            }.sign())

            if (!rechargeResult.isOk()) {
                logger.error("院内账户充值失败：#${payment.zyPayNo}, ${rechargeResult.message}")
                return RechargeResult.Failure("院内账户充值失败：${rechargeResult.message}", rechargeResult)
            }

            // 充值成功，更新订单状态
            wxPaymentService.updateHisRechargeStatusOnPay(
                payment,
                rechargeResult.serialNo,
                rechargeResult.invoiceNo
            )
        }

        // 获取当前支付记录用于后续判断
        val currentPayment = wxPaymentService.selectWxPaymentById(payment.id)!!

        // 检查是否需要门诊缴费（旧版本支付类型）
        if (currentPayment.hisTradeStatus == Constants.RECHARGE_OK && currentPayment.paymentType == WxPayment.PAYMENT_TYPE_OLD) {
            // 发起门诊缴费
            val outpatientPayResult = WitontekService.me.outpatientPay(
                OutpatientPayRequest().apply {
                    this.prescriptionNo = currentPayment.prescriptionNos
                    this.realName = currentPayment.patientName
                    this.patientCard = currentPayment.cardNo
                    this.payType = "eCardPay"
                    this.tradeNo = UnifiedOrderForm.newOrderNo()
                    this.feeType = "门诊缴费"
                    this.orderFee =
                        BigDecimal(currentPayment.amount).setScale(2, RoundingMode.HALF_DOWN).divide(BigDecimal(100))
                            .toPlainString()
                    this.payedTime = DateUtil.toLocalDateTime(currentPayment.createTime)
                })

            if (!outpatientPayResult.isOk()) {
                logger.error("门诊缴费失败：#${currentPayment.zyPayNo}, ${outpatientPayResult.message}")
                return RechargeResult.Failure("门诊缴费失败：${outpatientPayResult.message}", outpatientPayResult)
            }

            // 缴费成功，更新订单状态
            wxPaymentService.updateHisPayStatusOnPay(currentPayment)
            val finalPayment = wxPaymentService.selectWxPaymentById(payment.id)!!
            logger.info("门诊缴费成功：#${currentPayment.zyPayNo}")

            return RechargeResult.Success("门诊充值和缴费完成", finalPayment, outpatientPayResult)
        }

        return RechargeResult.Success("门诊充值完成", currentPayment)
    }

    /**
     * 处理住院充值流程
     */
    private fun processZhuyuanRecharge(payment: WxPayment, wxPaymentService: IWxPaymentService): RechargeResult {
        // 检查是否为住院充值
        if (!payment.isZhuyuan) {
            return RechargeResult.Skipped("非住院订单，无需充值", payment)
        }

        // 检查是否已支付
        if (payment.wxTradeStatus != UnifiedPayStatusEnum.SUCCESS.msg) {
            return RechargeResult.Skipped("订单未支付", payment)
        }

        // 检查是否已充值
        if (payment.hisTradeStatus == Constants.RECHARGE_OK) {
            return RechargeResult.Skipped("住院充值已完成", payment)
        }

        val zhuyuanRechargeResponse = WitontekService.me.prePayExpense(
            PrePayExpenseRequest().apply {
                this.realName = payment.patientName
                this.patientCard = payment.zhuyuanNo
                this.payType = "weChat"
                this.tradeNo = payment.wxPayNo
                this.orderNo = payment.zyPayNo
                this.prepayAmt = BigDecimal(payment.amount).setScale(2, RoundingMode.HALF_DOWN).divide(BigDecimal(100))
                    .toPlainString()
                this.payedTime = DateUtil.parseLocalDateTime(DateUtil.formatDateTime(payment.createTime))
            })

        if (!zhuyuanRechargeResponse.isOk()) {
            logger.error("住院充值失败：#${payment.zyPayNo}, ${zhuyuanRechargeResponse.message}")
            return RechargeResult.Failure("住院充值失败：${zhuyuanRechargeResponse.message}", zhuyuanRechargeResponse)
        }

        wxPaymentService.updateHisRechargeStatusOnPay(
            payment,
            zhuyuanRechargeResponse.serialNo,
            zhuyuanRechargeResponse.invoiceNo
        )

        val updatedPayment = wxPaymentService.selectWxPaymentById(payment.id)!!
        logger.info("住院充值成功：#${payment.zyPayNo}, 流水号：${zhuyuanRechargeResponse.serialNo}")

        return RechargeResult.Success("住院充值完成", updatedPayment, zhuyuanRechargeResponse)
    }

    fun processBingliRecharge(payment: WxPayment, wxPaymentService: IWxPaymentService): RechargeResult {
        // 检查是否为病历复印订单
        if (!payment.isBingli) {
            return RechargeResult.Skipped("非病历复印订单，无需充值", payment)
        }

        // 检查是否已支付
        if (payment.wxTradeStatus != UnifiedPayStatusEnum.SUCCESS.msg) {
            return RechargeResult.Skipped("订单未支付", payment)
        }

        // 检查是否已充值
        if (payment.hisTradeStatus == Constants.PAYMENT_OK) {
            return RechargeResult.Skipped("病历复印缴费已完成", payment)
        }

        wxPaymentService.updateHisPayStatusOnPay(payment)

        val medicalRecordApplicationService: IMedicalRecordApplicationService = SpringUtils.getBean(IMedicalRecordApplicationService::class.java)
        val medicalRecordApplication = medicalRecordApplicationService.lambdaQuery()
            .eq(MedicalRecordApplication::getZyPayNo, payment.zyPayNo)
            .one()
        medicalRecordApplication?.let {
            it.wxPayNo = payment.wxPayNo
            it.status = MedicalRecordApplicationStatus.PAID
            it.payTime = LocalDateTime.now()
            medicalRecordApplicationService.updateById(it)
        }

        val updatedPayment = wxPaymentService.selectWxPaymentById(payment.id)!!
        return RechargeResult.Success("病历复印缴费完成", updatedPayment)
    }

    fun settleAlipayMipOrder(outTradeNo: String): MipAlipayOrder {
        val mipAlipayOrderService: IMipAlipayOrderService = SpringUtils.getBean(IMipAlipayOrderService::class.java)
        synchronized("HisExt.settle#$outTradeNo".intern()) {
            val mipAlipayOrder: MipAlipayOrder = mipAlipayOrderService.getOneByOutTradeNo(outTradeNo)
            if (mipAlipayOrder.hisSettleTime != null || !mipAlipayOrder.alipayRefundStatus.isNullOrBlank()) {
                return mipAlipayOrder
            }

            val tradeQueryResponse: AlipayTradeQueryResponse =
                AlipayService.queryOrder(
                    outTradeNo = mipAlipayOrder.outTradeNo,
                    queryOptions = listOf("fund_bill_list")
                )
            check(tradeQueryResponse.isSuccess) { "查询支付宝订单失败：${tradeQueryResponse.subMsg}" }

            mipAlipayOrder.tradeStatus = tradeQueryResponse.tradeStatus
            mipAlipayOrder.tradeNo = tradeQueryResponse.tradeNo
            mipAlipayOrder.tradeTime = DateUtil.date(tradeQueryResponse.sendPayDate)
            mipAlipayOrderService.updateById(mipAlipayOrder)

            if (tradeQueryResponse.tradeStatus != Constants.TRADE_SUCCESS) {
                return mipAlipayOrder
            }

            val settleResponse: org.mospital.dongruan.yibao.bean.Response<SettleResult> =
                DongruanYibaoService.settle(
                    form = SettleForm(
                        clinicNo = mipAlipayOrder.clinicNo,
                        feeList = JacksonKit.readValue(mipAlipayOrder.feesJson, jacksonTypeRef()),
                        totalFee = mipAlipayOrder.feeSumAmount,
                        payOrdId = mipAlipayOrder.payOrderId,
                        personName = mipAlipayOrder.userName,
                        idCardNo = mipAlipayOrder.certNo,
                        thirdInfo = SettleForm.ThirdInfo(
                            orderId = mipAlipayOrder.outTradeNo,
                            tradeSerialNo = mipAlipayOrder.tradeNo,
                            payWay = "27", // 支付宝
                            payFee = mipAlipayOrder.ownPayAmount,
                            payTime = DateUtil.formatDateTime(mipAlipayOrder.tradeTime)
                        )
                    ),
                    thirdType = ThirdType.ALIPAY,
                    extendParams = Request.ExtendParams(appId = AlipaySetting.mipOrgAppId),
                    env = ALIPAY_MIP_ENV
                )
            if (settleResponse.isOk()) {
                val settleResult: SettleResult = settleResponse.data!!
                mipAlipayOrder.hisInvoiceNo = settleResult.hisInvoiceNo
                mipAlipayOrder.hisSettleTime = DateUtil.parse(settleResult.hisSetlTime as CharSequence)
                mipAlipayOrder.siSettleId = settleResult.siSetlId
                mipAlipayOrder.siSettleTime = DateUtil.parse(settleResult.siSetlTime as CharSequence)
                mipAlipayOrder.hisSettleMessage = settleResponse.message.take(250).ifBlank { "结算成功" }
                mipAlipayOrderService.updateById(mipAlipayOrder)
            } else {
                mipAlipayOrder.hisSettleTime = Date()
                mipAlipayOrder.hisSettleMessage = settleResponse.message.take(250)
                mipAlipayOrderService.updateById(mipAlipayOrder)
                if (settleResponse.message.startsWith("HIS执行失败")) {
                    AlipayExt.refundAlipay(mipAlipayOrder)
                }
            }

            return mipAlipayOrder
        }
    }

    fun settleWeixinMipOrder(clientType: org.mospital.wecity.mip.ClientType, outTradeNo: String): MipWxOrder {
        val mipWxOrderService: IMipWxOrderService = SpringUtils.getBean(IMipWxOrderService::class.java)

        val wxInsurancePayService: WxInsurancePayService = WeixinExt.getWxInsurancePayServiceByClientType(clientType)
        val mipEnv: String = getMipEnvByClientType(clientType)

        synchronized("HisExt.settle#$outTradeNo".intern()) {
            val mipWxOrder: MipWxOrder = mipWxOrderService.getOneByOutTradeNo(outTradeNo)
            if (!mipWxOrder.hisInvoiceNo.isNullOrBlank() || !mipWxOrder.hospOutRefundNo.isNullOrBlank()) {
                return mipWxOrder
            }

            // 查询微信订单时，两个参数只能传一个
            val orderQueryResult: WxInsurancePayOrderQueryResult =
                wxInsurancePayService.queryOrder(
                    medTransId = mipWxOrder.medTransactionId,
                    hospOutTradeNo = ""
                )

            if (orderQueryResult.cashTradeStatus == "SUCCESS" || (orderQueryResult.cashTradeStatus == "" && orderQueryResult.cashFee == 0)) {
                val payTime = DateUtil.parse(orderQueryResult.gmtOutCreate, DatePattern.PURE_DATETIME_PATTERN)
                    .toString(DatePattern.NORM_DATETIME_FORMAT)
                val weixinMipSetting = WeixinMipSetting.getInstance(clientType)
                val settleResponse: org.mospital.dongruan.yibao.bean.Response<SettleResult> =
                    DongruanYibaoService.settle(
                        form = SettleForm(
                            clinicNo = mipWxOrder.clinicNo,
                            feeList = JacksonKit.readValue(mipWxOrder.feesJson, jacksonTypeRef()),
                            totalFee = mipWxOrder.feeSumAmount,
                            payOrdId = mipWxOrder.payOrderId,
                            personName = mipWxOrder.patientName,
                            idCardNo = mipWxOrder.patientIdCardNo,
                            thirdInfo = SettleForm.ThirdInfo(
                                orderId = mipWxOrder.hospitalOutTradeNo,
                                tradeSerialNo = mipWxOrder.medTransactionId,
                                payWay = "98", // 微信
                                payFee = mipWxOrder.ownPayAmount,
                                payTime = payTime
                            )
                        ),
                        thirdType = ThirdType.WEIXIN,
                        extendParams = Request.ExtendParams(appId = weixinMipSetting.orgAppId),
                        env = mipEnv
                    )
                if (settleResponse.isOk()) {
                    val settleResult: SettleResult = settleResponse.data!!
                    mipWxOrder.hisInvoiceNo = settleResult.hisInvoiceNo
                    mipWxOrder.hisSettleTime = DateUtil.parse(settleResult.hisSetlTime as CharSequence)
                    mipWxOrder.siSettleId = settleResult.siSetlId
                    mipWxOrder.siSettleTime = DateUtil.parse(settleResult.siSetlTime as CharSequence)
                    mipWxOrder.hisSettleMessage = settleResponse.message.take(250).ifBlank { "结算成功" }
                    mipWxOrderService.updateById(mipWxOrder)
                } else {
                    mipWxOrder.hisSettleTime = Date()
                    mipWxOrder.hisSettleMessage = settleResponse.message.take(250)
                    mipWxOrderService.updateById(mipWxOrder)
                    if (settleResponse.message.startsWith("HIS执行失败")) {
                        WeixinExt.refundWeixin(mipWxOrder)
                    }
                }
            }

            return mipWxOrder
        }
    }

    fun getWsdlUrlByClientType(clientType: space.lzhq.ph.common.ClientType): URL {
        val wsdlUrlKey: String = when (clientType) {
            space.lzhq.ph.common.ClientType.WEIXIN_MP -> "WeixinGongzhonghao"
            space.lzhq.ph.common.ClientType.WEIXIN_MA -> "WeixinXiaochengxu"
            space.lzhq.ph.common.ClientType.ALIPAY -> "AlipayXiaochengxu"
            else -> "default"
        }
        return Configuration.getUrl(wsdlUrlKey)
            ?: throw IllegalStateException("WSDL URL not found for key: $wsdlUrlKey")
    }

    fun getWitontekServiceByClientType(clientType: space.lzhq.ph.common.ClientType): WitontekService {
        val wsdlUrl = getWsdlUrlByClientType(clientType)
        return WitontekService.getInstance(wsdlUrl)
    }
}

package space.lzhq.ph.job

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateTime
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper
import org.mospital.alipay.HospitalOrderStatus
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.common.Constants
import space.lzhq.ph.domain.Reservation
import space.lzhq.ph.service.IReservationService

/**
 * 向支付宝推送挂号单
 * 表达式：0 0 21 * * ?
 */
@Component("pushAlipayFinishedHospitalOrderJob")
class PushAlipayFinishedHospitalOrderJob {

    @Autowired
    private lateinit var reservationService: IReservationService

    fun execute() {
        val reservations: List<Reservation> = LambdaQueryChainWrapper<Reservation>(reservationService.baseMapper)
            .eq(Reservation::getJzDate, DateTime.now().toString(DatePattern.NORM_DATE_FORMAT))
            .eq(Reservation::getOperationType, Reservation.OPERATION_TYPE_RESERVE)
            .eq(Reservation::getOperationResult, Reservation.OPERATION_RESULT_SUCCESS)
            .eq(Reservation::getStatus, Reservation.RESERVATION_STATUS_INIT)
            .likeRight(Reservation::getOpenid, Constants.ALIPAY_USER_ID_PREFIX)
            .list()
        reservations.forEach {
            reservationService.pushAlipayHospitalOrder(
                it,
                HospitalOrderStatus.MERCHANT_FINISHED,
                it.operationTime,
                DateTime.now(),
                it.id
            )
        }
    }

}

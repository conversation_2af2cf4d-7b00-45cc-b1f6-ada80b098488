package space.lzhq.ph.job

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateTime
import cn.hutool.core.date.DateUtil
import kotlinx.coroutines.runBlocking
import org.mospital.dongruan.pay.UnifiedPayStatusEnum
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.OrderQueryOptions
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.service.IOrderQueryService
import space.lzhq.ph.service.IWxPaymentService

/**
 * 刷新微信订单状态
 * 表达式：0 0 5 * * ?
 * <AUTHOR>
 */
@Suppress("unused")
@Component("refreshWeixinOrderJob")
class RefreshWeixinOrderJob {

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var orderQueryService: IOrderQueryService

    private val logger = LoggerFactory.getLogger(RefreshWeixinOrderJob::class.java)

    fun execute() {
        val now = DateTime.now()
        val beginCreateTime = DateUtil.offsetHour(now, -2).toString(DatePattern.NORM_DATETIME_FORMAT)
        val endCreateTime = DateUtil.offsetHour(now, -1).toString(DatePattern.NORM_DATETIME_FORMAT)
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to beginCreateTime,
            "endCreateTime" to endCreateTime,
            "filterErrorOrder" to true
        )
        val payments = wxPaymentService.selectWxPaymentList(WxPayment().apply {
            this.params = params
        })
        payments.forEach { payment ->
            runBlocking {
                orderQueryService.queryAndUpdateOrderSafely(payment)
            }
        }
    }

}
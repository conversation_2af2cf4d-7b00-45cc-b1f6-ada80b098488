package space.lzhq.ph.job

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateTime
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.service.IMedicalRecordRevenueStatisticsService

/**
 * 病历邮寄业务收入统计定时任务
 * 表达式：0 30 5 * * ?（每天05:30执行）
 * <AUTHOR>
 */
@Component("medicalRecordRevenueStatisticsJob")
class MedicalRecordRevenueStatisticsJob {

    @Autowired
    private lateinit var medicalRecordRevenueStatisticsService: IMedicalRecordRevenueStatisticsService

    private val logger = LoggerFactory.getLogger(MedicalRecordRevenueStatisticsJob::class.java)

    fun execute() {
        val now = DateTime.now()
        val jobId = "medicalRecordRevenueStatisticsJob#" + now.toString(DatePattern.PURE_DATETIME_FORMAT)
        logger.info("Job starting: $jobId")

        try {
            // 统计前一天的收入数据
            val success = medicalRecordRevenueStatisticsService.calculateAndSaveYesterdayRevenue()
            
            if (success) {
                logger.info("病历邮寄业务收入统计任务执行成功")
            } else {
                logger.error("病历邮寄业务收入统计任务执行失败")
            }
        } catch (e: Exception) {
            logger.error("病历邮寄业务收入统计任务执行异常", e)
        }

        logger.info("Job done: $jobId")
    }
}

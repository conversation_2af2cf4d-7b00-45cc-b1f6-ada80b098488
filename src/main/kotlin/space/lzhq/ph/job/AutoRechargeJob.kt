package space.lzhq.ph.job

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateTime
import cn.hutool.core.date.DateUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.OrderQueryOptions
import space.lzhq.ph.domain.OrderQueryResult
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.service.IOrderQueryService
import space.lzhq.ph.service.IWxPaymentService

/**
 * 自动充值
 * 表达式：0 0/2 * * * ?
 * <AUTHOR>
 */
@Component("autoRechargeJob")
class AutoRechargeJob {

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var orderQueryService: IOrderQueryService

    private val logger = LoggerFactory.getLogger(AutoRechargeJob::class.java)
    
    // 限制并发数量，防止过多协程同时执行
    private val concurrencyLimit = Semaphore(10)

    fun execute() {
        val now = DateTime.now()
        val jobId = "autoRechargeJob#" + now.toString(DatePattern.PURE_DATETIME_FORMAT)
        logger.debug("Job starting: $jobId")

        val beginCreateTime = DateUtil.offsetMinute(now, -5).toString(DatePattern.NORM_DATETIME_FORMAT)
        val endCreateTime = DateUtil.offsetMinute(now, -3).toString(DatePattern.NORM_DATETIME_FORMAT)
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to beginCreateTime,
            "endCreateTime" to endCreateTime,
            "filterErrorOrder" to true
        )

        val payments = wxPaymentService.selectWxPaymentList(WxPayment().apply {
            this.params = params
        })
        
        logger.debug("Processing ${payments.size} payments in parallel with concurrency limit ${concurrencyLimit.availablePermits}")
        
        // 使用 runBlocking 包装以支持任务调度器的反射调用
        // 内部使用 supervisorScope 和 Dispatchers.IO 实现真正的并行处理
        runBlocking {
            supervisorScope {
                val jobs = payments.map { payment ->
                    async(Dispatchers.IO) {
                        concurrencyLimit.withPermit {
                            try {
                                logger.debug("Processing payment ${payment.id} on thread: ${Thread.currentThread().name}")
                                val result = orderQueryService.queryAndUpdateOrder(
                                    payment = payment,
                                    options = setOf(OrderQueryOptions.WITH_RECHARGE, OrderQueryOptions.SAFE_MODE)
                                )
                                when (result) {
                                    is OrderQueryResult.Success -> {
                                        logger.debug("Successfully processed payment ${payment.id}")
                                        result
                                    }
                                    is OrderQueryResult.Failure -> {
                                        logger.warn("Failed to process payment ${payment.id}: ${result.message}")
                                        result
                                    }
                                }
                            } catch (e: Exception) {
                                logger.error("Exception processing payment ${payment.id}: ${e.message}", e)
                                OrderQueryResult.Failure("处理异常: ${e.message}")
                            }
                        }
                    }
                }
                
                // 等待所有任务完成
                val results = jobs.awaitAll()
                val successCount = results.count { it is OrderQueryResult.Success }
                val failureCount = results.count { it is OrderQueryResult.Failure }
                
                logger.info("Parallel processing completed: $successCount success, $failureCount failed, total processed: ${results.size}")
            }
        }

        logger.debug("Job done: $jobId")
    }
}
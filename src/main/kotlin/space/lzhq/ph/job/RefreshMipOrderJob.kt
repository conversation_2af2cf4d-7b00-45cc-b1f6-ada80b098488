package space.lzhq.ph.job

import cn.hutool.core.date.DateField
import cn.hutool.core.date.DateTime
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper
import org.mospital.dongruan.yibao.DongruanYibaoService
import org.mospital.dongruan.yibao.bean.*
import org.mospital.wecity.mip.WeixinMipSetting
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.MipWxOrder
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.service.IMipWxOrderService

/**
 * 刷新医保移动支付订单，处理用于支付后直接退出应用的情况
 * 表达式：0 0/1 * * * ?
 * <AUTHOR>
 */
@Component("refreshMipOrderJob")
class RefreshMipOrderJob {

    @Autowired
    private lateinit var mipWxOrderService: IMipWxOrderService

    fun execute() {
        val now = DateTime.now()
        val beginCreateTime = now.offsetNew(DateField.MINUTE, -6)
        val endCreateTime = now.offsetNew(DateField.MINUTE, -1)
        val weixinMipSetting = WeixinMipSetting.getInstance(org.mospital.wecity.mip.ClientType.MP)
        LambdaQueryChainWrapper(mipWxOrderService.baseMapper)
            .ge(MipWxOrder::getCreateTime, beginCreateTime)
            .le(MipWxOrder::getCreateTime, endCreateTime)
            .isNotNull(MipWxOrder::getHospitalOutTradeNo)
            .isNull(MipWxOrder::getHisInvoiceNo)
            .list()
            .forEach {
                try {
                    val yibaoOrderStatusResponse: Response<QueryOrderResult> = DongruanYibaoService.queryOrder(
                        form = QueryOrderForm(
                            clinicNo = it.clinicNo,
                            payOrderId = it.payOrderId,
                            personName = it.patientName,
                            idCardNo = it.patientIdCardNo
                        ),
                        thirdType = ThirdType.WEIXIN,
                        extendParams = Request.ExtendParams(appId = weixinMipSetting.orgAppId),
                        env = it.env
                    )
                    if (yibaoOrderStatusResponse.isOk() && yibaoOrderStatusResponse.data?.orderStatus == "6") {
                        HisExt.settleWeixinMipOrder(it.clientType, it.hospitalOutTradeNo)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
    }
}

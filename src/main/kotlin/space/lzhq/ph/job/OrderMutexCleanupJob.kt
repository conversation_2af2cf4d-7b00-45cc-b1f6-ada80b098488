package space.lzhq.ph.job

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.service.OrderSerializationService

/**
 * 订单Mutex缓存清理任务
 * 定期清理过期的Mutex，释放内存
 * 表达式：0 0/10 * * * ? （每10分钟执行一次）
 * <AUTHOR>
 */
@Component("orderMutexCleanupJob")
class OrderMutexCleanupJob {

    @Autowired
    private lateinit var orderSerializationService: OrderSerializationService

    private val logger = LoggerFactory.getLogger(OrderMutexCleanupJob::class.java)

    fun execute() {
        try {
            logger.debug("开始执行订单Mutex缓存清理任务")
            
            // 获取清理前的统计信息
            val statsBefore = orderSerializationService.getCacheStats()
            logger.debug("清理前缓存统计：$statsBefore")
            
            // 执行清理
            orderSerializationService.cleanupExpiredMutexes()
            
            // 获取清理后的统计信息
            val statsAfter = orderSerializationService.getCacheStats()
            logger.debug("清理后缓存统计：$statsAfter")
            
            logger.debug("订单Mutex缓存清理任务执行完成")
        } catch (e: Exception) {
            logger.error("订单Mutex缓存清理任务执行失败", e)
        }
    }
} 
package space.lzhq.ph.job

import org.mospital.alipay.AlipayBillItem
import org.mospital.common.IdUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.ext.AlipayExt
import space.lzhq.ph.service.IAlipayCheckService
import java.time.LocalDate

@Component("syncAlipayBillJob")
class SyncAlipayBillJob {

    @Autowired
    private lateinit var alipayCheckService: IAlipayCheckService

    fun sync() {
        val billDate = LocalDate.now().minusDays(1)
        try {
            val billFile = AlipayExt.getOrDownloadBillFile(billDate)
            val alipayBillItems = AlipayBillItem.parseBillItems(billFile)
            alipayCheckService.syncAlipayBill(IdUtil.localDateToId(billDate), alipayBillItems)
        } catch (e: Exception) {
            alipayCheckService.syncAlipayBill(IdUtil.localDateToId(billDate), emptyList())
            throw e
        }

    }

}

package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.dto.request.CardRefundAgentUpdateDto
import space.lzhq.ph.dto.request.CardRefundBankCardUpdateDto
import space.lzhq.ph.dto.request.CardRefundPatientFormDto
import space.lzhq.ph.dto.response.CardRefundDetailResponseDto
import space.lzhq.ph.dto.response.CardRefundListResponseDto
import space.lzhq.ph.dto.response.CardRefundTimelineDto
import space.lzhq.ph.dto.response.ResponseDto
import space.lzhq.ph.enums.CardRefundStatus
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.ICardRefundService
import space.lzhq.ph.service.ICardRefundTimelineService

@Validated
@RestController
@RequestMapping("/api/card-refund-registration")
class CardRefundApiController : BaseController() {

    @Autowired
    private lateinit var registrationService: ICardRefundService

    @Autowired
    private lateinit var timelineService: ICardRefundTimelineService

    /**
     * 获取当前会话的OpenID
     * @return OpenID或null
     */
    private fun getOpenIdFromSession(): String? {
        return request.getClientSession()?.openId
    }

    /**
     * 检查会话有效性并获取OpenID
     * @return OpenID或错误响应
     */
    private fun <T> validateSessionAndGetOpenId(): Pair<String?, ResponseDto<T>?> {
        val openId = getOpenIdFromSession()
        return if (openId != null) {
            Pair(openId, null)
        } else {
            Pair(null, ResponseDto.error("会话无效"))
        }
    }

    /**
     * 更新操作后获取详情的通用处理方法
     * @param registrationId 注册记录ID
     * @param openId 用户OpenID
     * @return 统一的响应格式
     */
    private fun getDetailAfterUpdate(
        registrationId: String,
        openId: String
    ): ResponseDto<CardRefundDetailResponseDto?> {
        val responseDto: CardRefundDetailResponseDto =
            registrationService.getDetailById(registrationId, openId)

        return ResponseDto.success(responseDto)
    }

    @PostMapping(
        "/patientForm",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun patientForm(
        @Valid @RequestBody dto: CardRefundPatientFormDto
    ): ResponseDto<CardRefundDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<CardRefundDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        val registration = registrationService.save(dto, openId!!).orElse(null)
            ?: return ResponseDto.error("余额清退登记提交失败，请重试")

        return getDetailAfterUpdate(registration.id, openId)
    }

    @PutMapping(
        "/{id}/clear-agent-flag",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun clearAgentFlag(
        @PathVariable id: String
    ): ResponseDto<CardRefundDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<CardRefundDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val updatedRegistration = registrationService.clearAgentFlag(id, openId)

        return getDetailAfterUpdate(updatedRegistration.id, openId!!)
    }

    @PutMapping(
        "/{id}/agent",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun updateAgent(
        @PathVariable id: String,
        @Valid @RequestBody agentDto: CardRefundAgentUpdateDto
    ): ResponseDto<CardRefundDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<CardRefundDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val updatedRegistration = registrationService.updateAgent(id, agentDto, openId)

        return getDetailAfterUpdate(updatedRegistration.id, openId!!)
    }

    @PutMapping(
        "/{id}/bankCard",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun updateBankCard(
        @PathVariable id: String,
        @Valid @RequestBody bankCardDto: CardRefundBankCardUpdateDto
    ): ResponseDto<CardRefundDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<CardRefundDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val updatedRegistration = registrationService.updateBankCard(id, bankCardDto, openId)

        return getDetailAfterUpdate(updatedRegistration.id, openId!!)
    }

    @GetMapping(
        "/{id}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    fun getRegistrationDetail(
        @PathVariable id: String
    ): ResponseDto<CardRefundDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<CardRefundDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val registrationDetail = registrationService.getDetailById(id, openId!!)
        return ResponseDto.success(registrationDetail)
    }

    @GetMapping(
        "",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    fun getUserRegistrationList(
        @RequestParam(required = false)
        status: CardRefundStatus?
    ): ResponseDto<List<CardRefundListResponseDto>> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<List<CardRefundListResponseDto>>()
        if (errorResponse != null) return errorResponse

        return ResponseDto.success(registrationService.getUserRegistrationList(openId!!, status))
    }


    @PutMapping(
        "/{id}/submit",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun submitRegistration(
        @PathVariable id: String
    ): ResponseDto<CardRefundDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<CardRefundDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val updatedRegistration = registrationService.submitRegistration(id, openId)

        return getDetailAfterUpdate(updatedRegistration.id, openId!!)
    }

    @GetMapping(
        "/{id}/timeline",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    fun getRegistrationTimeline(
        @PathVariable id: String,
        @RequestParam(required = false, defaultValue = "true")
        includeDetails: Boolean?
    ): ResponseDto<List<CardRefundTimelineDto>> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<List<CardRefundTimelineDto>>()
        if (errorResponse != null) return errorResponse

        // 验证申报记录是否存在且属于当前用户
        registrationService.getDetailById(id, openId!!)

        // 获取时间线
        return ResponseDto.success(timelineService.getRegistrationTimeline(id, includeDetails ?: true))
    }
}
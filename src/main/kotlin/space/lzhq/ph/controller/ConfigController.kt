package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.domain.SysConfig
import com.ruoyi.system.service.ISysConfigService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/open/config")
class ConfigController {

    @Autowired
    private lateinit var configService: ISysConfigService

    @GetMapping()
    fun getConfigs(): AjaxResult {
        val configs = configService.selectConfigList(SysConfig()).filter {
            it.isPublic
        }.associate { it.configKey to it.configValue }
        return AjaxResult.success(configs)
    }

    @GetMapping("/{configKey}")
    fun getConfigValueAsString(@PathVariable("configKey") configKey: String): AjaxResult {
        val configValue = configService.selectConfigByKey(configKey)
        return AjaxResult.success(configValue)
    }

    @GetMapping("/bool/{configKey}")
    fun getConfigValueAsBoolean(@PathVariable("configKey") configKey: String): AjaxResult {
        val value = configService.getBoolean(configKey, true)
        return AjaxResult.success(value)
    }

}

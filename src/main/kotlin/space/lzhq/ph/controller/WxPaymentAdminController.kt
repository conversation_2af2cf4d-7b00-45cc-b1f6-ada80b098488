package space.lzhq.ph.controller

import com.ruoyi.common.annotation.Log
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import kotlinx.coroutines.runBlocking
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.common.Constants
import space.lzhq.ph.domain.OrderQueryOptions
import space.lzhq.ph.domain.OrderQueryResult
import space.lzhq.ph.domain.RechargeResult
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IOrderQueryService
import space.lzhq.ph.service.IWxPaymentService
import java.math.BigDecimal

/**
 * 充值记录Controller
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
@Controller
@RequestMapping("/ph/payment")
class WxPaymentAdminController : BaseController() {
    private val prefix = "ph/payment"

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var orderQueryService: IOrderQueryService

    @RequiresPermissions("ph:payment:view")
    @GetMapping
    fun payment(): String {
        return "$prefix/payment"
    }

    /**
     * 查询充值记录列表
     */
    @RequiresPermissions("ph:payment:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(wxPayment: WxPayment): TableDataInfo {
        startPage()
        val list = wxPaymentService.selectWxPaymentList(wxPayment)
        return getDataTable(list)
    }

    /**
     * 导出充值记录列表
     */
    @RequiresPermissions("ph:payment:export")
    @Log(title = "充值记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(wxPayment: WxPayment): AjaxResult {
        val list = wxPaymentService.selectWxPaymentList(wxPayment)
        val util = ExcelUtil(WxPayment::class.java)
        return util.exportExcel(list, "payment")
    }

    @RequiresPermissions("ph:payment:queryWXOrder")
    @GetMapping("/queryWXOrder/{id}")
    @ResponseBody
    fun queryWXOrder(@PathVariable("id") id: Long): AjaxResult {
        return runBlocking {
            try {
                val wxPayment = wxPaymentService.selectWxPaymentById(id)!!

                val queryResult = orderQueryService.queryAndUpdateOrder(
                    payment = wxPayment,
                    options = setOf(OrderQueryOptions.SKIP_STATUS_CHECK)
                )

                when (queryResult) {
                    is OrderQueryResult.Success -> AjaxResult.success(queryResult.queryResult)
                    is OrderQueryResult.Failure -> AjaxResult.error(queryResult.message)
                }
            } catch (e: Exception) {
                logger.error(e.message, e)
                AjaxResult.error(e.message)
            }
        }
    }

    /**
     * 申请HIS充值
     */
    @RequiresPermissions("ph:payment:requestHisRecharge")
    @PostMapping("/requestHisRecharge/{id}")
    @ResponseBody
    fun requestHisRecharge(@PathVariable("id") id: Long): AjaxResult {
        val wxPayment = wxPaymentService.selectWxPaymentById(id)!!
        if (Constants.PAY_OK != wxPayment.wxTradeStatus
            || Constants.RECHARGE_OK == wxPayment.hisTradeStatus
            || Constants.MANUAL_INIT != wxPayment.manualPayState
        ) {
            return AjaxResult.error("此订单不能申请HIS充值")
        }
        wxPayment.manualPayState = Constants.MANUAL_REQUESTED
        val ok = wxPaymentService.updateWxPayment(wxPayment) == 1
        return if (ok) AjaxResult.success() else AjaxResult.error()
    }

    /**
     * 放行HIS充值
     */
    @RequiresPermissions("ph:payment:approveHisRecharge")
    @PostMapping("/approveHisRecharge/{id}")
    @ResponseBody
    fun approveHisRecharge(@PathVariable("id") id: Long): AjaxResult {
        val payment = wxPaymentService.selectWxPaymentById(id)!!
        if (Constants.PAY_OK != payment.wxTradeStatus
            || Constants.RECHARGE_OK == payment.hisTradeStatus
            || Constants.MANUAL_REQUESTED != payment.manualPayState
        ) {
            return AjaxResult.error("此订单不能放行HIS充值")
        }

        return try {
            val rechargeResult = runBlocking {
                HisExt.recharge(payment.zyPayNo)
            }

            when (rechargeResult) {
                is RechargeResult.Success -> {
                    AjaxResult.success(rechargeResult.message, rechargeResult.updatedPayment)
                }

                is RechargeResult.Skipped -> {
                    AjaxResult.success(rechargeResult.message, rechargeResult.currentPayment)
                }

                is RechargeResult.Failure -> {
                    AjaxResult.error(rechargeResult.message)
                }
            }
        } catch (e: Exception) {
            logger.error("执行HIS充值时发生异常，订单ID: $id", e)
            AjaxResult.error("充值操作异常: ${e.message}")
        }
    }

    /**
     * 退款
     */
    @RequiresPermissions("ph:payment:refund")
    @PostMapping("/refund/{id}")
    @ResponseBody
    fun refund(@PathVariable id: Long, @RequestParam amount: BigDecimal): AjaxResult {
        try {
            val refund = WeixinExt.refundWeixin(id, amount)
            return AjaxResult.success(refund)
        } catch (e: Exception) {
            logger.error(e.message, e)
            return AjaxResult.error(e.message)
        }
    }
}

package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.tencent.healthcard.TencentHealthCardService
import org.mospital.witontek.QueryMenzhenPatientRequest
import org.mospital.witontek.QueryMenzhenPatientResponse
import org.mospital.witontek.WitontekService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.HisQrCode
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IPatientService

@RestController
@RequestMapping("/api/patient")
class PatientApiController : BaseController() {

    @Autowired
    private lateinit var patientService: IPatientService

    /**
     * 获取就诊人列表
     */
    @GetMapping("list")
    @RequireSession
    fun list(): AjaxResult {
        val session = request.getClientSession()!!
        val patientsSameOpenid = patientService.selectPatientListByOpenId(session.openId)
        return AjaxResult.success(patientsSameOpenid)
    }

    /**
     * 获取默认就诊人
     */
    @GetMapping("active")
    @RequireActivePatient
    fun active(): AjaxResult {
        return AjaxResult.success(request.getCurrentPatient())
    }

    /**
     * 根据就诊卡号查询患者信息
     * @param jzCardNo 就诊卡号
     */
    @GetMapping("jzCard")
    @RequireSession
    fun jzCard(@RequestParam("jzCardNo") jzCardNo: String): AjaxResult {
        val session: Session = request.getClientSession()!!
        val patient: Patient = patientService.selectPatientByJzCardNoAndOpenId(jzCardNo, session.openId)
            ?: return AjaxResult.error("查不到指定的就诊人信息")

        if (session.openId != patient.openId) {
            return AjaxResult.error("您无权访问该患者信息")
        }

        return AjaxResult.success(patient)
    }

    /**
     * 切换到指定就诊卡
     * @param jzCardNo 身份证号
     */
    @PostMapping("switchToJzCard")
    @RequireSession
    fun switchToJzCard(@RequestParam("jzCardNo") jzCardNo: String): AjaxResult {
        val session = request.getClientSession()!!
        val patientsSameOpenid = patientService.selectPatientListByOpenId(session.openId)

        var oldActive: Patient? = null
        var newActive: Patient? = null
        patientsSameOpenid.forEach {
            if (it.jzCardNo == jzCardNo) {
                it.active = 1
                newActive = it
            } else {
                if (it.active == 1) {
                    it.active = 0
                    oldActive = it
                }
            }
        }

        if (newActive == null) {
            return AjaxResult.error("查不到指定的就诊人信息")
        }

        if (oldActive != null) {
            patientService.updatePatient(oldActive)
        }

        patientService.updatePatient(newActive)
        return AjaxResult.success(newActive)
    }

    /**
     * 根据就诊卡号移除就诊人
     * @param jzCardNo 就诊卡号
     */
    @PostMapping("removeJzCard")
    @RequireSession
    fun removeJzCard(@RequestParam("jzCardNo") jzCardNo: String): AjaxResult {
        if (jzCardNo.isBlank()) {
            return AjaxResult.error("就诊卡号不能为空")
        }

        val session = request.getClientSession()!!
        val patientsSameOpenid = patientService.selectPatientListByOpenId(session.openId)
        val target = patientsSameOpenid.first { jzCardNo == it.jzCardNo }
            ?: return AjaxResult.error("查不到指定的就诊人信息")
        if (target.active == 1 && patientsSameOpenid.size > 1) {
            return AjaxResult.error("为避免影响使用，请先切换到其它就诊人，再来移除此就诊人")
        }
        patientService.deletePatientById(target.id)
        return AjaxResult.success()
    }

    /**
     * 处理就诊卡二维码并切换当前活跃就诊人。
     *
     * 首先检查二维码中包含的就诊卡号是否已存在于当前用户的就诊人列表中：
     * - 如果存在，则直接将该就诊人设置为活跃状态
     * - 如果不存在，则检查二维码是否过期：
     *   - 若已过期，返回错误
     *   - 若未过期，则通过外部服务获取患者信息，创建新的就诊人记录并设置为活跃状态
     *
     * @param token 包含就诊卡信息的二维码字符串
     * @return 操作结果，成功则返回就诊人信息，失败则返回错误信息
     */
    @PostMapping("checkAndSwitchJzCard")
    @RequireSession
    fun checkAndSwitchJzCard(@RequestParam("token") token: String): AjaxResult {
        if (token.isBlank()) {
            return AjaxResult.error("二维码无效")
        }

        val hisQrCode = HisQrCode.parse(token)
        val jzCardNo = hisQrCode.patientCard
        val session = request.getClientSession()!!
        val openId = session.openId

        synchronized("${jzCardNo}_${openId}".intern()) {
            val patients = patientService.selectPatientListByOpenId(openId)
            val targetPatient = patients.firstOrNull { jzCardNo == it.jzCardNo }

            // 如果找到目标患者，直接执行切换逻辑
            if (targetPatient != null) {
                if (targetPatient.active == 0) {
                    targetPatient.active = 1
                    patientService.switchActivePatient(jzCardNo, openId)
                }
                return AjaxResult.success(targetPatient)
            }

            // 只有在找不到目标患者时才检查二维码是否过期
            if (hisQrCode.isExpired()) {
                return AjaxResult.error("二维码已过期")
            }

            val queryMenzhenPatientResponse: QueryMenzhenPatientResponse = WitontekService.me.queryMenzhenPatient(
                QueryMenzhenPatientRequest().apply {
                    this.jzCardNo = jzCardNo
                }
            )
            if (!queryMenzhenPatientResponse.isOk()) {
                return AjaxResult.error(queryMenzhenPatientResponse.message)
            }
            if (!queryMenzhenPatientResponse.isValid()) {
                return AjaxResult.error("就诊账户状态异常")
            }

            val patient = Patient(session, queryMenzhenPatientResponse, queryMenzhenPatientResponse.mobile)
            patient.active = 1
            patient.jzCardNo = jzCardNo
            patient.empi = ""
            patient.erhcCardNo = ""
            patient.nation = ""

            val healthCardResult = TencentHealthCardService.getHealthCardByQRCode(
                qrCodeText = jzCardNo,
            )
            if (healthCardResult.isOk() && healthCardResult.data?.card != null) {
                val healthCard = healthCardResult.data!!.card!!
                patient.empi = healthCard.phid
                patient.erhcCardNo = healthCard.healthCardId
                patient.nation = healthCard.nation
            }

            patientService.insertPatient(patient)
            patientService.switchActivePatient(jzCardNo, openId)

            return AjaxResult.success(patient)
        }
    }

}

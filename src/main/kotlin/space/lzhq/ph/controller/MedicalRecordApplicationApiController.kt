package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import jakarta.validation.Valid
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.dongruan.pay.DongruanPayService
import org.mospital.dongruan.pay.UnifiedOrderForm
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.dto.request.MedicalRecordApplicationAgentUpdateDto
import space.lzhq.ph.dto.request.MedicalRecordApplicationMailingUpdateDto
import space.lzhq.ph.dto.request.MedicalRecordApplicationPatientFormDto
import space.lzhq.ph.dto.response.MedicalRecordApplicationDetailResponseDto
import space.lzhq.ph.dto.response.MedicalRecordApplicationListResponseDto
import space.lzhq.ph.dto.response.MedicalRecordApplicationTimelineDto
import space.lzhq.ph.dto.response.ResponseDto
import space.lzhq.ph.enums.MedicalRecordApplicationStatus
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IMedicalRecordApplicationService
import space.lzhq.ph.service.IMedicalRecordApplicationTimelineService
import space.lzhq.ph.service.IWxPaymentService
import space.lzhq.ph.service.IPatientService
import java.math.BigDecimal

@Validated
@RestController
@RequestMapping("/api/medical-record-application")
class MedicalRecordApplicationApiController : BaseController() {

    @Autowired
    private lateinit var applicationService: IMedicalRecordApplicationService

    @Autowired
    private lateinit var timelineService: IMedicalRecordApplicationTimelineService

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var patientService: IPatientService

    @PostMapping(
        "/patientForm",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun patientForm(
        @Valid @RequestBody dto: MedicalRecordApplicationPatientFormDto
    ): ResponseDto<MedicalRecordApplicationDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<MedicalRecordApplicationDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        val application = applicationService.save(dto, openId!!).orElse(null)
            ?: return ResponseDto.error("病历邮寄申请提交失败，请重试")

        return getDetailAfterUpdate(application.id, openId)
    }

    @PutMapping(
        "/{id}/clear-agent-flag",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun clearAgentFlag(
        @PathVariable id: String
    ): ResponseDto<MedicalRecordApplicationDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<MedicalRecordApplicationDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val updatedRegistration = applicationService.clearAgentFlag(id, openId)

        return getDetailAfterUpdate(updatedRegistration.id, openId!!)
    }

    @PutMapping(
        "/{id}/agent",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun updateAgent(
        @PathVariable id: String,
        @Valid @RequestBody agentDto: MedicalRecordApplicationAgentUpdateDto
    ): ResponseDto<MedicalRecordApplicationDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<MedicalRecordApplicationDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val updatedApplication = applicationService.updateAgent(id, agentDto, openId)

        return getDetailAfterUpdate(updatedApplication.id, openId!!)
    }

    @PutMapping(
        "/{id}/mailing-info",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun updateMailingInfo(
        @PathVariable id: String,
        @Valid @RequestBody mailingDto: MedicalRecordApplicationMailingUpdateDto
    ): ResponseDto<MedicalRecordApplicationDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<MedicalRecordApplicationDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val updatedApplication = applicationService.updateMailingInfo(id, mailingDto, openId)

        return getDetailAfterUpdate(updatedApplication.id, openId!!)
    }

    @PutMapping(
        "/{id}/submit",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun submitApplication(
        @PathVariable id: String
    ): ResponseDto<MedicalRecordApplicationDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<MedicalRecordApplicationDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val updatedApplication = applicationService.submitApplication(id, openId)

        return getDetailAfterUpdate(updatedApplication.id, openId!!)
    }

    /**
     * 获取当前会话的OpenID
     * @return OpenID或null
     */
    private fun getOpenIdFromSession(): String? {
        return request.getClientSession()?.openId
    }

    /**
     * 检查会话有效性并获取OpenID
     * @return OpenID或错误响应
     */
    private fun <T> validateSessionAndGetOpenId(): Pair<String?, ResponseDto<T>?> {
        val openId = getOpenIdFromSession()
        return if (openId != null) {
            Pair(openId, null)
        } else {
            Pair(null, ResponseDto.error("会话无效"))
        }
    }

    /**
     * 更新操作后获取详情的通用处理方法
     * @param applicationId 申请记录ID
     * @param openId 用户OpenID
     * @return 统一的响应格式
     */
    private fun getDetailAfterUpdate(
        applicationId: String,
        openId: String
    ): ResponseDto<MedicalRecordApplicationDetailResponseDto?> {
        val responseDto: MedicalRecordApplicationDetailResponseDto =
            applicationService.getDetailById(applicationId, openId)

        return ResponseDto.success(responseDto)
    }

    @GetMapping(
        "/{id}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    fun getApplicationDetail(
        @PathVariable id: String
    ): ResponseDto<MedicalRecordApplicationDetailResponseDto?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<MedicalRecordApplicationDetailResponseDto?>()
        if (errorResponse != null) return errorResponse

        // 直接调用服务方法，异常处理交给全局异常处理器
        val applicationDetail = applicationService.getDetailById(id, openId!!)
        return ResponseDto.success(applicationDetail)
    }

    @GetMapping(
        "",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    fun getUserApplicationList(
        @RequestParam(required = false)
        status: MedicalRecordApplicationStatus?
    ): ResponseDto<List<MedicalRecordApplicationListResponseDto>> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<List<MedicalRecordApplicationListResponseDto>>()
        if (errorResponse != null) return errorResponse

        return ResponseDto.success(applicationService.getUserApplicationList(openId!!, status))
    }

    @GetMapping(
        "/{id}/timeline",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    fun getApplicationTimeline(
        @PathVariable id: String,
        @RequestParam(required = false, defaultValue = "true")
        includeDetails: Boolean?
    ): ResponseDto<List<MedicalRecordApplicationTimelineDto>> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<List<MedicalRecordApplicationTimelineDto>>()
        if (errorResponse != null) return errorResponse

        // 验证申请记录是否存在且属于当前用户
        applicationService.getDetailById(id, openId!!)

        // 获取时间线
        return ResponseDto.success(timelineService.getApplicationTimeline(id, includeDetails ?: true))
    }

    @PostMapping(
        "/{id}/pay",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun payForApplication(
        @PathVariable id: String
    ): ResponseDto<Any?> {
        val (openId, errorResponse) = validateSessionAndGetOpenId<Any?>()
        if (errorResponse != null) return errorResponse

        // 获取申请记录详情并验证权限
        val application = applicationService.getDetailById(id, openId!!)
        
        // 验证申请状态 - 只有已通过的申请才允许支付
        if (application.status != MedicalRecordApplicationStatus.APPROVED) {
            return ResponseDto.error("只有状态为已通过的申请才允许支付")
        }

        // 检查是否已经支付
        if (application.payTime != null) {
            return ResponseDto.error("该申请已经支付，请勿重复支付")
        }

        // 验证费用信息
        val totalFee = application.totalFee
        if (totalFee == null || totalFee <= BigDecimal.ZERO) {
            return ResponseDto.error("申请费用信息异常，无法支付")
        }

        // 开始支付逻辑
        val session = request.getClientSession()
        val ip: String = ServletUtil.getClientIP(request)
        val outTradeNo: String = UnifiedOrderForm.newOrderNo()

        val form = UnifiedOrderForm(openid = openId)
            .orderNo(outTradeNo)
            .orderType(2)
            .payWayType(if (session?.clientType == ClientType.WEIXIN_MP.code) 32 else 35)
            .subject("${outTradeNo}-${application.admissionId}-病历复印")
            .amount(totalFee)
            .notifyUrl("https://xjzybyy.xjyqtl.cn/open/wx/onDongruanPay")
            .clientIp(ip)
        form.sign()

        val result = runBlocking {
            DongruanPayService.me.createUnifiedOrder(form)
        }

        return if (result.isOk()) {
            // 更新申请记录的支付订单号
            val applicationToUpdate = applicationService.getById(id)
            applicationToUpdate.zyPayNo = outTradeNo
            applicationService.updateById(applicationToUpdate)

            // 创建并保存微信支付记录
            wxPaymentService.createAndSave(applicationToUpdate, session!!.clientType)
            


            ResponseDto.success(result.data)
        } else {
            ResponseDto.error(result.message)
        }
    }
}
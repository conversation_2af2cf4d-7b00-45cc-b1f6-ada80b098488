package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.domain.Doctor
import space.lzhq.ph.service.IDoctorService

@RestController
@RequestMapping("/open/doctor")
class DoctorApiController {

    @Autowired
    private lateinit var doctorService: IDoctorService

    @GetMapping("/list")
    @ResponseBody
    fun list(@RequestParam("departmentId") departmentId: String): AjaxResult {
        val doctors = doctorService.selectDoctorList(Doctor().apply {
            this.departmentId = departmentId
        })
        return AjaxResult.success(doctors.sorted())
    }

    @GetMapping("/show/{id}")
    @ResponseBody
    fun show(@PathVariable("id") id: String): AjaxResult {
        val doctor: Doctor? = doctorService.selectDoctorById(id)

        return if (doctor == null) {
            AjaxResult.error("找不到指定的医生")
        } else {
            AjaxResult.success(doctor)
        }
    }

}
package space.lzhq.ph.controller

import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.dto.response.EnumDefinitionResponseDto
import space.lzhq.ph.dto.response.EnumItemDto
import space.lzhq.ph.dto.response.ResponseDto
import space.lzhq.ph.enums.*

@RestController
@RequestMapping("/api/enums")
class EnumController {

    /**
     * 获取系统相关枚举定义（公共接口，不需要session）
     *
     * @return 包含所有相关枚举定义的响应
     */
    @GetMapping(
        "",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun getEnums(): ResponseDto<EnumDefinitionResponseDto> {
        val response = EnumDefinitionResponseDto()

        // 添加人员关系枚举
        val relationships = Relationship.entries.map {
            EnumItemDto(it.code, it.description)
        }
        response.addEnum("Relationship", relationships)

        // 添加附件类型枚举
        val attachmentTypes = AttachmentType.entries.map {
            EnumItemDto(it.code, it.description)
        }
        response.addEnum("AttachmentType", attachmentTypes)

        // 添加登记状态枚举
        val statuses = CardRefundStatus.entries.map {
            EnumItemDto(it.code, it.description)
        }
        response.addEnum("CardRefundStatus", statuses)

        // 添加病历申请状态枚举
        val medicalRecordApplicationStatuses = MedicalRecordApplicationStatus.entries.map {
            EnumItemDto(it.code, it.description)
        }
        response.addEnum("MedicalRecordApplicationStatus", medicalRecordApplicationStatuses)

        // 添加病历复印用途枚举
        val medicalRecordCopyPurposes = MedicalRecordCopyPurpose.entries.map {
            EnumItemDto(it.code, it.description)
        }
        response.addEnum("MedicalRecordCopyPurpose", medicalRecordCopyPurposes)

        return ResponseDto.success(response)
    }
}
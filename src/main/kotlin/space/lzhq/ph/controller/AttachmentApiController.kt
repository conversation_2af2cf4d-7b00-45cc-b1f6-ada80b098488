package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.utils.file.MimeTypeUtils
import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult
import org.apache.commons.io.FileUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.dto.response.ResponseDto
import space.lzhq.ph.dto.response.attachment.IdCardUploadResponseDto
import space.lzhq.ph.dto.response.attachment.SimpleUploadResponseDto
import space.lzhq.ph.enums.AttachmentType
import space.lzhq.ph.ext.FileUploader
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IAttachmentService
import java.io.File
import kotlin.jvm.optionals.getOrNull

@Validated
@RestController
@RequestMapping("/api/attachment")
class AttachmentApiController : BaseController() {

    @Autowired
    private lateinit var attachmentService: IAttachmentService

    /**
     * 处理文件上传的通用逻辑
     * @param file 上传的文件
     * @param attachmentType 附件类型
     * @param errorMessage 错误信息
     * @return 处理结果
     */
    private fun processFileUpload(
        file: MultipartFile,
        attachmentType: AttachmentType,
        errorMessage: String
    ): ResponseDto<*> {
        val allowedExtensions = MimeTypeUtils.IMAGE_EXTENSION
        val openId = request.getClientSession()?.openId
            ?: return ResponseDto.error<Any>("会话无效")
        var newFile: File? = null
        return try {
            val originalFileName = FileUploader.checkAndGetFileName(file = file, allowedExtension = allowedExtensions)
            newFile = FileUploader.upload(file = file, allowedExtension = allowedExtensions)

            val fileUrl = FileUploader.getUrl(file = newFile)
            val attachment = attachmentService.save(openId, attachmentType, originalFileName, fileUrl).getOrNull()
                ?: return ResponseDto.error<Any>(errorMessage)

            when (attachmentType) {
                AttachmentType.ID_CARD -> {
                    val ocrResult: WxOcrIdCardResult = WeixinExt.idCardOcr(newFile)
                    ResponseDto.success(IdCardUploadResponseDto.fromDomain(attachment, ocrResult))
                }

                AttachmentType.BANK_CARD,
                AttachmentType.BIRTH_CERTIFICATE,
                AttachmentType.MEDICAL_INSURANCE_CARD,
                AttachmentType.HOUSEHOLD_REGISTER -> {
                    ResponseDto.success(SimpleUploadResponseDto.fromDomain(attachment))
                }
            }
        } catch (e: Exception) {
            logger.error("File processing failed", e)
            FileUtils.deleteQuietly(newFile)
            ResponseDto.error<Any>(errorMessage)
        }
    }

    /**
     * 通用文件上传端点
     * 支持所有附件类型，通过路径变量指定类型
     * @param type 附件类型，示例：id-card, bank-card, birth-certificate, medical-insurance-card, household-register
     * @param file 上传的文件
     * @return 处理结果
     */
    @PostMapping(
        "/{type}",
        consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun uploadFile(
        @PathVariable type: String,
        @RequestParam("file") file: MultipartFile
    ): ResponseDto<*> {
        val attachmentType = try {
            AttachmentType.valueOf(type.replace('-', '_').uppercase())
        } catch (_: IllegalArgumentException) {
            return ResponseDto.error<Any>("不支持的附件类型: $type")
        }

        val errorMessage = when (attachmentType) {
            AttachmentType.ID_CARD -> "身份证上传失败，请重试"
            AttachmentType.BANK_CARD -> "银行卡上传失败，请重试"
            AttachmentType.BIRTH_CERTIFICATE -> "出生证上传失败，请重试"
            AttachmentType.MEDICAL_INSURANCE_CARD -> "医保卡上传失败，请重试"
            AttachmentType.HOUSEHOLD_REGISTER -> "户口本上传失败，请重试"
        }

        return processFileUpload(file, attachmentType, errorMessage)
    }

    @Deprecated("Use /api/attachment/{type} instead")
    @Suppress("UNCHECKED_CAST")
    @PostMapping(
        "/idCard",
        consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun idCard(
        @RequestParam("file") file: MultipartFile
    ): ResponseDto<IdCardUploadResponseDto?> {
        return processFileUpload(
            file = file,
            attachmentType = AttachmentType.ID_CARD,
            errorMessage = "身份证上传失败，请重试"
        ) as ResponseDto<IdCardUploadResponseDto?>
    }

    @Deprecated("Use /api/attachment/{type} instead")
    @Suppress("UNCHECKED_CAST")
    @PostMapping(
        "/bankCard",
        consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun bankCard(
        @RequestParam("file") file: MultipartFile
    ): ResponseDto<SimpleUploadResponseDto?> {
        return processFileUpload(
            file = file,
            attachmentType = AttachmentType.BANK_CARD,
            errorMessage = "银行卡上传失败，请重试"
        ) as ResponseDto<SimpleUploadResponseDto?>
    }

    @Deprecated("Use /api/attachment/{type} instead")
    @Suppress("UNCHECKED_CAST")
    @PostMapping(
        "/birthCertificate",
        consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @RequireSession
    @Transactional
    fun birthCertificate(
        @RequestParam("file") file: MultipartFile
    ): ResponseDto<SimpleUploadResponseDto?> {
        return processFileUpload(
            file = file,
            attachmentType = AttachmentType.BIRTH_CERTIFICATE,
            errorMessage = "出生证上传失败，请重试"
        ) as ResponseDto<SimpleUploadResponseDto?>
    }

}
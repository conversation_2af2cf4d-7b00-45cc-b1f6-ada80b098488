package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.witontek.PriceInformationRequest
import org.mospital.witontek.WitontekService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/open/material")
class MaterialApiController : BaseController() {

    @GetMapping("/priceInformation")
    fun queryPriceInformation(@RequestParam(value = "keyword", defaultValue = "") keyword: String): AjaxResult {
        val resp = WitontekService.me.queryPriceInformation(PriceInformationRequest().apply {
            this.keyword = keyword
        })

        if (!resp.isOk()) {
            return AjaxResult.error("未查询到相关信息")
        }

        return AjaxResult.success(resp.results)
    }

}
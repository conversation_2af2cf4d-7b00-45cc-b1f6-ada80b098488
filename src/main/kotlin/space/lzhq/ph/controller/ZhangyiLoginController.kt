package space.lzhq.ph.controller

import cn.hutool.core.text.CharSequenceUtil
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.utils.ServletUtils
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.apache.shiro.SecurityUtils
import org.apache.shiro.authc.AuthenticationException
import org.apache.shiro.authc.UsernamePasswordToken
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.ResponseBody

@Controller
class ZhangyiLoginController : BaseController() {

    @Value("\${shiro.rememberMe.enabled: false}")
    private val rememberMe = false

    @GetMapping("/login")
    fun login(request: HttpServletRequest, response: HttpServletResponse, modelMap: ModelMap): String {
        if (ServletUtils.isAjaxRequest(request)) {
            return ServletUtils.renderString(response, """{"code":"1","msg":"未登录或登录超时。请重新登录"}""")
        }

        modelMap["isRemembered"] = rememberMe
        return "zhangyi-login"
    }

    @PostMapping("/login")
    @ResponseBody
    fun ajaxLogin(username: String, password: String, rememberMe: Boolean): AjaxResult {
        val token = UsernamePasswordToken(username, password, rememberMe)
        val subject = SecurityUtils.getSubject()
        try {
            subject.login(token)
            return success()
        } catch (e: AuthenticationException) {
            val msg: String = if (CharSequenceUtil.isBlank(e.message)) "用户或密码错误" else e.message!!
            return error(msg)
        }
    }

    @GetMapping("/unauth")
    fun unauth(): String {
        return "error/unauth"
    }
}
package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.shine.ShineServices
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.getCurrentPatient

@RestController
@RequestMapping("/api/menzhen")
class MenzhenController : BaseController() {

    /**
     * 排队候诊
     */
    @GetMapping("queue")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getQueue(): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val queueResponse = ShineServices.getPatientInfo(currentPatient.cardNo)
        return AjaxResult.success(queueResponse.data)
    }

}

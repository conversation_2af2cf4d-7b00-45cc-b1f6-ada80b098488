package space.lzhq.ph.controller

import cn.hutool.core.date.DateField
import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUnit
import cn.hutool.core.date.DateUtil
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.witontek.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.ValidDepartmentCode
import space.lzhq.ph.annotation.ValidDoctorCode
import space.lzhq.ph.annotation.ValidScheduleId
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Reservation
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IReservationService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 预约
 */
@RestController
@RequestMapping("/api/appointment")
@Validated
class AppointmentApiController : BaseController() {

    @Autowired
    private lateinit var reservationService: IReservationService

    @RequestMapping("add")
    @RequireActivePatient
    fun addAppointment(
        @RequestParam("appointmentId") @ValidScheduleId appointmentId: String,
        @RequestParam("sourceNo") sourceNo: String,
        @RequestParam("sourceOrder") sourceOrder: String,
        @RequestParam("departmentName") departmentName: String,
        @RequestParam("departmentCode") @ValidDepartmentCode departmentCode: String,
        @RequestParam("doctorName") doctorName: String,
        @RequestParam("doctorCode") @ValidDoctorCode doctorCode: String,
        @RequestParam("clinicType") clinicType: String,
        @RequestParam("clinicDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) clinicDate: LocalDate,
        @RequestParam("clinicTime") @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN) clinicTime: LocalDateTime,
        @RequestParam("clinicFee") clinicFee: String,
    ): AjaxResult {
        if (clinicTime.isBefore(LocalDateTime.now())) {
            return AjaxResult.error("预约日期不能早于当前日期")
        }

        val currentPatient: Patient = request.getCurrentPatient()

        synchronized(currentPatient.openId.intern()) {
            val reservations = reservationService.lambdaQuery()
                .eq(Reservation::getPatientId, currentPatient.cardNo)
                .eq(Reservation::getDepartmentCode, departmentCode)
                .eq(Reservation::getDoctorCode, doctorCode)
                .eq(Reservation::getStatus, Reservation.RESERVATION_STATUS_INIT)
                .eq(Reservation::getOperationResult, Reservation.OPERATION_RESULT_SUCCESS)
                .gt(Reservation::getJzTime, DateUtil.date())
                .list()
            val clinicTimeAsDate = DateUtil.date(clinicTime)
            val minIntervalInMinutes = 60 * 72  // 72小时
            for (reservation in reservations) {
                if (DateUtil.between(
                        reservation.jzTime,
                        clinicTimeAsDate,
                        DateUnit.MINUTE,
                        true
                    ) < minIntervalInMinutes
                ) {
                    return AjaxResult.error(
                        "已存在有效预约：${
                            DateUtil.format(
                                reservation.jzTime,
                                DatePattern.NORM_DATETIME_MINUTE_PATTERN
                            )
                        }"
                    )
                }
            }

            val queryMenzhenPatientResponse: QueryMenzhenPatientResponse = WitontekService.me.queryMenzhenPatient(
                QueryMenzhenPatientRequest().apply {
                    this.jzCardNo = currentPatient.jzCardNo
                }
            )
            if (!queryMenzhenPatientResponse.isOk()) {
                return AjaxResult.error(queryMenzhenPatientResponse.message)
            }
            if (!queryMenzhenPatientResponse.isValid()) {
                return AjaxResult.error("就诊账户状态异常")
            }

            val appointmentResponse = WitontekService.me.addAppointment(
                AddAppointmentRequest().apply {
                    this.scheduleId = appointmentId
                    this.sourceNo = sourceNo
                    this.sourceOrder = sourceOrder
                    this.realName = currentPatient.name
                    this.idCard = currentPatient.idCardNo
                    this.patientCard = currentPatient.cardNo
                    this.departmentName = departmentName
                    this.departmentCode = departmentCode
                    this.doctorName = doctorName
                    this.doctorCode = doctorCode
                    this.clinicType = clinicType
                    this.clinicDate = clinicDate
                    this.clinicTime = clinicTime
                    this.clinicFee = clinicFee
                }
            )


            val reservation = Reservation()
            reservation.operationType = Reservation.OPERATION_TYPE_RESERVE
            reservation.operationTime = DateUtil.date().setField(DateField.MILLISECOND, 0)
            reservation.jzDate = DateUtil.date(clinicDate)
            reservation.jzTime = DateUtil.date(clinicTime)
            reservation.jzCardNo = currentPatient.jzCardNo
            reservation.departmentCode = departmentCode
            reservation.departmentName = departmentName
            reservation.doctorCode = doctorCode
            reservation.doctorName = doctorName
            reservation.reservationType = clinicType
            reservation.registerFee = BigDecimal(clinicFee)
            reservation.schid = appointmentResponse.appointmentId
            reservation.patientId = currentPatient.cardNo
            reservation.idCardNo = currentPatient.idCardNo
            reservation.name = currentPatient.name
            reservation.ampm = if (clinicTime.hour < 12) "上午" else "下午"
            reservation.status = Reservation.RESERVATION_STATUS_INIT
            reservation.mobile = currentPatient.mobile
            if (appointmentResponse.isOk()) {
                reservation.visitNumber = sourceOrder
                reservation.clinicNo = sourceNo
                reservation.operationResult = Reservation.OPERATION_RESULT_SUCCESS
                reservation.operationDesc = "预约成功"
                reservationService.save(reservation)

                return AjaxResult.success(reservation)
            } else {
                reservation.visitNumber = ""
                reservation.clinicNo = ""
                reservation.operationResult = Reservation.OPERATION_RESULT_FAIL
                reservation.operationDesc = appointmentResponse.message
                reservationService.save(reservation)

                return AjaxResult.error(appointmentResponse.message)
            }
        }
    }

    @RequestMapping("cancel")
    @RequireActivePatient
    fun cancelAppointment(
        @RequestParam("appointmentRecordId") appointmentRecordId: Long
    ): AjaxResult {

        val reservation: Reservation = reservationService.getById(appointmentRecordId)
            ?: return AjaxResult.error("预约记录不存在")
        if (!reservation.isCanCancel) {
            return AjaxResult.error("当前预约记录不允许取消")
        }

        val currentPatient: Patient = request.getCurrentPatient()
        if (reservation.patientId != currentPatient.cardNo) {
            return AjaxResult.error("预约记录不存在")
        }

        val response = WitontekService.me.cancelAppointment(
            AddCancelAppointmentRequest().apply {
                this.appointmentId = reservation.schid
            }
        )

        return if (response.isOk()) {
            reservation.status = Reservation.RESERVATION_STATUS_CANCELED
            reservationService.updateById(reservation)

            val cancelReservation: Reservation = reservation.toCancel()
            cancelReservation.operationResult = Reservation.OPERATION_RESULT_SUCCESS
            cancelReservation.operationDesc = "取消预约成功"
            reservationService.save(cancelReservation)
            AjaxResult.success(cancelReservation)
        } else {
            AjaxResult.error(response.message)
        }
    }


    @RequestMapping("history")
    @RequireActivePatient
    fun list(): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val reservations: List<Reservation> = reservationService.lambdaQuery()
            .eq(Reservation::getPatientId, currentPatient.cardNo)
            .eq(Reservation::getOperationResult, Reservation.OPERATION_RESULT_SUCCESS)
            .eq(Reservation::getStatus, Reservation.RESERVATION_STATUS_INIT)
            .orderByDesc(Reservation::getOperationTime)
            .list()
        return AjaxResult.success(reservations)
    }

    @GetMapping("detail")
    @RequireActivePatient
    fun detail(@RequestParam(name = "appointmentRecordId", required = false) reservationId: Long): AjaxResult {
        val reservation: Reservation = reservationService.getById(reservationId)
            ?: return AjaxResult.error("预约记录不存在")

        if (reservation.patientId != request.getCurrentPatient().cardNo) {
            return AjaxResult.error("预约记录不存在")
        }

        val statusResp = WitontekService.me.queryClinicStatus(QueryClinicStatusRequest().apply {
            this.appointmentId = reservation.schid
        })

        if (statusResp.isOk()) {
            reservation.clinicStatus = statusResp.content.lastOrNull()?.status ?: "0"
        }
        return AjaxResult.success(reservation)
    }
}
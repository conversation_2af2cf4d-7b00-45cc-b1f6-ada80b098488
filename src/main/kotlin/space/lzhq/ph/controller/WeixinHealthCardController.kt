package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.tencent.healthcard.TencentHealthCardService
import org.mospital.witontek.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IPatientService

@RestController
@RequestMapping("/api/wx-health-card")
class WeixinHealthCardController: BaseController() {

    @Autowired
    private lateinit var patientService: IPatientService

    /**
     * 查询或注册健康卡
     * @param healthCode 健康卡号
     */
    @PostMapping("getOrRegister")
    @RequireSession
    fun getOrRegister(
        @RequestParam("healthCode") healthCode: String,
    ): AjaxResult {
        val session: Session = request.getClientSession()!!
        val witontekService = HisExt.getWitontekServiceByClientType(session.clientTypeEnum)

        synchronized(healthCode.intern()) {
            val healthCardResult = TencentHealthCardService.getHealthCardByHealthCode(
                healthCode = healthCode,
            )
            if (!healthCardResult.isOk()) {
                return AjaxResult.error(healthCardResult.msg)
            }

            if (healthCardResult.data?.card == null) {
                return AjaxResult.error("未找到健康卡信息")
            }

            val healthCard = healthCardResult.data?.card!!

            val erhcPatient = patientService.selectPatientListByIdCard(healthCard.idNumber)
                .firstOrNull { it.hasErhcCard() && it.clientTypeEnum == session.clientTypeEnum
                        && it.openId == session.openId }

            if (erhcPatient != null) {
                return AjaxResult.success(erhcPatient)
            }

            val addPatientCardResponse: AddPatientCardResponse = witontekService.addPatientCard(
                AddPatientCardRequest().apply {
                    this.realName = healthCard.name
                    this.mobile = healthCard.phone1
                    this.idCardNo = healthCard.idNumber
                    this.country = "中国"
                    this.nation = healthCard.nation
                    this.sex = healthCard.gender
                    this.address = if (healthCard.address.isNullOrEmpty()) "未知" else healthCard.address
                    this.erhcQrCode = healthCard.qrCodeText

                }
            )
            if (!addPatientCardResponse.isOk()) {
                return AjaxResult.error(addPatientCardResponse.message)
            }

            val queryMenzhenPatientResponse: QueryMenzhenPatientResponse = witontekService.queryMenzhenPatient(
                QueryMenzhenPatientRequest().apply {
                    this.jzCardNo = addPatientCardResponse.cardNo
                }
            )
            if (!queryMenzhenPatientResponse.isOk()) {
                return AjaxResult.error(queryMenzhenPatientResponse.message)
            }

            val patient = Patient(session, queryMenzhenPatientResponse, healthCard.phone1)
            patient.active = if (patientService.selectPatientListByOpenId(session.openId).isEmpty()) 1 else 0
            patient.jzCardNo = addPatientCardResponse.cardNo
            patient.empi = healthCard.phid
            patient.erhcCardNo = healthCard.healthCardId
            patient.nation = healthCard.nation
            patientService.insertPatient(patient)

            return AjaxResult.success(patient)
        }
    }
}
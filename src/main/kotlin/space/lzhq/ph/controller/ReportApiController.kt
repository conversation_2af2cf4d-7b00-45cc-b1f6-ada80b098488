package space.lzhq.ph.controller

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.ruoyi.common.core.domain.AjaxResult
import jakarta.servlet.http.HttpServletRequest
import org.mospital.common.PatientDataToken
import org.mospital.common.TokenManager
import org.mospital.witontek.QueryLISReportRequest
import org.mospital.witontek.QueryPACSReportRequest
import org.mospital.witontek.QueryReportListRequest
import org.mospital.witontek.QueryReportListResponse
import org.mospital.witontek.WitontekService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.dto.ReportDto
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IPatientService
import java.time.LocalDate

@RestController
@RequestMapping("/api/report")
class ReportApiController {

    @Autowired
    private lateinit var patientService: IPatientService

    @GetMapping("list/v1")
    @RequireActivePatient
    fun listV1(
        @RequestParam(value = "startDate", required = false) startDate: LocalDate? = null,
        @RequestParam(value = "endDate", required = false) endDate: LocalDate? = null,
        @RequestParam(value = "reportType", required = true) reportType: String,
        @RequestParam(value = "cardNo", required = true) cardNo: String,
        request: HttpServletRequest
    ): AjaxResult {
        val session = request.getClientSession() ?: return AjaxResult.error("会话无效")
        val openId = session.openId
        var id: String
        when (reportType) {
            "1" -> {
                if (patientService.selectPatientListByOpenId(openId).none { it.cardNo == cardNo }) {
                    return AjaxResult.error("访问受限")
                }
                // 门诊号直接使用cardNo
                id = cardNo
            }
            "2" -> {
                val patientDataToken = PatientDataToken.parse(cardNo) ?: return AjaxResult.error("访问受限")
                val hospitalizedId = patientDataToken.dataId
                // 住院号需要转换
                id = HospitalizedApiController.getShortInpatientId(hospitalizedId)
            }
            else -> {
                return AjaxResult.error("参数无效：reportType=$reportType")
            }
        }

        val resp = WitontekService.me.queryReportList(QueryReportListRequest().apply {
            this.realName = ""
            this.queryId = id
            this.startDate = startDate ?: LocalDate.now().minusMonths(6)
            this.endDate = (endDate ?: LocalDate.now()).plusDays(1) // 只能查询到 endDate 的前一天
            this.reportType = reportType
        })

        if (!resp.isOk()) {
            return AjaxResult.error(resp.message)
        }

        return AjaxResult.success(resp.message, resp.results.map { ReportDto(it) })
    }

    @GetMapping("detailLISV1")
    @RequireActivePatient
    fun getLISDetailV1(
        @RequestParam(value = "reportToken", required = true) reportToken: String
    ): AjaxResult {
        val reportListItem: QueryReportListResponse.ReportListItem = TokenManager.parseToken(
            token = reportToken,
            jacksonTypeRef()
        ) ?: return AjaxResult.error("参数无效")
        val resp = WitontekService.me.queryLISReportDetail(QueryLISReportRequest().apply {
            this.reportId = reportListItem.reportId
            this.checkType = "1"
        })

        if (!resp.isOk()) {
            return AjaxResult.error(resp.message)
        }

        return AjaxResult.success(resp.message, resp.results)
    }

    @GetMapping("detailPACSV1")
    @RequireActivePatient
    fun getPACSDetailV1(
        @RequestParam(value = "reportToken", required = true) reportToken: String
    ): AjaxResult {
        val reportListItem: QueryReportListResponse.ReportListItem = TokenManager.parseToken(
            token = reportToken,
            jacksonTypeRef()
        ) ?: return AjaxResult.error("参数无效")
        val resp = WitontekService.me.queryPACSReportDetail(QueryPACSReportRequest().apply {
            this.reportId = reportListItem.reportId
            this.checkType = "2"
        })

        if (!resp.isOk()) {
            return AjaxResult.error(resp.message)
        }

        return AjaxResult.success(resp.message, resp.results)
    }

}

package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import org.dromara.hutool.log.Log
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.ext.KeyUtil
import java.io.File

@RestController
@RequestMapping("/open/ssl")
class SSLController {
    private val log: Log = Log.get()

    @Value("\${app.domain}")
    private lateinit var domain: String

    @Value("\${app.authCode}")
    private lateinit var authCode: String

    // Nginx证书路径
    @Value("\${nginx.ssl.cert.path:/www/server/panel/vhost/cert/\${app.domain}.cer}")
    private lateinit var nginxCertPath: String

    // Nginx私钥路径
    @Value("\${nginx.ssl.key.path:/www/server/panel/vhost/cert/\${app.domain}.key}")
    private lateinit var nginxKeyPath: String

    data class RenewSSLRequest(
        @NotBlank
        val domain: String,
        @NotBlank
        val certificate: String,
        @NotBlank
        val privateKey: String,
        @NotBlank
        val authCode: String
    )

    @PostMapping
    fun renewSSL(@Valid @RequestBody request: RenewSSLRequest): AjaxResult {
        log.info("renewSSL: $request")

        if (request.authCode != authCode) {
            return AjaxResult.error("认证失败")
        }

        if (request.domain != domain) {
            return AjaxResult.error("域名不匹配")
        }

        return try {
            // 为Nginx生成cer和key文件
            val nginxCertFile = File(nginxCertPath)
            val nginxKeyFile = File(nginxKeyPath)
            KeyUtil.generateNginxSSLFiles(
                certificateString = request.certificate,
                privateKeyString = request.privateKey,
                certificateFile = nginxCertFile,
                privateKeyFile = nginxKeyFile
            )

            reloadNginx()
            
            // 返回成功结果
            AjaxResult.success(mapOf(
                "cerPath" to nginxCertFile.absolutePath,
                "keyPath" to nginxKeyFile.absolutePath
            ))
        } catch (e: Exception) {
            log.error("证书处理失败", e)
            AjaxResult.error("证书处理失败: ${e.message}")
        }
    }

    private fun reloadNginx() {
        // 重新加载Nginx配置
        try {
            log.info("重新加载Nginx配置...")
            val processBuilder = ProcessBuilder("nginx", "-s", "reload")
            val process = processBuilder.start()
            val exitCode = process.waitFor()
            if (exitCode == 0) {
                log.info("Nginx配置重新加载成功")
            } else {
                val errorMsg = process.errorStream.bufferedReader().use { it.readText() }
                log.warn("Nginx配置重新加载失败，退出码: $exitCode, 错误信息: $errorMsg")
            }
        } catch (e: Exception) {
            log.error("执行Nginx重新加载命令失败", e)
            // 不阻止流程继续，仅记录错误
        }
    }
    
}
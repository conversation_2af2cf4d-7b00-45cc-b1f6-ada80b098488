package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.bosi.ebill.EBillRequest
import org.mospital.bosi.ebill.EBillService
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.getCurrentPatient

@RestController
@RequestMapping("/api/ebill")
class EBillApiController : BaseController() {

    @RequestMapping("/getUrl")
    @RequireActivePatient
    fun getEBillUrl(): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        val data = mapOf("url" to EBillService.queryEBillUrl(EBillRequest(name = currentPatient.name, idCard = currentPatient.idCardNo, phone = currentPatient.mobile)))
        return AjaxResult.success(data)
    }
}
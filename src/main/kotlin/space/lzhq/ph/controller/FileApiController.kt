package space.lzhq.ph.controller

import com.ruoyi.common.config.RuoYiConfig
import com.ruoyi.common.config.ServerConfig
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.utils.file.MimeTypeUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.multipart.MultipartFile
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.ext.FileUploader

@Controller
class FileApiController {

    companion object {
        private val log = LoggerFactory.getLogger(FileApiController::class.java)
    }

    @Autowired
    private lateinit var serverConfig: ServerConfig

    @RequireSession
    @PostMapping("/api/upload")
    @ResponseBody
    fun upload(file: MultipartFile): AjaxResult {
        try {
            val newFileName = FileUploader.upload(
                file = file,
                baseDir = RuoYiConfig.getUploadPath(),
                allowedExtension = MimeTypeUtils.IMAGE_EXTENSION
            )
            return AjaxResult.success(
                mapOf(
                    "url" to serverConfig.baseUrl + newFileName
                )
            )
        } catch (e: Exception) {
            log.error("上传文件失败：", e)
            return AjaxResult.error("上传文件失败：${e.message}")
        }
    }

}

package space.lzhq.ph.controller

import cn.hutool.core.io.FileUtil
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import org.apache.shiro.authz.annotation.Logical
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.mospital.common.IdUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.domain.WxCheck
import space.lzhq.ph.domain.WxCheckReport
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IWxCheckService

/**
 * 对账Controller
 *
 * <AUTHOR>
 * @date 2020-06-09
 */
@Controller
@RequestMapping("/ph/check")
class WxCheckAdminController : BaseController() {

    @Autowired
    private lateinit var wxCheckService: IWxCheckService

    @RequiresPermissions("ph:check:view")
    @GetMapping
    fun check(modelMap: ModelMap): String {
        modelMap.addAttribute("user", sysUser);
        return "ph/check/check"
    }

    @RequiresPermissions("ph:check:reportView")
    @GetMapping("/report")
    fun report(): String {
        return "ph/check/report"
    }

    /**
     * 查询对账列表
     */
    @RequiresPermissions("ph:check:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(wxCheck: WxCheck?): TableDataInfo {
        startPage()
        val list = wxCheckService.selectWxCheckList(wxCheck)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:check:reportList")
    @PostMapping("/reportList")
    @ResponseBody
    fun reportList(wxCheck: WxCheck?): TableDataInfo {
        startPage()
        val list: List<WxCheck> = wxCheckService.selectWxCheckList(wxCheck)
        return getDataTable(list).apply { rows = list.map(::WxCheckReport) }
    }

    /**
     * 同步微信医保账单
     */
    @RequiresPermissions("ph:check:syncWxMipBill")
    @PostMapping("/syncWxMipBill/{id}")
    @ResponseBody
    fun syncWxMipBill(@PathVariable("id") id: Long): AjaxResult {
        val billDate = IdUtil.idToLocalDate(id)
        val billFile = WeixinExt.getOrDownloadMipBillFile(billDate)
        val ok = wxCheckService.syncWxMipBill(billDate, billFile)
        return toAjax(ok)
    }

    /**
     * 下载微信医保账单
     */
    @RequiresPermissions(
        value = ["ph:check:downloadWxMipBill", "ph:check:reportDownloadWxMipBill"],
        logical = Logical.OR
    )
    @GetMapping("/downloadWxMipBill/{id}")
    @ResponseBody
    fun downloadWxMipBill(@PathVariable("id") id: Long): ResponseEntity<StreamingResponseBody> {
        val billDate = IdUtil.idToLocalDate(id)
        val billFile = WeixinExt.getOrDownloadMipBillFile(billDate)
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("text/csv"))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=weixin_yibao_bill_${billDate}.csv")
            .body(StreamingResponseBody {
                try {
                    FileUtil.writeToStream(billFile, it)
                } catch (e: Exception) {
                    it.write("Unknown error occurred".toByteArray())
                }
            })
    }

}

package space.lzhq.ph.controller

import cn.hutool.core.util.IdcardUtil
import cn.hutool.log.LogFactory
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import jakarta.servlet.http.HttpServletRequest
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.common.PatientDataToken
import org.mospital.dongruan.pay.DongruanPayService
import org.mospital.dongruan.pay.UnifiedOrderForm
import org.mospital.witontek.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.Constants
import space.lzhq.ph.common.OptimisticLockException
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.OrderQueryResult
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.dto.InpatientInfo
import space.lzhq.ph.dto.InpatientInfoDto
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IOrderQueryService
import space.lzhq.ph.service.IWxPaymentService
import space.lzhq.ph.service.OrderSerializationService
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import kotlin.random.Random

@RestController
@RequestMapping("/api/hospitalized")
class HospitalizedApiController : BaseController() {

    companion object {
        private val log = LogFactory.get()

        private val ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT: String = "身份证号或住院号不正确"

        fun getShortInpatientId(longInpatientId: String): String {
            return if (longInpatientId.startsWith("ZY") && longInpatientId.length == 14) {
                longInpatientId.substring(4)
            } else if (longInpatientId.length == 10) {
                longInpatientId
            } else {
                throw IllegalArgumentException(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
            }
        }

        fun isLongInpatientId(longInpatientId: String): Boolean {
            return if (longInpatientId.startsWith("ZY") && longInpatientId.length == 14) {
                true
            } else {
                log.info("这里需要14位住院号: $longInpatientId")
                false
            }
        }
    }

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var orderQueryService: IOrderQueryService

    @Autowired
    private lateinit var orderSerializationService: OrderSerializationService

    /**
     * 查询住院一日清
     */
    @RequestMapping("/getDayExpenseV2")
    fun getDayExpenseV2(
        @RequestParam("idCard", required = false) idCard: String,
        @RequestParam("hospitalizedId", required = true) longInpatientId: String,
        @RequestParam("date", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate? = null,
    ): AjaxResult {
        val shortInpatientId = try {
            getShortInpatientId(longInpatientId)
        } catch (e: IllegalArgumentException) {
            return AjaxResult.error(e.message)
        }

        val response = WitontekService.me.queryDayExpenseV2(QueryDayExpenseRequestV2().apply {
            this.queryId = shortInpatientId
            this.idNo = idCard
            this.date = date ?: LocalDate.now()
        })

        if (!response.isOk()) {
            return AjaxResult.error(response.message)
        }

        return AjaxResult.success(
            response.message, mapOf(
                "totalInDay" to response.results.sumOf { it.amount.toBigDecimal() },
                "results" to response.results
            )
        )

    }

    /**
     * 查询住院费用
     */
    @RequestMapping("/getPatientExpense/v1")
    fun queryPatientExpenseV1(
        @RequestParam("hospitalizedId", required = true) longInpatientId: String,
        @RequestParam("idCardNo", required = true) idCardNo: String
    ): AjaxResult {
        val shortInpatientId = try {
            getShortInpatientId(longInpatientId)
        } catch (e: IllegalArgumentException) {
            return AjaxResult.error(e.message)
        }

        val inpatientResponse = WitontekService.me.queryInpatient(InpatientInfoRequest().apply {
            this.serialNumber = shortInpatientId
        })

        if (!inpatientResponse.isOk()
            || inpatientResponse.idCard != idCardNo
            || IdcardUtil.isValidCard(idCardNo).not()
        ) {
            return AjaxResult.error(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
        }

        val response = WitontekService.me.queryPatientExpense(QueryPatientExpenseRequest().apply {
            this.realName = ""
            this.queryId = shortInpatientId
        })

        if (!response.isOk()) {
            return AjaxResult.error(response.message)
        }

        return AjaxResult.success(
            mapOf(
                "prepayAmt" to response.prepayAmt,
                "usedAmt" to response.usedAmt,
                "remainAmt" to response.remainAmt,
            )
        )
    }

    @GetMapping("/info/v1")
    fun getInpatientInfoV1(
        @RequestParam("hospitalizedId") shortInpatientId: String,
        @RequestParam("idCardNo") idCardNo: String
    ): AjaxResult {
        val resp = WitontekService.me.queryInpatient(InpatientInfoRequest().apply {
            this.serialNumber = shortInpatientId
        })

        if (!resp.isOk() || resp.idCard != idCardNo || IdcardUtil.isValidCard(idCardNo).not()) {
            return AjaxResult.error(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
        }

        return AjaxResult.success(resp)
    }

    @GetMapping("/info/v2")
    fun getInpatientInfoV2(
        @RequestParam("hospitalizedId") hospitalizedId: String,
        @RequestParam(value = "idCardNo", required = false, defaultValue = "") idCardNo: String,
        @RequestParam(value = "name", required = false, defaultValue = "") name: String
    ): AjaxResult {
        if (idCardNo.isBlank() && name.isBlank()) {
            return AjaxResult.error("身份证号和姓名不能同时为空")
        }

        val shortInpatientId = try {
            getShortInpatientId(hospitalizedId)
        } catch (e: IllegalArgumentException) {
            return AjaxResult.error(e.message)
        }

        val resp = WitontekService.me.queryInpatient(InpatientInfoRequest().apply {
            this.serialNumber = shortInpatientId
        })

        if (!resp.isOk()) {
            return AjaxResult.error(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
        }

        if (idCardNo.isNotBlank() && idCardNo != resp.idCard) {
            return AjaxResult.error(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
        }

        if (name.isNotBlank() && !HisExt.matchesChinese(resp.realName.orEmpty(), name)) {
            return AjaxResult.error(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
        }

        val inpatientInfo = InpatientInfo.fromWitontekInpatientInfoResponse(resp)
            ?: return AjaxResult.error("解析住院信息失败")
        return AjaxResult.success(InpatientInfoDto(inpatientInfo))
    }

    @GetMapping("/infoList/v1")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun queryInpatientInfoListV1(
        @RequestParam("hospitalizedId") shortInpatientId: String,
        @RequestParam("idCardNo") idCardNo: String
    ): AjaxResult {
        val singleResp = WitontekService.me.queryInpatient(InpatientInfoRequest().apply {
            this.serialNumber = shortInpatientId
        })

        if (!singleResp.isOk() || singleResp.idCard != idCardNo || IdcardUtil.isValidCard(idCardNo).not()) {
            return AjaxResult.error(ID_CARD_NO_OR_HOSPITALIZED_ID_NOT_CORRECT)
        }

        val resp = WitontekService.me.queryInpatientList(InpatientInfoListRequest().apply {
            this.realName = singleResp.realName
            this.idCard = singleResp.idCard
            this.serialNumber = shortInpatientId
        })

        return if (resp.isOk()) {
            AjaxResult.success(resp.inpatientInfoList)
        } else {
            AjaxResult.error(resp.message)
        }
    }

    /**
     * 充值
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("wxpay/v1")
    @RequireSession
    fun wxpayV1(
        @RequestParam("amount") amount: Long,
        @RequestParam("token") token: String,
        request: HttpServletRequest
    ): AjaxResult {
        val patientDataToken = PatientDataToken.parse(token)
            ?: return AjaxResult.error("访问受限")

        if (!request.getClientSession()!!.isWeixinSession) {
            return AjaxResult.error("请使用微信登录")
        }

        val session = request.getClientSession()

        if (amount < 100) {
            return AjaxResult.error("最小充值金额为1元")
        }

        if (amount > 2000000) {
            return AjaxResult.error("最大充值金额为20000元")
        }

        val longInpatientId = patientDataToken.dataId
        val shortInpatientId = try {
            getShortInpatientId(longInpatientId)
        } catch (e: IllegalArgumentException) {
            return AjaxResult.error(e.message)
        }
        val inpatientResp = WitontekService.me.queryInpatient(InpatientInfoRequest().apply {
            this.serialNumber = shortInpatientId
        })

        if (!inpatientResp.isOk() || inpatientResp.status.isNullOrEmpty()) {
            return AjaxResult.error("未查询到住院信息")
        }

        val zhuyuanRecordsResp = WitontekService.me.queryInpatientList(InpatientInfoListRequest().apply {
            this.realName = inpatientResp.realName
            this.idCard = inpatientResp.idCard
            this.serialNumber = shortInpatientId
        })
        if (!zhuyuanRecordsResp.isOk() || zhuyuanRecordsResp.inpatientInfoList.isNullOrEmpty()) {
            return AjaxResult.error("未查询到住院信息")
        }
        val zhuyuanRecord = zhuyuanRecordsResp.inpatientInfoList!!.firstOrNull {
            it.inpatientNo == longInpatientId
        }
        if (zhuyuanRecord == null) {
            return AjaxResult.error("未查询到住院信息")
        }
        if (!zhuyuanRecord.isWeiJieSuan()) {
            return AjaxResult.error("已办理出院结算，不能进行预缴")
        }

        val openid: String = request.getClientSession()!!.openId
        val mockPatient: Patient = Patient().also {
            it.openId = openid
            it.accountNo = ""
            it.jzCardNo = ""
            it.idCardNo = inpatientResp.idCard
            it.zhuyuanNo = longInpatientId
            it.clientType = session!!.clientType
            it.name = inpatientResp.realName
        }
        val ip: String = ServletUtil.getClientIP(request)
        val serviceType = ServiceType.ZY
        val clientType = session!!.clientType

        val outTradeNo = UnifiedOrderForm.newOrderNo()
        val form = UnifiedOrderForm(openid = mockPatient.openId)
            .orderNo(outTradeNo)
            .orderType(1)
            .payWayType(if (clientType == ClientType.WEIXIN_MP.code) 32 else 35)
            .subject("${outTradeNo}-$longInpatientId-住院预缴")
            .amount(BigDecimal(amount).setScale(2, RoundingMode.HALF_DOWN).divide(BigDecimal(100)))
            .notifyUrl("https://xjzybyy.xjyqtl.cn/open/wx/onDongruanPay")
            .clientIp(ip)
        form.sign()
        val result = runBlocking {
            DongruanPayService.me.createUnifiedOrder(form)
        }

        if (result.isOk()) {
            wxPaymentService.createAndSave(serviceType, mockPatient, outTradeNo, amount, "")
        } else {
            return AjaxResult.error(result.message)
        }

        return AjaxResult.success(
            mapOf(
                "wxPayResult" to result,
                "outOrderNo" to outTradeNo
            )
        )
    }

    /**
     * 刷新订单状态 - 使用非阻塞订单检测优化版本
     */
    @PostMapping("refreshWxPayOrder")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun refreshWxPayOrder(@RequestParam("outOrderNo") outOrderNo: String): AjaxResult {
        return runBlocking {
            // 使用非阻塞方式尝试处理订单
            val result = orderSerializationService.tryExecuteWithOrderLock(outOrderNo) {
                // 最大重试次数
                val maxRetries = 3
                var currentRetry = 0

                while (currentRetry < maxRetries) {
                    try {
                        // 重新查询最新数据（包含版本号）
                        val payment: WxPayment = wxPaymentService.selectWxPaymentByZyPayNo(outOrderNo)
                            ?: return@tryExecuteWithOrderLock AjaxResult.error("订单不存在[$outOrderNo]")

                        // 检查订单状态
                        if (!payment.wxPayNo.isNullOrBlank() && payment.hisTradeStatus == Constants.RECHARGE_OK) {
                            return@tryExecuteWithOrderLock AjaxResult.success(payment)
                        }

                        // 查询并更新订单状态
                        val queryResult = orderQueryService.queryAndUpdateOrderWithRecharge(payment)

                        return@tryExecuteWithOrderLock when (queryResult) {
                            is OrderQueryResult.Success -> AjaxResult.success(queryResult.updatedPayment)
                            is OrderQueryResult.Failure -> AjaxResult.error(queryResult.message)
                        }
                    } catch (e: OptimisticLockException) {
                        // 乐观锁冲突，重试
                        currentRetry++
                        if (currentRetry >= maxRetries) {
                            return@tryExecuteWithOrderLock AjaxResult.error("系统繁忙，请稍后重试")
                        }

                        // 等待一小段时间再重试
                        delay(50 + Random.nextLong(50)) // 50-100ms随机延迟
                        continue

                    } catch (e: Exception) {
                        return@tryExecuteWithOrderLock AjaxResult.error("操作失败: ${e.message}")
                    }
                }

                AjaxResult.error("系统繁忙，请稍后重试")
            }

            // 如果无法获取锁（其他线程正在处理），返回当前状态
            result ?: run {
                val payment = wxPaymentService.selectWxPaymentByZyPayNo(outOrderNo)
                    ?: return@runBlocking AjaxResult.error("订单不存在[$outOrderNo]")
                logger.info("无法获取订单锁，其他线程正在处理: $outOrderNo")
                AjaxResult.success("订单处理中", payment)
            }
        }
    }
}

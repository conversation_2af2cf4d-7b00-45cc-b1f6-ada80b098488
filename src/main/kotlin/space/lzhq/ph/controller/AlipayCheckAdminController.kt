package space.lzhq.ph.controller

import cn.hutool.core.io.FileUtil
import com.ruoyi.common.annotation.Log
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import org.apache.shiro.authz.annotation.Logical
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.mospital.alipay.AlipayBillItem
import org.mospital.common.IdUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.domain.AlipayCheck
import space.lzhq.ph.domain.AlipayCheckReport
import space.lzhq.ph.ext.AlipayExt
import space.lzhq.ph.service.IAlipayCheckService

@Controller
@RequestMapping("/ph/alipaycheck")
class AlipayCheckAdminController : BaseController() {

    @Autowired
    private lateinit var alipayCheckService: IAlipayCheckService

    @RequiresPermissions("ph:alipaycheck:view")
    @GetMapping
    fun alipaycheck(modelMap: ModelMap): String {
        modelMap.addAttribute("user", sysUser);
        return "ph/alipaycheck/alipaycheck"
    }

    @RequiresPermissions("ph:alipaycheck:reportView")
    @GetMapping("/report")
    fun report(): String {
        return "ph/alipaycheck/report"
    }

    @RequiresPermissions("ph:alipaycheck:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(alipayCheck: AlipayCheck): TableDataInfo? {
        startPage()
        val list: List<AlipayCheck> = alipayCheckService.selectAlipayCheckList(alipayCheck)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:alipaycheck:reportList")
    @PostMapping("/reportList")
    @ResponseBody
    fun reportList(alipayCheck: AlipayCheck): TableDataInfo {
        startPage()
        val list: List<AlipayCheck> = alipayCheckService.selectAlipayCheckList(alipayCheck)
        return getDataTable(list).apply { rows = list.map(::AlipayCheckReport) }
    }

    @RequiresPermissions("ph:alipaycheck:list")
    @Log(title = "对账", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    fun editSave(alipayCheck: AlipayCheck): AjaxResult {
        return toAjax(alipayCheckService.updateAlipayCheck(alipayCheck))
    }

    @RequiresPermissions("ph:alipaycheck:syncAlipayBill")
    @PostMapping("/syncAlipayBill/{id}")
    @ResponseBody
    fun syncAlipayBill(@PathVariable("id") id: Long): AjaxResult {
        val billDate = IdUtil.idToLocalDate(id)
        return try {
            val billFile = AlipayExt.getOrDownloadBillFile(billDate)
            val alipayBillItems = AlipayBillItem.parseBillItems(billFile)
            val ok = alipayCheckService.syncAlipayBill(id, alipayBillItems)
            toAjax(ok)
        } catch (e: Exception) {
            val alipayBillItems = emptyList<AlipayBillItem>()
            alipayCheckService.syncAlipayBill(id, alipayBillItems)
            AjaxResult.error(e.message ?: "发生未知错误")
        }
    }

    @RequiresPermissions(
        value = ["ph:alipaycheck:downloadAlipayBill", "ph:alipaycheck:reportDownloadAlipayBill"],
        logical = Logical.OR
    )
    @GetMapping("/downloadAlipayBill/{id}")
    @ResponseBody
    fun downloadAlipayBill(@PathVariable("id") id: Long): ResponseEntity<StreamingResponseBody> {
        val billDate = IdUtil.idToLocalDate(id)
        return try {
            val billFile = AlipayExt.getOrDownloadBillFile(billDate)
            ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(billFile.length())
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=${billFile.name}")
                .body(StreamingResponseBody {
                    FileUtil.writeToStream(billFile, it)
                })
        } catch (e: Exception) {
            ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("text/plain"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=error.txt")
                .body(StreamingResponseBody {
                    it.write((e.message ?: "发生未知错误").toByteArray())
                })
        }
    }

}

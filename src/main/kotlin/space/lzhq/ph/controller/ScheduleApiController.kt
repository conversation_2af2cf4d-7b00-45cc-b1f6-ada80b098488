package space.lzhq.ph.controller

import cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN
import cn.hutool.core.date.DateUtil
import cn.hutool.core.date.Week
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.witontek.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.ValidDepartmentCode
import space.lzhq.ph.annotation.ValidDoctorCode
import space.lzhq.ph.annotation.ValidScheduleId
import space.lzhq.ph.annotation.ValidTimePeriod
import space.lzhq.ph.common.Values
import space.lzhq.ph.service.DepartmentKit
import space.lzhq.ph.service.DoctorKit
import space.lzhq.ph.service.IDepartmentService
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("/open/schedule")
@Validated
class ScheduleApiController {

    @Autowired
    private lateinit var departmentService: IDepartmentService

    @GetMapping("indexedDepartments")
    fun indexedDepartments(): AjaxResult {
        val departments = DepartmentKit.getDepartmentsFromHis()
        return AjaxResult.success(DepartmentKit.indexDepartments(departments))
    }

    /**
     * 查询医生简介
     */
    @GetMapping("doctor")
    fun doctor(
        @RequestParam("doctorCode") @ValidDoctorCode doctorCode: String,
    ): AjaxResult {
        if ("None".equals(doctorCode, ignoreCase = true)) {
            return AjaxResult.error("普通号无医生简介")
        }

        if (Values.CACHED_DOCTORS.isEmpty()) {
            DoctorKit.buildDoctorsCache()
        }

        val doctor = Values.CACHED_DOCTORS[doctorCode]
        return if (doctor != null) {
            AjaxResult.success(doctor)
        } else {
            AjaxResult.error("未找到医生简介")
        }
    }

    /**
     * 查询排班列表
     */
    @GetMapping("realtimeList")
    fun scheduleList(
        @RequestParam(
            "startDate",
            required = false
        ) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDateParam: LocalDate?,
        @RequestParam(
            "endDate",
            required = false
        ) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDateParam: LocalDate?,
        @RequestParam("departmentCode") @ValidDepartmentCode departmentCode: String,
    ): AjaxResult {
        val today = LocalDate.now()
        val startDate = startDateParam ?: today
        val endDate = endDateParam ?: today.plusDays(7)
        val scheduleResponse: QueryRealTimeScheduleResponse = WitontekService.me.queryRealTimeSchedule(
            QueryRealTimeScheduleRequest().apply {
                this.startDate = startDate
                this.endDate = endDate
                this.departmentCode = departmentCode
            }
        )
        if (Values.CACHED_DOCTORS.isEmpty()) {
            DoctorKit.buildDoctorsCache()
        }
        return if (scheduleResponse.isOk()) {
            AjaxResult.success(scheduleResponse.groupByDoctor().onEach {
                it.doctorSpecialty = Values.CACHED_DOCTORS[it.doctorCode]?.expertise ?: ""
                it.doctorSummary = Values.CACHED_DOCTORS[it.doctorCode]?.title ?: ""
                var date = LocalDate.from(startDate)
                do {
                    if (it.schedules.find { s -> s.clinicDate == date } == null) {
                        it.schedules.add(
                            QueryRealTimeScheduleResponse.ScheduleDoctorDayGroup(
                                clinicDate = date,
                                dayOfWeek = Week.of(date.dayOfWeek).toChinese(""),
                                status = 0,
                                scheduleIdMorning = null,
                                scheduleIdAfternoon = null,
                                scheduleIdEvening = null,
                                haveNo = false
                            )
                        )
                    }
                    date = date.plusDays(1)
                } while (date <= endDate)

                // sort by date
                it.schedules.sortBy { s -> s.clinicDate }
            })
        } else {
            AjaxResult.error(scheduleResponse.message)
        }
    }

    /**
     * 查询排班详情
     */
    @GetMapping("detail")
    fun scheduleDetail(
        @RequestParam("clinicDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) clinicDate: LocalDate,
        @RequestParam("departmentCode") @ValidDepartmentCode departmentCode: String,
        @RequestParam("doctorCode") @ValidDoctorCode doctorCode: String,
    ): AjaxResult {
        val scheduleResponse: QueryScheduleDetailResponse = WitontekService.me.getScheduleDetail(
            QueryScheduleDetailRequest().apply {
                this.clinicDate = clinicDate.format(DateTimeFormatter.ISO_LOCAL_DATE)
                this.departmentCode = departmentCode
                this.doctorCode = doctorCode
            }
        )

        if (Values.CACHED_DOCTORS.isEmpty()) {
            DoctorKit.buildDoctorsCache()
        }

        return if (scheduleResponse.isOk()) {
            scheduleResponse.details.forEach {
                it.doctorSummary = Values.CACHED_DOCTORS[doctorCode]?.title ?: ""
                it.doctorSpecialty = Values.CACHED_DOCTORS[doctorCode]?.expertise ?: ""
            }
            AjaxResult.success(scheduleResponse.details)
        } else {
            AjaxResult.error(scheduleResponse.message)
        }
    }

    @GetMapping("timeDetail")
    fun scheduleTimeDetail(
        @RequestParam("departmentCode") @ValidDepartmentCode departmentCode: String,
        @RequestParam("clinicDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) clinicDate: LocalDate,
        @RequestParam("clinicTime") @ValidTimePeriod clinicTime: String,
        @RequestParam("doctorCode") @ValidDoctorCode doctorCode: String,
        @RequestParam("scheduleId") @ValidScheduleId scheduleId: String,
    ): AjaxResult {
        val scheduleTimeResponse: QueryScheduleTimeResponse = WitontekService.me.getScheduleTime(
            QueryScheduleTimeRequest().apply {
                this.departmentCode = departmentCode
                this.clinicDate = clinicDate
                this.clinicTime = clinicTime
                this.doctorCode = doctorCode
                this.scheduleId = scheduleId
            }
        )
        return if (scheduleTimeResponse.isOk()) {
            val nowAfterTenMin = DateUtil.date(DateUtil.date().toLocalDateTime().plusMinutes(10))
            val contents = scheduleTimeResponse.contents.filter {

                val visitTime = DateUtil.date(DateUtil.parseDateTime("${DateUtil.format(it.clinicDate, NORM_DATE_PATTERN)} ${it.visitTime}:00"))

                val haveNo = !it.havaNo.isNullOrEmpty() && it.havaNo!!.toInt() > 0
                visitTime.isAfterOrEquals(nowAfterTenMin) && haveNo
            }
            AjaxResult.success(contents)
        } else {
            AjaxResult.error(scheduleTimeResponse.message)
        }
    }

}

package space.lzhq.ph.controller

import cn.hutool.core.util.IdUtil
import com.alipay.api.response.AlipaySystemOauthTokenResponse
import com.alipay.api.response.AlipayUserInfoShareResponse
import com.ruoyi.common.core.domain.AjaxResult
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.mospital.alipay.AlipayService
import org.mospital.alipay.GrantType
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.domain.AlipayUserInfo
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IAlipayUserInfoService
import space.lzhq.ph.service.ISessionService

@RestController
class AlipayController {

    private val logger: Logger = LoggerFactory.getLogger(AlipayController::class.java)

    @Autowired
    private lateinit var sessionService: ISessionService

    @Autowired
    private lateinit var alipayUserInfoService: IAlipayUserInfoService

    @PostMapping("/open/alipay/login")
    fun login(
        @RequestParam("code") code: String,
        @RequestParam(name = "scope", defaultValue = "auth_user") scope: String,
        response: HttpServletResponse,
    ): AjaxResult {
        val oauthTokenResponse: AlipaySystemOauthTokenResponse = AlipayService.getOrRefreshAccessToken(
            authCode = code,
            grantType = GrantType.AUTHORIZATION_CODE
        )
        return if (oauthTokenResponse.isSuccess) {
            val openid: String = oauthTokenResponse.userId
            var ahoat = ""

            val oldSession: Session? = sessionService.selectSessionByOpenId(openid)
            if (oldSession != null) {
                ahoat = oldSession.ahoat.orEmpty()
                sessionService.deleteSessionById(oldSession.id)
            }

            val sid: String = IdUtil.fastUUID()
            val newSession: Session = Session().apply {
                this.id = sid
                this.clientType = ClientType.ALIPAY.code
                this.openId = openid
                this.unionId = openId
                this.sessionKey = ""
                this.ahoat = ahoat
            }
            sessionService.insertSession(newSession)
            response.setHeader(HttpHeaders.AUTHORIZATION, sid)

            var infoShareResponse: AlipayUserInfoShareResponse? = null
            if (scope.contains("auth_user")) {
                infoShareResponse = AlipayService.getUserInfoShare(oauthTokenResponse.accessToken)
                if (infoShareResponse.isSuccess) {
                    val alipayUserInfo = AlipayUserInfo(infoShareResponse)
                    if (alipayUserInfoService.selectAlipayUserInfoById(alipayUserInfo.id) == null) {
                        alipayUserInfoService.insertAlipayUserInfo(alipayUserInfo)
                    } else {
                        alipayUserInfoService.updateAlipayUserInfo(alipayUserInfo)
                    }
                }
            }

            val obj = mapOf(
                HttpHeaders.AUTHORIZATION to sid,
                "avatar" to infoShareResponse?.avatar.orEmpty(),
                "nickName" to infoShareResponse?.nickName.orEmpty(),
                "userName" to AlipayService.hideName(infoShareResponse?.userName.orEmpty()),
                "mobile" to AlipayService.hideMobile(infoShareResponse?.mobile.orEmpty()),
                "certNo" to AlipayService.hideIdCardNo(infoShareResponse?.certNo.orEmpty())
            )
            AjaxResult.success(obj)
        } else {
            AjaxResult.success(oauthTokenResponse)
        }
    }

    @PostMapping("/open/alipay/onPay")
    fun onPay(
        @RequestParam("out_trade_no") outTradeNo: String,
    ): String {
        return "SUCCESS"
    }

    @PostMapping("onMipPay")
    fun onMipPay(
        @RequestParam("out_trade_no") outTradeNo: String,
        request: HttpServletRequest,
    ): String {
        try {
            logger.debug("onMipPay: ${JacksonKit.writeValueAsString(request.parameterMap)}")
            return "success"
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return "success"
        }
    }

    /**
     * 换取医疗小程序智能消息推送的授权访问令牌
     *
     * 参见 <a href="https://opendocs.alipay.com/pre-open/01odaz#b%E3%80%81%E6%9C%8D%E5%8A%A1%E7%AB%AF%E8%8E%B7%E5%8F%96%20access_token">医疗小程序智能消息推送接入方案</a>
     *
     * 重要：医院小程序通过用户授权scope为hospital_order（可与获取用户信息一起进行组合授权，授权scope为auth_user,hospital_order）
     * 进行智能推送授权，授权返回后的access_token永久有效（如果与auth_user一起进行聚合授权，则有效期15天），
     * ISV需落地授权令牌（如有效期15天情况下，需要进行定时刷新token，详见“服务端获取 access_token”）。
     *
     * accessToken 长度为 40
     */
    @PostMapping("/api/alipay/authorizeHospitalOrder")
    fun authorizeHospitalOrder(
        @RequestParam("authCode") authCode: String,
        request: HttpServletRequest,
    ): AjaxResult {
        val oauthTokenResponse: AlipaySystemOauthTokenResponse = AlipayService.getOrRefreshAccessToken(
            authCode = authCode,
            grantType = GrantType.AUTHORIZATION_CODE
        )
        if (!oauthTokenResponse.isSuccess) {
            return AjaxResult.error(oauthTokenResponse.subCode + "-" + oauthTokenResponse.subMsg)
        }

        val session: Session = request.getClientSession()
            ?: return AjaxResult.error("会话已失效")
        if (!session.isAlipaySession) {
            return AjaxResult.error("此功能限支付宝小程序使用")
        }

        session.ahoat = oauthTokenResponse.accessToken
        val updateCount: Int = sessionService.updateSession(session)
        return if (updateCount == 1) {
            AjaxResult.success()
        } else {
            AjaxResult.error("更新会话失败")
        }
    }

}

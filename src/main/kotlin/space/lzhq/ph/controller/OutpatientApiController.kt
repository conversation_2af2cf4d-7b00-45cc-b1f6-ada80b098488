package space.lzhq.ph.controller

import cn.hutool.core.util.NumberUtil
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.utils.spring.SpringUtils
import jakarta.servlet.http.HttpServletRequest
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.mospital.witontek.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.annotation.ValidOrderNo
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.domain.OrderQueryResult
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.dto.IntelligentPayRequest
import space.lzhq.ph.dto.IntelligentPayResult
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime


@RestController
@RequestMapping("/api/outpatient")
@Validated
class OutpatientApiController : BaseController() {

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var intelligentOutpatientPayService: IIntelligentOutpatientPayService

    @Autowired
    private lateinit var orderQueryService: IOrderQueryService

    @Autowired
    private lateinit var orderSerializationService: OrderSerializationService

    @RequestMapping("/payments")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getOutpatientList(): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val availableRegistrationResponse: QueryClinicNoResponse =
            WitontekService.me.queryClinicNo(QueryClinicNoRequest().apply {
                this.realName = currentPatient.name
                this.patientCard = currentPatient.cardNo
            })
        if (!availableRegistrationResponse.isOk()) {
            return AjaxResult.error(availableRegistrationResponse.message)
        }

        val availableRegistrations = availableRegistrationResponse.results
        val prescriptions = mutableListOf<QueryOutpatientPayResponse.OutpatientPayItem>()
        runBlocking {
            flow {
                availableRegistrations.forEach { registration ->
                    val prescriptionResponse: QueryOutpatientPayResponse =
                        WitontekService.me.queryOutpatientPay(
                            QueryOutpatientPayRequest().apply {
                                this.patientCard = currentPatient.cardNo
                                this.clinicNo = registration.clinicNo
                                this.realName = currentPatient.name
                            }
                        )
                    if (!prescriptionResponse.isOk()) {
                        logger.error("Failed to query prescriptions for clinicNo=${registration.clinicNo}")
                        return@forEach
                    }
                    prescriptionResponse.results.forEach { prescription ->
                        // 返回未收费、非慢病、非急诊、非零的费用
                        if (!prescription.isCharged() &&
                            !prescription.isChronic() &&
                            !prescription.isEmergency() &&
                            NumberUtil.isNumber(prescription.orderFee) &&
                            NumberUtil.parseDouble(prescription.orderFee) > 0
                        ) {
                            emit(prescription)
                        }

                    }
                }
            }.toList(prescriptions)
        }
        return AjaxResult.success(prescriptions)
    }

    @RequestMapping("/refundRecords")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun queryOutpatientRefundRecords(
        @RequestParam("startDate") startDate: LocalDate? = null,
        @RequestParam("endDate") endDate: LocalDate? = null,
        request: HttpServletRequest
    ): AjaxResult {
        val patient = request.getCurrentPatient()
        val resp = WitontekService.me.queryOutpatientRefundRecords(OutpatientRefundRecordsRequest().apply {
            this.cardNo = patient.cardNo
            this.beginDate = startDate?.atStartOfDay() ?: LocalDateTime.now().minusMonths(6).with(LocalTime.MIN)
            this.endDate = endDate?.atTime(LocalTime.MAX) ?: LocalDateTime.now().with(LocalTime.MAX)
        })
        if (!resp.isOk()) {
            return AjaxResult.error(resp.message)
        }
        return AjaxResult.success(resp.results)
    }

    @RequestMapping("/feeDetails")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun queryOutpatientFeeDetails(
        @RequestParam("startDate") startDate: LocalDate? = null,
        @RequestParam("endDate") endDate: LocalDate? = null,
        request: HttpServletRequest
    ): AjaxResult {
        val patient = request.getCurrentPatient()
        val resp = WitontekService.me.queryOutpatientFeeDetails(OutpatientFeeDetailsRequest().apply {
            this.cardNo = patient.cardNo
            this.beginDate = startDate?.atStartOfDay() ?: LocalDateTime.now().minusMonths(6).with(LocalTime.MIN)
            this.endDate = endDate?.atTime(LocalTime.MAX) ?: LocalDateTime.now().with(LocalTime.MAX)
        })
        if (!resp.isOk()) {
            return AjaxResult.error(resp.message)
        }
        return AjaxResult.success(resp.results)
    }

    @RequestMapping("/info")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getOutpatientInfo(): AjaxResult {
        val patient = request.getCurrentPatient()

        val resp = WitontekService.me.queryMenzhenPatient(QueryMenzhenPatientRequest().apply {
            this.jzCardNo = patient.jzCardNo
        })

        if (!resp.isOk()) {
            return AjaxResult.error(resp.message)
        }

        if (!(resp.isValid())) {
            return AjaxResult.error("就诊账户状态异常")
        }

        return AjaxResult.success(
            resp.message, mapOf(
                "idCardNo" to resp.idCardNo,
                "hisNo" to resp.hisNo,
                "balance" to resp.balance,
                "name" to resp.name,
                "sex" to resp.sex,
                "mobile" to resp.mobile,
                "birthday" to resp.birthday
            )
        )
    }

    @GetMapping("/registerArrears")
    @RequireSession(clientType = ClientType.ANY)
    fun registerArrears(request: HttpServletRequest): AjaxResult {
        val session = request.getClientSession() ?: return AjaxResult.success()

        val patientService = SpringUtils.getBean(IPatientService::class.java)
        val currentPatient: Patient = patientService.selectActivePatientByOpenId(session.openId)
            ?: return AjaxResult.success()

        val registerArrearsResponse = WitontekService.me.queryRegisterArrears(
            request = QueryRegisterArrearsRequest(cardNo = currentPatient.cardNo)
        )
        return if (registerArrearsResponse.isOk()) {
            AjaxResult.success(registerArrearsResponse.data)
        } else {
            AjaxResult.error(registerArrearsResponse.message)
        }
    }

    /**
     * 智能门诊缴费
     * 优先使用就诊卡余额，余额不足时创建微信支付订单补充差额
     *
     * @param request 缴费请求，包含费用单号集合
     * @return 缴费结果
     */
    @PostMapping("/intelligentPay")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun intelligentPay(@RequestBody request: IntelligentPayRequest): AjaxResult {
        val patient = this.request.getCurrentPatient()

        try {
            val result = intelligentOutpatientPayService.processIntelligentPayment(patient, request.feeNos)

            return when (result) {
                is IntelligentPayResult.DirectPaySuccess -> {
                    AjaxResult.success(
                        "缴费成功", mapOf(
                            "paymentType" to "DIRECT_PAY",
                            "paymentRecord" to result.paymentRecord
                        )
                    )
                }

                is IntelligentPayResult.NeedRecharge -> {
                    AjaxResult.success(
                        "需要补充充值", mapOf(
                            "paymentType" to "NEED_RECHARGE",
                            "wxPayment" to result.wxPayment,
                            "wxPayParams" to result.wxPayParams,
                            "outOrderNo" to result.wxPayment.zyPayNo
                        )
                    )
                }

                is IntelligentPayResult.Error -> {
                    AjaxResult.error(result.message)
                }
            }
        } catch (e: Exception) {
            logger.error("智能缴费处理失败", e)
            return AjaxResult.error("系统异常，请稍后重试")
        }
    }

    /**
     * 智能缴费支付确认
     * 用于前端主动通知后端患者已完成支付，后端查询订单状态并充值就诊卡
     * 注意：此接口只处理充值，不进行门诊缴费，充值完成后需要再次调用 intelligentPay 接口完成缴费
     *
     * @param outOrderNo 订单号
     * @return 充值确认结果
     */
    @PostMapping("/confirmPayment")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun confirmPayment(
        @RequestParam("outOrderNo") @ValidOrderNo outOrderNo: String
    ): AjaxResult {
        val patient = request.getCurrentPatient()

        return runBlocking {
            // 使用非阻塞方式尝试处理订单
            val result = orderSerializationService.tryExecuteWithOrderLock(outOrderNo) {
                val maxRetries = 3
                var currentAttempt = 0

                while (currentAttempt < maxRetries) {
                    try {
                        val result = orderQueryService.confirmPaymentAndRecharge(outOrderNo, patient)

                        return@tryExecuteWithOrderLock when (result) {
                            is OrderQueryResult.Success -> {
                                AjaxResult.success(result.message, result.updatedPayment)
                            }

                            is OrderQueryResult.Failure -> {
                                // 检查是否是乐观锁冲突
                                if (result.message.contains("订单状态冲突") || result.message.contains("请重试")) {
                                    currentAttempt++
                                    if (currentAttempt < maxRetries) {
                                        // 随机延迟后重试
                                        delay((50..200).random().toLong())
                                        logger.warn("智能缴费充值确认乐观锁冲突，第${currentAttempt}次重试, outOrderNo=$outOrderNo")
                                        continue
                                    }
                                }
                                AjaxResult.error(result.message)
                            }
                        }

                    } catch (e: Exception) {
                        logger.error("智能缴费充值确认处理异常, outOrderNo=$outOrderNo", e)
                        return@tryExecuteWithOrderLock AjaxResult.error("充值确认处理异常，请稍后重试")
                    }
                }

                AjaxResult.error("充值确认处理超时，请稍后重试")
            }

            // 如果无法获取锁（其他线程正在处理），返回当前状态
            result ?: run {
                val payment = wxPaymentService.selectWxPaymentByZyPayNo(outOrderNo)
                    ?: return@runBlocking AjaxResult.error("订单不存在")
                logger.info("无法获取订单锁，其他线程正在处理: $outOrderNo")
                AjaxResult.success("订单处理中", payment)
            }
        }
    }

}
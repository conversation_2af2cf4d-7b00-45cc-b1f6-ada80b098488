package space.lzhq.ph.controller

import cn.hutool.log.Log
import com.github.binarywang.wxpay.bean.request.WxInsurancePayRefundRequest
import com.ruoyi.common.core.controller.BaseController
import jakarta.validation.Valid
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Digits
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Positive
import org.mospital.dongruan.pay.DongruanPayService
import org.mospital.dongruan.pay.RefundForm
import org.mospital.dongruan.pay.UnifiedPayStatusEnum
import org.mospital.jackson.JacksonKit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.MipWxOrder
import space.lzhq.ph.domain.WxRefund
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IMipWxOrderService
import space.lzhq.ph.service.ITraceService
import space.lzhq.ph.service.IWxPaymentService
import space.lzhq.ph.service.IWxRefundService
import java.math.BigDecimal

@RestController
@RequestMapping("/open/refund")
class OpenRefundController() : BaseController() {

    private val log: Log = Log.get()

    @Autowired
    private lateinit var mipWxOrderService: IMipWxOrderService

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var traceService: ITraceService

    companion object {
        private const val AUTH_CODE = "AQBr6@rJc_7jgV7C"
    }

    /**
     * 退款请求类
     */
    data class RefundRequest(

        @field:[NotBlank]
        val outTradeNo: String,

        /**
         * 1=诊间缴费，2=医保移动支付
         */
        @field:[Positive]
        val type: Int,

        @field:[Digits(integer = 6, fraction = 2) DecimalMin(value = "0.0", inclusive = false)]
        val amount: BigDecimal,

        val refundDesc: String?,

        @field:[NotBlank]
        val authCode: String,
    ) {
        fun isAuthorized(): Boolean {
            return authCode == AUTH_CODE
        }

        fun isYibao(): Boolean {
            return outTradeNo.startsWith(ServiceType.YB.name) && type == 2
        }

        fun isMenzhen(): Boolean {
            return outTradeNo.startsWith("YQTL") && type == 1
        }
    }

    @PostMapping
    fun index(@Valid @RequestBody request: RefundRequest): Map<String, Any> {
        if (!(request.isAuthorized())) {
            return mapOf(
                "code" to 401,
                "msg" to "认证失败"
            )
        }

        log.info("/open/refund 退款参数:${JacksonKit.writeValueAsString(request)}")
        if (request.isYibao()) {
            return refundMpMip(request)
        }
        if (request.isMenzhen()) {
            return refundMenzhen(request)
        }
        return mapOf(
            "code" to 500,
            "msg" to "暂不支持此类型退款"
        )
    }

    private fun refundMpMip(request: RefundRequest): Map<String, Any> {
        val mipWxOrder: MipWxOrder? = mipWxOrderService.getOneByOutTradeNo(request.outTradeNo)
        return try {
            checkNotNull(mipWxOrder) { "本地无此订单: ${request.outTradeNo}" }

            if (PaymentKit.yuanToFen(mipWxOrder.ownPayAmount) == 0L) {
                mapOf(
                    "code" to 500,
                    "msg" to "现金支付金额为0，无需退现金"
                )
            }
            val wxInsurancePayService = WeixinExt.getWxMPInsurancePayService()
            val wxInsurancePayOrderQueryResult =
                wxInsurancePayService.queryOrder(medTransId = mipWxOrder.medTransactionId, hospOutTradeNo = "")
            if (wxInsurancePayOrderQueryResult.cashTradeStatus != "SUCCESS") {
                traceService.traceMipWxOrder(
                    mipWxOrder.id,
                    "退款失败, 现金退款状态为${wxInsurancePayOrderQueryResult.cashTradeStatusDescription}，不可退款"
                )
                return mapOf(
                    "code" to 500,
                    "msg" to "现金退款状态为${wxInsurancePayOrderQueryResult.cashTradeStatusDescription}，不可退款"
                )
            }

            mipWxOrder.hospOutRefundNo = mipWxOrder.hospitalOutTradeNo + "#R"
            val refundRequest = WxInsurancePayRefundRequest().apply {
                this.medTransId = mipWxOrder.medTransactionId
                this.hospOutRefundNo = mipWxOrder.hospOutRefundNo
                this.partRefundType = "CASH_ONLY"
                this.payOrderId = mipWxOrder.payOrderId
                this.refReason = request.refundDesc ?: "申请退款"
            }
            val refundResult = wxInsurancePayService.refund(refundRequest)
            mipWxOrderService.updateOnRefund(mipWxOrder, refundResult)
            traceService.traceMipWxOrder(mipWxOrder.id, "退款成功, ${JacksonKit.writeValueAsString(refundResult)}")

            mapOf(
                "code" to 200,
                "msg" to "处理成功",
                "data" to mipWxOrder.hospOutRefundNo,
            )
        } catch (e: Exception) {
            traceService.traceMipWxOrder(mipWxOrder?.id ?: 0L, "退款失败, ${e.stackTraceToString()}")
            mapOf(
                "code" to 500,
                "msg" to e.message.orEmpty()
            )
        }
    }

    private fun refundMenzhen(request: RefundRequest): Map<String, Any> {
        val payment = wxPaymentService.selectWxPaymentByZyPayNo(request.outTradeNo)
            ?: return mapOf(
                "code" to 500,
                "msg" to "本地无此订单: ${request.outTradeNo}"
            )

        if (payment.wxPayNo.isNullOrBlank()) {
            val wxPayOrderQueryResult = DongruanPayService.queryOrder(
                openid = payment.openid,
                orderNo = payment.zyPayNo
            )
            wxPaymentService.updateOnPay(payment, wxPayOrderQueryResult.data)
        }

        if (payment.wxTradeStatus != UnifiedPayStatusEnum.SUCCESS.msg) {
            return mapOf(
                "code" to 500,
                "msg" to "支付状态为${payment.wxTradeStatus}, 不可退款"
            )
        }

        val refundAmountCents = PaymentKit.yuanToFen(request.amount)
        if (refundAmountCents > (payment.unrefund ?: payment.amount)) {
            return mapOf(
                "code" to 500,
                "msg" to "退款金额超过可退款金额",
                "unrefund" to (payment.unrefund ?: payment.amount),
                "refundAmountCents" to refundAmountCents
            )
        }

        val refund: WxRefund = wxRefundService.createAndSave(payment, refundAmountCents)
        wxPaymentService.updateOnRefund(payment, refundAmountCents)

        return try {
            val refundResponse = DongruanPayService.refund(
                form = RefundForm()
                    .orderNo(refund.zyPayNo)
                    .tradeNo(refund.wxPayNo)
                    .refundNo(refund.zyRefundNo)
                    .refundAmount(PaymentKit.fenToYuan(refund.amount))
                    .refundReason(request.refundDesc ?: "申请退款")
            )
            if (refundResponse.isOk()) {
                mapOf(
                    "code" to 200,
                    "msg" to "退款成功",
                    "data" to refundResponse.data!!
                )
            } else {
                mapOf(
                    "code" to 500,
                    "msg" to "退款失败, ${refundResponse.message}"
                )
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            mapOf(
                "code" to 500,
                "msg" to e.message.orEmpty()
            )
        }
    }
}
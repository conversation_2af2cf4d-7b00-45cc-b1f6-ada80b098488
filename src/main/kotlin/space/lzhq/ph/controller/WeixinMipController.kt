package space.lzhq.ph.controller

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUtil
import cn.hutool.crypto.digest.MD5
import com.github.binarywang.wxpay.bean.request.WxInsurancePayUnifiedOrderRequest
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult
import com.github.binarywang.wxpay.bean.result.WxInsurancePayUnifiedOrderResult
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import jakarta.validation.Valid
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.toList
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.dongruan.yibao.DongruanYibaoService
import org.mospital.dongruan.yibao.bean.*
import org.mospital.jackson.JacksonKit
import org.mospital.wecity.mip.*
import org.mospital.witontek.QueryClinicNoRequest
import org.mospital.witontek.QueryClinicNoResponse
import org.mospital.witontek.QueryOutpatientPayRequest
import org.mospital.witontek.QueryOutpatientPayResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.MipWxOrder
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.dto.MipOrderCreationForm
import space.lzhq.ph.ext.*
import space.lzhq.ph.service.IMipWxOrderService
import java.math.BigDecimal
import java.util.*

@RestController
@RequestMapping("/api/weixinMip")
@Validated
class WeixinMipController : BaseController() {

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var mipWxOrderService: IMipWxOrderService

    private fun getEnvByClientType(clientType: ClientType): String = when (clientType) {
        ClientType.MP -> "WeixinGongzhonghao"
        ClientType.MA -> "WeixinXiaochengxu"
    }

    /**
     * 获取待结算费用清单，包含费用明细
     */
    @GetMapping("pendingSettlements")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPendingSettlements(): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val session: Session = request.getClientSession()!!
        val witontekService = HisExt.getWitontekServiceByClientType(session.clientTypeEnum)
        val currentPatient: Patient = request.getCurrentPatient()
        val availableRegistrationResponse: QueryClinicNoResponse =
            witontekService.queryClinicNo(QueryClinicNoRequest().apply {
                this.realName = currentPatient.name
                this.patientCard = currentPatient.cardNo
            })
        if (!availableRegistrationResponse.isOk()) {
            return AjaxResult.error(availableRegistrationResponse.message)
        }

        val availableRegistrations = availableRegistrationResponse.results.filterNot {
            it.departmentCode in arrayOf(
                "0153", // 妇科门诊
                "0154", // 产科门诊
            )
        }
        val prescriptions = mutableListOf<QueryOutpatientPayResponse.OutpatientPayItem>()
        runBlocking {
            flow {
                availableRegistrations.forEach { registration ->
                    val prescriptionResponse: QueryOutpatientPayResponse =
                        witontekService.queryOutpatientPay(
                            QueryOutpatientPayRequest().apply {
                                this.patientCard = currentPatient.cardNo
                                this.clinicNo = registration.clinicNo
                                this.realName = currentPatient.name
                            }
                        )
                    if (!prescriptionResponse.isOk()) {
                        logger.error("Failed to query prescriptions for clinicNo=${registration.clinicNo}")
                        return@forEach
                    }
                    prescriptionResponse.results.forEach { prescription ->
                        // 返回未收费的费用
                        if (!prescription.isCharged()) {
                            emit(prescription)
                        }
                    }
                }
            }.toList(prescriptions)
        }
        return AjaxResult.success(prescriptions)
    }

    /**
     * 获取授权地址
     * @param accountType 账户类型：self=本人医保，family=亲属医保
     */
    @GetMapping("authUrl")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getAuthUrl(
        @RequestParam("accountType") accountType: String,
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val clientType = request.getMipClientType()
        val authUrl = WecityMipService.buildAuthUrl(
            clientType = clientType,
            accountType = accountType,
            userName = activePatient.name,
            userCardNo = activePatient.idCardNo,
        )
        return AjaxResult.success(authUrl)
    }

    /**
     * 创建订单
     */
    @PostMapping("mp/createOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun mpCreateOrder(
        @RequestBody @Valid body: MipOrderCreationForm,
    ): AjaxResult {
        return createOrder(body)
    }

    @PostMapping("createOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createOrder(
        @RequestBody @Valid body: MipOrderCreationForm,
    ): AjaxResult {
        val clientType: ClientType = request.getMipClientType()

        val clinicNo = body.clinicNo
        val feeList = body.feeList
        val mipAuthCode = body.mipAuthCode
        val acctUsedFlag = body.acctUsedFlag

        return doCreateOrder(
            clientType,
            clinicNo,
            feeList.map { CreateOrderForm.Fee(it.prescriptionNo, it.amount) },
            mipAuthCode,
            acctUsedFlag
        )
    }

    private fun doCreateOrder(
        clientType: ClientType,
        clinicNo: String,
        feeList: List<CreateOrderForm.Fee>,
        mipAuthCode: String,
        acctUsedFlag: String
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val currentPatient: Patient = request.getCurrentPatient()
        val userQueryResult: UserQueryResult = WecityMipService.queryUser(
            qrcode = mipAuthCode,
            openid = currentPatient.openId,
            clientType = clientType
        )
        if (!userQueryResult.isOK()) {
            return AjaxResult.error(userQueryResult.message)
        }
        if (userQueryResult.cityId.isNullOrBlank()) {
            return AjaxResult.error("获取医保参保地失败")
        }
        if (!userQueryResult.cityId!!.startsWith("65")) {
            return AjaxResult.error("目前仅支持疆内异地结算")
        }
        if (userQueryResult.isSelfPay() && (userQueryResult.userCardNo != currentPatient.idCardNo || userQueryResult.userName != currentPatient.name)) {
            return AjaxResult.error("医保信息与当前就诊人信息不一致")
        }

        val payAuthNo = if (userQueryResult.isSelfPay()) {
            userQueryResult.payAuthNo!!
        } else if (userQueryResult.isFamilyPay()) {
            userQueryResult.familyPayAuthNo!!
        } else {
            // Unreachable
            return AjaxResult.error("未知的医保支付方式")
        }

        val userName = if (userQueryResult.isSelfPay()) {
            userQueryResult.userName!!
        } else {
            currentPatient.name
        }
        val userCardNo = if (userQueryResult.isSelfPay()) {
            userQueryResult.userCardNo!!
        } else {
            currentPatient.idCardNo
        }

        val fees: List<CreateOrderForm.Fee> = feeList
        val amount: BigDecimal = fees.sumOf { it.amount }

        val weixinMipSetting = WeixinMipSetting.getInstance(clientType)

        val createOrderResponse: Response<CreateOrderResult> = DongruanYibaoService.createOrder(
            form = CreateOrderForm(
                clinicNo = clinicNo,
                feeList = fees,
                totalFee = amount,
                payAuthNo = payAuthNo,
                ecToken = "",
                personName = userName,
                idCardNo = userCardNo,
                insuCode = userQueryResult.cityId!!,
                medType = "11",
                acctUsedFlag = acctUsedFlag,
                latlnt = userQueryResult.longitudeLatitude?.toString() ?: "0,0"
            ),
            thirdType = ThirdType.WEIXIN,
            extendParams = Request.ExtendParams(appId = weixinMipSetting.orgAppId),
            env = getEnvByClientType(clientType = clientType)
        )
        return if (createOrderResponse.isOk()) {
            val preSettlement: CreateOrderResult.PreSettlement = createOrderResponse.data!!.preSettlement
            val mipWxOrder = MipWxOrder(userQueryResult, currentPatient, preSettlement)
            mipWxOrder.clinicNo = clinicNo
            mipWxOrder.feesJson = JacksonKit.writeValueAsString(fees)
            mipWxOrder.payToken = createOrderResponse.data!!.payToken
            mipWxOrder.medOrgOrd = createOrderResponse.data!!.medOrgOrd
            mipWxOrderService.save(mipWxOrder)
            AjaxResult.success(createOrderResponse.data!!)
        } else {
            AjaxResult.error(createOrderResponse.message)
        }
    }

    /**
     * 小程序支付订单
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     */
    @PostMapping("payOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun payOrder(
        @RequestParam("payOrderId") payOrderId: String,
    ): AjaxResult {
        val clientType: ClientType = request.getMipClientType()
        return doPayOrder(clientType, payOrderId)
    }

    /**
     * 公众号支付订单
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     */
    @PostMapping("mp/payOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun mpPayOrder(
        @RequestParam("payOrderId") payOrderId: String,
    ): AjaxResult {
        return payOrder(payOrderId)
    }

    private fun doPayOrder(clientType: ClientType, payOrderId: String): AjaxResult {
        synchronized(payOrderId.intern()) {
            val currentPatient: Patient = request.getCurrentPatient()
            val mipWxOrder: MipWxOrder = mipWxOrderService.getOneByPayOrderId(payOrderId)
                ?: return AjaxResult.error("订单不存在")
            if (mipWxOrder.openid != currentPatient.openId) {
                return AjaxResult.error("订单异常")
            }
            if (mipWxOrder.hospitalOutTradeNo.isNotBlank() || mipWxOrder.medTransactionId.isNotBlank()) {
                return AjaxResult.error("订单已支付")
            }

            val weixinMipSetting = WeixinMipSetting.getInstance(clientType)

            val unifiedOrderRequest: WxInsurancePayUnifiedOrderRequest = WxInsurancePayUnifiedOrderRequest().apply {
                this.orderType = Constants.ORDER_TYPE_DiagPay
                this.openid = currentPatient.openId
                this.hospOutTradeNo = PaymentKit.newOrderId(ServiceType.YB)
                this.hospitalName = space.lzhq.ph.common.Constants.MERCHANT_NAME
                // 总共需要支付现金金额，对应 feeSumamt
                this.totalFee = PaymentKit.yuanToFen(mipWxOrder.feeSumAmount).toInt()
                // 现金需要支付的金额，对应 ownPayAmt
                this.cashFee = PaymentKit.yuanToFen(mipWxOrder.ownPayAmount).toInt()
                this.payType = if (this.cashFee > 0) 3 else 2
                // 医保支付金额，医保基金支付+个人账户支出，对应 fundPay + psnAcctPay
                this.insuranceFee =
                    PaymentKit.yuanToFen(mipWxOrder.fundPayAmount + mipWxOrder.personalAccountPayAmount).toInt()
                this.spbillCreateIp = ServletUtil.getClientIP(request)
                this.notifyUrl = "https://xjzybyy.xjyqtl.cn/open/wx/onWxInsurancePay"
                val authorization = request.getHeader("Authorization")
                if (clientType == ClientType.MP) {
                    this.returnUrl =
                        "https://xjzybyy.xjyqtl.cn/mp/index.html#/pages_yibao/ConfirmOrder/ConfirmOrder?isPay=true&payOrdId=${payOrderId}&authorization=${authorization}"
                } else {
                    this.returnUrl =
                        "pages_yibao/ConfirmOrder/ConfirmOrder?isPay=true&payOrdId=${payOrderId}&authorization=${authorization}"
                }
                this.body = space.lzhq.ph.common.Constants.MERCHANT_NAME + "-诊间缴费"
                this.userCardType = 1
                this.userCardNo = MD5.create().digestHex(mipWxOrder.userCardNo)
                this.userName = mipWxOrder.userName
                this.longitude = mipWxOrder.longitudeLatitude.split(",")[0].trim()
                this.latitude = mipWxOrder.longitudeLatitude.split(",")[1].trim()
                this.gmtOutCreate = DateUtil.format(Date(), DatePattern.PURE_DATETIME_PATTERN)
                this.serialNo = mipWxOrder.medOrgOrd
                this.payAuthNo = mipWxOrder.payAuthNo
                this.payOrderId = mipWxOrder.payOrderId
                this.cityId = weixinMipSetting.cityId
                this.orgNo = weixinMipSetting.orgCode
                this.channelNo = weixinMipSetting.channelNo

                if (!mipWxOrder.isSelfPay) {
                    this.extends = JacksonKit.writeValueAsString(
                        mapOf(
                            "rel_user_name_md5" to MD5.create().digestHex(mipWxOrder.patientName),
                            "rel_user_card_no_md5" to MD5.create().digestHex(mipWxOrder.patientIdCardNo)
                        )
                    )
                }
            }
            try {
                val wxInsurancePayUnifiedOrderResult: WxInsurancePayUnifiedOrderResult =
                    WeixinExt.getWxInsurancePayServiceByClientType(clientType).unifiedOrder(unifiedOrderRequest)

                mipWxOrder.hospitalOutTradeNo = unifiedOrderRequest.hospOutTradeNo
                mipWxOrder.medTransactionId = wxInsurancePayUnifiedOrderResult.medTransId
                mipWxOrderService.updateById(mipWxOrder)
                return AjaxResult.success(
                    mapOf(
                        "medTransactionId" to wxInsurancePayUnifiedOrderResult.medTransId,
                        "payUrl" to wxInsurancePayUnifiedOrderResult.payUrl,
                        "payAppId" to wxInsurancePayUnifiedOrderResult.payAppId
                    )
                )
            } catch (e: Exception) {
                return AjaxResult.error(e.message)
            }
        }
    }

    @GetMapping("queryOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun queryOrder(
        @RequestParam("payOrderId") payOrderId: String,
    ): AjaxResult {
        val clientType: ClientType = request.getMipClientType()
        return doQueryOrder(clientType, payOrderId)
    }

    @GetMapping("mp/queryOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun mpQueryOrder(
        @RequestParam("payOrderId") payOrderId: String,
    ): AjaxResult {
        return queryOrder(payOrderId)
    }


    private fun doQueryOrder(clientType: ClientType, payOrderId: String): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val mipWxOrder: MipWxOrder = mipWxOrderService.getOneByPayOrderId(payOrderId)
            ?: return AjaxResult.error("订单不存在")
        if (mipWxOrder.openid != currentPatient.openId) {
            return AjaxResult.error("订单异常")
        }

        try {
            val wxInsurancePayOrderQueryResult: WxInsurancePayOrderQueryResult =
                WeixinExt.getWxInsurancePayServiceByClientType(clientType).queryOrder(
                    medTransId = mipWxOrder.medTransactionId,
                    hospOutTradeNo = ""
                )

            // 如果已经有hisInvoiceNo，说明已经结算成功
            if (!mipWxOrder.hisInvoiceNo.isNullOrBlank()) {
                return buildAjaxResult(wxInsurancePayOrderQueryResult, mipWxOrder.hisSettleMessage)
            }

            if (wxInsurancePayOrderQueryResult.medTradeState == "PAYING") {
                return AjaxResult.error("RETRY_REQUIRED")
            }
            if (wxInsurancePayOrderQueryResult.medTradeState != "SUCCESS") {
                return buildAjaxResult(wxInsurancePayOrderQueryResult, "未结算")
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error("RETRY_REQUIRED")
        }

        val weixinMipSetting = WeixinMipSetting.getInstance(clientType)
        logger.debug("payOrderId: $payOrderId 准备处理HIS状态")
        val yibaoOrderStatusResponse: Response<QueryOrderResult> = DongruanYibaoService.queryOrder(
            form = QueryOrderForm(
                clinicNo = mipWxOrder.clinicNo,
                payOrderId = mipWxOrder.payOrderId,
                personName = mipWxOrder.patientName,
                idCardNo = mipWxOrder.patientIdCardNo
            ),
            thirdType = ThirdType.WEIXIN,
            extendParams = Request.ExtendParams(appId = weixinMipSetting.orgAppId),
            env = getEnvByClientType(clientType = clientType)
        )

        logger.debug("payOrderId: $payOrderId HIS状态: ${yibaoOrderStatusResponse.data?.orderStatus}, HIS返回message: ${yibaoOrderStatusResponse.message}")
        return if (yibaoOrderStatusResponse.isOk()) {
            if (yibaoOrderStatusResponse.data?.orderStatus == "6") {
                try {
                    val settledMipWxOrder = HisExt.settleWeixinMipOrder(clientType, mipWxOrder.hospitalOutTradeNo)
                    logger.debug("payOrderId: $payOrderId HIS settleWeixinMipOrder执行成功，此时数据库中应该已有his相关状态，尝试查询医保订单状态")
                    val wxInsurancePayOrderQueryResult: WxInsurancePayOrderQueryResult =
                        WeixinExt.getWxInsurancePayServiceByClientType(clientType).queryOrder(
                            medTransId = mipWxOrder.medTransactionId,
                            hospOutTradeNo = ""
                        )
                    buildAjaxResult(wxInsurancePayOrderQueryResult, settledMipWxOrder.hisSettleMessage)
                } catch (e: Exception) {
                    logger.debug(e.message, e)
                    AjaxResult.error(e.message)
                }
            } else {
                AjaxResult.error("RETRY_REQUIRED")
            }
        } else {
            if (mipWxOrder.ownPayAmount > BigDecimal.ZERO) {
                CoroutineScope(Dispatchers.IO).launch {
                    WeixinExt.refundWeixin(mipWxOrder)
                }
                AjaxResult.error("HIS执行失败，将自动退还您的钱款")
            } else {
                AjaxResult.error("HIS执行失败，请稍后重试")
            }
        }
    }

    fun buildAjaxResult(
        wxInsurancePayOrderQueryResult: WxInsurancePayOrderQueryResult,
        hisTradeStatus: String
    ): AjaxResult {
        return AjaxResult.success(
            mapOf<String, Any>(
                "medTradeState" to wxInsurancePayOrderQueryResult.medTradeState,
                "cashTradeStatus" to wxInsurancePayOrderQueryResult.cashTradeStatus,
                "cashFee" to wxInsurancePayOrderQueryResult.cashFee,
                "insuranceTradeStatus" to wxInsurancePayOrderQueryResult.insuranceTradeStatus,
                "hisTradeStatus" to hisTradeStatus,
            )
        )
    }

    data class RefundOrderForm(
        val payOrderId: String,
    )

    @PostMapping("refundOrder")
    fun refundOrder(
        @RequestBody form: RefundOrderForm,
    ): AjaxResult {
        try {
            val mipWxOrder: MipWxOrder = mipWxOrderService.getOneByPayOrderId(form.payOrderId)
                ?: return AjaxResult.error("订单不存在")

            val refundResult: WxInsurancePayRefundResult = WeixinExt.refundWeixin(mipWxOrder)
            return AjaxResult.success(refundResult)
        } catch (e: Exception) {
            return AjaxResult.error(e.message)
        }
    }

}

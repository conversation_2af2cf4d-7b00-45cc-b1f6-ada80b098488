package space.lzhq.ph.controller

import cn.hutool.core.codec.Base64
import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ByteArrayResource
import org.springframework.core.io.Resource
import org.springframework.http.ContentDisposition
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.service.MedicalRecordService
import space.lzhq.ph.service.PmrInformation
import java.nio.charset.StandardCharsets

@RestController
@RequestMapping("/api/medical-record")
class MedicalRecordController {

    @Autowired
    private lateinit var medicalRecordService: MedicalRecordService

    @GetMapping("")
    fun index(
        @RequestParam("patientName", required = false) patientName: String?,
        @RequestParam("idCardNo", required = false) idCardNo: String?,
        @RequestParam("patientNo", required = false) patientNo: String?,
        @RequestParam("inPatientNo", required = false) inPatientNo: String?
    ): AjaxResult {
        var inpatientVisitRecord: List<Map<String, String>>? = null
        val pmrInformationList: MutableList<PmrInformation> = mutableListOf()
        try {
            patientNo?.let {
                inpatientVisitRecord =
                    medicalRecordService.queryInpatientVisitRecordWithCache(patientNo, patientName!!, idCardNo!!)
            }
            inPatientNo?.let {
                medicalRecordService.downloadWithCache(it).forEach { info ->
                    pmrInformationList.add(PmrInformation(
                        recordName = info.recordName,
                        recordType = info.recordType,
                        content = ""
                    ))
                }
            }
        } catch (e: Exception) {
            return AjaxResult.error(e.message?.substringAfter(":")?.trim() ?: "系统异常")
        }
        return AjaxResult.success(mapOf("inpatientVisitRecord" to inpatientVisitRecord, "pmrInformationList" to pmrInformationList))
    }

    @ResponseBody
    @GetMapping("/download.pdf", produces = [MediaType.APPLICATION_PDF_VALUE])
    fun download(
        @RequestParam("inPatientNo") inPatientNo: String,
        @RequestParam("index") index: Int,
    ): ResponseEntity<Resource> {
        val pmrInfomationsList = medicalRecordService.downloadWithCache(inPatientNo)
        val info = pmrInfomationsList[index]
        val contentByteArray = Base64.decode(info.content)
        val contentDisposition = ContentDisposition.attachment()
            .filename(info.recordName, StandardCharsets.UTF_8)
            .build()
            .toString()
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
            .contentLength(contentByteArray.size.toLong())
            .body(ByteArrayResource(contentByteArray))
    }

}
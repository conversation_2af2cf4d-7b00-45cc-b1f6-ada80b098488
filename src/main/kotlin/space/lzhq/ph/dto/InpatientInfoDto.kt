package space.lzhq.ph.dto

import com.fasterxml.jackson.annotation.JsonUnwrapped
import org.mospital.common.TokenManager
import org.mospital.witontek.InpatientInfoResponse
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

data class InpatientInfo(
    val admissionId: String,
    val name: String,
    val idCardNo: String,
    val phone: String,
    val sex: String,
    val birthday: LocalDate,
    val status: String,
    val admissionTime: LocalDateTime,
    val dischargeTime: LocalDateTime?,
    val ward: String
) {
    companion object {

        private val log: Logger = LoggerFactory.getLogger(InpatientInfo::class.java)
        private val witontekDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/M/d H:mm:ss")

        @JvmStatic
        fun fromWitontekInpatientInfoResponse(resp: InpatientInfoResponse): InpatientInfo? {
            if (!resp.isOk()) {
                return null
            }

            // 检查必要字段是否为空
            if (resp.birthday.isNullOrEmpty() || resp.inHospitalDate.isNullOrEmpty()) {
                log.error("住院信息缺少必要字段: birthday=${resp.birthday}, inHospitalDate=${resp.inHospitalDate}")
                return null
            }

            return try {
                val birthday = LocalDateTime.parse(resp.birthday, witontekDateTimeFormatter).toLocalDate()
                val admissionTime = LocalDateTime.parse(resp.inHospitalDate, witontekDateTimeFormatter)
                var dischargeTime = if (resp.leaveDate.isNullOrEmpty()) {
                    null
                } else {
                    LocalDateTime.parse(resp.leaveDate.orEmpty(), witontekDateTimeFormatter)
                }
                if (dischargeTime != null && dischargeTime.isBefore(admissionTime)) {
                    dischargeTime = null
                }

                InpatientInfo(
                    admissionId = resp.hospitalizedId,
                    name = resp.realName.orEmpty(),
                    idCardNo = resp.idCard.orEmpty(),
                    phone = resp.phone.orEmpty(),
                    sex = resp.sex.orEmpty(),
                    birthday = birthday,
                    status = resp.status.orEmpty(),
                    admissionTime = admissionTime,
                    dischargeTime = dischargeTime,
                    ward = resp.inpatientArea.orEmpty()
                )
            } catch (e: Exception) {
                log.error("解析住院信息失败: ${e.message}", e)
                null
            }
        }
    }
}

data class InpatientInfoDto(
    @field:JsonUnwrapped
    val data: InpatientInfo
) {
    val token: String = TokenManager.generateToken(data)
}
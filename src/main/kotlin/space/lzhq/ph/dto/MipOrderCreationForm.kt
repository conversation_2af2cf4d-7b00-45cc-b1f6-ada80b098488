package space.lzhq.ph.dto

import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import space.lzhq.ph.annotation.NoDuplicatePrescriptionNo
import java.math.BigDecimal

data class MipOrderCreationForm(
    // 门诊号
    @field:NotBlank(message = "门诊号不能为空")
    val clinicNo: String,
    // 费用明细
    @field:NotEmpty(message = "费用明细不能为空")
    @field:NoDuplicatePrescriptionNo
    val feeList: List<Fee> = emptyList(),
    // 医保授权码
    @field:NotBlank(message = "医保授权码不能为空")
    val mipAuthCode: String,
    // 个人账户使用标志：0=不使用，1=使用
    val acctUsedFlag: String = "1"
) {

    data class Fee(
        @field:NotBlank(message = "处方号不能为空")
        val prescriptionNo: String,
        @field:NotNull(message = "金额不能为空")
        @field:Min(value = 0, message = "金额不能小于0")
        val amount: BigDecimal,
    )

}
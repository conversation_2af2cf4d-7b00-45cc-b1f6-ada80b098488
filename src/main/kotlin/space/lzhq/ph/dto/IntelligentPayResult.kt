package space.lzhq.ph.dto

import org.mospital.dongruan.pay.UnifiedOrderResult
import org.mospital.witontek.QueryOutpatientPayResponse
import space.lzhq.ph.domain.OutpatientPaymentRecord
import space.lzhq.ph.domain.WxPayment
import java.math.BigDecimal

/**
 * 处理结果类型
 */
sealed class IntelligentPayResult {
    data class DirectPaySuccess(val paymentRecord: OutpatientPaymentRecord) : IntelligentPayResult()
    data class NeedRecharge(val wxPayment: WxPayment, val wxPayParams: UnifiedOrderResult) : IntelligentPayResult()
    data class Error(val message: String) : IntelligentPayResult()
}

/**
 * 费用计算结果
 */
data class FeeCalculationResult(
    val feeNos: List<String>,
    val totalAmount: BigDecimal,
    val validPrescriptions: List<QueryOutpatientPayResponse.OutpatientPayItem>
)

/**
 * 缴费结果
 */
data class PaymentResult(
    val success: Boolean,
    val paymentRecord: OutpatientPaymentRecord? = null,
    val errorMessage: String? = null
)

/**
 * 微信支付结果
 */
data class WxPaymentResult(
    val success: Boolean,
    val wxPayment: WxPayment? = null,
    val wxPayParams: UnifiedOrderResult? = null,
    val errorMessage: String? = null
) 
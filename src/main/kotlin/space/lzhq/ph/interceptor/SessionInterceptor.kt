package space.lzhq.ph.interceptor

import com.ruoyi.common.utils.ServletUtils
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.slf4j.MDC
import org.springframework.stereotype.Component
import org.springframework.web.method.HandlerMethod
import org.springframework.web.servlet.HandlerInterceptor
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.Values.INVALID_CLIENT_SESSION_AJAX_RESULT
import space.lzhq.ph.common.Values.REQUIRE_ALIPAY_SESSION_AJAX_RESULT
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.getClientSession

@Component
class SessionInterceptor : HandlerInterceptor {

    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Bo<PERSON>an {
        if (handler !is Handler<PERSON>ethod) {
            return super.preHandle(request, response, handler)
        }

        val requireSession = handler.method.getAnnotation(RequireSession::class.java)
            ?: return true

        val session: Session? = request.getClientSession()
        if (session == null) {
            ServletUtils.renderString(response, INVALID_CLIENT_SESSION_AJAX_RESULT)
            return false
        }

        MDC.put("openId", session.openId)

        if (requireSession.clientType == ClientType.ALIPAY && !session.isAlipaySession) {
            ServletUtils.renderString(response, REQUIRE_ALIPAY_SESSION_AJAX_RESULT)
            return false
        }

        return true
    }

    override fun afterCompletion(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        ex: Exception?
    ) {
        MDC.remove("openId")
    }

}

package space.lzhq.ph.interceptor

import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.json.JSON
import com.ruoyi.common.utils.ServletUtils
import com.ruoyi.common.utils.spring.SpringUtils
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.stereotype.Component
import org.springframework.web.method.HandlerMethod
import org.springframework.web.servlet.HandlerInterceptor
import space.lzhq.ph.annotation.RequirePscNurseSession
import space.lzhq.ph.common.Values.CURRENT_PSC_NURSE
import space.lzhq.ph.domain.PscNurseSession
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IPscNurseSessionService

@Component
class PscNurseSessionInterceptor : HandlerInterceptor {

    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Boolean {
        if (handler !is HandlerMethod) {
            return super.preHandle(request, response, handler)
        }

        if (handler.method.getAnnotation(RequirePscNurseSession::class.java) == null) {
            return true
        }

        val session: Session? = request.getClientSession()
        if (session == null) {
            ServletUtils.renderString(response, JSON.marshal(AjaxResult.error("会话无效，请重新进入小程序")))
            return false
        }

        val nurseSessionService = SpringUtils.getBean(IPscNurseSessionService::class.java)
        val pscNurseSession: PscNurseSession? = nurseSessionService.selectPscNurseSessionByOpenId(session.openId)
        if (pscNurseSession == null) {
            ServletUtils.renderString(response, JSON.marshal(AjaxResult.error("护士未登录")))
            return false
        }

        request.setAttribute(CURRENT_PSC_NURSE, pscNurseSession)
        return true
    }
}

<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/"
                  xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  targetNamespace="http://tempuri.org/">
    <wsdl:types>
        <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
            <s:element name="findAllDepartments">
                <s:complexType/>
            </s:element>
            <s:element name="findAllDepartmentsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllDepartmentsResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllDoctors">
                <s:complexType/>
            </s:element>
            <s:element name="findAllDoctorsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllDoctorsResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllMedicalRecords">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllMedicalRecordsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllMedicalRecordsResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllquerySchedule">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryScheduleResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllqueryScheduleResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryRealtimeSchedule">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryRealtimeScheduleResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllqueryRealtimeScheduleResult"
                                   type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryRealtimeScheduleDetail">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryRealtimeScheduleDetailResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllqueryRealtimeScheduleDetailResult"
                                   type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryScheduleTime">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryScheduleTimeResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllqueryScheduleTimeResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAlladdPatientCard">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAlladdPatientCardResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAlladdPatientCardResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllcheckPatientCard">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllcheckPatientCardResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllcheckPatientCardResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryPatientInfo">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryPatientInfoResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllqueryPatientInfoResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryInHospitalPatient">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryInHospitalPatientResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllqueryInHospitalPatientResult"
                                   type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryInHospitalList">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllqueryInHospitalListResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllqueryInHospitalListResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllinsertSubscription">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllinsertSubscriptionResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllinsertSubscriptionResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllcancelSubscription">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findAllcancelSubscriptionResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findAllcancelSubscriptionResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findInspectionReport">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findInspectionReportResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findInspectionReportResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findCompleteLISReport">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findCompleteLISReportResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findCompleteLISReportResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findCompletePACReport">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findCompletePACReportResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findCompletePACReportResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findClinicNo">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findClinicNoResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findClinicNoResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findOutpatientPay">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findOutpatientPayResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findOutpatientPayResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="outpatientPay">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="outpatientPayResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="outpatientPayResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findgetPatientExpense">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findgetPatientExpenseResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findgetPatientExpenseResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findgetDayExpense">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findgetDayExpenseResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findgetDayExpenseResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findprePayExpense">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findprePayExpenseResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findprePayExpenseResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findprePayExpense_10_1">
                <s:complexType/>
            </s:element>
            <s:element name="findprePayExpense_10_1Response">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findprePayExpense_10_1Result" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findcheckECard">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findcheckECardResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findcheckECardResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="rechargeECard">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="rechargeECardResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="rechargeECardResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetCheckLedgerInfo">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetCheckLedgerInfoResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetCheckLedgerInfoResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findGetCheckLedgerInfoDetail">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findGetCheckLedgerInfoDetailResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findGetCheckLedgerInfoDetailResult"
                                   type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findOrderStatus">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findOrderStatusResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findOrderStatusResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findOrderStatus_15_1">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findOrderStatus_15_1Response">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findOrderStatus_15_1Result" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryClinicStatus">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryClinicStatusResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findqueryClinicStatusResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryOutpatientRefundRecords">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryOutpatientRefundRecordsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findqueryOutpatientRefundRecordsResult"
                                   type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryOutpatientFeeDetails">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryOutpatientFeeDetailsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findqueryOutpatientFeeDetailsResult"
                                   type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryPriceInformation">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryPriceInformationResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findqueryPriceInformationResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryRegisterArrears">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="findqueryRegisterArrearsResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="findqueryRegisterArrearsResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="string" nillable="true" type="s:string"/>
        </s:schema>
    </wsdl:types>
    <wsdl:message name="findAllDepartmentsSoapIn">
        <wsdl:part name="parameters" element="tns:findAllDepartments"/>
    </wsdl:message>
    <wsdl:message name="findAllDepartmentsSoapOut">
        <wsdl:part name="parameters" element="tns:findAllDepartmentsResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllDoctorsSoapIn">
        <wsdl:part name="parameters" element="tns:findAllDoctors"/>
    </wsdl:message>
    <wsdl:message name="findAllDoctorsSoapOut">
        <wsdl:part name="parameters" element="tns:findAllDoctorsResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllMedicalRecordsSoapIn">
        <wsdl:part name="parameters" element="tns:findAllMedicalRecords"/>
    </wsdl:message>
    <wsdl:message name="findAllMedicalRecordsSoapOut">
        <wsdl:part name="parameters" element="tns:findAllMedicalRecordsResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleSoapIn">
        <wsdl:part name="parameters" element="tns:findAllquerySchedule"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleSoapOut">
        <wsdl:part name="parameters" element="tns:findAllqueryScheduleResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleSoapIn">
        <wsdl:part name="parameters" element="tns:findAllqueryRealtimeSchedule"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleSoapOut">
        <wsdl:part name="parameters" element="tns:findAllqueryRealtimeScheduleResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleDetailSoapIn">
        <wsdl:part name="parameters" element="tns:findAllqueryRealtimeScheduleDetail"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleDetailSoapOut">
        <wsdl:part name="parameters" element="tns:findAllqueryRealtimeScheduleDetailResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleTimeSoapIn">
        <wsdl:part name="parameters" element="tns:findAllqueryScheduleTime"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleTimeSoapOut">
        <wsdl:part name="parameters" element="tns:findAllqueryScheduleTimeResponse"/>
    </wsdl:message>
    <wsdl:message name="findAlladdPatientCardSoapIn">
        <wsdl:part name="parameters" element="tns:findAlladdPatientCard"/>
    </wsdl:message>
    <wsdl:message name="findAlladdPatientCardSoapOut">
        <wsdl:part name="parameters" element="tns:findAlladdPatientCardResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllcheckPatientCardSoapIn">
        <wsdl:part name="parameters" element="tns:findAllcheckPatientCard"/>
    </wsdl:message>
    <wsdl:message name="findAllcheckPatientCardSoapOut">
        <wsdl:part name="parameters" element="tns:findAllcheckPatientCardResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryPatientInfoSoapIn">
        <wsdl:part name="parameters" element="tns:findAllqueryPatientInfo"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryPatientInfoSoapOut">
        <wsdl:part name="parameters" element="tns:findAllqueryPatientInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalPatientSoapIn">
        <wsdl:part name="parameters" element="tns:findAllqueryInHospitalPatient"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalPatientSoapOut">
        <wsdl:part name="parameters" element="tns:findAllqueryInHospitalPatientResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalListSoapIn">
        <wsdl:part name="parameters" element="tns:findAllqueryInHospitalList"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalListSoapOut">
        <wsdl:part name="parameters" element="tns:findAllqueryInHospitalListResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllinsertSubscriptionSoapIn">
        <wsdl:part name="parameters" element="tns:findAllinsertSubscription"/>
    </wsdl:message>
    <wsdl:message name="findAllinsertSubscriptionSoapOut">
        <wsdl:part name="parameters" element="tns:findAllinsertSubscriptionResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllcancelSubscriptionSoapIn">
        <wsdl:part name="parameters" element="tns:findAllcancelSubscription"/>
    </wsdl:message>
    <wsdl:message name="findAllcancelSubscriptionSoapOut">
        <wsdl:part name="parameters" element="tns:findAllcancelSubscriptionResponse"/>
    </wsdl:message>
    <wsdl:message name="findInspectionReportSoapIn">
        <wsdl:part name="parameters" element="tns:findInspectionReport"/>
    </wsdl:message>
    <wsdl:message name="findInspectionReportSoapOut">
        <wsdl:part name="parameters" element="tns:findInspectionReportResponse"/>
    </wsdl:message>
    <wsdl:message name="findCompleteLISReportSoapIn">
        <wsdl:part name="parameters" element="tns:findCompleteLISReport"/>
    </wsdl:message>
    <wsdl:message name="findCompleteLISReportSoapOut">
        <wsdl:part name="parameters" element="tns:findCompleteLISReportResponse"/>
    </wsdl:message>
    <wsdl:message name="findCompletePACReportSoapIn">
        <wsdl:part name="parameters" element="tns:findCompletePACReport"/>
    </wsdl:message>
    <wsdl:message name="findCompletePACReportSoapOut">
        <wsdl:part name="parameters" element="tns:findCompletePACReportResponse"/>
    </wsdl:message>
    <wsdl:message name="findClinicNoSoapIn">
        <wsdl:part name="parameters" element="tns:findClinicNo"/>
    </wsdl:message>
    <wsdl:message name="findClinicNoSoapOut">
        <wsdl:part name="parameters" element="tns:findClinicNoResponse"/>
    </wsdl:message>
    <wsdl:message name="findOutpatientPaySoapIn">
        <wsdl:part name="parameters" element="tns:findOutpatientPay"/>
    </wsdl:message>
    <wsdl:message name="findOutpatientPaySoapOut">
        <wsdl:part name="parameters" element="tns:findOutpatientPayResponse"/>
    </wsdl:message>
    <wsdl:message name="outpatientPaySoapIn">
        <wsdl:part name="parameters" element="tns:outpatientPay"/>
    </wsdl:message>
    <wsdl:message name="outpatientPaySoapOut">
        <wsdl:part name="parameters" element="tns:outpatientPayResponse"/>
    </wsdl:message>
    <wsdl:message name="findgetPatientExpenseSoapIn">
        <wsdl:part name="parameters" element="tns:findgetPatientExpense"/>
    </wsdl:message>
    <wsdl:message name="findgetPatientExpenseSoapOut">
        <wsdl:part name="parameters" element="tns:findgetPatientExpenseResponse"/>
    </wsdl:message>
    <wsdl:message name="findgetDayExpenseSoapIn">
        <wsdl:part name="parameters" element="tns:findgetDayExpense"/>
    </wsdl:message>
    <wsdl:message name="findgetDayExpenseSoapOut">
        <wsdl:part name="parameters" element="tns:findgetDayExpenseResponse"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpenseSoapIn">
        <wsdl:part name="parameters" element="tns:findprePayExpense"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpenseSoapOut">
        <wsdl:part name="parameters" element="tns:findprePayExpenseResponse"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpense_10_1SoapIn">
        <wsdl:part name="parameters" element="tns:findprePayExpense_10_1"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpense_10_1SoapOut">
        <wsdl:part name="parameters" element="tns:findprePayExpense_10_1Response"/>
    </wsdl:message>
    <wsdl:message name="findcheckECardSoapIn">
        <wsdl:part name="parameters" element="tns:findcheckECard"/>
    </wsdl:message>
    <wsdl:message name="findcheckECardSoapOut">
        <wsdl:part name="parameters" element="tns:findcheckECardResponse"/>
    </wsdl:message>
    <wsdl:message name="rechargeECardSoapIn">
        <wsdl:part name="parameters" element="tns:rechargeECard"/>
    </wsdl:message>
    <wsdl:message name="rechargeECardSoapOut">
        <wsdl:part name="parameters" element="tns:rechargeECardResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCheckLedgerInfoSoapIn">
        <wsdl:part name="parameters" element="tns:GetCheckLedgerInfo"/>
    </wsdl:message>
    <wsdl:message name="GetCheckLedgerInfoSoapOut">
        <wsdl:part name="parameters" element="tns:GetCheckLedgerInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="findGetCheckLedgerInfoDetailSoapIn">
        <wsdl:part name="parameters" element="tns:findGetCheckLedgerInfoDetail"/>
    </wsdl:message>
    <wsdl:message name="findGetCheckLedgerInfoDetailSoapOut">
        <wsdl:part name="parameters" element="tns:findGetCheckLedgerInfoDetailResponse"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatusSoapIn">
        <wsdl:part name="parameters" element="tns:findOrderStatus"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatusSoapOut">
        <wsdl:part name="parameters" element="tns:findOrderStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatus_15_1SoapIn">
        <wsdl:part name="parameters" element="tns:findOrderStatus_15_1"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatus_15_1SoapOut">
        <wsdl:part name="parameters" element="tns:findOrderStatus_15_1Response"/>
    </wsdl:message>
    <wsdl:message name="findqueryClinicStatusSoapIn">
        <wsdl:part name="parameters" element="tns:findqueryClinicStatus"/>
    </wsdl:message>
    <wsdl:message name="findqueryClinicStatusSoapOut">
        <wsdl:part name="parameters" element="tns:findqueryClinicStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientRefundRecordsSoapIn">
        <wsdl:part name="parameters" element="tns:findqueryOutpatientRefundRecords"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientRefundRecordsSoapOut">
        <wsdl:part name="parameters" element="tns:findqueryOutpatientRefundRecordsResponse"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientFeeDetailsSoapIn">
        <wsdl:part name="parameters" element="tns:findqueryOutpatientFeeDetails"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientFeeDetailsSoapOut">
        <wsdl:part name="parameters" element="tns:findqueryOutpatientFeeDetailsResponse"/>
    </wsdl:message>
    <wsdl:message name="findqueryPriceInformationSoapIn">
        <wsdl:part name="parameters" element="tns:findqueryPriceInformation"/>
    </wsdl:message>
    <wsdl:message name="findqueryPriceInformationSoapOut">
        <wsdl:part name="parameters" element="tns:findqueryPriceInformationResponse"/>
    </wsdl:message>
    <wsdl:message name="findqueryRegisterArrearsSoapIn">
        <wsdl:part name="parameters" element="tns:findqueryRegisterArrears"/>
    </wsdl:message>
    <wsdl:message name="findqueryRegisterArrearsSoapOut">
        <wsdl:part name="parameters" element="tns:findqueryRegisterArrearsResponse"/>
    </wsdl:message>
    <wsdl:message name="findAllDepartmentsHttpGetIn"/>
    <wsdl:message name="findAllDepartmentsHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllDoctorsHttpGetIn"/>
    <wsdl:message name="findAllDoctorsHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllMedicalRecordsHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllMedicalRecordsHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleDetailHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleDetailHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleTimeHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleTimeHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAlladdPatientCardHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAlladdPatientCardHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllcheckPatientCardHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllcheckPatientCardHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryPatientInfoHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryPatientInfoHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalPatientHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalPatientHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalListHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalListHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllinsertSubscriptionHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllinsertSubscriptionHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllcancelSubscriptionHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllcancelSubscriptionHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findInspectionReportHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findInspectionReportHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findCompleteLISReportHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findCompleteLISReportHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findCompletePACReportHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findCompletePACReportHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findClinicNoHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findClinicNoHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findOutpatientPayHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findOutpatientPayHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="outpatientPayHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="outpatientPayHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findgetPatientExpenseHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findgetPatientExpenseHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findgetDayExpenseHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findgetDayExpenseHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpenseHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpenseHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpense_10_1HttpGetIn"/>
    <wsdl:message name="findprePayExpense_10_1HttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findcheckECardHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findcheckECardHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="rechargeECardHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="rechargeECardHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetCheckLedgerInfoHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetCheckLedgerInfoHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findGetCheckLedgerInfoDetailHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findGetCheckLedgerInfoDetailHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatusHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatusHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatus_15_1HttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatus_15_1HttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryClinicStatusHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryClinicStatusHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientRefundRecordsHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientRefundRecordsHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientFeeDetailsHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientFeeDetailsHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryPriceInformationHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryPriceInformationHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryRegisterArrearsHttpGetIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryRegisterArrearsHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllDepartmentsHttpPostIn"/>
    <wsdl:message name="findAllDepartmentsHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllDoctorsHttpPostIn"/>
    <wsdl:message name="findAllDoctorsHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllMedicalRecordsHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllMedicalRecordsHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleDetailHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryRealtimeScheduleDetailHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleTimeHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryScheduleTimeHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAlladdPatientCardHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAlladdPatientCardHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllcheckPatientCardHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllcheckPatientCardHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryPatientInfoHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryPatientInfoHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalPatientHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalPatientHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalListHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllqueryInHospitalListHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllinsertSubscriptionHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllinsertSubscriptionHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findAllcancelSubscriptionHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findAllcancelSubscriptionHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findInspectionReportHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findInspectionReportHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findCompleteLISReportHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findCompleteLISReportHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findCompletePACReportHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findCompletePACReportHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findClinicNoHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findClinicNoHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findOutpatientPayHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findOutpatientPayHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="outpatientPayHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="outpatientPayHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findgetPatientExpenseHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findgetPatientExpenseHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findgetDayExpenseHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findgetDayExpenseHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpenseHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpenseHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findprePayExpense_10_1HttpPostIn"/>
    <wsdl:message name="findprePayExpense_10_1HttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findcheckECardHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findcheckECardHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="rechargeECardHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="rechargeECardHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetCheckLedgerInfoHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetCheckLedgerInfoHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findGetCheckLedgerInfoDetailHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findGetCheckLedgerInfoDetailHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatusHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatusHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatus_15_1HttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findOrderStatus_15_1HttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryClinicStatusHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryClinicStatusHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientRefundRecordsHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientRefundRecordsHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientFeeDetailsHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryOutpatientFeeDetailsHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryPriceInformationHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryPriceInformationHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryRegisterArrearsHttpPostIn">
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="findqueryRegisterArrearsHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:portType name="ServiceSoap">
        <wsdl:operation name="findAllDepartments">
            <wsdl:input message="tns:findAllDepartmentsSoapIn"/>
            <wsdl:output message="tns:findAllDepartmentsSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllDoctors">
            <wsdl:input message="tns:findAllDoctorsSoapIn"/>
            <wsdl:output message="tns:findAllDoctorsSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllMedicalRecords">
            <wsdl:input message="tns:findAllMedicalRecordsSoapIn"/>
            <wsdl:output message="tns:findAllMedicalRecordsSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllquerySchedule">
            <wsdl:input message="tns:findAllqueryScheduleSoapIn"/>
            <wsdl:output message="tns:findAllqueryScheduleSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeSchedule">
            <wsdl:input message="tns:findAllqueryRealtimeScheduleSoapIn"/>
            <wsdl:output message="tns:findAllqueryRealtimeScheduleSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeScheduleDetail">
            <wsdl:input message="tns:findAllqueryRealtimeScheduleDetailSoapIn"/>
            <wsdl:output message="tns:findAllqueryRealtimeScheduleDetailSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryScheduleTime">
            <wsdl:input message="tns:findAllqueryScheduleTimeSoapIn"/>
            <wsdl:output message="tns:findAllqueryScheduleTimeSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAlladdPatientCard">
            <wsdl:input message="tns:findAlladdPatientCardSoapIn"/>
            <wsdl:output message="tns:findAlladdPatientCardSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllcheckPatientCard">
            <wsdl:input message="tns:findAllcheckPatientCardSoapIn"/>
            <wsdl:output message="tns:findAllcheckPatientCardSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryPatientInfo">
            <wsdl:input message="tns:findAllqueryPatientInfoSoapIn"/>
            <wsdl:output message="tns:findAllqueryPatientInfoSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalPatient">
            <wsdl:input message="tns:findAllqueryInHospitalPatientSoapIn"/>
            <wsdl:output message="tns:findAllqueryInHospitalPatientSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalList">
            <wsdl:input message="tns:findAllqueryInHospitalListSoapIn"/>
            <wsdl:output message="tns:findAllqueryInHospitalListSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllinsertSubscription">
            <wsdl:input message="tns:findAllinsertSubscriptionSoapIn"/>
            <wsdl:output message="tns:findAllinsertSubscriptionSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllcancelSubscription">
            <wsdl:input message="tns:findAllcancelSubscriptionSoapIn"/>
            <wsdl:output message="tns:findAllcancelSubscriptionSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findInspectionReport">
            <wsdl:input message="tns:findInspectionReportSoapIn"/>
            <wsdl:output message="tns:findInspectionReportSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findCompleteLISReport">
            <wsdl:input message="tns:findCompleteLISReportSoapIn"/>
            <wsdl:output message="tns:findCompleteLISReportSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findCompletePACReport">
            <wsdl:input message="tns:findCompletePACReportSoapIn"/>
            <wsdl:output message="tns:findCompletePACReportSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findClinicNo">
            <wsdl:input message="tns:findClinicNoSoapIn"/>
            <wsdl:output message="tns:findClinicNoSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOutpatientPay">
            <wsdl:input message="tns:findOutpatientPaySoapIn"/>
            <wsdl:output message="tns:findOutpatientPaySoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="outpatientPay">
            <wsdl:input message="tns:outpatientPaySoapIn"/>
            <wsdl:output message="tns:outpatientPaySoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findgetPatientExpense">
            <wsdl:input message="tns:findgetPatientExpenseSoapIn"/>
            <wsdl:output message="tns:findgetPatientExpenseSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findgetDayExpense">
            <wsdl:input message="tns:findgetDayExpenseSoapIn"/>
            <wsdl:output message="tns:findgetDayExpenseSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense">
            <wsdl:input message="tns:findprePayExpenseSoapIn"/>
            <wsdl:output message="tns:findprePayExpenseSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense_10_1">
            <wsdl:input message="tns:findprePayExpense_10_1SoapIn"/>
            <wsdl:output message="tns:findprePayExpense_10_1SoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findcheckECard">
            <wsdl:input message="tns:findcheckECardSoapIn"/>
            <wsdl:output message="tns:findcheckECardSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="rechargeECard">
            <wsdl:input message="tns:rechargeECardSoapIn"/>
            <wsdl:output message="tns:rechargeECardSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetCheckLedgerInfo">
            <wsdl:input message="tns:GetCheckLedgerInfoSoapIn"/>
            <wsdl:output message="tns:GetCheckLedgerInfoSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findGetCheckLedgerInfoDetail">
            <wsdl:input message="tns:findGetCheckLedgerInfoDetailSoapIn"/>
            <wsdl:output message="tns:findGetCheckLedgerInfoDetailSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus">
            <wsdl:input message="tns:findOrderStatusSoapIn"/>
            <wsdl:output message="tns:findOrderStatusSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus_15_1">
            <wsdl:input message="tns:findOrderStatus_15_1SoapIn"/>
            <wsdl:output message="tns:findOrderStatus_15_1SoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryClinicStatus">
            <wsdl:input message="tns:findqueryClinicStatusSoapIn"/>
            <wsdl:output message="tns:findqueryClinicStatusSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientRefundRecords">
            <wsdl:input message="tns:findqueryOutpatientRefundRecordsSoapIn"/>
            <wsdl:output message="tns:findqueryOutpatientRefundRecordsSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientFeeDetails">
            <wsdl:input message="tns:findqueryOutpatientFeeDetailsSoapIn"/>
            <wsdl:output message="tns:findqueryOutpatientFeeDetailsSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryPriceInformation">
            <wsdl:input message="tns:findqueryPriceInformationSoapIn"/>
            <wsdl:output message="tns:findqueryPriceInformationSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryRegisterArrears">
            <wsdl:input message="tns:findqueryRegisterArrearsSoapIn"/>
            <wsdl:output message="tns:findqueryRegisterArrearsSoapOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:portType name="ServiceHttpGet">
        <wsdl:operation name="findAllDepartments">
            <wsdl:input message="tns:findAllDepartmentsHttpGetIn"/>
            <wsdl:output message="tns:findAllDepartmentsHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllDoctors">
            <wsdl:input message="tns:findAllDoctorsHttpGetIn"/>
            <wsdl:output message="tns:findAllDoctorsHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllMedicalRecords">
            <wsdl:input message="tns:findAllMedicalRecordsHttpGetIn"/>
            <wsdl:output message="tns:findAllMedicalRecordsHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllquerySchedule">
            <wsdl:input message="tns:findAllqueryScheduleHttpGetIn"/>
            <wsdl:output message="tns:findAllqueryScheduleHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeSchedule">
            <wsdl:input message="tns:findAllqueryRealtimeScheduleHttpGetIn"/>
            <wsdl:output message="tns:findAllqueryRealtimeScheduleHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeScheduleDetail">
            <wsdl:input message="tns:findAllqueryRealtimeScheduleDetailHttpGetIn"/>
            <wsdl:output message="tns:findAllqueryRealtimeScheduleDetailHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryScheduleTime">
            <wsdl:input message="tns:findAllqueryScheduleTimeHttpGetIn"/>
            <wsdl:output message="tns:findAllqueryScheduleTimeHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAlladdPatientCard">
            <wsdl:input message="tns:findAlladdPatientCardHttpGetIn"/>
            <wsdl:output message="tns:findAlladdPatientCardHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllcheckPatientCard">
            <wsdl:input message="tns:findAllcheckPatientCardHttpGetIn"/>
            <wsdl:output message="tns:findAllcheckPatientCardHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryPatientInfo">
            <wsdl:input message="tns:findAllqueryPatientInfoHttpGetIn"/>
            <wsdl:output message="tns:findAllqueryPatientInfoHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalPatient">
            <wsdl:input message="tns:findAllqueryInHospitalPatientHttpGetIn"/>
            <wsdl:output message="tns:findAllqueryInHospitalPatientHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalList">
            <wsdl:input message="tns:findAllqueryInHospitalListHttpGetIn"/>
            <wsdl:output message="tns:findAllqueryInHospitalListHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllinsertSubscription">
            <wsdl:input message="tns:findAllinsertSubscriptionHttpGetIn"/>
            <wsdl:output message="tns:findAllinsertSubscriptionHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllcancelSubscription">
            <wsdl:input message="tns:findAllcancelSubscriptionHttpGetIn"/>
            <wsdl:output message="tns:findAllcancelSubscriptionHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findInspectionReport">
            <wsdl:input message="tns:findInspectionReportHttpGetIn"/>
            <wsdl:output message="tns:findInspectionReportHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findCompleteLISReport">
            <wsdl:input message="tns:findCompleteLISReportHttpGetIn"/>
            <wsdl:output message="tns:findCompleteLISReportHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findCompletePACReport">
            <wsdl:input message="tns:findCompletePACReportHttpGetIn"/>
            <wsdl:output message="tns:findCompletePACReportHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findClinicNo">
            <wsdl:input message="tns:findClinicNoHttpGetIn"/>
            <wsdl:output message="tns:findClinicNoHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOutpatientPay">
            <wsdl:input message="tns:findOutpatientPayHttpGetIn"/>
            <wsdl:output message="tns:findOutpatientPayHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="outpatientPay">
            <wsdl:input message="tns:outpatientPayHttpGetIn"/>
            <wsdl:output message="tns:outpatientPayHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findgetPatientExpense">
            <wsdl:input message="tns:findgetPatientExpenseHttpGetIn"/>
            <wsdl:output message="tns:findgetPatientExpenseHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findgetDayExpense">
            <wsdl:input message="tns:findgetDayExpenseHttpGetIn"/>
            <wsdl:output message="tns:findgetDayExpenseHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense">
            <wsdl:input message="tns:findprePayExpenseHttpGetIn"/>
            <wsdl:output message="tns:findprePayExpenseHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense_10_1">
            <wsdl:input message="tns:findprePayExpense_10_1HttpGetIn"/>
            <wsdl:output message="tns:findprePayExpense_10_1HttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findcheckECard">
            <wsdl:input message="tns:findcheckECardHttpGetIn"/>
            <wsdl:output message="tns:findcheckECardHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="rechargeECard">
            <wsdl:input message="tns:rechargeECardHttpGetIn"/>
            <wsdl:output message="tns:rechargeECardHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetCheckLedgerInfo">
            <wsdl:input message="tns:GetCheckLedgerInfoHttpGetIn"/>
            <wsdl:output message="tns:GetCheckLedgerInfoHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findGetCheckLedgerInfoDetail">
            <wsdl:input message="tns:findGetCheckLedgerInfoDetailHttpGetIn"/>
            <wsdl:output message="tns:findGetCheckLedgerInfoDetailHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus">
            <wsdl:input message="tns:findOrderStatusHttpGetIn"/>
            <wsdl:output message="tns:findOrderStatusHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus_15_1">
            <wsdl:input message="tns:findOrderStatus_15_1HttpGetIn"/>
            <wsdl:output message="tns:findOrderStatus_15_1HttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryClinicStatus">
            <wsdl:input message="tns:findqueryClinicStatusHttpGetIn"/>
            <wsdl:output message="tns:findqueryClinicStatusHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientRefundRecords">
            <wsdl:input message="tns:findqueryOutpatientRefundRecordsHttpGetIn"/>
            <wsdl:output message="tns:findqueryOutpatientRefundRecordsHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientFeeDetails">
            <wsdl:input message="tns:findqueryOutpatientFeeDetailsHttpGetIn"/>
            <wsdl:output message="tns:findqueryOutpatientFeeDetailsHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryPriceInformation">
            <wsdl:input message="tns:findqueryPriceInformationHttpGetIn"/>
            <wsdl:output message="tns:findqueryPriceInformationHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryRegisterArrears">
            <wsdl:input message="tns:findqueryRegisterArrearsHttpGetIn"/>
            <wsdl:output message="tns:findqueryRegisterArrearsHttpGetOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:portType name="ServiceHttpPost">
        <wsdl:operation name="findAllDepartments">
            <wsdl:input message="tns:findAllDepartmentsHttpPostIn"/>
            <wsdl:output message="tns:findAllDepartmentsHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllDoctors">
            <wsdl:input message="tns:findAllDoctorsHttpPostIn"/>
            <wsdl:output message="tns:findAllDoctorsHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllMedicalRecords">
            <wsdl:input message="tns:findAllMedicalRecordsHttpPostIn"/>
            <wsdl:output message="tns:findAllMedicalRecordsHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllquerySchedule">
            <wsdl:input message="tns:findAllqueryScheduleHttpPostIn"/>
            <wsdl:output message="tns:findAllqueryScheduleHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeSchedule">
            <wsdl:input message="tns:findAllqueryRealtimeScheduleHttpPostIn"/>
            <wsdl:output message="tns:findAllqueryRealtimeScheduleHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeScheduleDetail">
            <wsdl:input message="tns:findAllqueryRealtimeScheduleDetailHttpPostIn"/>
            <wsdl:output message="tns:findAllqueryRealtimeScheduleDetailHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryScheduleTime">
            <wsdl:input message="tns:findAllqueryScheduleTimeHttpPostIn"/>
            <wsdl:output message="tns:findAllqueryScheduleTimeHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAlladdPatientCard">
            <wsdl:input message="tns:findAlladdPatientCardHttpPostIn"/>
            <wsdl:output message="tns:findAlladdPatientCardHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllcheckPatientCard">
            <wsdl:input message="tns:findAllcheckPatientCardHttpPostIn"/>
            <wsdl:output message="tns:findAllcheckPatientCardHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryPatientInfo">
            <wsdl:input message="tns:findAllqueryPatientInfoHttpPostIn"/>
            <wsdl:output message="tns:findAllqueryPatientInfoHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalPatient">
            <wsdl:input message="tns:findAllqueryInHospitalPatientHttpPostIn"/>
            <wsdl:output message="tns:findAllqueryInHospitalPatientHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalList">
            <wsdl:input message="tns:findAllqueryInHospitalListHttpPostIn"/>
            <wsdl:output message="tns:findAllqueryInHospitalListHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllinsertSubscription">
            <wsdl:input message="tns:findAllinsertSubscriptionHttpPostIn"/>
            <wsdl:output message="tns:findAllinsertSubscriptionHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findAllcancelSubscription">
            <wsdl:input message="tns:findAllcancelSubscriptionHttpPostIn"/>
            <wsdl:output message="tns:findAllcancelSubscriptionHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findInspectionReport">
            <wsdl:input message="tns:findInspectionReportHttpPostIn"/>
            <wsdl:output message="tns:findInspectionReportHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findCompleteLISReport">
            <wsdl:input message="tns:findCompleteLISReportHttpPostIn"/>
            <wsdl:output message="tns:findCompleteLISReportHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findCompletePACReport">
            <wsdl:input message="tns:findCompletePACReportHttpPostIn"/>
            <wsdl:output message="tns:findCompletePACReportHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findClinicNo">
            <wsdl:input message="tns:findClinicNoHttpPostIn"/>
            <wsdl:output message="tns:findClinicNoHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOutpatientPay">
            <wsdl:input message="tns:findOutpatientPayHttpPostIn"/>
            <wsdl:output message="tns:findOutpatientPayHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="outpatientPay">
            <wsdl:input message="tns:outpatientPayHttpPostIn"/>
            <wsdl:output message="tns:outpatientPayHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findgetPatientExpense">
            <wsdl:input message="tns:findgetPatientExpenseHttpPostIn"/>
            <wsdl:output message="tns:findgetPatientExpenseHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findgetDayExpense">
            <wsdl:input message="tns:findgetDayExpenseHttpPostIn"/>
            <wsdl:output message="tns:findgetDayExpenseHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense">
            <wsdl:input message="tns:findprePayExpenseHttpPostIn"/>
            <wsdl:output message="tns:findprePayExpenseHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense_10_1">
            <wsdl:input message="tns:findprePayExpense_10_1HttpPostIn"/>
            <wsdl:output message="tns:findprePayExpense_10_1HttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findcheckECard">
            <wsdl:input message="tns:findcheckECardHttpPostIn"/>
            <wsdl:output message="tns:findcheckECardHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="rechargeECard">
            <wsdl:input message="tns:rechargeECardHttpPostIn"/>
            <wsdl:output message="tns:rechargeECardHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetCheckLedgerInfo">
            <wsdl:input message="tns:GetCheckLedgerInfoHttpPostIn"/>
            <wsdl:output message="tns:GetCheckLedgerInfoHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findGetCheckLedgerInfoDetail">
            <wsdl:input message="tns:findGetCheckLedgerInfoDetailHttpPostIn"/>
            <wsdl:output message="tns:findGetCheckLedgerInfoDetailHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus">
            <wsdl:input message="tns:findOrderStatusHttpPostIn"/>
            <wsdl:output message="tns:findOrderStatusHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus_15_1">
            <wsdl:input message="tns:findOrderStatus_15_1HttpPostIn"/>
            <wsdl:output message="tns:findOrderStatus_15_1HttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryClinicStatus">
            <wsdl:input message="tns:findqueryClinicStatusHttpPostIn"/>
            <wsdl:output message="tns:findqueryClinicStatusHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientRefundRecords">
            <wsdl:input message="tns:findqueryOutpatientRefundRecordsHttpPostIn"/>
            <wsdl:output message="tns:findqueryOutpatientRefundRecordsHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientFeeDetails">
            <wsdl:input message="tns:findqueryOutpatientFeeDetailsHttpPostIn"/>
            <wsdl:output message="tns:findqueryOutpatientFeeDetailsHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryPriceInformation">
            <wsdl:input message="tns:findqueryPriceInformationHttpPostIn"/>
            <wsdl:output message="tns:findqueryPriceInformationHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="findqueryRegisterArrears">
            <wsdl:input message="tns:findqueryRegisterArrearsHttpPostIn"/>
            <wsdl:output message="tns:findqueryRegisterArrearsHttpPostOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="ServiceSoap" type="tns:ServiceSoap">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="findAllDepartments">
            <soap:operation soapAction="http://tempuri.org/findAllDepartments" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllDoctors">
            <soap:operation soapAction="http://tempuri.org/findAllDoctors" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllMedicalRecords">
            <soap:operation soapAction="http://tempuri.org/findAllMedicalRecords" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllquerySchedule">
            <soap:operation soapAction="http://tempuri.org/findAllquerySchedule" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeSchedule">
            <soap:operation soapAction="http://tempuri.org/findAllqueryRealtimeSchedule" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeScheduleDetail">
            <soap:operation soapAction="http://tempuri.org/findAllqueryRealtimeScheduleDetail" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryScheduleTime">
            <soap:operation soapAction="http://tempuri.org/findAllqueryScheduleTime" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAlladdPatientCard">
            <soap:operation soapAction="http://tempuri.org/findAlladdPatientCard" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllcheckPatientCard">
            <soap:operation soapAction="http://tempuri.org/findAllcheckPatientCard" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryPatientInfo">
            <soap:operation soapAction="http://tempuri.org/findAllqueryPatientInfo" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalPatient">
            <soap:operation soapAction="http://tempuri.org/findAllqueryInHospitalPatient" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalList">
            <soap:operation soapAction="http://tempuri.org/findAllqueryInHospitalList" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllinsertSubscription">
            <soap:operation soapAction="http://tempuri.org/findAllinsertSubscription" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllcancelSubscription">
            <soap:operation soapAction="http://tempuri.org/findAllcancelSubscription" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findInspectionReport">
            <soap:operation soapAction="http://tempuri.org/findInspectionReport" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findCompleteLISReport">
            <soap:operation soapAction="http://tempuri.org/findCompleteLISReport" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findCompletePACReport">
            <soap:operation soapAction="http://tempuri.org/findCompletePACReport" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findClinicNo">
            <soap:operation soapAction="http://tempuri.org/findClinicNo" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOutpatientPay">
            <soap:operation soapAction="http://tempuri.org/findOutpatientPay" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="outpatientPay">
            <soap:operation soapAction="http://tempuri.org/outpatientPay" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findgetPatientExpense">
            <soap:operation soapAction="http://tempuri.org/findgetPatientExpense" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findgetDayExpense">
            <soap:operation soapAction="http://tempuri.org/findgetDayExpense" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense">
            <soap:operation soapAction="http://tempuri.org/findprePayExpense" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense_10_1">
            <soap:operation soapAction="http://tempuri.org/findprePayExpense_10_1" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findcheckECard">
            <soap:operation soapAction="http://tempuri.org/findcheckECard" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="rechargeECard">
            <soap:operation soapAction="http://tempuri.org/rechargeECard" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCheckLedgerInfo">
            <soap:operation soapAction="http://tempuri.org/GetCheckLedgerInfo" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findGetCheckLedgerInfoDetail">
            <soap:operation soapAction="http://tempuri.org/findGetCheckLedgerInfoDetail" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus">
            <soap:operation soapAction="http://tempuri.org/findOrderStatus" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus_15_1">
            <soap:operation soapAction="http://tempuri.org/findOrderStatus_15_1" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryClinicStatus">
            <soap:operation soapAction="http://tempuri.org/findqueryClinicStatus" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientRefundRecords">
            <soap:operation soapAction="http://tempuri.org/findqueryOutpatientRefundRecords" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientFeeDetails">
            <soap:operation soapAction="http://tempuri.org/findqueryOutpatientFeeDetails" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryPriceInformation">
            <soap:operation soapAction="http://tempuri.org/findqueryPriceInformation" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryRegisterArrears">
            <soap:operation soapAction="http://tempuri.org/findqueryRegisterArrears" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="ServiceSoap12" type="tns:ServiceSoap">
        <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="findAllDepartments">
            <soap12:operation soapAction="http://tempuri.org/findAllDepartments" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllDoctors">
            <soap12:operation soapAction="http://tempuri.org/findAllDoctors" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllMedicalRecords">
            <soap12:operation soapAction="http://tempuri.org/findAllMedicalRecords" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllquerySchedule">
            <soap12:operation soapAction="http://tempuri.org/findAllquerySchedule" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeSchedule">
            <soap12:operation soapAction="http://tempuri.org/findAllqueryRealtimeSchedule" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeScheduleDetail">
            <soap12:operation soapAction="http://tempuri.org/findAllqueryRealtimeScheduleDetail" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryScheduleTime">
            <soap12:operation soapAction="http://tempuri.org/findAllqueryScheduleTime" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAlladdPatientCard">
            <soap12:operation soapAction="http://tempuri.org/findAlladdPatientCard" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllcheckPatientCard">
            <soap12:operation soapAction="http://tempuri.org/findAllcheckPatientCard" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryPatientInfo">
            <soap12:operation soapAction="http://tempuri.org/findAllqueryPatientInfo" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalPatient">
            <soap12:operation soapAction="http://tempuri.org/findAllqueryInHospitalPatient" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalList">
            <soap12:operation soapAction="http://tempuri.org/findAllqueryInHospitalList" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllinsertSubscription">
            <soap12:operation soapAction="http://tempuri.org/findAllinsertSubscription" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllcancelSubscription">
            <soap12:operation soapAction="http://tempuri.org/findAllcancelSubscription" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findInspectionReport">
            <soap12:operation soapAction="http://tempuri.org/findInspectionReport" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findCompleteLISReport">
            <soap12:operation soapAction="http://tempuri.org/findCompleteLISReport" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findCompletePACReport">
            <soap12:operation soapAction="http://tempuri.org/findCompletePACReport" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findClinicNo">
            <soap12:operation soapAction="http://tempuri.org/findClinicNo" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOutpatientPay">
            <soap12:operation soapAction="http://tempuri.org/findOutpatientPay" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="outpatientPay">
            <soap12:operation soapAction="http://tempuri.org/outpatientPay" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findgetPatientExpense">
            <soap12:operation soapAction="http://tempuri.org/findgetPatientExpense" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findgetDayExpense">
            <soap12:operation soapAction="http://tempuri.org/findgetDayExpense" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense">
            <soap12:operation soapAction="http://tempuri.org/findprePayExpense" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense_10_1">
            <soap12:operation soapAction="http://tempuri.org/findprePayExpense_10_1" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findcheckECard">
            <soap12:operation soapAction="http://tempuri.org/findcheckECard" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="rechargeECard">
            <soap12:operation soapAction="http://tempuri.org/rechargeECard" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCheckLedgerInfo">
            <soap12:operation soapAction="http://tempuri.org/GetCheckLedgerInfo" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findGetCheckLedgerInfoDetail">
            <soap12:operation soapAction="http://tempuri.org/findGetCheckLedgerInfoDetail" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus">
            <soap12:operation soapAction="http://tempuri.org/findOrderStatus" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus_15_1">
            <soap12:operation soapAction="http://tempuri.org/findOrderStatus_15_1" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryClinicStatus">
            <soap12:operation soapAction="http://tempuri.org/findqueryClinicStatus" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientRefundRecords">
            <soap12:operation soapAction="http://tempuri.org/findqueryOutpatientRefundRecords" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientFeeDetails">
            <soap12:operation soapAction="http://tempuri.org/findqueryOutpatientFeeDetails" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryPriceInformation">
            <soap12:operation soapAction="http://tempuri.org/findqueryPriceInformation" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryRegisterArrears">
            <soap12:operation soapAction="http://tempuri.org/findqueryRegisterArrears" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="ServiceHttpGet" type="tns:ServiceHttpGet">
        <http:binding verb="GET"/>
        <wsdl:operation name="findAllDepartments">
            <http:operation location="/findAllDepartments"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllDoctors">
            <http:operation location="/findAllDoctors"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllMedicalRecords">
            <http:operation location="/findAllMedicalRecords"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllquerySchedule">
            <http:operation location="/findAllquerySchedule"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeSchedule">
            <http:operation location="/findAllqueryRealtimeSchedule"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeScheduleDetail">
            <http:operation location="/findAllqueryRealtimeScheduleDetail"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryScheduleTime">
            <http:operation location="/findAllqueryScheduleTime"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAlladdPatientCard">
            <http:operation location="/findAlladdPatientCard"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllcheckPatientCard">
            <http:operation location="/findAllcheckPatientCard"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryPatientInfo">
            <http:operation location="/findAllqueryPatientInfo"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalPatient">
            <http:operation location="/findAllqueryInHospitalPatient"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalList">
            <http:operation location="/findAllqueryInHospitalList"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllinsertSubscription">
            <http:operation location="/findAllinsertSubscription"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllcancelSubscription">
            <http:operation location="/findAllcancelSubscription"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findInspectionReport">
            <http:operation location="/findInspectionReport"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findCompleteLISReport">
            <http:operation location="/findCompleteLISReport"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findCompletePACReport">
            <http:operation location="/findCompletePACReport"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findClinicNo">
            <http:operation location="/findClinicNo"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOutpatientPay">
            <http:operation location="/findOutpatientPay"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="outpatientPay">
            <http:operation location="/outpatientPay"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findgetPatientExpense">
            <http:operation location="/findgetPatientExpense"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findgetDayExpense">
            <http:operation location="/findgetDayExpense"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense">
            <http:operation location="/findprePayExpense"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense_10_1">
            <http:operation location="/findprePayExpense_10_1"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findcheckECard">
            <http:operation location="/findcheckECard"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="rechargeECard">
            <http:operation location="/rechargeECard"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCheckLedgerInfo">
            <http:operation location="/GetCheckLedgerInfo"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findGetCheckLedgerInfoDetail">
            <http:operation location="/findGetCheckLedgerInfoDetail"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus">
            <http:operation location="/findOrderStatus"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus_15_1">
            <http:operation location="/findOrderStatus_15_1"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryClinicStatus">
            <http:operation location="/findqueryClinicStatus"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientRefundRecords">
            <http:operation location="/findqueryOutpatientRefundRecords"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientFeeDetails">
            <http:operation location="/findqueryOutpatientFeeDetails"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryPriceInformation">
            <http:operation location="/findqueryPriceInformation"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryRegisterArrears">
            <http:operation location="/findqueryRegisterArrears"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="ServiceHttpPost" type="tns:ServiceHttpPost">
        <http:binding verb="POST"/>
        <wsdl:operation name="findAllDepartments">
            <http:operation location="/findAllDepartments"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllDoctors">
            <http:operation location="/findAllDoctors"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllMedicalRecords">
            <http:operation location="/findAllMedicalRecords"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllquerySchedule">
            <http:operation location="/findAllquerySchedule"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeSchedule">
            <http:operation location="/findAllqueryRealtimeSchedule"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryRealtimeScheduleDetail">
            <http:operation location="/findAllqueryRealtimeScheduleDetail"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryScheduleTime">
            <http:operation location="/findAllqueryScheduleTime"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAlladdPatientCard">
            <http:operation location="/findAlladdPatientCard"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllcheckPatientCard">
            <http:operation location="/findAllcheckPatientCard"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryPatientInfo">
            <http:operation location="/findAllqueryPatientInfo"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalPatient">
            <http:operation location="/findAllqueryInHospitalPatient"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllqueryInHospitalList">
            <http:operation location="/findAllqueryInHospitalList"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllinsertSubscription">
            <http:operation location="/findAllinsertSubscription"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findAllcancelSubscription">
            <http:operation location="/findAllcancelSubscription"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findInspectionReport">
            <http:operation location="/findInspectionReport"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findCompleteLISReport">
            <http:operation location="/findCompleteLISReport"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findCompletePACReport">
            <http:operation location="/findCompletePACReport"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findClinicNo">
            <http:operation location="/findClinicNo"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOutpatientPay">
            <http:operation location="/findOutpatientPay"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="outpatientPay">
            <http:operation location="/outpatientPay"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findgetPatientExpense">
            <http:operation location="/findgetPatientExpense"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findgetDayExpense">
            <http:operation location="/findgetDayExpense"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense">
            <http:operation location="/findprePayExpense"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findprePayExpense_10_1">
            <http:operation location="/findprePayExpense_10_1"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findcheckECard">
            <http:operation location="/findcheckECard"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="rechargeECard">
            <http:operation location="/rechargeECard"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCheckLedgerInfo">
            <http:operation location="/GetCheckLedgerInfo"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findGetCheckLedgerInfoDetail">
            <http:operation location="/findGetCheckLedgerInfoDetail"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus">
            <http:operation location="/findOrderStatus"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findOrderStatus_15_1">
            <http:operation location="/findOrderStatus_15_1"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryClinicStatus">
            <http:operation location="/findqueryClinicStatus"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientRefundRecords">
            <http:operation location="/findqueryOutpatientRefundRecords"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryOutpatientFeeDetails">
            <http:operation location="/findqueryOutpatientFeeDetails"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryPriceInformation">
            <http:operation location="/findqueryPriceInformation"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="findqueryRegisterArrears">
            <http:operation location="/findqueryRegisterArrears"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="Service">
        <wsdl:port name="ServiceSoap" binding="tns:ServiceSoap">
            <soap:address location="http://127.0.0.1:6001/Service.asmx"/>
        </wsdl:port>
        <wsdl:port name="ServiceSoap12" binding="tns:ServiceSoap12">
            <soap12:address location="http://127.0.0.1:6001/Service.asmx"/>
        </wsdl:port>
        <wsdl:port name="ServiceHttpGet" binding="tns:ServiceHttpGet">
            <http:address location="http://127.0.0.1:6001/Service.asmx"/>
        </wsdl:port>
        <wsdl:port name="ServiceHttpPost" binding="tns:ServiceHttpPost">
            <http:address location="http://127.0.0.1:6001/Service.asmx"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
<!DOCTYPE html>
<html
        lang="zh"
        xmlns:th="http://www.thymeleaf.org"
>
<head>
    <th:block th:include="include :: header('病历邮寄收入统计管理')"/>
    <title>病历邮寄收入统计管理</title>
    <style>
        /* 美化搜索和重置按钮 */
        .btn-custom {
            border-radius: 4px;
            border: none;
            padding: 6px 15px;
            transition: all 0.3s;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }

        .btn-search {
            background-color: #1890ff;
            color: white;
        }

        .btn-search:hover {
            background-color: #40a9ff;
        }

        .btn-reset {
            background-color: #faad14;
            color: white;
        }

        .btn-reset:hover {
            background-color: #ffc53d;
        }

        /* 工具栏按钮美化 */
        .btn-toolbar {
            padding: 8px 16px;
            margin-right: 10px;
            border-radius: 4px;
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            font-weight: 500;
            transition: all 0.3s;
        }

        /* 概览卡片样式 */
        .overview-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .overview-item {
            display: inline-block;
            margin-right: 40px;
            text-align: center;
        }

        .overview-number {
            font-size: 28px;
            font-weight: bold;
            display: block;
        }

        .overview-label {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
        }

        /* 表格样式优化 */
        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }

        .table td {
            vertical-align: middle;
        }

        /* 金额高亮 */
        .amount-highlight {
            color: #e74c3c;
            font-weight: bold;
        }

        /* 日期范围选择器样式 */
        .date-range-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        /* 日期链接样式 */
        .date-link {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .date-link:hover {
            color: #40a9ff;
            text-decoration: underline;
        }

        .form-control {
            border-radius: 4px;
            border: 1px solid #ddd;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 0.2rem rgba(24, 144, 255, 0.25);
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12">
            <!-- 概览统计卡片 -->
            <div class="overview-card" id="overviewCard">
                <h5 style="margin-bottom: 15px; font-size: 18px;">收入统计概览</h5>
                <div id="overviewContent">
                    <div class="overview-item">
                        <span class="overview-number" id="totalDays">-</span>
                        <div class="overview-label">统计天数</div>
                    </div>
                    <div class="overview-item">
                        <span class="overview-number" id="totalTransactions">-</span>
                        <div class="overview-label">总交易笔数</div>
                    </div>
                    <div class="overview-item">
                        <span class="overview-number" id="totalAmount">-</span>
                        <div class="overview-label">总交易金额(元)</div>
                    </div>
                    <div class="overview-item">
                        <span class="overview-number" id="avgDailyTransactions">-</span>
                        <div class="overview-label">日均交易笔数</div>
                    </div>
                    <div class="overview-item">
                        <span class="overview-number" id="avgDailyAmount">-</span>
                        <div class="overview-label">日均交易金额(元)</div>
                    </div>
                </div>
            </div>

            <!-- 搜索表单 -->
            <div class="ibox-content">
                <div class="date-range-container">
                    <form class="form-horizontal" id="searchForm">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">日期范围：</label>
                            <div class="col-sm-3">
                                <input type="date" class="form-control" id="startDate" name="startDate" placeholder="开始日期">
                            </div>
                            <div class="col-sm-3">
                                <input type="date" class="form-control" id="endDate" name="endDate" placeholder="结束日期">
                            </div>
                            <div class="col-sm-4">
                                <button type="button" class="btn btn-custom btn-search" onclick="$.table.search()">
                                    <i class="fa fa-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-custom btn-reset" onclick="resetSearch()">
                                    <i class="fa fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 工具栏 -->
                <div class="row">
                    <div class="col-sm-12">
                        <div class="toolbar">
                            <button type="button" class="btn btn-success btn-toolbar" onclick="$.table.exportExcel()">
                                <i class="fa fa-download"></i> 导出
                            </button>
                            <button type="button" class="btn btn-info btn-toolbar" onclick="showCalculateModal()">
                                <i class="fa fa-calculator"></i> 手动统计
                            </button>
                            <button type="button" class="btn btn-warning btn-toolbar" onclick="refreshOverview()">
                                <i class="fa fa-refresh"></i> 刷新概览
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
</div>

<!-- 手动统计模态框 -->
<div class="modal fade" id="calculateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">手动统计收入数据</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="calculateForm">
                    <div class="form-group">
                        <label>统计类型：</label>
                        <select class="form-control" id="calculateType">
                            <option value="single">单日统计</option>
                            <option value="range">日期范围统计</option>
                        </select>
                    </div>
                    <div class="form-group" id="singleDateGroup">
                        <label>目标日期：</label>
                        <input type="date" class="form-control" id="targetDate" name="targetDate">
                    </div>
                    <div class="form-group" id="rangeDateGroup" style="display: none;">
                        <label>开始日期：</label>
                        <input type="date" class="form-control" id="rangeStartDate" name="rangeStartDate">
                        <br>
                        <label>结束日期：</label>
                        <input type="date" class="form-control" id="rangeEndDate" name="rangeEndDate">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeCalculate()">开始统计</button>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script src="//cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>

<script th:inline="javascript">
    const prefix = ctx + "ph/medical-record-revenue-statistics";
    // 权限检查
    const hasViewPermission = /*[[${@permission.isPermitted('ph:medical-record-application:view')}]]*/ false;
    const hasListPermission = /*[[${@permission.isPermitted('ph:medical-record-application:list')}]]*/ false;
    const canViewDetails = hasViewPermission && hasListPermission;

    $(function() {
        // 设置默认日期：开始时间为一个月前，结束时间为今天
        const startDate = dayjs().subtract(1, "month").format("YYYY-MM-DD");
        const endDate = dayjs().format("YYYY-MM-DD");
        $("#startDate").val(startDate);
        $("#endDate").val(endDate);
        
        var options = {
            url: prefix + "/list",
            createUrl: "",
            updateUrl: "",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "收入统计",
            showSearch: false,
            showColumns: false,
            showToggle: false,
            columns: [{
                field: 'transactionDate',
                title: '交易日期',
                sortable: true,
                formatter: function(value, row, index) {
                    if (!value || value === '-') {
                        return '-';
                    }
                    
                    // 如果用户有权限，显示为可点击的链接
                    if (canViewDetails) {
                        return '<a href="javascript:void(0)" class="date-link" onclick="viewDateDetails(\'' + value + '\')">'
                             + value + '</a>';
                    } else {
                        return value;
                    }
                }
            }, {
                field: 'transactionCount',
                title: '交易笔数',
                formatter: function(value, row, index) {
                    return value || 0;
                }
            }, {
                field: 'transactionAmount',
                title: '交易金额(元)',
                formatter: function(value, row, index) {
                    if (value) {
                        return '<span class="amount-highlight">¥' + parseFloat(value).toFixed(2) + '</span>';
                    }
                    return '<span class="amount-highlight">¥0.00</span>';
                }
            }]
        };
        $.table.init(options);
        
        // 初始化概览数据
        refreshOverview();
        
        // 统计类型切换事件
        $('#calculateType').change(function() {
            if ($(this).val() === 'single') {
                $('#singleDateGroup').show();
                $('#rangeDateGroup').hide();
            } else {
                $('#singleDateGroup').hide();
                $('#rangeDateGroup').show();
            }
        });
    });

    // 重置搜索
    function resetSearch() {
        // 重置默认日期
        const startDate = dayjs().subtract(1, "month").format("YYYY-MM-DD");
        const endDate = dayjs().format("YYYY-MM-DD");
        $("#startDate").val(startDate);
        $("#endDate").val(endDate);
        
        $.table.search();
        refreshOverview();
    }

    // 显示手动统计模态框
    function showCalculateModal() {
        $('#calculateModal').modal('show');
        // 使用dayjs设置默认日期
        $('#targetDate').val(dayjs().format('YYYY-MM-DD'));
        $('#rangeStartDate').val(dayjs().subtract(7, 'day').format('YYYY-MM-DD'));
        $('#rangeEndDate').val(dayjs().format('YYYY-MM-DD'));
    }

    // 执行统计
    function executeCalculate() {
        var calculateType = $('#calculateType').val();
        var url, data;
        
        if (calculateType === 'single') {
            var targetDate = $('#targetDate').val();
            if (!targetDate) {
                $.modal.msgError("请选择目标日期");
                return;
            }
            url = prefix + "/calculate";
            data = { targetDate: targetDate };
        } else {
            var rangeStartDate = $('#rangeStartDate').val();
            var rangeEndDate = $('#rangeEndDate').val();
            if (!rangeStartDate || !rangeEndDate) {
                $.modal.msgError("请选择开始和结束日期");
                return;
            }
            if (rangeStartDate > rangeEndDate) {
                $.modal.msgError("开始日期不能晚于结束日期");
                return;
            }
            
            // 限制批量统计的日期范围不得超过30天
            var startDate = dayjs(rangeStartDate);
            var endDate = dayjs(rangeEndDate);
            var daysDiff = endDate.diff(startDate, 'day') + 1; // +1因为包含开始和结束日期
            if (daysDiff > 30) {
                $.modal.msgError("批量统计的日期范围不能超过30天，当前选择了" + daysDiff + "天");
                return;
            }
            url = prefix + "/calculate-range";
            data = { startDate: rangeStartDate, endDate: rangeEndDate };
        }
        
        // 根据操作类型显示不同的提示信息
        var loadingMessage = url.includes('/calculate-range') 
            ? "正在批量统计数据，请耐心等待（可能需要几分钟）..." 
            : "正在统计数据，请稍候...";
        
        $.modal.loading(loadingMessage);
        
        $.ajax({
            type: "POST",
            url: url,
            data: data,
            timeout: 360000, // 6分钟超时，略大于后端5分钟超时
            success: function(result) {
                $.modal.closeLoading();
                if (result.code == web_status.SUCCESS) {
                    $.modal.msgSuccess(result.msg || "统计完成");
                    $('#calculateModal').modal('hide');
                    $.table.refresh();
                    refreshOverview();
                } else {
                    $.modal.msgError(result.msg || "统计失败");
                }
            },
            error: function(xhr, status, error) {
                $.modal.closeLoading();
                if (status === 'timeout') {
                    $.modal.msgError("操作超时，请稍后再试或联系管理员");
                } else {
                    $.modal.msgError("统计失败，请重试");
                }
            }
        });
    }

    // 刷新概览数据
    function refreshOverview() {
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        
        var params = {};
        if (startDate) params.startDate = startDate;
        if (endDate) params.endDate = endDate;
        
        $.ajax({
            type: "GET",
            url: prefix + "/overview",
            data: params,
            success: function(result) {
                if (result.code == web_status.SUCCESS && result.data) {
                    var data = result.data;
                    $('#totalDays').text(data.totalDays || 0);
                    $('#totalTransactions').text(data.totalTransactions || 0);
                    $('#totalAmount').text('¥' + (parseFloat(data.totalAmount || 0)).toFixed(2));
                    $('#avgDailyTransactions').text((data.avgDailyTransactions || 0).toFixed(1));
                    $('#avgDailyAmount').text('¥' + (parseFloat(data.avgDailyAmount || 0)).toFixed(2));
                } else {
                    console.error("获取概览数据失败:", result.msg);
                }
            },
            error: function() {
                console.error("获取概览数据失败");
            }
        });
    }

    // 防抖定时器
    let refreshOverviewTimer = null;
    
    // 监听搜索表单变化，自动刷新概览（使用防抖机制）
    $('#startDate, #endDate').change(function() {
        // 清除之前的定时器
        if (refreshOverviewTimer) {
            clearTimeout(refreshOverviewTimer);
        }
        
        // 设置新的定时器，500ms后执行
        refreshOverviewTimer = setTimeout(function() {
            refreshOverview();
        }, 500);
    });
    
    // 查看指定日期的收入详情
    function viewDateDetails(date) {
        if (!canViewDetails) {
            $.modal.msgWarning("您没有查看病历申请详情的权限");
            return;
        }
        
        // 构建跳转URL，包含日期参数
        var targetUrl = ctx + "ph/medical-record-application";
        var params = "payDateStart=" + encodeURIComponent(date) + "&payDateEnd=" + encodeURIComponent(date);
        
        // 打开新的页签
        $.modal.openTab("病历申请_" + date, targetUrl + "?" + params);
    }
</script>

</body>
</html>

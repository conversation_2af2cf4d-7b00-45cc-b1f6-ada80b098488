<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增订单')"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-pscOrder-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">住院号：</label>
            <div class="col-sm-8">
                <input name="admissionNumber" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者ID：</label>
            <div class="col-sm-8">
                <input name="patientId" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者姓名：</label>
            <div class="col-sm-8">
                <input name="patientName" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者性别：</label>
            <div class="col-sm-8">
                <select name="patientSex" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者民族：</label>
            <div class="col-sm-8">
                <select name="patientNation" class="form-control m-b" th:with="type=${@dict.getType('minzu')}" required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者出生日期：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="patientBirthday" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者手机号：</label>
            <div class="col-sm-8">
                <input name="patientMobile" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">服务类型：</label>
            <div class="col-sm-8">
                <select name="serviceType" class="form-control m-b" th:with="type=${@dict.getType('psc_service_type')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">目标科室：</label>
            <div class="col-sm-8">
                <select name="destinationDepartment" class="form-control m-b"
                        th:with="type=${@dict.getType('psc_destination_department')}" required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">护士工号：</label>
            <div class="col-sm-8">
                <input name="nurseEmployeeNo" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">护士姓名：</label>
            <div class="col-sm-8">
                <input name="nurseName" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">护士手机号：</label>
            <div class="col-sm-8">
                <input name="nurseMobile" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">陪检工号：</label>
            <div class="col-sm-8">
                <input name="carerEmployeeNo" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">陪检姓名：</label>
            <div class="col-sm-8">
                <input name="carerName" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">陪检手机号：</label>
            <div class="col-sm-8">
                <input name="carerMobile" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">状态：0=待分配陪检，1=待陪检接单，2=服务已就绪，3=正在服务，4=待护士验收，99=服务结束：</label>
            <div class="col-sm-8">
                <select name="status" class="form-control m-b" th:with="type=${@dict.getType('psc_order_status')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">派发时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="assignTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">接单时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="takeTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">开始时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="startTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">结束时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="endTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">验收时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="confirmTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/pscOrder"
    $("#form-pscOrder-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/add", $('#form-pscOrder-add').serialize());
        }
    }

    $("input[name='patientBirthday']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='assignTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='takeTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='startTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='endTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='confirmTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });
</script>
</body>
</html>

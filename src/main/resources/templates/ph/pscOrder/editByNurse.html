<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改订单')"/>
    <th:block th:include="include :: datetimepicker-css"/>
    <style>
        .exam-item-wrapper {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
        }
    </style>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-pscOrder-edit" th:object="${pscOrder}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label">患者索引号：</label>
            <div class="col-sm-8">
                <input id="patientId" name="patientId" th:field="*{patientId}"
                       class="form-control" type="text" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">患者住院号：</label>
            <div class="col-sm-8">
                <input name="admissionNumber" th:field="*{admissionNumber}" class="form-control" type="text" readonly>
            </div>
        </div>
        <div class="form-group patient-field">
            <label class="col-sm-3 control-label">患者科室：</label>
            <div class="col-sm-8">
                <input id="patientDepartment" name="patientDepartment" th:field="*{patientDepartment}"
                       class="form-control" type="text" readonly>
            </div>
        </div>
        <div class="form-group patient-field">
            <label class="col-sm-3 control-label">患者床号：</label>
            <div class="col-sm-8">
                <input id="patientBedNumber" name="patientBedNumber" th:field="*{patientBedNumber}" class="form-control"
                       type="text" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">患者姓名：</label>
            <div class="col-sm-8">
                <input name="patientName" th:field="*{patientName}" class="form-control" type="text" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者手机号：</label>
            <div class="col-sm-8">
                <input name="patientMobile" th:field="*{patientMobile}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group patient-field">
            <label class="col-sm-3 control-label">患者身份证号：</label>
            <div class="col-sm-8">
                <input name="patientIdCardNo" th:field="*${patientIdCardNo}"
                       class="form-control" type="text" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">服务类型：</label>
            <div class="col-sm-8">
                <select name="serviceType" class="form-control m-b" th:with="type=${@dict.getType('psc_service_type')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                            th:field="*{serviceType}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">检查项目：</label>
            <div class="col-sm-8 exam-item-wrapper" th:with="type=${@dict.getType('psc_exam_item')}">
                <label th:each="dict : ${type}" class="check-box" style="display: block">
                    <input name="examItems" type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}"
                           th:attr="checked=${pscOrder.examItems.contains(dict.dictValue)?true:false}" required>
                </label>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/pscOrder";
    $("#form-pscOrder-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/editByNurse", $('#form-pscOrder-edit').serialize());
        }
    }
</script>
</body>
</html>

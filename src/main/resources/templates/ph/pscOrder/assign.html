<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <th:block th:include="include :: header('派单')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>姓名：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <label>工号：</label>
                            <input type="text" name="employeeNo"/>
                        </li>
                        <li>
                            <label>手机号：</label>
                            <input type="text" name="mobile"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/pscCarer";

    $(function () {
        var options = {
            url: prefix + "/list",
            columns: [
                {
                    radio: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'employeeNo',
                    title: '工号'
                },
                {
                    field: 'mobile',
                    title: '手机号'
                }]
        };
        $.table.init(options);
    });

    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        const carerId = rows[0]
        const ids = [[${ids}]]
        $.operate.save('/ph/pscOrder/assign', {ids, carerId})
    }
</script>
</body>
</html>

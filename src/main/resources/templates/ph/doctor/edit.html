<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改医生')" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <!--/*@thymesVar id="doctor" type="space.lzhq.ph.domain.Doctor"*/-->
    <form class="form-horizontal m" id="form-doctor-edit" th:object="${doctor}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">科室编码：</label>
            <div class="col-sm-8">
                <select name="departmentId" class="form-control m-b" required readonly="readonly">
                    <option value="">请选择科室</option>
                    <!--/*@thymesVar id="departments" type="java.util.List<space.lzhq.ph.domain.Department>"*/-->
                    <option th:each="department:${departments}"
                            th:value="${department.id}"
                            th:text="${department.name}"
                            th:field="*{departmentId}"
                    ></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">姓名：</label>
            <div class="col-sm-8">
                <input name="name" th:field="*{name}" class="form-control" type="text" required readonly="readonly">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">性别：</label>
            <div class="col-sm-8">
                <select name="sex" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}" required readonly="readonly">
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{sex}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">职称：</label>
            <div class="col-sm-8">
                <input name="title" th:field="*{title}" class="form-control" type="text" required readonly="readonly">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">擅长：</label>
            <div class="col-sm-8">
                <textarea name="expertise" class="form-control" readonly="readonly">[[*{expertise}]]</textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">简介：</label>
            <div class="col-sm-8">
                <textarea name="intro" class="form-control" readonly="readonly">[[*{intro}]]</textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label" id="photoLabel">照片：</label>
            <div class="file-loading">
                <input name="photoFile" id="photoUploader" type="file">
            </div>
        </div>
        <div class="form-group hidden">
            <label class="col-sm-3 control-label">照片：</label>
            <div class="col-sm-8">
                <input name="photo" id="photo" th:field="*{photo}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">排序号：</label>
            <div class="col-sm-8">
                <input name="sortNo" th:field="*{sortNo}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group hidden">
            <label class="col-sm-3 control-label">简拼：</label>
            <div class="col-sm-8">
                <input name="simplePinyin" th:field="*{simplePinyin}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group hidden">
            <label class="col-sm-3 control-label">全拼：</label>
            <div class="col-sm-8">
                <input name="fullPinyin" th:field="*{fullPinyin}" class="form-control" type="text">
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-fileinput-js" />
<script type="text/javascript">
    var prefix = ctx + "ph/doctor";

    $(function () {
        $("#form-doctor-edit").validate({
            focusCleanup: true
        });
        $('#photoUploader').fileinput({
            theme: 'explorer-fas',
            allowedFileExtensions: ['jpg', 'png'],
            overwriteInitial: true,
            autoReplace: true,
            maxFileCount: 1,
            initialPreviewAsData: true,
            initialPreview: $('#photo').val(),
            layoutTemplates : {
                footer: ''
                // 参考fileinput.js/fileinput.min.js, 搜索 layoutTemplates，可以看到模板内所有元素
                // 需要改哪个，直接在这里赋空字符串就行了
            }
        });
        $('#photoLabel').next().addClass('col-sm-8');
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.ajax({
                url: prefix + "/edit",
                type: 'post',
                cache: false,
                async: false,
                data: new FormData($('#form-doctor-edit').get(0)),
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(result) {
                    $.operate.successCallback(result);
                }
            });
        }
    }
</script>
</body>
</html>
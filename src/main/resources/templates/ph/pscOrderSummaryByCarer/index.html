<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单统计')"/>
    <style>
        th, td {
            font-weight: bold !important;
        }

        .carer-name {
            font-weight: bold;
            text-decoration: underline;
            cursor: pointer;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>查询日期：</label>
                            <input type="text" class="time-input" id="minDate" placeholder="开始日期" name="minDate"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="maxDate" placeholder="截止日期" name="maxDate"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()"
               shiro:hasPermission="ph:pscOrderSummaryByCarer:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table">
            <table id="bootstrap-table" data-mobile-responsive="true" class="table-bordered table-striped"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:src="@{/ajax/libs/dayjs/dayjs.min.js?v=1.11.3}"></script>
<script th:inline="javascript">

    $(function () {
        const today = dayjs().format('YYYY-MM-DD')
        $('#minDate').val(today)
        $('#maxDate').val(today)

        var options = {
            url: "/ph/pscOrderSummaryByCarer/list",
            exportUrl: "/ph/pscOrderSummaryByCarer/export",
            modalName: "订单统计",
            columns: [
                [
                    {
                        field: 'carerName',
                        title: '姓名',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 2,
                        formatter: function (value, row, index) {
                            return `<span class="carer-name" onclick="onClickCarerName('${value}', '${row.carerEmployeeNo}')">${value}</span>`
                        }
                    },
                    {
                        title: '病床',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                    {
                        title: '轮椅',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                    {
                        title: '陪检',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                    {
                        title: '手术',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                    {
                        title: '总合计',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                ],
                [
                    {
                        field: 'sickbedOrderCount',
                        title: '人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'sickbedItemCount',
                        title: '项目',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'wheelchairOrderCount',
                        title: '人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'wheelchairItemCount',
                        title: '项目',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'guideOrderCount',
                        title: '人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'guideItemCount',
                        title: '项目',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'surgeryOrderCount',
                        title: '人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'surgeryItemCount',
                        title: '项目',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'totalOrderCount',
                        title: '总人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'totalItemCount',
                        title: '总项目',
                        align: 'center',
                        valign: 'middle',
                    },
                ]
            ]
        };
        $.table.init(options);
    });

    function onClickCarerName(carerName, carerEmployeeNo) {
        const minDate = $('#minDate').val()
        const maxDate = $('#maxDate').val()
        var url = `/ph/pscOrderSummaryByCarer/carerPage?carerEmployeeNo=${carerEmployeeNo}&minDate=${minDate}&maxDate=${maxDate}`;
        $.modal.openTab(`${carerName}-工作量统计表`, url);
    }
</script>
</body>
</html>

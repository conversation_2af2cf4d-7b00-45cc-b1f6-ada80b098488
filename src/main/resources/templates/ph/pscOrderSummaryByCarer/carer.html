<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('工作量统计')"/>
    <style>
        th, td {
            font-weight: bold !important;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>陪检员工号：</label>
                            <input type="text" name="carerEmployeeNo" th:value="${carerEmployeeNo}"/>
                        </li>
                        <li class="select-time">
                            <label>查询日期：</label>
                            <input type="text" class="time-input" placeholder="开始日期" name="minDate" id="minDate"
                                   th:value="${#temporals.format(minDate, 'yyyy-MM-dd')}"/>
                            <span>-</span>
                            <input type="text" class="time-input" placeholder="截止日期" name="maxDate" id="maxDate"
                                   th:value="${#temporals.format(maxDate, 'yyyy-MM-dd')}"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()"
               shiro:hasPermission="ph:pscOrderSummaryByCarer:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table">
            <table id="bootstrap-table" data-mobile-responsive="true" class="table-bordered table-striped"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:src="@{/ajax/libs/dayjs/dayjs.min.js?v=1.11.3}"></script>
<script th:inline="javascript">

    $(function () {
        var options = {
            url: "/ph/pscOrderSummaryByCarer/carerData",
            exportUrl: "/ph/pscOrderSummaryByCarer/exportCarerData",
            modalName: "工作量统计",
            columns: [
                [
                    {
                        field: 'patientDepartment',
                        title: '科室',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 2,
                    },
                    {
                        title: '病床',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                    {
                        title: '轮椅',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                    {
                        title: '陪检',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                    {
                        title: '手术',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                    {
                        title: '总合计',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2,
                    },
                ],
                [
                    {
                        field: 'sickbedOrderCount',
                        title: '人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'sickbedItemCount',
                        title: '项目',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'wheelchairOrderCount',
                        title: '人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'wheelchairItemCount',
                        title: '项目',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'guideOrderCount',
                        title: '人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'guideItemCount',
                        title: '项目',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'surgeryOrderCount',
                        title: '人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'surgeryItemCount',
                        title: '项目',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'totalOrderCount',
                        title: '总人数',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'totalItemCount',
                        title: '总项目',
                        align: 'center',
                        valign: 'middle',
                    },
                ]
            ]
        };
        $.table.init(options);
    });
</script>
</body>
</html>

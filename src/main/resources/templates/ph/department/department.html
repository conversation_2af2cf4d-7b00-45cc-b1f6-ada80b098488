<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('科室列表')" />
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>名称：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <label>简拼：</label>
                            <input type="text" name="simplePinyin"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:department:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ph:department:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="ph:department:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-danger" onclick="syncDepartments()" shiro:hasPermission="ph:department:sync">
                <i class="fa fa-refresh"></i> 同步
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:department:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-table-editable-js" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('ph:department:edit')}]];
    var removeFlag = [[${@permission.hasPermi('ph:department:remove')}]];
    var prefix = ctx + "ph/department";

    $(function() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            onEditableSave: updateCell,
            modalName: "科室",
            columns: [
                {
                checkbox: true
            },
                {
                    field: 'parentId',
                    title: '科室分类编号'
                },
                {
                    field: 'parentName',
                    title: '科室分类名称'
                },
                {
                    field: 'id',
                    title: '编号'
                },
                {
                    field: 'name',
                    title: '名称'
                },
                {
                    field: 'telephone',
                    title: '联系电话'
                },

                {
                    field: 'intro',
                    title: '简介',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 50);
                    }
                },
                {
                    field: 'address',
                    title: '位置'
                },
                {
                    field: 'sortNo',
                    title: '排序号',
                    editable: function (value, row, index) {
                        if ([[${@permission.hasPermi('ph:department:edit')}]] === "") {
                            return {
                                type : 'text',
                                // title : '排序号',
                                validate : function(value) {
                                    if (!value) {
                                        return '排序号不能为空';
                                    }
                                    var v = Number(value)
                                    if (typeof v !== 'number') {
                                        return '排序号必须是数字';
                                    }
                                    if ((v | 0) !== v) {
                                        return '排序号必须是整数';
                                    }
                                    if (v < 0) {
                                        return '排序号必须大于等于0';
                                    }
                                }
                            }
                        } else {
                            return false
                        }
                    }
                },
                {
                    field: 'simplePinyin',
                    title: '简拼'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function syncDepartments() {
        $.operate.post(prefix + "/sync");
    }

    function updateCell(field, row) {
        var form = {};
        form['id'] = row['id'];
        form[field] = row[field];
        $.operate.post(prefix + '/edit', form);
    }
</script>
</body>
</html>
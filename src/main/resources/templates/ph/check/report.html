<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('对账列表')"/>
    <style>
        th, td {
            font-weight: bold !important;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>日期：</label>
                            <input type="text" class="time-input" style="display: inline-block;" id="id"
                                   placeholder="日期" name="id" data-format="yyyyMMdd"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:check:reportExport">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table">
            <table id="bootstrap-table" data-mobile-responsive="true" class="table-bordered table-striped"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    const downloadWxBillFlag = [[${@permission.hasPermi('ph:check:reportDownloadWxBill')}]];
    const downloadWxMipBillFlag = [[${@permission.hasPermi('ph:check:reportDownloadWxMipBill')}]];
    const prefix = ctx + "ph/check";

    $(function () {
        var options = {
            url: prefix + "/reportList",
            exportUrl: prefix + "/reportExport",
            modalName: "微信支付日报表",
            sortName: "id",
            sortOrder: "desc",
            columns: [
                [
                    {
                        field: 'id',
                        title: '日期',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 3,
                    },
                    {
                        title: '门诊',
                        align: 'center',
                        valign: 'middle',
                        colspan: 9,
                    },
                    {
                        title: '住院',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3,
                        rowspan: 2
                    },
                    {
                        title: '合计',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3,
                        rowspan: 2
                    },
                    {
                        title: '操作',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 2,
                        colspan: 2
                    }
                ],
                [
                    {
                        title: '预交金',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3,
                    },
                    {
                        title: '医保移动支付',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3,
                    },
                    {
                        title: '合计',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3,
                    },
                ],
                [
                    {
                        field: 'mzPayAmount',
                        title: '充值',
                        align: 'right',
                        valign: 'middle',
                    },
                    {
                        field: 'mzRefundAmount',
                        title: '退款',
                        align: 'right',
                        valign: 'middle',
                    },
                    {
                        field: 'mzNetInAmount',
                        title: '净流入',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'mipPayAmount',
                        title: '充值',
                        align: 'right',
                        valign: 'middle',
                    },
                    {
                        field: 'mipRefundAmount',
                        title: '退款',
                        align: 'right',
                        valign: 'middle',
                    },
                    {
                        field: 'mipNetInAmount',
                        title: '净流入',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'mzTotalPayAmount',
                        title: '充值',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'mzTotalRefundAmount',
                        title: '退款',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'mzTotalNetInAmount',
                        title: '净流入',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'zyPayAmount',
                        title: '充值',
                        align: 'right',
                        valign: 'middle',
                    },
                    {
                        field: 'zyRefundAmount',
                        title: '退款',
                        align: 'right',
                        valign: 'middle',
                    },
                    {
                        field: 'zyNetInAmount',
                        title: '净流入',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'totalPayAmount',
                        title: '充值',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'totalRefundAmount',
                        title: '退款',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'totalNetInAmount',
                        title: '净流入',
                        align: 'right',
                        valign: 'middle'
                    },
                    {
                        field: 'id',
                        title: '微信支付对账单',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            const actions = [];
                            actions.push('<a class="btn btn-primary btn-xs ' + downloadWxBillFlag + '" href="javascript:void(0)" onclick="downloadWxBill(\'' + row.id + '\')"><i class="fa fa-download"></i>下载</a> ');
                            return actions.join('');
                        }
                    },
                    {
                        field: 'id',
                        title: '微信医保对账单',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            const actions = [];
                            actions.push('<a class="btn btn-primary btn-xs ' + downloadWxMipBillFlag + '" href="javascript:void(0)" onclick="downloadWxMipBill(\'' + row.id + '\')"><i class="fa fa-download"></i>下载</a> ');
                            return actions.join('');
                        }
                    }
                ]
            ]
        };
        $.table.init(options);
    });

    function downloadWxBill(id) {
        window.open(prefix + '/downloadWxBill/' + id, "_blank");
    }

    function downloadWxMipBill(id) {
        window.open(prefix + '/downloadWxMipBill/' + id, "_blank");
    }
</script>
</body>
</html>

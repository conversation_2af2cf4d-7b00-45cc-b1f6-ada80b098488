<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('支付宝支付列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>下单时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginCreateTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endCreateTime]"/>
                        </li>
                        <li>
                            <label>患者索引号：</label>
                            <input type="text" name="patientId"/>
                        </li>
                        <li>
                            <label>就诊卡号：</label>
                            <input type="text" name="jzCardNo"/>
                        </li>
                        <li>
                            <label>掌医订单号：</label>
                            <input type="text" name="outTradeNo"/>
                        </li>
                        <li>
                            <label>支付宝订单号：</label>
                            <input type="text" name="tradeNo"/>
                        </li>
                        <li>
                            <label>HIS收据号：</label>
                            <input type="text" name="hisReceiptNo"/>
                        </li>
                        <li>
                            <label>人工充值状态：</label>
                            <select name="manualRechargeState" th:with="type=${@dict.getType('manual_state')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>只显示错单：</label>
                            <select name="params[filterErrorOrder]">
                                <option value="">所有</option>
                                <option value="true">是</option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:alipayPayment:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var manualRechargeStateDatas = [[${@dict.getType('manual_state')}]];
    var queryAlipayOrderFlag = [[${@permission.hasPermi('ph:alipayPayment:queryAlipayOrder')}]]
    var requestHisRechargeFlag = [[${@permission.hasPermi('ph:alipayPayment:requestHisRecharge')}]];
    var approveHisRechargeFlag = [[${@permission.hasPermi('ph:alipayPayment:approveHisRecharge')}]];
    var prefix = ctx + "ph/alipayPayment";

    var strToChunks = (s, size) => {
        var numChunks = Math.ceil(s.length / size);
        var chunks = new Array(numChunks);

        for (var i = 0, o = 0; i < numChunks; ++i, o += size) {
            chunks[i] = s.substr(o, size);
        }

        return chunks;
    }

    $(function () {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            modalName: "支付宝充值记录",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'createTime',
                    title: '下单时间'
                },
                {
                    field: 'patientId',
                    title: '患者索引号'
                },
                {
                    field: 'jzCardNo',
                    title: '就诊卡号',
                    formatter: function (value, row, index) {
                        if (!value) {
                            return value;
                        } else {
                            return strToChunks(value, 16).join('<br/>');
                        }
                    }
                },
                {
                    field: 'outTradeNo',
                    title: '掌医订单号'
                },
                {
                    field: 'totalAmount',
                    title: '订单金额',
                    formatter: function (value, row, index) {
                        return (Number(value) / 100).toFixed(2);
                    }
                },
                {
                    field: 'exrefund',
                    title: '已退金额',
                    formatter: function (value, row, index) {
                        return (Number(value) / 100).toFixed(2);
                    }
                },
                {
                    field: 'unrefund',
                    title: '未退金额',
                    formatter: function (value, row, index) {
                        if (value) {
                            return (Number(value) / 100).toFixed(2);
                        } else {
                            return value;
                        }
                    }
                },
                {
                    field: 'tradeNo',
                    title: '支付宝订单号'
                },
                {
                    field: 'tradeStatus',
                    title: '支付宝交易状态描述',
                    formatter: function (value, row, index) {
                        if (value === 'WAIT_BUYER_PAY') {
                            return '等待付款'
                        }
                        if (value === 'TRADE_CLOSED') {
                            if (row.exrefund === 0) {
                                return '未付款'
                            }
                            if (row.exrefund === row.totalAmount) {
                                return '全额退款'
                            }
                        }
                        if (value === 'TRADE_SUCCESS') {
                            return '支付成功'
                        }
                        if (value === 'TRADE_FINISHED') {
                            return '交易结束，不可退款'
                        }
                        return value
                    }
                },
                {
                    field: 'hisTradeStatus',
                    title: 'HIS交易状态描述'
                },
                {
                    field: 'hisSuccessTime',
                    title: '充值到账时间'
                },
                {
                    field: 'manualRechargeState',
                    title: '人工充值状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(manualRechargeStateDatas, value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + queryAlipayOrderFlag + '" href="javascript:void(0)" onclick="queryAlipayOrder(\'' + row.id + '\')"><i class="fa fa-refresh"></i>查询支付宝订单状态</a> ');
                        if (row['tradeStatus'] === 'TRADE_SUCCESS' && row['hisTradeStatus'] !== '充值成功' && row['manualRechargeState'] == 0) {
                            actions.push('<a class="btn btn-success btn-xs ' + requestHisRechargeFlag + '" href="javascript:void(0)" onclick="requestHisRecharge(\'' + row.id + '\')"><i class="fa fa-paper-plane-o"></i>申请HIS充值</a> ');
                        }
                        if (row['tradeStatus'] === 'TRADE_SUCCESS' && row['hisTradeStatus'] !== '充值成功' && row['manualRechargeState'] == 1) {
                            actions.push('<a class="btn btn-success btn-xs ' + approveHisRechargeFlag + '" href="javascript:void(0)" onclick="approveHisRecharge(\'' + row.id + '\')"><i class="fa fa-check"></i>放行HIS充值</a> ');
                        }
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function queryAlipayOrder(id) {
        $.operate.get(prefix + '/queryAlipayOrder/' + id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询订单状态',
                shade: 0.6,
                shadeClose: true,
                content: '<pre style="margin: 20px;">' + JSON.stringify(resp, null, 4) + '</pre>'
            });
        });
    }

    function requestHisRecharge(id) {
        $.operate.post(prefix + '/requestHisRecharge/' + id, {})
    }

    function approveHisRecharge(id) {
        $.operate.post(prefix + '/approveHisRecharge/' + id, {})
    }
</script>
</body>
</html>

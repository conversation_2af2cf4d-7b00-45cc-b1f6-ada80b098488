<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('余额清退登记详情')"/>
    <title>余额清退登记详情</title>
    <style>
      .container-div .row {
        height: auto;
      }
      .detail-card {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .detail-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
      }
      .info-row {
        margin-bottom: 15px;
      }
      .info-label {
        font-weight: bold;
        color: #666;
        width: 120px;
        display: inline-block;
      }
      .info-value {
        color: #333;
      }
      .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: bold;
        display: inline-block;
      }
      .status-draft {
        background-color: #ffc107;
        color: #856404;
      }
      .status-submitted {
        background-color: #17a2b8;
        color: #fff;
      }
      .status-approved {
        background-color: #28a745;
        color: #fff;
      }
      .status-rejected {
        background-color: #dc3545;
        color: #fff;
      }

      .attachment-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        margin: 5px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .attachment-info {
        flex: 1;
        min-width: 0; /* 允许flex项目收缩到内容以下 */
      }
      .attachment-info > div {
        word-break: break-all;
        overflow-wrap: break-word;
        line-height: 1.4;
      }
      .attachment-actions {
        margin-left: 10px;
        flex-shrink: 0; /* 防止按钮被压缩 */
      }

      /* 响应式样式 - 小屏幕优化 */
      @media (max-width: 768px) {
        .attachment-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .attachment-info {
          width: 100%;
          margin-bottom: 0;
        }

        .attachment-actions {
          margin-left: 0;
          width: 100%;
          display: flex;
          justify-content: flex-end;
          gap: 5px;
        }

        .attachment-actions .btn {
          flex: 0 0 auto;
          min-width: 44px; /* 提高移动端可点击性 */
          padding: 8px 12px;
        }

        /* 表格和长文本在小屏幕上的优化 */
        .detail-title {
          font-size: 16px;
          padding-bottom: 8px;
          margin-bottom: 15px;
        }

        .detail-title .pull-right {
          float: none !important;
          text-align: left;
          margin-top: 10px;
        }

        .switch-container {
          font-size: 12px;
        }

        .info-row {
          margin-bottom: 12px;
        }
      }

      /* 超小屏幕优化 */
      @media (max-width: 480px) {
        .attachment-actions {
          justify-content: center;
        }

        .attachment-actions .btn {
          flex: 1;
          max-width: 80px;
          margin: 0 2px;
        }

        .detail-card {
          padding: 15px;
          margin-top: 15px;
          margin-bottom: 15px;
        }

        .info-label {
          width: 100px;
          font-size: 13px;
        }

        .info-value {
          font-size: 13px;
          word-break: break-all;
          overflow-wrap: break-word;
        }

        /* 申报进度卡片优化 */
        .detail-card.timeline-progress-card {
          min-height: 350px; /* 超小屏幕下适当减小最小高度 */
          max-height: 50vh; /* 减小最大高度，为其他内容留出空间 */
        }
      }
      .timeline {
        position: relative;
        padding-left: 35px;
        margin-top: 10px;
      }
      .timeline::before {
        content: "";
        position: absolute;
        left: 18px;
        top: 0;
        height: calc(100% - 10px);
        width: 3px;
        background: linear-gradient(to bottom, #007bff, #28a745);
        border-radius: 2px;
      }
      .timeline-item {
        position: relative;
        margin-bottom: 25px;
        opacity: 0.7;
        transition: all 0.3s ease;
      }
      .timeline-item.active {
        opacity: 1;
      }
      .timeline-item.completed {
        opacity: 1;
      }
      .timeline-item:hover {
        opacity: 1;
      }
      .timeline-item::before {
        content: "";
        position: absolute;
        left: -22px;
        top: 20px;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: #e9ecef;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #e9ecef;
        z-index: 2;
        transition: all 0.3s ease;
      }
      .timeline-item:hover::before {
        width: 18px;
        height: 18px;
        left: -24px;
        top: 18px;
        box-shadow: 0 0 0 3px #e9ecef, 0 0 8px rgba(0, 0, 0, 0.1);
      }
      .timeline-item.active::before {
        background: #007bff;
        box-shadow: 0 0 0 2px #007bff, 0 0 10px rgba(0, 123, 255, 0.3);
        animation: pulse 1.5s infinite;
      }
      .timeline-item.active:hover::before {
        width: 18px;
        height: 18px;
        left: -24px;
        top: 18px;
        box-shadow: 0 0 0 3px #007bff, 0 0 15px rgba(0, 123, 255, 0.4);
        animation: pulse 1.5s infinite;
      }
      .timeline-item.completed::before {
        background: #28a745;
        box-shadow: 0 0 0 2px #28a745;
      }
      .timeline-item.completed:hover::before {
        width: 18px;
        height: 18px;
        left: -24px;
        top: 18px;
        box-shadow: 0 0 0 3px #28a745, 0 0 8px rgba(40, 167, 69, 0.3);
      }
      .timeline-item.rejected::before {
        background: #dc3545;
        box-shadow: 0 0 0 2px #dc3545;
      }
      .timeline-item.rejected:hover::before {
        width: 18px;
        height: 18px;
        left: -24px;
        top: 18px;
        box-shadow: 0 0 0 3px #dc3545, 0 0 8px rgba(220, 53, 69, 0.3);
      }
      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 2px #007bff, 0 0 0 rgba(0, 123, 255, 0.8);
        }
        50% {
          box-shadow: 0 0 0 2px #007bff, 0 0 25px rgba(0, 123, 255, 0.6);
        }
        100% {
          box-shadow: 0 0 0 2px #007bff, 0 0 0 rgba(0, 123, 255, 0.8);
        }
      }
      .timeline-content {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
      }
      .timeline-item:hover .timeline-content {
        border-color: #007bff;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
      }
      .timeline-content strong {
        color: #333;
        font-size: 15px;
        margin-bottom: 8px;
        display: block;
      }
      .timeline-time {
        color: #666;
        font-size: 13px;
        margin: 6px 0;
        font-weight: 500;
      }
      .timeline-content small {
        color: #888;
        font-size: 12px;
        line-height: 1.4;
      }
      .timeline-item.completed .timeline-content strong {
        color: #28a745;
      }
      .timeline-item.rejected .timeline-content strong {
        color: #dc3545;
      }
      .timeline-item.active .timeline-content strong {
        color: #007bff;
      }
      .empty-state {
        text-align: center;
        color: #999;
        padding: 40px;
        font-style: italic;
      }
      /* 文件预览模态框样式 */
      #filePreviewModal .modal-dialog {
        margin: 30px auto;
      }

      #filePreviewModal .modal-content {
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }

      #filePreviewModal .modal-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 8px 8px 0 0;
      }

      #filePreviewModal .modal-title {
        display: inline-block;
        font-weight: 600;
        color: #333;
      }

      #filePreviewModal .modal-body {
        background-color: #fff;
      }

      #filePreviewModal .modal-footer {
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 8px 8px;
      }

      /* 预览内容样式 */
      #previewContainer img {
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      #previewContainer iframe {
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* 加载动画 */
      .fa-spinner {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* 时间线开关样式 */
      .switch-container {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #666;
        cursor: pointer;
      }

      .switch-container input {
        position: relative;
        width: 40px;
        height: 20px;
        appearance: none;
        background: #ccc;
        border-radius: 20px;
        outline: none;
        transition: all 0.3s;
      }

      .switch-container input:checked {
        background: #007bff;
      }

      .switch-container input::before {
        content: "";
        position: absolute;
        top: 2px;
        left: 2px;
        width: 16px;
        height: 16px;
        background: white;
        border-radius: 50%;
        transition: all 0.3s;
      }

      .switch-container input:checked::before {
        transform: translateX(20px);
      }

      .timeline-loading {
        padding: 40px;
        color: #666;
      }

      /* Grid布局 - 先分行再分列方案 */

      /* 重要：禁用Bootstrap的.row伪元素，避免干扰Grid布局 */
      .container-div > .row:before,
      .container-div > .row:after {
        display: none !important;
      }
      .container-div > .row {
        padding-top: 20px;
      }

      /* 大屏幕和中屏幕：Grid两行两列布局 */
      @media (min-width: 768px) {
        .container-div > .row {
          display: grid;
          grid-template-columns: 2fr 1fr; /* 左侧占2份，右侧占1份 */
          grid-template-rows: auto auto; /* 两行：自动高度 */
          gap: 20px;
          align-items: start; /* 改为start，让左侧决定行高 */
        }

        /* 第一行左侧：患者信息 + 代办人信息 */
        .top-left-section {
          grid-column: 1;
          grid-row: 1;
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        /* 第一行右侧：申报进度 */
        .top-right-section {
          grid-column: 2;
          grid-row: 1;
          display: flex;
          flex-direction: column;
          /* 关键：让右侧高度匹配左侧，但内容可滚动 */
          height: fit-content;
        }

        /* 第二行左侧：申报信息 + 附件信息 */
        .bottom-left-section {
          grid-column: 1;
          grid-row: 2;
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        /* 第二行右侧：操作 + 审核面板 */
        .bottom-right-section {
          grid-column: 2;
          grid-row: 2;
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        /* 申报进度卡片：使用JavaScript动态计算高度并内部滚动 */
        .detail-card.timeline-progress-card {
          overflow-y: auto;
          /* 高度将通过JavaScript动态设置 */
        }
      }

      /* 中等屏幕：调整Grid比例 */
      @media (min-width: 768px) and (max-width: 991px) {
        .container-div > .row {
          grid-template-columns: 1.5fr 1fr; /* 调整左右比例为3:2 */
          gap: 15px;
        }

        .top-left-section,
        .bottom-left-section {
          gap: 15px;
        }

        .bottom-right-section {
          gap: 15px;
        }
      }

      /* 小屏幕：单列垂直布局 */
      @media (max-width: 767px) {
        .container-div > .row {
          display: block; /* 小屏幕下使用简单的块布局 */
        }

        .top-left-section,
        .top-right-section,
        .bottom-left-section,
        .bottom-right-section {
          display: block;
          width: 100%;
          margin-bottom: 20px;
        }

        .top-left-section .detail-card,
        .top-right-section .detail-card,
        .bottom-left-section .detail-card,
        .bottom-right-section .detail-card {
          margin-bottom: 20px;
        }

        .detail-card.timeline-progress-card {
          min-height: 400px; /* 保证小屏幕下有足够的高度 */
          max-height: 60vh; /* 使用视口高度，更灵活 */
          height: auto; /* 允许自动调整高度 */
          overflow-y: auto; /* 确保有纵向滚动 */
          flex: none;
        }
      }

      /* 审核面板容器样式 */
      .approval-card {
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      /* 审核相关样式 */
      .approval-options {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
      }
      .approval-option {
        flex: 1;
        text-align: center;
        padding: 20px;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        background: #fff;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        outline: none;
        width: 100%;
        display: block;
      }
      .approval-option:hover,
      .approval-option:focus {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
      }
      .approval-option:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
      }
      .approval-option.selected {
        border-color: #28a745;
        background-color: #d4edda;
      }
      .approval-option.reject.selected {
        border-color: #dc3545;
        background-color: #f8d7da;
      }
      .approval-option i {
        font-size: 36px;
        margin-bottom: 10px;
        display: block;
      }
      .approval-option.approve i {
        color: #28a745;
      }
      .approval-option.reject i {
        color: #dc3545;
      }
      .approval-details {
        display: none;
        margin-top: 20px;
        padding: 20px;
        background: #fff;
        border-radius: 5px;
        border: 1px solid #dee2e6;
      }
      .required-field {
        border-left: 4px solid #dc3545;
        padding-left: 15px;
      }
      .char-count {
        font-size: 12px;
        color: #666;
        float: right;
        margin-top: 5px;
      }
      /* 审核相关面板基础样式 */
      .approval-panel {
        display: none;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
      }
      .approval-panel.show {
        display: block;
        opacity: 1;
        transform: translateY(0);
      }

      /* 审核操作面板 - 主要交互区域 */
      .approval-operation-panel {
        border-left: 4px solid #007bff;
        position: relative;
      }

      /* 审核提醒面板 - 辅助指导区域 */
      .approval-tips-panel {
        border-left: 4px solid #17a2b8;
        background-color: #f8f9fa;
        margin-top: 15px;
      }

      /* 审核面板标题增强 */
      .approval-panel .detail-title {
        position: relative;
      }

      .approval-operation-panel .detail-title::before {
        content: "";
        position: absolute;
        left: -24px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #007bff, #0056b3);
        border-radius: 2px;
      }

      .approval-tips-panel .detail-title::before {
        content: "";
        position: absolute;
        left: -24px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #17a2b8, #117a8b);
        border-radius: 2px;
      }

      /* 视觉反馈动画 */
      .pulse-highlight {
        animation: pulseHighlight 0.6s ease-in-out;
      }

      @keyframes pulseHighlight {
        0% {
          transform: scale(1);
          box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
        }
        50% {
          transform: scale(1.02);
          box-shadow: 0 4px 16px rgba(0, 123, 255, 0.4);
        }
        100% {
          transform: scale(1);
          box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
        }
      }

      /* 提交按钮状态样式增强 */
      #submitApproval.btn-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        border-color: #28a745;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
      }

      #submitApproval.btn-warning {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        border-color: #ffc107;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
      }

      #submitApproval.btn-success:hover,
      #submitApproval.btn-warning:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      /* 优化滚动条样式 */
      .detail-card.timeline-progress-card::-webkit-scrollbar {
        width: 8px;
      }

      .detail-card.timeline-progress-card::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      .detail-card.timeline-progress-card::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      .detail-card.timeline-progress-card::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      /* 整体响应式布局优化 */
      @media (max-width: 991px) {
        /* 旧的col-md响应式规则已被Grid布局替代 */

        .approval-options {
          flex-direction: column;
          gap: 15px;
        }

        .approval-option {
          padding: 15px;
        }

        .approval-option i {
          font-size: 28px;
        }

        /* 审核面板在小屏幕上的优化 */
        .approval-operation-panel,
        .approval-tips-panel {
          margin-left: 0;
          margin-right: 0;
        }

        .approval-operation-panel .detail-title::before,
        .approval-tips-panel .detail-title::before {
          display: none; /* 小屏幕隐藏装饰线 */
        }
      }
    </style>
  </head>
  <body class="gray-bg">
    <div
      class="container-div"
      th:classappend="${registration.agentFlag == 1 ? 'has-agent' : 'no-agent'}"
    >
      <div class="row">
        <!-- 第一行左侧：患者信息 + 代办人信息 -->
        <div class="top-left-section">
          <!-- 基本信息 -->
          <div class="detail-card patient-card">
            <div class="detail-title"><i class="fa fa-user"></i> 患者信息</div>
            <div class="row">
              <div class="col-md-6">
                <div class="info-row">
                  <span class="info-label">姓名：</span>
                  <span class="info-value" th:text="${registration.patientName}"
                    >-</span
                  >
                </div>
                <div class="info-row">
                  <span class="info-label">身份证号：</span>
                  <span
                    class="info-value"
                    th:text="${registration.patientIdCardNo}"
                    >-</span
                  >
                </div>
                <div class="info-row">
                  <span class="info-label">手机号：</span>
                  <span
                          class="info-value"
                          th:text="${registration.patientMobile}"
                  >-</span
                  >
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-row">
                  <span class="info-label">出生日期：</span>
                  <span
                          class="info-value"
                          th:text="${registration.patientBirthDate != null ? #temporals.format(registration.patientBirthDate, 'yyyy-MM-dd') : '-'}"
                  >-</span
                  >
                </div>
                <div class="info-row">
                  <span class="info-label">性别：</span>
                  <span
                          class="info-value"
                          th:text="${registration.patientGender}"
                  >-</span
                  >
                </div>
              </div>
            </div>

            <!-- 患者附件 -->
            <div class="row" style="margin-top: 20px">
              <div class="col-md-6">
                <h6><strong>身份证附件</strong></h6>
                <div
                  class="attachment-item"
                  th:if="${registration.patientIdCardAttachment}"
                >
                  <div class="attachment-info">
                    <div
                      th:text="${registration.patientIdCardAttachment?.fileName ?: '-'}"
                    >
                      -
                    </div>
                    <small
                      class="text-muted"
                      th:text="${registration.patientIdCardAttachment?.createTime ?: '-'}"
                      >-</small
                    >
                  </div>
                  <div class="attachment-actions">
                    <button
                      class="btn btn-primary btn-xs"
                      onclick="downloadFile(this)"
                      th:data-url="${registration.patientIdCardAttachment?.fileUrl ?: ''}"
                      th:data-filename="${registration.patientIdCardAttachment?.fileName ?: ''}"
                      title="下载"
                    >
                      <i class="fa fa-download"></i>
                    </button>
                    <button
                      class="btn btn-success btn-xs"
                      onclick="previewFile(this)"
                      th:data-url="${registration.patientIdCardAttachment?.fileUrl ?: ''}"
                      th:data-filename="${registration.patientIdCardAttachment?.fileName ?: ''}"
                      title="预览"
                    >
                      <i class="fa fa-eye"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 代办信息 -->
          <div
            class="detail-card agent-card"
            th:if="${registration.agentFlag == 1}"
          >
            <div class="detail-title">
              <i class="fa fa-users"></i> 代办人信息
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="info-row">
                  <span class="info-label">代办人姓名：</span>
                  <span class="info-value" th:text="${registration.agentName}"
                    >-</span
                  >
                </div>
                <div class="info-row">
                  <span class="info-label">身份证号：</span>
                  <span
                    class="info-value"
                    th:text="${registration.agentIdCardNo}"
                    >-</span
                  >
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-row">
                  <span class="info-label">手机号：</span>
                  <span class="info-value" th:text="${registration.agentMobile}"
                    >-</span
                  >
                </div>
                <div class="info-row">
                  <span class="info-label">与患者关系：</span>
                  <span
                    class="info-value"
                    th:text="${registration.agentRelation?.description ?: '-'}"
                    >-</span
                  >
                </div>
              </div>
            </div>

            <!-- 代办人附件 -->
            <div
              class="row"
              style="margin-top: 20px"
              th:if="${registration.agentIdCardAttachment != null}"
            >
              <div class="col-md-6">
                <h6><strong>身份证附件</strong></h6>
                <div class="attachment-item">
                  <div class="attachment-info">
                    <div
                      th:text="${registration.agentIdCardAttachment?.fileName ?: '-'}"
                    >
                      -
                    </div>
                    <small
                      class="text-muted"
                      th:text="${registration.agentIdCardAttachment?.createTime ?: '-'}"
                      >-</small
                    >
                  </div>
                  <div class="attachment-actions">
                    <button
                      class="btn btn-primary btn-xs"
                      onclick="downloadFile(this)"
                      th:data-url="${registration.agentIdCardAttachment?.fileUrl ?: ''}"
                      th:data-filename="${registration.agentIdCardAttachment?.fileName ?: ''}"
                      title="下载"
                    >
                      <i class="fa fa-download"></i>
                    </button>
                    <button
                      class="btn btn-success btn-xs"
                      onclick="previewFile(this)"
                      th:data-url="${registration.agentIdCardAttachment?.fileUrl ?: ''}"
                      th:data-filename="${registration.agentIdCardAttachment?.fileName ?: ''}"
                      title="预览"
                    >
                      <i class="fa fa-eye"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 银行卡信息 -->
          <div class="detail-card attachments-card">
            <div class="detail-title">
              <i class="fa fa-paperclip"></i> 银行卡信息
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="info-row">
                  <span class="info-label">银行卡号：</span>
                  <span
                          class="info-value"
                          th:text="${registration.bankCardNo}"
                  >-</span
                  >
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <h6><strong>银行卡附件</strong></h6>
                <div
                        class="attachment-item"
                        th:if="${registration.bankCardAttachment}"
                >
                  <div class="attachment-info">
                    <div
                            th:text="${registration.bankCardAttachment?.fileName ?: '-'}"
                    >
                      -
                    </div>
                    <small
                            class="text-muted"
                            th:text="${registration.bankCardAttachment?.createTime ?: '-'}"
                    >-</small
                    >
                  </div>
                  <div class="attachment-actions">
                    <button
                            class="btn btn-primary btn-xs"
                            onclick="downloadFile(this)"
                            th:data-url="${registration.bankCardAttachment?.fileUrl ?: ''}"
                            th:data-filename="${registration.bankCardAttachment?.fileName ?: ''}"
                            title="下载"
                    >
                      <i class="fa fa-download"></i>
                    </button>
                    <button
                            class="btn btn-success btn-xs"
                            onclick="previewFile(this)"
                            th:data-url="${registration.bankCardAttachment?.fileUrl ?: ''}"
                            th:data-filename="${registration.bankCardAttachment?.fileName ?: ''}"
                            title="预览"
                    >
                      <i class="fa fa-eye"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第一行右侧：申报进度 -->
        <div class="top-right-section">
          <div class="detail-card timeline-progress-card">
            <div class="detail-title">
              <i class="fa fa-tasks"></i> 申报进度
              <div class="pull-right">
                <label class="switch-container">
                  <input type="checkbox" id="timelineDetailSwitch" />
                  <span class="slider"></span>
                  <span class="switch-label">详细模式</span>
                </label>
              </div>
            </div>
            <div class="timeline" id="timelineContainer">
              <!-- 时间线内容将通过AJAX动态加载 -->
              <div class="timeline-loading text-center">
                <i class="fa fa-spinner fa-spin"></i> 加载时间线中...
              </div>
            </div>
          </div>
        </div>

        <!-- 第二行左侧：申报信息 + 附件信息 -->
        <div class="bottom-left-section">
          <!-- 申报信息 -->
          <div class="detail-card application-card" th:if="${registration.status?.name() == 'REJECTED'}">

            <!-- 驳回信息 -->
            <div
              th:if="${registration.status?.name() == 'REJECTED'}"
              class="alert alert-warning"
              style="margin-top: 15px"
            >
              <h5><i class="fa fa-exclamation-triangle"></i> 驳回信息</h5>
              <div class="info-row" th:if="${registration.rejectReason}">
                <span class="info-label">驳回原因：</span>
                <span class="info-value" th:text="${registration.rejectReason}"
                  >-</span
                >
              </div>
              <div class="info-row" th:if="${registration.correctionAdvice}">
                <span class="info-label">整改意见：</span>
                <span
                  class="info-value"
                  th:text="${registration.correctionAdvice}"
                  >-</span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 第二行右侧：操作 + 审核面板 -->
        <div class="bottom-right-section">
          <!-- 操作按钮 -->
          <div class="detail-card operations-card">
            <div class="detail-title"><i class="fa fa-cogs"></i> 操作</div>
            <div class="btn-group-vertical" style="width: 100%">
              <button
                type="button"
                class="btn btn-primary"
                id="toggleApproval"
                th:if="${registration.canApprove}"
                shiro:hasPermission="ph:card-refund:approve"
              >
                <i class="fa fa-gavel"></i> 审核
              </button>
              <button
                type="button"
                class="btn btn-danger"
                onclick="deleteRecord()"
                th:if="${registration.status?.name() == 'DRAFT'}"
                shiro:hasPermission="ph:card-refund:remove"
              >
                <i class="fa fa-trash"></i> 删除
              </button>
            </div>
          </div>

          <!-- 审核相关面板容器 -->
          <div class="approval-card">
            <!-- 审核操作面板 -->
            <div
              class="detail-card approval-panel approval-operation-panel"
              id="approvalPanel"
              role="main"
              aria-labelledby="approvalPanelTitle"
              aria-hidden="true"
            >
              <div class="detail-title" id="approvalPanelTitle">
                <i class="fa fa-gavel" aria-hidden="true"></i> 审核操作
                <button
                  type="button"
                  class="btn btn-sm btn-secondary pull-right"
                  id="cancelApproval"
                  aria-label="取消审核操作"
                >
                  <i class="fa fa-times" aria-hidden="true"></i> 取消
                </button>
              </div>

              <form id="approvalForm">
                <input
                  type="hidden"
                  id="registrationId"
                  th:value="${registration?.id ?: 0}"
                />

                <!-- 审核选项 -->
                <div class="approval-options">
                  <button
                    type="button"
                    class="approval-option approve"
                    data-status="APPROVED"
                    aria-label="选择审核通过"
                    aria-pressed="false"
                    tabindex="0"
                  >
                    <i class="fa fa-check-circle" aria-hidden="true"></i>
                    <strong>审核通过</strong>
                    <p>申报材料齐全，符合要求</p>
                  </button>
                  <button
                    type="button"
                    class="approval-option reject"
                    data-status="REJECTED"
                    aria-label="选择审核驳回"
                    aria-pressed="false"
                    tabindex="0"
                  >
                    <i class="fa fa-times-circle" aria-hidden="true"></i>
                    <strong>审核驳回</strong>
                    <p>申报材料不符合要求</p>
                  </button>
                </div>

                <!-- 驳回详情 -->
                <div id="rejectDetails" class="approval-details">
                  <div class="required-field">
                    <h6>
                      <strong>驳回原因</strong>
                      <span class="text-danger">*</span>
                    </h6>
                    <textarea
                      id="rejectReason"
                      class="form-control"
                      rows="4"
                      placeholder="请详细说明驳回原因，帮助申请人了解问题所在"
                      maxlength="500"
                      required
                    ></textarea>
                    <div class="char-count">
                      <span id="rejectReasonCount">0</span>/500
                    </div>
                  </div>

                  <div style="margin-top: 20px">
                    <h6>
                      <strong>整改意见</strong>
                      <span class="text-muted">(可选)</span>
                    </h6>
                    <textarea
                      id="correctionAdvice"
                      class="form-control"
                      rows="4"
                      placeholder="请提供具体的整改建议，指导申请人如何修正申报材料"
                      maxlength="1000"
                    ></textarea>
                    <div class="char-count">
                      <span id="correctionAdviceCount">0</span>/1000
                    </div>
                  </div>
                </div>

                <!-- 通过详情 -->
                <div id="approveDetails" class="approval-details">
                  <div class="alert alert-success">
                    <h6><i class="fa fa-check"></i> 审核通过确认</h6>
                    <p>
                      请确认申报材料已审核无误。审核通过后，申请人将收到通知。
                    </p>
                  </div>
                </div>

                <!-- 提交按钮 -->
                <div class="text-center" style="margin-top: 20px">
                  <button
                    type="button"
                    id="submitApproval"
                    class="btn btn-primary"
                    disabled
                  >
                    <i class="fa fa-gavel"></i> 提交审核
                  </button>
                </div>
              </form>
            </div>

            <!-- 审核提醒 -->
            <div
              class="detail-card approval-panel approval-tips-panel"
              id="approvalTips"
              role="complementary"
              aria-labelledby="approvalTipsTitle"
              aria-hidden="true"
            >
              <div class="detail-title" id="approvalTipsTitle">
                <i class="fa fa-lightbulb-o" aria-hidden="true"></i> 审核提醒
                <small class="text-muted pull-right">审核指导</small>
              </div>
              <div class="alert alert-info">
                <h6><strong>审核要点：</strong></h6>
                <ul style="margin-bottom: 0; padding-left: 20px">
                  <li>核实患者身份信息的真实性</li>
                  <li>检查银行卡资料的完整性和有效性</li>
                  <li>如有代办，验证代办人身份和关系</li>
                  <li>驳回时请提供明确的整改指导</li>
                </ul>
              </div>
            </div>
          </div>
          <!-- 结束 approval-card 容器 -->
        </div>
      </div>
    </div>

    <!-- 文件预览模态框 -->
    <div
      class="modal fade"
      id="filePreviewModal"
      tabindex="-1"
      aria-labelledby="filePreviewModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-lg" style="max-width: 90%; width: 90%">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="filePreviewModalLabel">文件预览</h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="关闭"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div
            class="modal-body"
            id="filePreviewBody"
            style="padding: 0; max-height: 70vh; overflow: auto"
          >
            <div id="previewContainer" style="text-align: center">
              <!-- 预览内容将在这里动态加载 -->
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-dismiss="modal"
            >
              关闭
            </button>
            <button
              type="button"
              class="btn btn-primary"
              id="downloadFromPreview"
            >
              下载文件
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 预定义模板 - 用于安全地生成文件预览UI -->
    <script type="text/template" id="office-hint-template">
      <div class="alert alert-info text-center" style="margin: 50px auto;">
          <i class="fa fa-file-text-o fa-3x" style="display: block; margin-bottom: 15px; color: #007bff;"></i>
          <h5>Office文档预览</h5>
          <p>该文件类型需要下载后使用相应软件打开查看</p>
          <button type="button" class="btn btn-primary download-btn">
              <i class="fa fa-download"></i> 立即下载
          </button>
      </div>
    </script>

    <script type="text/template" id="unsupported-hint-template">
      <div class="alert alert-warning text-center" style="margin: 50px auto;">
          <i class="fa fa-file-o fa-3x" style="display: block; margin-bottom: 15px; color: #f39c12;"></i>
          <h5>暂不支持预览</h5>
          <p>该文件类型暂不支持在线预览，请下载后查看</p>
          <button type="button" class="btn btn-primary download-btn">
              <i class="fa fa-download"></i> 立即下载
          </button>
      </div>
    </script>

    <!-- 预定义模板 - 用于安全地生成时间线项UI -->
    <script type="text/template" id="timeline-item-template">
      <div class="timeline-item" data-event-type="">
          <div class="timeline-content">
              <strong>
                  <i class="timeline-icon"></i>
                  <span class="timeline-description"></span>
              </strong>
              <div class="timeline-time"></div>
              <small class="text-muted timeline-status-description"></small>
          </div>
      </div>
    </script>

    <script type="text/template" id="timeline-empty-template">
      <div class="empty-state">
          <i class="fa fa-file-o fa-2x"></i>
          <p>暂无时间线记录</p>
      </div>
    </script>

    <!-- 操作区无可用操作提示模板 -->
    <script type="text/template" id="no-operations-tip-template">
      <div class="no-operations-tip" style="color: #888; text-align: center; padding: 20px 0; font-size: 14px;">
          <i class="fa fa-info-circle" style="margin-right: 5px;"></i>当前无可用操作
      </div>
    </script>

    <th:block th:include="include :: footer"/>
    <script th:inline="javascript">
      /*<![CDATA[*/
      const prefix = ctx + "ph/card-refund";
      const registrationId = /*[[${registration?.id ?: 0}]]*/ 0;

      // 删除记录
      function deleteRecord() {
        $.modal.confirm(
          "确认要删除这条申报记录吗？删除后无法恢复！",
          function () {
            $.ajax({
              url: prefix + "/remove/" + registrationId,
              type: "POST",
              success: function (result) {
                if (result.code === 0) {
                  top.layer.msg(
                    "删除成功",
                    {
                      icon: $.modal.icon(modal_status.SUCCESS),
                      time: 1000,
                      shade: [0.1, "#8F8F8F"],
                    },
                    function () {
                      $.modal.closeTab();
                    }
                  );
                } else {
                  $.modal.alertError(result.msg);
                }
              },
            });
          }
        );
      }

      // 下载文件
      function downloadFile(button) {
        const fileUrl = button.getAttribute("data-url");
        const fileName = button.getAttribute("data-filename");

        // 创建一个临时的 a 标签用于下载
        const link = document.createElement("a");
        link.href = fileUrl;
        link.download = fileName;
        link.style.display = "none";

        // 将链接添加到页面并触发点击
        document.body.appendChild(link);
        link.click();

        // 清理临时元素
        document.body.removeChild(link);
      }

      // 预览文件
      function previewFile(button) {
        const fileUrl = button.getAttribute("data-url");
        const fileName = button.getAttribute("data-filename");

        // 设置模态框标题
        $("#filePreviewModalLabel").text(fileName);

        // 获取文件扩展名
        const extension = fileName.toLowerCase().split(".").pop();
        const previewContainer = $("#previewContainer");

        // 清空预览容器
        previewContainer.empty();

        // 根据文件类型生成预览内容
        if (["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension)) {
          // 图片预览
          const img = $("<img>").attr({
            src: fileUrl,
            style: "max-width: 100%; max-height: 60vh; object-fit: contain;",
            alt: fileName,
          });
          previewContainer.append(img);

          // 图片加载错误处理
          img.on("error", function () {
            previewContainer.html(
              '<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> 图片加载失败</div>'
            );
          });
        } else if (extension === "pdf") {
          // PDF预览
          const iframe = $("<iframe>").attr({
            src: fileUrl,
            style: "width: 100%; height: 60vh; border: none;",
            title: fileName,
          });
          previewContainer.append(iframe);

          // 添加PDF加载提示和备选方案
          const pdfHint = $(
            '<div class="alert alert-info" style="margin-bottom: 10px;">' +
              '<i class="fa fa-info-circle"></i> PDF预览加载中... 如无法显示，请点击下载按钮下载查看</div>'
          );
          previewContainer.prepend(pdfHint);
        } else if (
          ["txt", "log", "md", "json", "xml", "csv"].includes(extension)
        ) {
          // 文本文件预览
          previewContainer.html(
            '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> 加载中...</div>'
          );

          $.get(fileUrl)
            .done(function (data) {
              const pre = $("<pre>")
                .css({
                  "text-align": "left",
                  "max-height": "60vh",
                  overflow: "auto",
                  background: "#f8f9fa",
                  padding: "15px",
                  "border-radius": "4px",
                })
                .text(data);
              previewContainer.html(pre);
            })
            .fail(function () {
              previewContainer.html(
                '<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> 文本文件加载失败</div>'
              );
            });
        } else if (
          ["doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(extension)
        ) {
          // Office文档预览提示 - 使用预定义模板，安全防XSS
          const template = $("#office-hint-template").html();
          const element = $(template);

          // 安全地绑定下载事件，避免XSS攻击
          element.find(".download-btn").on("click", function () {
            downloadFileFromUrl(fileUrl, fileName);
          });

          previewContainer.append(element);
        } else {
          // 不支持预览的文件类型 - 使用预定义模板，安全防XSS
          const template = $("#unsupported-hint-template").html();
          const element = $(template);

          // 安全地绑定下载事件，避免XSS攻击
          element.find(".download-btn").on("click", function () {
            downloadFileFromUrl(fileUrl, fileName);
          });

          previewContainer.append(element);
        }

        // 设置下载按钮事件
        $("#downloadFromPreview")
          .off("click")
          .on("click", function () {
            downloadFileFromUrl(fileUrl, fileName);
          });

        // 显示模态框
        $("#filePreviewModal").modal("show");
      }

      // 从URL下载文件的通用函数
      function downloadFileFromUrl(fileUrl, fileName) {
        const link = document.createElement("a");
        link.href = fileUrl;
        link.download = fileName;
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      // 加载时间线数据
      function loadTimeline(includeDetails = false) {
        const container = $("#timelineContainer");

        // 显示加载状态
        container.html(
          '<div class="timeline-loading text-center"><i class="fa fa-spinner fa-spin"></i> 加载时间线中...</div>'
        );

        $.ajax({
          url: prefix + "/timeline/" + registrationId,
          type: "GET",
          data: { includeDetails: includeDetails },
          success: function (result) {
            if (result.code === 0) {
              renderTimeline(result.data);
            } else {
              container.html(
                '<div class="alert alert-warning text-center"><i class="fa fa-exclamation-triangle"></i> 加载时间线失败：' +
                  result.msg +
                  "</div>"
              );
            }
          },
          error: function () {
            container.html(
              '<div class="alert alert-danger text-center"><i class="fa fa-times-circle"></i> 时间线加载失败，请刷新页面重试</div>'
            );
          },
        });
      }

      // 渲染时间线
      function renderTimeline(timelineData) {
        const container = $("#timelineContainer");

        if (!timelineData || timelineData.length === 0) {
          // 使用预定义模板，安全防XSS
          const emptyTemplate = $("#timeline-empty-template").html();
          container.html(emptyTemplate);
          return;
        }

        // 清空容器
        container.empty();

        // 时间线数据是按时间倒序的，需要反转以便正序显示
        const sortedData = timelineData.reverse();

        sortedData.forEach(function (event, index) {
          // 确定时间线项的状态类
          let statusClass = "";
          if (event.isImportant) {
            if (event.eventType.code === "REGISTRATION_APPROVED") {
              statusClass = "completed";
            } else if (event.eventType.code === "REGISTRATION_REJECTED") {
              statusClass = "rejected";
            } else if (index === sortedData.length - 1) {
              // 最后一个重要事件标记为当前活动状态
              statusClass = "active";
            } else {
              statusClass = "completed";
            }
          } else {
            statusClass = "completed"; // 非重要事件都标记为已完成
          }

          // 使用预定义模板，安全防XSS
          const template = $("#timeline-item-template").html();
          const timelineItem = $(template);

          // 安全地设置属性和内容
          timelineItem.addClass(statusClass);
          timelineItem.attr(
            "data-event-type",
            sanitizeText(event.eventType.code || "")
          );

          // 设置图标（使用安全的图标类名）
          const iconClass = getEventIconClass(event);
          timelineItem
            .find(".timeline-icon")
            .attr("class", "timeline-icon " + iconClass);

          // 安全地设置文本内容
          timelineItem
            .find(".timeline-description")
            .text(event.eventDescription || "未知事件");
          timelineItem
            .find(".timeline-time")
            .text(formatDateTime(event.eventTime));
          timelineItem
            .find(".timeline-status-description")
            .text(generateStatusDescription(event));

          // 添加到容器
          container.append(timelineItem);
        });

        // 时间线渲染完成后调整高度
        setTimeout(adjustTimelineCardHeight, 20);
      }

      // 获取事件图标CSS类名（安全）
      function getEventIconClass(event) {
        const iconClassMap = {
          REGISTRATION_CREATED: "fa fa-plus-circle",
          REGISTRATION_SUBMITTED: "fa fa-paper-plane",
          REGISTRATION_RESUBMITTED: "fa fa-paper-plane",
          REGISTRATION_APPROVED: "fa fa-check-circle",
          REGISTRATION_REJECTED: "fa fa-times-circle",
          PATIENT_INFO_UPDATED: "fa fa-edit",
          AGENT_INFO_UPDATED: "fa fa-edit",
          AGENT_INFO_SET: "fa fa-user-plus",
          AGENT_INFO_CLEARED: "fa fa-user-times",
          BANK_CARD_UPDATED: "fa fa-edit",
          BANK_CARD_UPLOADED: "fa fa-upload",
        };

        return iconClassMap[event.eventType.code] || "fa fa-info-circle";
      }

      // 生成状态描述（纯文本，安全）
      function generateStatusDescription(event) {
        const descriptionMap = {
          REGISTRATION_CREATED: "申报记录创建完成",
          REGISTRATION_SUBMITTED: "申报已提交，等待审核",
          REGISTRATION_RESUBMITTED: "申报已重新提交，等待审核",
          REGISTRATION_APPROVED: "审核通过，等待医院放款",
          REGISTRATION_REJECTED: "申报被驳回，需要整改后重新提交",
          PATIENT_INFO_UPDATED: "患者信息已更新",
          AGENT_INFO_UPDATED: "代办人信息已更新",
          AGENT_INFO_SET: "代办人信息已设置",
          AGENT_INFO_CLEARED: "代办人信息已清除",
          BANK_CARD_UPDATED: "银行卡信息已更新",
          BANK_CARD_UPLOADED: "银行卡信息已上传",
        };

        let description = descriptionMap[event.eventType.code] || "操作完成";

        // 安全地添加额外数据的描述
        if (event.additionalData) {
          if (event.additionalData.agentName) {
            description += "：" + sanitizeText(event.additionalData.agentName);
          } else if (event.additionalData.attachmentCount) {
            description +=
              "，共 " +
              parseInt(event.additionalData.attachmentCount) +
              " 个附件";
          }
        }

        return description;
      }

      // 文本清理函数，防止XSS攻击
      function sanitizeText(text) {
        if (typeof text !== "string") {
          return String(text || "");
        }

        // 移除HTML标签和潜在的恶意字符
        return text.replace(/[<>&'"]/g, function (char) {
          const entityMap = {
            "<": "&lt;",
            ">": "&gt;",
            "&": "&amp;",
            "'": "&#39;",
            '"': "&quot;",
          };
          return entityMap[char];
        });
      }

      // 格式化日期时间
      function formatDateTime(dateTimeString) {
        if (!dateTimeString) return "-";

        const date = new Date(dateTimeString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      const $timelineDetailSwitch = $("#timelineDetailSwitch");
      function onTimelineDetailSwitchChange() {
        const includeDetails = $timelineDetailSwitch.is(":checked");
        loadTimeline(includeDetails);
      }

      // 审核模式状态管理
      let isApprovalMode = false;

      // 进入审核模式
      function enterApprovalMode() {
        isApprovalMode = true;

        // 更新ARIA属性
        $("#approvalPanel, #approvalTips").attr("aria-hidden", "false");

        // 渐进式显示审核面板
        showApprovalPanels();

        // 更新按钮状态
        $("#toggleApproval")
          .prop("disabled", true)
          .html(
            '<i class="fa fa-spinner fa-spin" aria-hidden="true"></i> 审核中...'
          )
          .attr("aria-label", "正在进行审核操作");

        // 平滑滚动到审核区域
        scrollToApprovalArea();
      }

      // 退出审核模式
      function exitApprovalMode() {
        isApprovalMode = false;

        // 更新ARIA属性
        $("#approvalPanel, #approvalTips").attr("aria-hidden", "true");

        // 隐藏审核面板
        hideApprovalPanels();

        // 恢复按钮状态
        $("#toggleApproval")
          .prop("disabled", false)
          .html('<i class="fa fa-gavel" aria-hidden="true"></i> 审核')
          .attr("aria-label", "开始审核操作");

        // 重置表单
        resetApprovalForm();
      }

      // 渐进式显示审核面板
      function showApprovalPanels() {
        // 先显示操作面板
        $("#approvalPanel").addClass("show");

        // 稍后显示提醒面板，避免信息过载
        setTimeout(() => {
          $("#approvalTips").addClass("show");
        }, 150);
      }

      // 隐藏审核面板
      function hideApprovalPanels() {
        $(".approval-panel").removeClass("show");
      }

      // 平滑滚动到审核区域
      function scrollToApprovalArea() {
        $("html, body").animate(
          {
            scrollTop: $("#approvalPanel").offset().top - 20,
          },
          500
        );
      }

      // 审核相关功能
      function initApprovalFunctions() {
        // 显示/隐藏审核面板
        $("#toggleApproval").click(function () {
          enterApprovalMode();
        });

        // 取消审核
        $("#cancelApproval").click(function () {
          exitApprovalMode();
        });

        // 审核选项点击事件
        $(".approval-option").on("click keydown", function (e) {
          // 支持键盘操作 - Enter键或Space键或点击
          if (
            e.type === "click" ||
            (e.type === "keydown" && (e.key === "Enter" || e.key === " "))
          ) {
            if (e.type === "keydown") {
              e.preventDefault(); // 防止Space键滚动页面
            }

            const $this = $(this);
            const status = $this.data("status");
            const isApprove = status === "APPROVED";

            // 移除所有选项的选中状态
            $(".approval-option")
              .removeClass("selected")
              .attr("aria-pressed", "false");

            // 设置当前选项为选中状态
            $this.addClass("selected").attr("aria-pressed", "true");

            // 显示/隐藏相关详情面板，添加平滑过渡
            if (isApprove) {
              $("#rejectDetails").slideUp(200, function () {
                $("#approveDetails").slideDown(300);
              });
              $("#rejectReason").removeAttr("required");
            } else {
              $("#approveDetails").slideUp(200, function () {
                $("#rejectDetails").slideDown(300);
              });
              $("#rejectReason").attr("required", true);
            }

            // 更新提交按钮状态
            updateSubmitButtonState(status, isApprove);

            // 播放选择音效和屏幕阅读器反馈
            provideFeedback($this, isApprove);

            // 设置焦点
            this.focus();
          }
        });

        // 更新提交按钮状态
        function updateSubmitButtonState(status, isApprove) {
          const $submitBtn = $("#submitApproval");
          const actionText = isApprove ? "通过" : "驳回";
          const buttonClass = isApprove ? "btn-success" : "btn-warning";

          $submitBtn
            .prop("disabled", false)
            .attr("data-status", status)
            .removeClass("btn-success btn-warning btn-primary")
            .addClass(buttonClass)
            .html(
              `<i class="fa fa-gavel" aria-hidden="true"></i> 提交${actionText}`
            )
            .attr("aria-label", `提交审核${actionText}决定`);
        }

        // 提供用户反馈
        function provideFeedback($option, isApprove) {
          const actionText = isApprove ? "通过" : "驳回";
          const message = `已选择审核${actionText}`;

          // 视觉反馈 - 短暂高亮效果
          $option.addClass("pulse-highlight");
          setTimeout(() => {
            $option.removeClass("pulse-highlight");
          }, 600);
        }

        // 字符计数
        $("#rejectReason").on("input", function () {
          const count = $(this).val().length;
          $("#rejectReasonCount").text(count);

          if (count > 500) {
            $(this).val($(this).val().substring(0, 500));
            $("#rejectReasonCount").text(500);
          }
        });

        $("#correctionAdvice").on("input", function () {
          const count = $(this).val().length;
          $("#correctionAdviceCount").text(count);

          if (count > 1000) {
            $(this).val($(this).val().substring(0, 1000));
            $("#correctionAdviceCount").text(1000);
          }
        });

        // 提交审核
        $("#submitApproval").click(function () {
          const status = $(this).attr("data-status");
          const rejectReason = $("#rejectReason").val().trim();
          const correctionAdvice = $("#correctionAdvice").val().trim();

          if (status === "REJECTED" && !rejectReason) {
            $.modal.alertWarning("驳回时必须填写驳回原因");
            return;
          }

          const statusText = status === "APPROVED" ? "通过" : "驳回";
          const message = `确认要${statusText}这条申报记录吗？`;

          $.modal.confirm(message, function () {
            const data = {
              status: status,
              rejectReason: rejectReason,
              correctionAdvice: correctionAdvice,
            };

            // 显示提交中状态
            $("#submitApproval")
              .prop("disabled", true)
              .html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

            $.ajax({
              url: prefix + "/approve/" + registrationId,
              type: "POST",
              contentType: "application/json",
              data: JSON.stringify(data),
              success: function (result) {
                if (result.code === 0) {
                  top.layer.msg(
                    "审核成功",
                    {
                      icon: $.modal.icon(modal_status.SUCCESS),
                      time: 1000,
                      shade: [0.1, "#8F8F8F"],
                    },
                    function () {
                      window.location.reload();
                    }
                  );
                } else {
                  handleApprovalError(result.msg || "审核操作失败");
                }
              },
              error: function () {
                handleApprovalError("网络连接失败，请检查网络后重试");
              },
            });
          });
        });
      }

      // 重置审核表单
      function resetApprovalForm() {
        // 重置选项状态
        $(".approval-option")
          .removeClass("selected pulse-highlight")
          .attr("aria-pressed", "false");

        // 隐藏详情面板
        $("#rejectDetails, #approveDetails").hide();

        // 清空文本域
        $("#rejectReason, #correctionAdvice").val("");

        // 重置字符计数
        $("#rejectReasonCount, #correctionAdviceCount").text("0");

        // 重置提交按钮
        $("#submitApproval")
          .prop("disabled", true)
          .attr("data-status", "")
          .removeClass("btn-success btn-warning")
          .addClass("btn-primary")
          .html('<i class="fa fa-gavel" aria-hidden="true"></i> 提交审核')
          .attr("aria-label", "提交审核决定");

        // 播放重置完成的反馈
        announceScreenReader("审核表单已重置");
      }

      // 错误处理增强
      function handleApprovalError(errorMessage) {
        // 恢复提交按钮状态
        const $submitBtn = $("#submitApproval");
        const currentStatus = $submitBtn.attr("data-status");
        const isApprove = currentStatus === "APPROVED";
        const actionText = isApprove ? "通过" : "驳回";
        const buttonClass = isApprove ? "btn-success" : "btn-warning";

        $submitBtn
          .prop("disabled", false)
          .removeClass("btn-primary")
          .addClass(buttonClass)
          .html(
            `<i class="fa fa-gavel" aria-hidden="true"></i> 提交${actionText}`
          )
          .attr("aria-label", `提交审核${actionText}决定`);

        // 显示错误信息
        $.modal.alertError(errorMessage);

        // 屏幕阅读器播报错误
        announceScreenReader(`审核失败：${errorMessage}`);
      }

      // 动态调整申报进度卡片高度
      function adjustTimelineCardHeight() {
        const $timelineCard = $(".detail-card.timeline-progress-card");

        // 仅在大屏幕下生效
        if ($(window).width() >= 768) {
          const $topLeftSection = $(".top-left-section");

          if ($topLeftSection.length && $timelineCard.length) {
            // 获取左侧区域的高度
            const leftHeight = $topLeftSection.outerHeight();

            // 设置申报进度卡片的高度与左侧一致
            $timelineCard.css("height", leftHeight + "px");
          }
        } else {
          // 小屏幕下移除固定高度，让CSS规则生效
          $timelineCard.css("height", "");
        }
      }

      // 检查操作区是否有可用按钮，没有则显示提示
      function checkOperationsAvailability() {
        const $operationsCard = $('.operations-card .btn-group-vertical');
        const $visibleButtons = $operationsCard.find('button:visible');
        
        if ($visibleButtons.length === 0) {
          // 如果没有可见按钮，添加提示信息
          if ($operationsCard.find('.no-operations-tip').length === 0) {
            const tipTemplate = $('#no-operations-tip-template').html();
            const $tip = $(tipTemplate);
            $operationsCard.append($tip);
          }
        } else {
          // 如果有可见按钮，移除提示信息
          $operationsCard.find('.no-operations-tip').remove();
        }
      }

      // 页面加载完成后初始化时间线
      $(document).ready(function () {
        // 加载默认时间线（详细模式）
        onTimelineDetailSwitchChange();

        // 监听详细模式开关
        $timelineDetailSwitch.change(onTimelineDetailSwitchChange);

        // 初始化审核功能
        initApprovalFunctions();

        // 检查操作区按钮可用性
        checkOperationsAvailability();

        // 初始化高度调整
        setTimeout(adjustTimelineCardHeight, 100); // 等待DOM完全渲染

        // 监听窗口大小变化
        $(window).resize(adjustTimelineCardHeight);
      });
      /*]]>*/
    </script>
  </body>
</html>

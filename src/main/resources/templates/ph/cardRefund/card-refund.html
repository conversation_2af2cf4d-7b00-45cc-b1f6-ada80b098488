<!DOCTYPE html>
<html
        lang="zh"
        xmlns:th="http://www.thymeleaf.org"
>
  <head>
    <th:block th:include="include :: header('余额清退登记管理')"/>
    <title>余额清退登记管理</title>
    <style>
      /* 美化搜索和重置按钮 */
      .btn-custom {
        border-radius: 4px;
        border: none;
        padding: 6px 15px;
        transition: all 0.3s;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        font-weight: 500;
      }

      .btn-search {
        background-color: #1890ff;
        color: white;
      }

      .btn-search:hover {
        background-color: #40a9ff;
      }

      .btn-reset {
        background-color: #faad14;
        color: white;
      }

      .btn-reset:hover {
        background-color: #ffc53d;
      }

      /* 工具栏按钮美化 */
      .btn-toolbar {
        padding: 8px 16px;
        margin-right: 10px;
        border-radius: 4px;
        border: none;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        font-weight: 500;
        transition: all 0.3s;
      }

      /* 刷新统计按钮 */
      .btn-refresh {
        background-color: #1890ff;
        color: white;
      }

      .btn-refresh:hover {
        background-color: #40a9ff;
      }

      /* 禁用状态样式 */
      .btn-toolbar.disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .btn-toolbar.disabled:hover {
        opacity: 0.6;
      }

      /* 调整搜索区域样式 */
      .search-collapse {
        padding: 15px;
        background: #f9f9f9;
        border-radius: 4px;
        margin-bottom: 15px;
      }

      .select-list ul li {
        margin-bottom: 5px;
      }

      /* 调整按钮内的图标间距 */
      .btn-custom i,
      .btn-toolbar i {
        margin-right: 5px;
      }

      .status-badge {
        display: inline-block;
        min-width: 60px;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
      }
      .status-draft {
        background-color: #f39c12;
        color: white;
      }
      .status-submitted {
        background-color: #3498db;
        color: white;
      }
      .status-approved {
        background-color: #27ae60;
        color: white;
      }
      .status-rejected {
        background-color: #e74c3c;
        color: white;
      }

      .statistics-card {
        height: auto !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 4px;
        margin-top: 15px;
        margin-bottom: 15px;
      }
      .stat-item {
        display: inline-block;
        margin-right: 30px;
      }
      .stat-number {
        font-size: 24px;
        font-weight: bold;
      }
      .stat-label {
        font-size: 14px;
        opacity: 0.9;
      }

      .datetime {
        display: inline-block;
        min-width: 80px;
        text-align: center;
      }

      /* 操作列按钮美化 */
      .action-btn {
        border-radius: 4px;
        border: none;
        padding: 4px 8px;
        margin: 0 2px;
        transition: all 0.3s;
        font-size: 12px;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        min-width: 60px;
        text-decoration: none !important;
      }

      .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        text-decoration: none !important;
      }

      .action-btn:active {
        transform: translateY(0);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .action-btn i {
        margin-right: 4px;
      }

      /* 查看按钮 */
      .action-btn.btn-view {
        background-color: #52c41a;
        color: white;
      }

      .action-btn.btn-view:hover {
        background-color: #73d13d;
        color: white;
      }

      /* 操作列容器 */
      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 4px;
      }

      /* 响应式调整 */
      @media (max-width: 768px) {
        .action-buttons {
          flex-direction: column;
          align-items: center;
        }

        .action-btn {
          min-width: 80px;
          margin: 2px 0;
        }
      }
    </style>
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <!-- 统计信息卡片 -->
      <div class="row statistics-card">
        <div class="col-md-12">
          <h5><i class="fa fa-bar-chart"></i> 申报统计概况</h5>
          <div id="statisticsContent">
            <div class="stat-item">
              <div class="stat-number" id="totalCount">-</div>
              <div class="stat-label">总申报数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" id="draftCount">-</div>
              <div class="stat-label">待提交</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" id="pendingCount">-</div>
              <div class="stat-label">待审核</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" id="approvedCount">-</div>
              <div class="stat-label">已通过</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" id="rejectedCount">-</div>
              <div class="stat-label">已驳回</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" id="todayCount">-</div>
              <div class="stat-label">今日新增</div>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list">
              <ul>
                <li>
                  <label for="patientName">姓名：</label>
                  <input
                    type="text"
                    id="patientName"
                    name="patientName"
                    placeholder="请输入患者姓名"
                  />
                </li>
                <li>
                  <label for="patientIdCardNo">身份证号：</label>
                  <input
                    type="text"
                    id="patientIdCardNo"
                    name="patientIdCardNo"
                    placeholder="请输入身份证号"
                  />
                </li>
                <li>
                  <label for="patientMobile">手机号：</label>
                  <input
                    type="text"
                    id="patientMobile"
                    name="patientMobile"
                    placeholder="请输入手机号"
                  />
                </li>
                <li>
                  <label for="status">申报状态：</label>
                  <select id="status" name="status">
                    <option value="">全部</option>
                    <option value="SUBMITTED">待审核</option>
                    <option value="APPROVED">已通过</option>
                    <option value="REJECTED">已驳回</option>
                  </select>
                </li>
                <li class="select-time">
                  <label for="submitDateStart">提交时间：</label>
                  <input
                    type="text"
                    class="time-input"
                    id="submitDateStart"
                    placeholder="开始时间"
                    name="submitDateStart"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="submitDateEnd"
                    placeholder="结束时间"
                    name="submitDateEnd"
                  />
                </li>
                <li>
                  <button
                    type="button"
                    class="btn btn-custom btn-search"
                    onclick="$.table.search()"
                  >
                    <i class="fa fa-search"></i>&nbsp;搜索
                  </button>
                  <button
                    type="button"
                    class="btn btn-custom btn-reset"
                    onclick="$.form.reset()"
                  >
                    <i class="fa fa-refresh"></i>&nbsp;重置
                  </button>
                </li>
              </ul>
            </div>
          </form>
        </div>

        <fieldset class="btn-group-sm" id="toolbar">
          <button
            type="button"
            class="btn btn-toolbar btn-refresh"
            onclick="refreshStatistics()"
          >
            <i class="fa fa-refresh"></i> 刷新统计
          </button>
        </fieldset>

        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>

    <th:block th:include="include :: footer"/>
    <script src="//cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>
    <script th:inline="javascript">
      const prefix = ctx + "ph/card-refund";
      /*<![CDATA[*/
      const queryFlag = /*[[${@permission.isPermitted('ph:card-refund:query')}]]*/ false;
      /*]]>*/

      $(function () {
        // 设置默认日期：开始时间为一个月前，结束时间为明天
        const startDate = dayjs().subtract(1, "month").format("YYYY-MM-DD");
        const endDate = dayjs().add(1, "day").format("YYYY-MM-DD");
        $("#submitDateStart").val(startDate);
        $("#submitDateEnd").val(endDate);

        const options = {
          url: prefix + "/list",
          exportUrl: prefix + "/export",
          modalName: "余额清退登记记录",
          showSearch: false,
          showColumns: false,
          showToggle: false,
          columns: [
            {
              checkbox: true,
            },
            {
              field: "id",
              title: "ID",
              visible: false,
            },
            {
              field: "status.code",
              title: "状态",
              width: "80px",
              align: "center",
              formatter: function (value, row, index) {
                let badgeClass = "";
                let statusText = "";
                switch (value) {
                  case "DRAFT":
                    badgeClass = "status-draft";
                    statusText = "待提交";
                    break;
                  case "SUBMITTED":
                    badgeClass = "status-submitted";
                    statusText = "待审核";
                    break;
                  case "APPROVED":
                    badgeClass = "status-approved";
                    statusText = "已通过";
                    break;
                  case "REJECTED":
                    badgeClass = "status-rejected";
                    statusText = "已驳回";
                    break;
                  default:
                    statusText = value;
                }
                return `<span class="status-badge ${badgeClass}">${statusText}</span>`;
              },
            },
            {
              field: "patientName",
              title: "姓名",
              width: "100px",
              align: "center",
            },
            {
              field: "patientIdCardNo",
              title: "身份证号",
              width: "100px",
              align: "center",
            },
            {
              field: "patientMobile",
              title: "手机号",
              width: "100px",
              align: "center",
            },
            {
              field: "bankCardNo",
              title: "银行卡号",
              width: "150px",
              align: "center",
            },
            {
              field: "submitTime",
              title: "提交时间",
              width: "150px",
              align: "center",
              formatter: function (value, row, index) {
                const content = value ? value.replace(" ", "<br>") : "-";
                return `<div class="datetime">${content}</div>`;
              },
            },
            {
              field: "approveTime",
              title: "审核时间",
              width: "150px",
              align: "center",
              formatter: function (value, row, index) {
                const content = value ? value.replace(" ", "<br>") : "-";
                return `<div class="datetime">${content}</div>`;
              },
            },
            {
              title: "操作",
              width: "150px",
              align: "center",
              formatter: function (value, row, index) {
                const actions = [];

                if (queryFlag) {
                  actions.push(
                    `<a class="action-btn btn-view" href="javascript:void(0)" onclick="viewDetail('${row.id}', '登记详情 - ${row.patientName}')" title="查看详情"><i class="fa fa-eye"></i>查看</a>`
                  );
                }

                return `<div class="action-buttons">${actions.join("")}</div>`;
              },
            },
          ],
        };
        $.table.init(options);

        // 加载统计信息
        loadStatistics();
      });

      // 加载统计信息
      function loadStatistics() {
        $.ajax({
          url: prefix + "/statistics",
          type: "GET",
          success: function (result) {
            if (result.code === 0) {
              const data = result.data;
              $("#totalCount").text(data.totalCount || 0);
              $("#draftCount").text(data.draftCount || 0);
              $("#pendingCount").text(data.pendingApprovalCount || 0);
              $("#approvedCount").text(data.approvedCount || 0);
              $("#rejectedCount").text(data.rejectedCount || 0);
              $("#todayCount").text(data.todayCount || 0);
            }
          },
        });
      }

      // 刷新统计信息
      function refreshStatistics() {
        loadStatistics();
        $.modal.msgSuccess("统计信息已刷新");
      }

      // 查看详情
      function viewDetail(id, title) {
        const url = prefix + "/detail/" + id;
        $.modal.openTab(title, url);
      }

      // 重置表单后刷新统计
      $.form.reset = function () {
        $("#formId")[0].reset();
        $.table.search();
        loadStatistics();
      };
    </script>
  </body>
</html>

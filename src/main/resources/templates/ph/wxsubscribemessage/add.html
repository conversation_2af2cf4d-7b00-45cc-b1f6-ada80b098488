<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增微信订阅消息')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-wxsubscribemessage-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">模板ID：</label>
                <div class="col-sm-8">
                    <input name="templateId" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">openid：</label>
                <div class="col-sm-8">
                    <input name="openid" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">消息内容：</label>
                <div class="col-sm-8">
                    <input name="data" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">消息类型：</label>
                <div class="col-sm-8">
                    <select name="type" class="form-control m-b" required>
                        <option value="">所有</option>
                    </select>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">关联ID：</label>
                <div class="col-sm-8">
                    <input name="companionId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发送时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="sendTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发送状态：</label>
                <div class="col-sm-8">
                    <select name="sendStatus" class="form-control m-b" th:with="type=${@dict.getType('ok_fail')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">错误信息：</label>
                <div class="col-sm-8">
                    <input name="error" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "ph/wxsubscribemessage"
        $("#form-wxsubscribemessage-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-wxsubscribemessage-add').serialize());
            }
        }

        $("input[name='sendTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
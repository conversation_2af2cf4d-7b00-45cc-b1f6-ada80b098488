<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('微信医保订单列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>用户姓名：</label>
                            <input type="text" name="userName"/>
                        </li>
                        <li>
                            <label>用户证件号码：</label>
                            <input type="text" name="userCardNo"/>
                        </li>
                        <li>
                            <label>支付订单号：</label>
                            <input type="text" name="payOrderId"/>
                        </li>
                        <li>
                            <label>费用总额：</label>
                            <input type="text" name="feeSumAmount"/>
                        </li>
                        <li>
                            <label>第三方服务商订单号：</label>
                            <input type="text" name="hospitalOutTradeNo"/>
                        </li>
                        <li>
                            <label>诊疗单ID：</label>
                            <input type="text" name="medTransactionId"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="ph:mipWxOrder:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary single disabled" onclick="queryTransaction()"
               shiro:hasPermission="ph:mipWxOrder:queryTransaction">
                <i class="fa fa-refresh"></i> 查询交易
            </a>
            <a class="btn btn-primary single disabled" onclick="refundWeixin()"
               shiro:hasPermission="ph:mipWxOrder:refundWeixin">
                <i class="fa fa-backward"></i> 微信退款
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:mipWxOrder:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/mipWxOrder";

    $(function () {
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "微信医保订单",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'userName',
                    title: '用户姓名'
                },
                {
                    field: 'userCardNo',
                    title: '用户证件号码'
                },
                {
                    field: 'payAuthNo',
                    title: '医保授权码'
                },
                {
                    field: 'longitudeLatitude',
                    title: '经纬度'
                },
                {
                    field: 'payOrderId',
                    title: '支付订单号'
                },
                {
                    field: 'orderStatus',
                    title: '订单状态'
                },
                {
                    field: 'feeSumAmount',
                    title: '费用总额'
                },
                {
                    field: 'ownPayAmount',
                    title: '现金支付金额'
                },
                {
                    field: 'personalAccountPayAmount',
                    title: '个人账户支出'
                },
                {
                    field: 'fundPayAmount',
                    title: '医保基金支出'
                },
                {
                    field: 'hospitalOutTradeNo',
                    title: '第三方服务商订单号'
                },
                {
                    field: 'medTransactionId',
                    title: '诊疗单ID'
                }
            ]
        };
        $.table.init(options);
    });

    function queryTransaction() {
        table.set();
        var row = $("#bootstrap-table").bootstrapTable('getSelections')[0];
        if (!row.medTransactionId) {
            $.modal.alertWarning("诊疗单ID为空，说明此单下单失败");
            return;
        }
        $.operate.get(prefix + '/queryTransaction/' + row.id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询交易状态',
                shade: 0.6,
                shadeClose: true,
                content: '<pre style="margin: 20px;">' + JSON.stringify(resp, null, 4) + '</pre>'
            });
        });
    }

    function refundWeixin() {
        table.set();
        var row = $("#bootstrap-table").bootstrapTable('getSelections')[0];
        $.operate.post(prefix + '/refundWeixin/' + row.id, function (resp) {
            if (resp.code === 0) {
                $.table.search();
                $.modal.alertSuccess('操作成功');
            } else {
                $.modal.alertWarning(resp.msg);
            }
        });
    }
</script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改微信医保订单')" />
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-mipWxOrder-edit" th:object="${mipWxOrder}">
                <input name="id" th:field="*{id}" type="hidden">
                                                                                                                                            <div class="form-group">
                            <label class="col-sm-3 control-label is-required">用户姓名：</label>
                            <div class="col-sm-8">
                                <input name="userName" th:field="*{userName}" class="form-control" type="text" required>
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">用户证件号码：</label>
                            <div class="col-sm-8">
                                <input name="userCardNo" th:field="*{userCardNo}" class="form-control" type="text" required>
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">用户证件类型：</label>
                            <div class="col-sm-8">
                                <select name="userCardType" class="form-control m-b" required>
                                    <option value="">所有</option>
                                </select>
                                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">医保授权码：</label>
                            <div class="col-sm-8">
                                <input name="payAuthNo" th:field="*{payAuthNo}" class="form-control" type="text" required>
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">经纬度：</label>
                            <div class="col-sm-8">
                                <input name="longitudeLatitude" th:field="*{longitudeLatitude}" class="form-control" type="text" required>
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label">支付订单号：</label>
                            <div class="col-sm-8">
                                <input name="payOrderId" th:field="*{payOrderId}" class="form-control" type="text">
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label">订单状态：</label>
                            <div class="col-sm-8">
                                <div class="radio-box">
                                    <input type="radio" name="orderStatus" value="">
                                    <label th:for="orderStatus" th:text="未知"></label>
                                </div>
                                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label">费用总额：</label>
                            <div class="col-sm-8">
                                <input name="feeSumAmount" th:field="*{feeSumAmount}" class="form-control" type="text">
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label">现金支付金额：</label>
                            <div class="col-sm-8">
                                <input name="ownPayAmount" th:field="*{ownPayAmount}" class="form-control" type="text">
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label">个人账户支出：</label>
                            <div class="col-sm-8">
                                <input name="personalAccountPayAmount" th:field="*{personalAccountPayAmount}" class="form-control" type="text">
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label">医保基金支出：</label>
                            <div class="col-sm-8">
                                <input name="fundPayAmount" th:field="*{fundPayAmount}" class="form-control" type="text">
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label">第三方服务商订单号：</label>
                            <div class="col-sm-8">
                                <input name="hospitalOutTradeNo" th:field="*{hospitalOutTradeNo}" class="form-control" type="text">
                            </div>
                        </div>
                                                                                                                                                                        <div class="form-group">
                            <label class="col-sm-3 control-label">诊疗单ID：</label>
                            <div class="col-sm-8">
                                <input name="medTransactionId" th:field="*{medTransactionId}" class="form-control" type="text">
                            </div>
                        </div>
                                                                    </form>
</div>
<th:block th:include="include :: footer" />
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <script th:inline="javascript">
    var prefix = ctx + "ph/mipWxOrder";
            $("#form-mipWxOrder-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-mipWxOrder-edit').serialize());
        }
    }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </script>
</body>
</html>
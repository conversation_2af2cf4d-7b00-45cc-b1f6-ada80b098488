<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单统计')"/>
    <style>
        th, td {
            font-weight: bold !important;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>查询日期：</label>
                            <input type="text" class="time-input" id="minDate" placeholder="开始日期" name="minDate"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="maxDate" placeholder="截止日期" name="maxDate"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()"
               shiro:hasPermission="ph:pscOrderScoreSummary:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table">
            <table id="bootstrap-table" data-mobile-responsive="true" class="table-bordered table-striped"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:src="@{/ajax/libs/dayjs/dayjs.min.js?v=1.11.3}"></script>
<script th:inline="javascript">

    $(function () {
        const today = dayjs().format('YYYY-MM-DD')
        $('#minDate').val(today)
        $('#maxDate').val(today)

        var options = {
            url: "/ph/pscOrderScoreSummary/list",
            exportUrl: "/ph/pscOrderScoreSummary/export",
            modalName: "订单统计",
            columns: [
                [
                    {
                        field: 'carerName',
                        title: '姓名',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 2,
                    },
                    {
                        title: '评价人次',
                        align: 'center',
                        valign: 'middle',
                        colspan: 5,
                    },
                    {
                        field: 'total',
                        title: '合计',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 2,
                    },
                ],
                [
                    {
                        field: 'score1',
                        title: '非常不满意',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'score2',
                        title: '不满意',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'score3',
                        title: '一般',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'score4',
                        title: '满意',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'score5',
                        title: '非常满意',
                        align: 'center',
                        valign: 'middle',
                    }
                ]
            ]
        };
        $.table.init(options);
    });
</script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('支付宝对账列表')"/>
    <style>
        th, td {
            font-weight: bold !important;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>日期：</label>
                            <input type="text" name="id"/>
                        </li>
                        <li>
                            <label>支付平账？：</label>
                            <select name="payBalanced" th:with="type=${@dict.getType('yes_no')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>退款平账？：</label>
                            <select name="refundBalanced" th:with="type=${@dict.getType('yes_no')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:alipaycheck:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table">
            <table id="bootstrap-table" data-mobile-responsive="true" class="table-bordered table-striped"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    const payBalancedDatas = [[${@dict.getType('yes_no')}]];
    const refundBalancedDatas = [[${@dict.getType('yes_no')}]];
    const syncAlipayBillFlag = [[${@permission.hasPermi('ph:alipaycheck:syncAlipayBill')}]];
    const downloadAlipayBillFlag = [[${@permission.hasPermi('ph:alipaycheck:downloadAlipayBill')}]];
    const syncHisBillFlag = [[${@permission.hasPermi('ph:alipaycheck:syncHisBill')}]];
    const downloadHisBillFlag = [[${@permission.hasPermi('ph:alipaycheck:downloadHisBill')}]];
    const showDiffOrdersFlag = [[${@permission.hasPermi('ph:alipaycheck:showDiffOrders')}]];
    const prefix = ctx + "ph/alipaycheck";

    $(function () {
        const options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            modalName: "支付宝对账",
            sortName: "id",
            sortOrder: "desc",
            columns: [
                [
                    {
                        field: 'id',
                        title: '日期',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 2,
                    },
                    {
                        title: '充值',
                        align: 'center',
                        valign: 'middle',
                        colspan: 4
                    },
                    {
                        title: '退款',
                        align: 'center',
                        valign: 'middle',
                        colspan: 4
                    },
                    {
                        title: '医保',
                        align: 'center',
                        valign: 'middle',
                        colspan: 2
                    },
                    {
                        title: '净流入',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3
                    },
                    {
                        title: '操作',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3
                    },
                ],
                [
                    {
                        field: 'alipayPayAmount',
                        title: '掌医',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'hisPayAmount',
                        title: 'HIS',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'diffPayAmount',
                        title: '掌医-HIS',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'payBalanced',
                        title: '平账？',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(payBalancedDatas, value);
                        }
                    },
                    {
                        field: 'alipayRefundAmount',
                        title: '掌医',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'hisRefundAmount',
                        title: 'HIS',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'diffRefundAmount',
                        title: '掌医-HIS',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'refundBalanced',
                        title: '平账？',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(refundBalancedDatas, value);
                        }
                    },
                    {
                        field: 'mipPayAmount',
                        title: '支付',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'mipRefundAmount',
                        title: '退款',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'alipayNetIn',
                        title: '掌医',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'mipNetIn',
                        title: '医保',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'totalNetIn',
                        title: '掌医+医保',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        title: '支付宝账单',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            const actions = [];
                            if ([[${user.isAdmin()}]]) {
                                actions.push('<a class="btn btn-success btn-xs ' + syncAlipayBillFlag + '" href="javascript:void(0)" onclick="syncAlipayBill(\'' + row.id + '\')"><i class="fa fa-refresh"></i>同步</a> ');
                            }
                            actions.push('<a class="btn btn-success btn-xs ' + downloadAlipayBillFlag + '" href="javascript:void(0)" onclick="downloadAlipayBill(\'' + row.id + '\')"><i class="fa fa-download"></i>下载</a> ');
                            return actions.join('');
                        }
                    },
                    {
                        title: 'HIS账单',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            const actions = [];
                            if ([[${user.isAdmin()}]]) {
                                actions.push('<a class="btn btn-success btn-xs ' + syncHisBillFlag + '" href="javascript:void(0)" onclick="syncHisBill(\'' + row.id + '\')"><i class="fa fa-refresh"></i>同步</a> ');
                            }
                            actions.push('<a class="btn btn-success btn-xs ' + downloadHisBillFlag + '" href="javascript:void(0)" onclick="downloadHisBill(\'' + row.id + '\')"><i class="fa fa-download"></i>下载</a> ');
                            return actions.join('');
                        }
                    },
                    {
                        title: '错单',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            const actions = [];
                            if (row.payBalanced === 0 || row.refundBalanced === 0) {
                                actions.push('<a class="btn btn-success btn-xs ' + showDiffOrdersFlag + '" href="javascript:void(0)" onclick="showDiffOrders(\'' + row.id + '\')"><i class="fa fa-eye"></i>查看</a> ');
                            }
                            return actions.join('');
                        }
                    }
                ]
            ]
        };
        $.table.init(options);
    });

    function syncAlipayBill(id) {
        $.operate.post(prefix + '/syncAlipayBill/' + id, {});
    }

    function downloadAlipayBill(id) {
        window.open(prefix + '/downloadAlipayBill/' + id, "_blank");
    }

    function syncHisBill(id) {
        $.operate.post(prefix + '/syncHisBill/' + id, {});
    }

    function downloadHisBill(id) {
        window.open(prefix + '/downloadHisBill/' + id, "_blank");
    }

    function showDiffOrders(id) {
        const url = prefix + '/showDiffOrders/' + id;
        $.modal.openTab("错单 - " + id, url);
    }
</script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('绑卡人次')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>日期：</label>
                            <input type="text" class="time-input" id="startDate" placeholder="开始日期" th:value="${startDate}"
                                   name="params[startDate]" data-format="yyyy-MM-dd" data-callback="onSelectDate"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endDate" placeholder="结束日期" th:value="${endDate}"
                                   name="params[endDate]" data-format="yyyy-MM-dd" data-callback="onSelectDate"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="checkThenSearch()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12" style="background: #fff; margin-top: 10px; border-radius: 6px;">
            <div class="echarts" id="echarts1"></div>
        </div>

        <div class="col-sm-12 select-table table-striped" style="min-height: 50%;">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: echarts-js"/>
<th:block th:include="include :: dayjs"/>
<script th:inline="javascript">
    $(function () {
        const prefix = '/ph/report/patient_daily_increment';
        $.table.init({
            url: prefix,
            exportUrl: prefix + "/export",
            modalName: "绑卡人次报表",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showColumns: false,
            showToggle: true,
            showExport: true,
            showFooter: true,
            columns: [
                {
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'date',
                    title: '日期',
                    formatter: function (value) {
                        return value.substring(0, 10)
                    },
                    footerFormatter: function () {
                        return '合计'
                    }
                },
                {
                    field: 'count',
                    title: '绑卡人次',
                    footerFormatter: function (rows) {
                        return rows.map(row => Number(row['count']) || 0).reduce((sum, x) => sum + x, 0)
                    }
                }
            ],
            onLoadSuccess: function (data) {
                renderChart(data.rows);
            }
        });
    });

    function renderChart(records) {
        var chart = echarts.init(document.getElementById('echarts1'), 'light');
        chart.setOption({
            legend: {},
            tooltip: {},
            dataset: {
                source: records.map(function (record) {
                    return {
                        "日期": record['date'],
                        "绑卡人次": record['count']
                    };
                } )
            },
            xAxis: {
                type: 'category'
            },
            yAxis: {},
            series: [
                {type: 'bar'}
            ],
            toolbox: {
                show: true,
                feature: {
                    saveAsImage: {
                        show: true,
                        excludeComponents: ['toolbox'],
                        pixelRatio: 2
                    }
                }
            }
        })
        window.onresize = chart.resize;
    }

    function checkThenSearch() {
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        if (!startDate) {
            $.modal.msg('请选择开始日期');
            return;
        }
        if (!endDate) {
            $.modal.msg('请选择截止日期');
            return;
        }

        if (startDate > endDate) {
            $.modal.msg('截止日期不应小于开始日期');
            $('#endDate').val('');
            return;
        }

        var minDate = dayjs(startDate, 'YYYY-MM-DD');
        var maxDate = dayjs(endDate, 'YYYY-MM-DD');
        if (minDate.add(3, 'month').isBefore(maxDate)) {
            $.modal.msg('查询范围不应大于三个月');
            $('#endDate').val('');
            return;
        }

        $.table.search();
    }

    function onSelectDate() {
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        if (startDate && endDate) {
            if (startDate > endDate) {
                $.modal.msg('截止日期不应小于开始日期');
                $('#endDate').val('');
                return;
            }

            var minDate = dayjs(startDate, 'YYYY-MM-DD');
            var maxDate = dayjs(endDate, 'YYYY-MM-DD');
            if (minDate.add(3, 'month').isBefore(maxDate)) {
                $.modal.msg('查询范围不应大于三个月');
                $('#endDate').val('');
                return;
            }
        }
    }
</script>
</body>
</html>
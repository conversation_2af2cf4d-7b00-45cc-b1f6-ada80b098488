<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://tempuri.org/"
                  xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" targetNamespace="http://tempuri.org/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="CallService">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="jsonInData" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CallServiceResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CallServiceResult" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="CallServiceSoapIn">
    <wsdl:part name="parameters" element="tns:CallService"/>
  </wsdl:message>
  <wsdl:message name="CallServiceSoapOut">
    <wsdl:part name="parameters" element="tns:CallServiceResponse"/>
  </wsdl:message>
  <wsdl:portType name="MPServiceSoap">
    <wsdl:operation name="CallService">
      <wsdl:input message="tns:CallServiceSoapIn"/>
      <wsdl:output message="tns:CallServiceSoapOut"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="MPServiceSoap" type="tns:MPServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="CallService">
      <soap:operation soapAction="http://tempuri.org/CallService" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="MPServiceSoap12" type="tns:MPServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="CallService">
      <soap12:operation soapAction="http://tempuri.org/CallService" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="MPService">
    <wsdl:port name="MPServiceSoap" binding="tns:MPServiceSoap">
      <soap:address location="http://127.0.0.1:6004/MPService.asmx"/>
    </wsdl:port>
    <wsdl:port name="MPServiceSoap12" binding="tns:MPServiceSoap12">
      <soap12:address location="http://127.0.0.1:6004/MPService.asmx"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>

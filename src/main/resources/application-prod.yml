ruoyi:
  profile: /www/server/tomcat_zyxcx/upload

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 主库数据源
      master:
        url: ***********************************************************************************************************************************************************************
        driverClassName: com.mysql.cj.jdbc.Driver
        username: zhangyi
        password: nTzTWPYFbkS6maKy
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 3000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      keep-alive: true
      pool-prepared-statements: true

# 开发环境配置
server:
  forward-headers-strategy: native
  max-http-request-header-size: 10KB
  port: 9001
  servlet:
    # 应用的访问路径
    context-path: /
    session:
      cookie:
        http-only: true
        secure: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接超时设置为15秒，适合网络状况良好的环境
    connection-timeout: 15000
    # tomcat线程池配置
    threads:
      # 2核CPU，设置适中的线程数
      max: 400
      # 最小空闲线程，提高突发请求响应速度
      min-spare: 40
    # 启用keep-alive配置
    keep-alive-timeout: 60
    max-keep-alive-requests: 10000
    # 连接器配置
    max-connections: 8000
    # 队列长度
    accept-count: 500
    basedir: /www/server/tomcat_zyxcx
    accesslog:
      enabled: true
      suffix: .log
      prefix: access_log
      file-date-format: .yyyy-MM-dd
      directory: logs
    remoteip:
      remote-ip-header: X-Forwarded-For
      protocol-header: X-Forwarded-Proto
mhd:
  serviceUrl: http://127.0.0.1:6003/PmrMedicalRecord.asmx
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.CategoryMapper">

    <resultMap type="Category" id="CategoryResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCategoryVo">
        select id, name, create_by, create_time, update_by, update_time from ph_category
    </sql>

    <select id="selectCategoryAll" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
    </select>

    <select id="selectCategoryList" parameterType="Category" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectCategoryById" parameterType="Long" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertCategory" parameterType="Category" useGeneratedKeys="true" keyProperty="id">
        insert into ph_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''">name,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="updateTime != null ">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''">#{name},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null ">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCategory" parameterType="Category">
        update ph_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null  and name != ''">name = #{name},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCategoryById" parameterType="Long">
        delete from ph_category where id = #{id}
    </delete>

    <delete id="deleteCategoryByIds" parameterType="String">
        delete from ph_category where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
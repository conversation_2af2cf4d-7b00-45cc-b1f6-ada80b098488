<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.ArticleMapper">

    <resultMap type="Article" id="ArticleResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="title"    column="title"    />
        <result property="summary"    column="summary"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectArticleVo">
        select id, category_id, category_name, title, summary, content, create_by, create_time, update_by, update_time from ph_article
    </sql>

    <select id="selectArticleList" parameterType="Article" resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        <where>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="summary != null  and summary != ''"> and summary = #{summary}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>

    <select id="selectArticleById" parameterType="Long" resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        where id = #{id}
    </select>

    <insert id="insertArticle" parameterType="Article" useGeneratedKeys="true" keyProperty="id">
        insert into ph_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null ">category_id,</if>
            <if test="categoryName != null  and categoryName != ''">category_name,</if>
            <if test="title != null  and title != ''">title,</if>
            <if test="summary != null  and summary != ''">summary,</if>
            <if test="content != null  and content != ''">content,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="updateTime != null ">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null ">#{categoryId},</if>
            <if test="categoryName != null  and categoryName != ''">#{categoryName},</if>
            <if test="title != null  and title != ''">#{title},</if>
            <if test="summary != null  and summary != ''">#{summary},</if>
            <if test="content != null  and content != ''">#{content},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null ">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateArticle" parameterType="Article">
        update ph_article
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null ">category_id = #{categoryId},</if>
            <if test="categoryName != null  and categoryName != ''">category_name = #{categoryName},</if>
            <if test="title != null  and title != ''">title = #{title},</if>
            <if test="summary != null  and summary != ''">summary = #{summary},</if>
            <if test="content != null  and content != ''">content = #{content},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteArticleById" parameterType="Long">
        delete from ph_article where id = #{id}
    </delete>

    <delete id="deleteArticleByIds" parameterType="String">
        delete from ph_article where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
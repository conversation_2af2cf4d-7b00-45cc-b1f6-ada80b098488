<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.OutpatientPaymentRecordMapper">

    <resultMap type="space.lzhq.ph.domain.OutpatientPaymentRecord" id="OutpatientPaymentRecordResult">
        <result property="id" column="id"/>
        <result property="patientCardNo" column="patient_card_no"/>
        <result property="patientName" column="patient_name"/>
        <result property="feeDetails" column="fee_details"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="hisTradeNo" column="his_trade_no"/>
        <result property="hisResponseCode" column="his_response_code"/>
        <result property="hisResponseMessage" column="his_response_message"/>
        <result property="hisResponseData" column="his_response_data"/>
        <result property="errorDetails" column="error_details"/>
        <result property="status" column="status"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
    </resultMap>

    <sql id="selectOutpatientPaymentRecordVo">
        select id,
               patient_card_no,
               patient_name,
               fee_details,
               total_amount,
               payment_method,
               his_trade_no,
               his_response_code,
               his_response_message,
               his_response_data,
               error_details,
               status,
               created_time,
               updated_time
        from outpatient_payment_record
    </sql>

    <select id="selectOutpatientPaymentRecordList" parameterType="OutpatientPaymentRecord"
            resultMap="OutpatientPaymentRecordResult">
        <include refid="selectOutpatientPaymentRecordVo"/>
        <where>
            <if test="patientCardNo != null and patientCardNo != ''">and patient_card_no = #{patientCardNo}</if>
            <if test="patientName != null and patientName != ''">and patient_name LIKE CONCAT('%', #{patientName}, '%')</if>
            <if test="paymentMethod != null and paymentMethod != ''">and payment_method = #{paymentMethod}</if>
            <if test="hisTradeNo != null and hisTradeNo != ''">and his_trade_no = #{hisTradeNo}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        order by created_time desc
    </select>

    <select id="selectOutpatientPaymentRecordById" parameterType="Long" resultMap="OutpatientPaymentRecordResult">
        <include refid="selectOutpatientPaymentRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectOutpatientPaymentRecordListByPatientCardNo" parameterType="String" resultMap="OutpatientPaymentRecordResult">
        <include refid="selectOutpatientPaymentRecordVo"/>
        where patient_card_no = #{patientCardNo}
        order by created_time desc
    </select>

    <insert id="insertOutpatientPaymentRecord" parameterType="OutpatientPaymentRecord" useGeneratedKeys="true" keyProperty="id">
        insert into outpatient_payment_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="patientCardNo != null">patient_card_no,</if>
            <if test="patientName != null">patient_name,</if>
            <if test="feeDetails != null">fee_details,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="hisTradeNo != null">his_trade_no,</if>
            <if test="hisResponseCode != null">his_response_code,</if>
            <if test="hisResponseMessage != null">his_response_message,</if>
            <if test="hisResponseData != null">his_response_data,</if>
            <if test="errorDetails != null">error_details,</if>
            <if test="status != null">status,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="patientCardNo != null">#{patientCardNo},</if>
            <if test="patientName != null">#{patientName},</if>
            <if test="feeDetails != null">#{feeDetails},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="hisTradeNo != null">#{hisTradeNo},</if>
            <if test="hisResponseCode != null">#{hisResponseCode},</if>
            <if test="hisResponseMessage != null">#{hisResponseMessage},</if>
            <if test="hisResponseData != null">#{hisResponseData},</if>
            <if test="errorDetails != null">#{errorDetails},</if>
            <if test="status != null">#{status},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
        </trim>
    </insert>

    <update id="updateOutpatientPaymentRecord" parameterType="OutpatientPaymentRecord">
        update outpatient_payment_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="patientCardNo != null">patient_card_no = #{patientCardNo},</if>
            <if test="patientName != null">patient_name = #{patientName},</if>
            <if test="feeDetails != null">fee_details = #{feeDetails},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="hisTradeNo != null">his_trade_no = #{hisTradeNo},</if>
            <if test="hisResponseCode != null">his_response_code = #{hisResponseCode},</if>
            <if test="hisResponseMessage != null">his_response_message = #{hisResponseMessage},</if>
            <if test="hisResponseData != null">his_response_data = #{hisResponseData},</if>
            <if test="errorDetails != null">error_details = #{errorDetails},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 缴费记录作为重要审计数据，不提供删除功能 -->
    <!-- 如需删除请联系系统管理员手动处理 -->

</mapper> 
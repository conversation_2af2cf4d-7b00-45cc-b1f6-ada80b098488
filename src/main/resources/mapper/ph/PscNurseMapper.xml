<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.PscNurseMapper">

    <resultMap type="PscNurse" id="PscNurseResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="employeeNo" column="employee_no"/>
        <result property="mobile" column="mobile"/>
    </resultMap>

    <sql id="selectPscNurseVo">
        select id, name, employee_no, mobile
        from psc_nurse
    </sql>

    <select id="selectPscNurseList" parameterType="PscNurse" resultMap="PscNurseResult">
        <include refid="selectPscNurseVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="employeeNo != null  and employeeNo != ''">and employee_no = #{employeeNo}</if>
        </where>
    </select>

    <select id="selectPscNurseById" parameterType="Long" resultMap="PscNurseResult">
        <include refid="selectPscNurseVo"/>
        where id = #{id}
    </select>

    <select id="selectPscNurseByEmployeeNo" resultMap="PscNurseResult">
        <include refid="selectPscNurseVo"/>
        where employee_no = #{employeeNo}
    </select>

    <select id="isEmployeeNoUnique" parameterType="PscNurse" resultType="Boolean">
        select count(*) = 0
        from psc_nurse
        where employee_no = #{employeeNo}
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="isMobileNoUnique" parameterType="PscNurse" resultType="Boolean">
        select count(*) = 0
        from psc_nurse
        where mobile = #{mobile}
        <if test="id != null">and id != #{id}</if>
    </select>

    <insert id="insertPscNurse" parameterType="PscNurse" useGeneratedKeys="true" keyProperty="id">
        insert into psc_nurse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="employeeNo != null and employeeNo != ''">employee_no,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="employeeNo != null and employeeNo != ''">#{employeeNo},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
        </trim>
    </insert>

    <update id="updatePscNurse" parameterType="PscNurse">
        update psc_nurse
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="employeeNo != null and employeeNo != ''">employee_no = #{employeeNo},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePscNurseById" parameterType="Long">
        delete
        from psc_nurse
        where id = #{id}
    </delete>

    <delete id="deletePscNurseByIds" parameterType="String">
        delete from psc_nurse where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.MipAlipayOrderMapper">

    <resultMap type="MipAlipayOrder" id="MipAlipayOrderResult">
        <result property="id" column="id"/>
        <result property="authCode" column="auth_code"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="certNo" column="cert_no"/>
        <result property="certType" column="cert_type"/>
        <result property="cityId" column="city_id"/>
        <result property="accessToken" column="access_token"/>
        <result property="payAuthNo" column="pay_auth_no"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="payOrderId" column="pay_order_id"/>
        <result property="clinicNo" column="clinic_no"/>
        <result property="feesJson" column="fees_json"/>
        <result property="feeSumAmount" column="fee_sum_amount"/>
        <result property="personalAccountPayAmount" column="personal_account_pay_amount"/>
        <result property="fundPayAmount" column="fund_pay_amount"/>
        <result property="ownPayAmount" column="own_pay_amount"/>
        <result property="medicalCardInstId" column="medical_card_inst_id"/>
        <result property="medicalCardId" column="medical_card_id"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="medOrgOrd" column="med_org_ord"/>
        <result property="payToken" column="pay_token"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="tradeStatus" column="trade_status"/>
        <result property="createTime" column="create_time"/>
        <result property="tradeTime" column="trade_time"/>
        <result property="hisInvoiceNo" column="his_invoice_no"/>
        <result property="hisSettleMessage" column="his_settle_message"/>
        <result property="hisSettleTime" column="his_settle_time"/>
        <result property="siSettleId" column="si_settle_id"/>
        <result property="siSettleTime" column="si_settle_time"/>
        <result property="alipayOutRequestNo" column="alipay_out_request_no"/>
        <result property="alipayRefundStatus" column="alipay_refund_status"/>
    </resultMap>

    <sql id="selectMipAlipayOrderVo">
        select id,
               auth_code,
               user_id,
               user_name,
               cert_no,
               cert_type,
               city_id,
               access_token,
               pay_auth_no,
               latitude,
               longitude,
               pay_order_id,
               clinic_no,
               fees_json,
               fee_sum_amount,
               personal_account_pay_amount,
               fund_pay_amount,
               own_pay_amount,
               medical_card_inst_id,
               medical_card_id,
               out_trade_no,
               med_org_ord,
               pay_token,
               trade_no,
               trade_status,
               create_time,
               trade_time,
               his_invoice_no,
               his_settle_message,
               his_settle_time,
               si_settle_id,
               si_settle_time,
               alipay_out_request_no,
               alipay_refund_status
        from mip_alipay_order
    </sql>

    <select id="selectMipAlipayOrderList" parameterType="MipAlipayOrder" resultMap="MipAlipayOrderResult">
        <include refid="selectMipAlipayOrderVo"/>
        <where>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="certNo != null  and certNo != ''">and cert_no = #{certNo}</if>
            <if test="accessToken != null  and accessToken != ''">and access_token = #{accessToken}</if>
            <if test="payAuthNo != null  and payAuthNo != ''">and pay_auth_no = #{payAuthNo}</if>
            <if test="payOrderId != null  and payOrderId != ''">and pay_order_id = #{payOrderId}</if>
            <if test="clinicNo != null  and clinicNo != ''">and clinic_no = #{clinicNo}</if>
            <if test="outTradeNo != null  and outTradeNo != ''">and out_trade_no = #{outTradeNo}</if>
            <if test="tradeNo != null  and tradeNo != ''">and trade_no = #{tradeNo}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="tradeTime != null ">and trade_time = #{tradeTime}</if>
            <if test="hisInvoiceNo != null  and hisInvoiceNo != ''">and his_invoice_no = #{hisInvoiceNo}</if>
            <if test="siSettleId != null  and siSettleId != ''">and si_settle_id = #{siSettleId}</if>
            <if test="alipayOutRequestNo != null  and alipayOutRequestNo != ''">and alipay_out_request_no =
                #{alipayOutRequestNo}
            </if>
        </where>
    </select>

</mapper>

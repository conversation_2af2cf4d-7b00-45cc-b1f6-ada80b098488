<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.PscCarerMapper">

    <resultMap type="PscCarer" id="PscCarerResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="employeeNo" column="employee_no"/>
        <result property="mobile" column="mobile"/>
    </resultMap>

    <sql id="selectPscCarerVo">
        select id, name, employee_no, mobile
        from psc_carer
    </sql>

    <select id="selectPscCarerList" parameterType="PscCarer" resultMap="PscCarerResult">
        <include refid="selectPscCarerVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="employeeNo != null  and employeeNo != ''">and employee_no = #{employeeNo}</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
        </where>
    </select>

    <select id="selectPscCarerById" parameterType="Long" resultMap="PscCarerResult">
        <include refid="selectPscCarerVo"/>
        where id = #{id}
    </select>

    <select id="selectPscCarerByEmployeeNo" resultMap="PscCarerResult">
        <include refid="selectPscCarerVo"/>
        where employee_no = #{employeeNo}
    </select>

    <select id="isEmployeeNoUnique" parameterType="PscCarer" resultType="Boolean">
        select count(*) = 0
        from psc_carer
        where employee_no = #{employeeNo}
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="isMobileNoUnique" parameterType="PscCarer" resultType="Boolean">
        select count(*) = 0
        from psc_carer
        where mobile = #{mobile}
        <if test="id != null">and id != #{id}</if>
    </select>

    <insert id="insertPscCarer" parameterType="PscCarer" useGeneratedKeys="true" keyProperty="id">
        insert into psc_carer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="employeeNo != null and employeeNo != ''">employee_no,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="employeeNo != null and employeeNo != ''">#{employeeNo},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
        </trim>
    </insert>

    <update id="updatePscCarer" parameterType="PscCarer">
        update psc_carer
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="employeeNo != null and employeeNo != ''">employee_no = #{employeeNo},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePscCarerById" parameterType="Long">
        delete
        from psc_carer
        where id = #{id}
    </delete>

    <delete id="deletePscCarerByIds" parameterType="String">
        delete from psc_carer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
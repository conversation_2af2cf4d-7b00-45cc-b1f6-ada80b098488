<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.NurseCardMapper">

    <resultMap type="NurseCard" id="NurseCardResult">
        <result property="id" column="id"/>
        <result property="zhuyuanNo" column="zhuyuan_no"/>
        <result property="department" column="department"/>
        <result property="bed" column="bed"/>
        <result property="ruyuanTime" column="ruyuan_time"/>
        <result property="chuyuanTime" column="chuyuan_time"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientIdCardNo" column="patient_id_card_no"/>
        <result property="patientMobile" column="patient_mobile"/>
        <result property="nurseName" column="nurse_name"/>
        <result property="nurseIdCardNo" column="nurse_id_card_no"/>
        <result property="nurseMobile" column="nurse_mobile"/>
        <result property="relationship" column="relationship"/>
        <result property="nurseNatImage" column="nurse_nat_image"/>
        <result property="nursePhoto" column="nurse_photo"/>
        <result property="createTime" column="create_time"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="auditTime" column="audit_time"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="invalidTime" column="invalid_time"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="status" column="status"/>
        <result property="auditRemark" column="audit_remark"/>
    </resultMap>

    <sql id="selectNurseCardVo">
        select id,
               zhuyuan_no,
               department,
               bed,
               ruyuan_time,
               chuyuan_time,
               patient_name,
               patient_id,
               patient_id_card_no,
               patient_mobile,
               nurse_name,
               nurse_id_card_no,
               nurse_mobile,
               relationship,
               nurse_nat_image,
               nurse_photo,
               create_time,
               cancel_time,
               audit_time,
               effective_time,
               invalid_time,
               expired_time,
               status,
               audit_remark
        from ph_nurse_card
    </sql>

    <select id="selectNurseCardList" parameterType="NurseCard" resultMap="NurseCardResult">
        <include refid="selectNurseCardVo"/>
        <where>
            <if test="zhuyuanNo != null  and zhuyuanNo != ''">and zhuyuan_no = #{zhuyuanNo}</if>
            <if test="patientName != null  and patientName != ''">and patient_name like concat('%', #{patientName},
                '%')
            </if>
            <if test="patientId != null  and patientId != ''">and patient_id = #{patientId}</if>
            <if test="patientIdCardNo != null  and patientIdCardNo != ''">and patient_id_card_no = #{patientIdCardNo}
            </if>
            <if test="patientMobile != null  and patientMobile != ''">and patient_mobile = #{patientMobile}</if>
            <if test="nurseName != null  and nurseName != ''">and nurse_name like concat('%', #{nurseName}, '%')</if>
            <if test="nurseIdCardNo != null  and nurseIdCardNo != ''">and nurse_id_card_no = #{nurseIdCardNo}</if>
            <if test="nurseMobile != null  and nurseMobile != ''">and nurse_mobile = #{nurseMobile}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
    </select>

    <select id="selectNurseCardById" parameterType="Long" resultMap="NurseCardResult">
        <include refid="selectNurseCardVo"/>
        where id = #{id}
    </select>

    <insert id="insertNurseCard" parameterType="NurseCard" useGeneratedKeys="true" keyProperty="id">
        insert into ph_nurse_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="zhuyuanNo != null and zhuyuanNo != ''">zhuyuan_no,</if>
            <if test="department != null and department != ''">department,</if>
            <if test="bed != null and bed != ''">bed,</if>
            <if test="ruyuanTime != null">ruyuan_time,</if>
            <if test="chuyuanTime != null">chuyuan_time,</if>
            <if test="patientName != null and patientName != ''">patient_name,</if>
            <if test="patientId != null and patientId != ''">patient_id,</if>
            <if test="patientIdCardNo != null and patientIdCardNo != ''">patient_id_card_no,</if>
            <if test="patientMobile != null and patientMobile != ''">patient_mobile,</if>
            <if test="nurseName != null and nurseName != ''">nurse_name,</if>
            <if test="nurseIdCardNo != null and nurseIdCardNo != ''">nurse_id_card_no,</if>
            <if test="nurseMobile != null and nurseMobile != ''">nurse_mobile,</if>
            <if test="relationship != null">relationship,</if>
            <if test="nurseNatImage != null and nurseNatImage != ''">nurse_nat_image,</if>
            <if test="nursePhoto != null and nursePhoto != ''">nurse_photo,</if>
            <if test="createTime != null">create_time,</if>
            <if test="cancelTime != null">cancel_time,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="effectiveTime != null">effective_time,</if>
            <if test="invalidTime != null">invalid_time,</if>
            <if test="expiredTime != null">expired_time,</if>
            <if test="status != null">status,</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="zhuyuanNo != null and zhuyuanNo != ''">#{zhuyuanNo},</if>
            <if test="department != null and department != ''">#{department},</if>
            <if test="bed != null and bed != ''">#{bed},</if>
            <if test="ruyuanTime != null">#{ruyuanTime},</if>
            <if test="chuyuanTime != null">#{chuyuanTime},</if>
            <if test="patientName != null and patientName != ''">#{patientName},</if>
            <if test="patientId != null and patientId != ''">#{patientId},</if>
            <if test="patientIdCardNo != null and patientIdCardNo != ''">#{patientIdCardNo},</if>
            <if test="patientMobile != null and patientMobile != ''">#{patientMobile},</if>
            <if test="nurseName != null and nurseName != ''">#{nurseName},</if>
            <if test="nurseIdCardNo != null and nurseIdCardNo != ''">#{nurseIdCardNo},</if>
            <if test="nurseMobile != null and nurseMobile != ''">#{nurseMobile},</if>
            <if test="relationship != null">#{relationship},</if>
            <if test="nurseNatImage != null and nurseNatImage != ''">#{nurseNatImage},</if>
            <if test="nursePhoto != null and nursePhoto != ''">#{nursePhoto},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="effectiveTime != null">#{effectiveTime},</if>
            <if test="invalidTime != null">#{invalidTime},</if>
            <if test="expiredTime != null">#{expiredTime},</if>
            <if test="status != null">#{status},</if>
            <if test="auditRemark != null and auditRemark != ''">#{auditRemark},</if>
        </trim>
    </insert>

    <update id="updateNurseCard" parameterType="NurseCard">
        update ph_nurse_card
        <trim prefix="SET" suffixOverrides=",">
            <if test="zhuyuanNo != null and zhuyuanNo != ''">zhuyuan_no = #{zhuyuanNo},</if>
            <if test="department != null and department != ''">department = #{department},</if>
            <if test="bed != null and bed != ''">bed = #{bed},</if>
            <if test="ruyuanTime != null">ruyuan_time = #{ruyuanTime},</if>
            <if test="chuyuanTime != null">chuyuan_time = #{chuyuanTime},</if>
            <if test="patientName != null and patientName != ''">patient_name = #{patientName},</if>
            <if test="patientId != null and patientId != ''">patient_id = #{patientId},</if>
            <if test="patientIdCardNo != null and patientIdCardNo != ''">patient_id_card_no = #{patientIdCardNo},</if>
            <if test="patientMobile != null and patientMobile != ''">patient_mobile = #{patientMobile},</if>
            <if test="nurseName != null and nurseName != ''">nurse_name = #{nurseName},</if>
            <if test="nurseIdCardNo != null and nurseIdCardNo != ''">nurse_id_card_no = #{nurseIdCardNo},</if>
            <if test="nurseMobile != null and nurseMobile != ''">nurse_mobile = #{nurseMobile},</if>
            <if test="relationship != null">relationship = #{relationship},</if>
            <if test="nurseNatImage != null and nurseNatImage != ''">nurse_nat_image = #{nurseNatImage},</if>
            <if test="nursePhoto != null and nursePhoto != ''">nurse_photo = #{nursePhoto},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="effectiveTime != null">effective_time = #{effectiveTime},</if>
            <if test="invalidTime != null">invalid_time = #{invalidTime},</if>
            <if test="expiredTime != null">expired_time = #{expiredTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark = #{auditRemark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNurseCardById" parameterType="Long">
        delete
        from ph_nurse_card
        where id = #{id}
    </delete>

    <delete id="deleteNurseCardByIds" parameterType="String">
        delete from ph_nurse_card where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
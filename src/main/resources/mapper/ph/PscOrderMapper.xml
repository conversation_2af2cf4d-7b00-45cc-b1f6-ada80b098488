<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.PscOrderMapper">

    <resultMap type="PscOrder" id="PscOrderResult">
        <result property="id" column="id"/>
        <result property="admissionNumber" column="admission_number"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientIdCardNo" column="patient_id_card_no"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientSex" column="patient_sex"/>
        <result property="patientNation" column="patient_nation"/>
        <result property="patientBirthday" column="patient_birthday"/>
        <result property="patientMobile" column="patient_mobile"/>
        <result property="patientDepartment" column="patient_department"/>
        <result property="patientBedNumber" column="patient_bed_number"/>
        <result property="serviceType" column="service_type"/>
        <result property="examItems" column="exam_items"/>
        <result property="examItemsCount" column="exam_items_count"/>
        <result property="nurseEmployeeNo" column="nurse_employee_no"/>
        <result property="nurseName" column="nurse_name"/>
        <result property="nurseMobile" column="nurse_mobile"/>
        <result property="carerEmployeeNo" column="carer_employee_no"/>
        <result property="carerName" column="carer_name"/>
        <result property="carerMobile" column="carer_mobile"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="assignTime" column="assign_time"/>
        <result property="takeTime" column="take_time"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="confirmTime" column="confirm_time"/>
        <result property="score" column="score"/>
    </resultMap>

    <sql id="selectPscOrderVo">
        select id,
               admission_number,
               patient_id,
               patient_id_card_no,
               patient_name,
               patient_sex,
               patient_nation,
               patient_birthday,
               patient_mobile,
               patient_department,
               patient_bed_number,
               service_type,
               exam_items,
               exam_items_count,
               nurse_employee_no,
               nurse_name,
               nurse_mobile,
               carer_employee_no,
               carer_name,
               carer_mobile,
               status,
               create_time,
               assign_time,
               take_time,
               start_time,
               end_time,
               confirm_time,
               score
        from psc_order
    </sql>

    <select id="selectPscOrderList" parameterType="PscOrder" resultMap="PscOrderResult">
        <include refid="selectPscOrderVo"/>
        <where>
            <if test="admissionNumber != null  and admissionNumber != ''">and admission_number = #{admissionNumber}</if>
            <if test="patientId != null  and patientId != ''">and patient_id = #{patientId}</if>
            <if test="patientIdCardNo != null  and patientIdCardNo != ''">and patient_id_card_no = #{patientIdCardNo}
            </if>
            <if test="patientDepartment != null and patientDepartment != ''">
                and patient_department = #{patientDepartment}
            </if>
            <if test="patientName != null  and patientName != ''">
                and patient_name like concat('%', #{patientName}, '%')
            </if>
            <if test="nurseEmployeeNo != null  and nurseEmployeeNo != ''">and nurse_employee_no = #{nurseEmployeeNo}
            </if>
            <if test="carerEmployeeNo != null  and carerEmployeeNo != ''">and carer_employee_no = #{carerEmployeeNo}
            </if>
            <if test="status != null ">and status = #{status}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''">
                and create_time >=#{params.beginCreateTime}
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><![CDATA[
                and create_time <=#{params.endCreateTime}
            ]]></if>
            <if test="score != null ">and score = #{score}</if>
        </where>
    </select>

    <select id="selectPscOrderById" parameterType="Long" resultMap="PscOrderResult">
        <include refid="selectPscOrderVo"/>
        where id = #{id}
    </select>

    <select id="selectDepartments" resultType="String">
        select distinct patient_department
        from psc_order
    </select>

    <insert id="insertPscOrder" parameterType="PscOrder" useGeneratedKeys="true" keyProperty="id">
        insert into psc_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="admissionNumber != null and admissionNumber != ''">admission_number,</if>
            <if test="patientId != null and patientId != ''">patient_id,</if>
            <if test="patientIdCardNo != null and patientIdCardNo != ''">patient_id_card_no,</if>
            <if test="patientName != null and patientName != ''">patient_name,</if>
            <if test="patientSex != null and patientSex != ''">patient_sex,</if>
            <if test="patientNation != null and patientNation != ''">patient_nation,</if>
            <if test="patientBirthday != null">patient_birthday,</if>
            <if test="patientMobile != null">patient_mobile,</if>
            <if test="patientDepartment != null">patient_department,</if>
            <if test="patientBedNumber != null">patient_bed_number,</if>
            <if test="serviceType != null and serviceType != ''">service_type,</if>
            <if test="examItems != null and examItems != ''">exam_items,</if>
            <if test="examItemsCount != null">exam_items_count,</if>
            <if test="nurseEmployeeNo != null and nurseEmployeeNo != ''">nurse_employee_no,</if>
            <if test="nurseName != null and nurseName != ''">nurse_name,</if>
            <if test="nurseMobile != null and nurseMobile != ''">nurse_mobile,</if>
            <if test="carerEmployeeNo != null">carer_employee_no,</if>
            <if test="carerName != null">carer_name,</if>
            <if test="carerMobile != null">carer_mobile,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="assignTime != null">assign_time,</if>
            <if test="takeTime != null">take_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="confirmTime != null">confirm_time,</if>
            <if test="score != null">score,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="admissionNumber != null and admissionNumber != ''">#{admissionNumber},</if>
            <if test="patientId != null and patientId != ''">#{patientId},</if>
            <if test="patientIdCardNo != null and patientIdCardNo != ''">#{patientIdCardNo},</if>
            <if test="patientName != null and patientName != ''">#{patientName},</if>
            <if test="patientSex != null and patientSex != ''">#{patientSex},</if>
            <if test="patientNation != null and patientNation != ''">#{patientNation},</if>
            <if test="patientBirthday != null">#{patientBirthday},</if>
            <if test="patientMobile != null">#{patientMobile},</if>
            <if test="patientDepartment != null">#{patientDepartment},</if>
            <if test="patientBedNumber != null">#{patientBedNumber},</if>
            <if test="serviceType != null and serviceType != ''">#{serviceType},</if>
            <if test="examItems != null and examItems != ''">#{examItems},</if>
            <if test="examItemsCount != null">#{examItemsCount},</if>
            <if test="nurseEmployeeNo != null and nurseEmployeeNo != ''">#{nurseEmployeeNo},</if>
            <if test="nurseName != null and nurseName != ''">#{nurseName},</if>
            <if test="nurseMobile != null and nurseMobile != ''">#{nurseMobile},</if>
            <if test="carerEmployeeNo != null">#{carerEmployeeNo},</if>
            <if test="carerName != null">#{carerName},</if>
            <if test="carerMobile != null">#{carerMobile},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="assignTime != null">#{assignTime},</if>
            <if test="takeTime != null">#{takeTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="score != null">#{score},</if>
        </trim>
    </insert>

    <update id="updatePscOrder" parameterType="PscOrder">
        update psc_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="admissionNumber != null and admissionNumber != ''">admission_number = #{admissionNumber},</if>
            <if test="patientId != null and patientId != ''">patient_id = #{patientId},</if>
            <if test="patientIdCardNo != null and patientIdCardNo != ''">patient_id_card_no = #{patientIdCardNo},</if>
            <if test="patientName != null and patientName != ''">patient_name = #{patientName},</if>
            <if test="patientSex != null and patientSex != ''">patient_sex = #{patientSex},</if>
            <if test="patientNation != null and patientNation != ''">patient_nation = #{patientNation},</if>
            <if test="patientBirthday != null">patient_birthday = #{patientBirthday},</if>
            <if test="patientMobile != null">patient_mobile = #{patientMobile},</if>
            <if test="patientDepartment != null">patient_department = #{patientDepartment},</if>
            <if test="patientBedNumber != null">patient_bed_number = #{patientBedNumber},</if>
            <if test="serviceType != null and serviceType != ''">service_type = #{serviceType},</if>
            <if test="examItems != null and examItems != ''">exam_items =
                #{examItems},
            </if>
            <if test="examItemsCount != null">exam_items_count = #{examItemsCount},</if>
            <if test="nurseEmployeeNo != null and nurseEmployeeNo != ''">nurse_employee_no = #{nurseEmployeeNo},</if>
            <if test="nurseName != null and nurseName != ''">nurse_name = #{nurseName},</if>
            <if test="nurseMobile != null">nurse_mobile = #{nurseMobile},</if>
            <if test="carerEmployeeNo != null">carer_employee_no = #{carerEmployeeNo},</if>
            <if test="carerName != null">carer_name = #{carerName},</if>
            <if test="carerMobile != null">carer_mobile = #{carerMobile},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="assignTime != null">assign_time = #{assignTime},</if>
            <if test="takeTime != null">take_time = #{takeTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="score != null">score = #{score},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="assignCarer">
        update psc_order
        set carer_employee_no=#{carer.employeeNo},
        carer_name = #{carer.name},
        carer_mobile = #{carer.mobile},
        status = 1,
        assign_time = current_timestamp()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and status in (0, 1)
    </update>

    <delete id="deletePscOrderById" parameterType="Long">
        delete
        from psc_order
        where id = #{id}
    </delete>

    <delete id="deletePscOrderByIds" parameterType="String">
        delete from psc_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>

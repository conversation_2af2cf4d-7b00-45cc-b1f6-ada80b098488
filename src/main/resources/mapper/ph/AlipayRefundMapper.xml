<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.AlipayRefundMapper">

    <resultMap type="AlipayRefund" id="AlipayRefundResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="type" column="type"/>
        <result property="openid" column="openid"/>
        <result property="patientId" column="patient_id"/>
        <result property="jzCardNo" column="jz_card_no"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="cardNo" column="card_no"/>
        <result property="zhuyuanNo" column="zhuyuan_no"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="amount" column="amount"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="outRefundNo" column="out_refund_no"/>
        <result property="hisTradeStatus" column="his_trade_status"/>
        <result property="hisTradeNo" column="his_trade_no"/>
        <result property="alipayTradeStatus" column="alipay_trade_status"/>
        <result property="alipayTradeTime" column="alipay_trade_time"/>
        <result property="manualRefundState" column="manual_refund_state"/>
    </resultMap>

    <sql id="selectAlipayRefundVo">
        select id,
               create_time,
               type,
               openid,
               patient_id,
               jz_card_no,
               id_card_no,
               card_no,
               zhuyuan_no,
               total_amount,
               amount,
               out_trade_no,
               trade_no,
               out_refund_no,
               his_trade_status,
               his_trade_no,
               alipay_trade_status,
               alipay_trade_time,
               manual_refund_state
        from alipay_refund
    </sql>

    <select id="selectAlipayRefundList" parameterType="AlipayRefund" resultMap="AlipayRefundResult">
        <include refid="selectAlipayRefundVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="openid != null  and openid != ''">and openid = #{openid}</if>
            <if test="patientId != null  and patientId != ''">and patient_id = #{patientId}</if>
            <if test="jzCardNo != null  and jzCardNo != ''">and jz_card_no = #{jzCardNo}</if>
            <if test="idCardNo != null  and idCardNo != ''">and id_card_no = #{jzCardNo}</if>
            <if test="cardNo != null  and cardNo != ''">and card_no = #{cardNo}</if>
            <if test="outTradeNo != null  and outTradeNo != ''">and out_trade_no = #{outTradeNo}</if>
            <if test="tradeNo != null  and tradeNo != ''">and trade_no = #{tradeNo}</if>
            <if test="outRefundNo != null  and outRefundNo != ''">and out_refund_no = #{outRefundNo}</if>
            <if test="hisTradeStatus != null  and hisTradeStatus != ''">and his_trade_status = #{hisTradeStatus}</if>
            <if test="hisTradeNo != null  and hisTradeNo != ''">and his_trade_no = #{hisTradeNo}</if>
            <if test="alipayTradeStatus != null  and alipayTradeStatus != ''">and alipay_trade_status =
                #{alipayTradeStatus}
            </if>
            <if test="alipayTradeTime != null ">and alipay_trade_time = #{alipayTradeTime}</if>
            <if test="manualRefundState != null ">and manual_refund_state = #{manualRefundState}</if>
        </where>
    </select>

    <select id="selectAlipayRefundById" parameterType="Long" resultMap="AlipayRefundResult">
        <include refid="selectAlipayRefundVo"/>
        where id = #{id}
    </select>

    <insert id="insertAlipayRefund" parameterType="AlipayRefund" useGeneratedKeys="true" keyProperty="id">
        insert into alipay_refund
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="type != null">type,</if>
            <if test="openid != null">openid,</if>
            <if test="patientId != null">patient_id,</if>
            <if test="jzCardNo != null">jz_card_no,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="cardNo != null">card_no,</if>
            <if test="zhuyuanNo != null">zhuyuan_no,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="outTradeNo != null">out_trade_no,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="outRefundNo != null">out_refund_no,</if>
            <if test="hisTradeStatus != null">his_trade_status,</if>
            <if test="hisTradeNo != null">his_trade_no,</if>
            <if test="alipayTradeStatus != null">alipay_trade_status,</if>
            <if test="alipayTradeTime != null">alipay_trade_time,</if>
            <if test="manualRefundState != null">manual_refund_state,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="type != null">#{type},</if>
            <if test="openid != null">#{openid},</if>
            <if test="patientId != null">#{patientId},</if>
            <if test="jzCardNo != null">#{jzCardNo},</if>
            <if test="idCardNo != null">#{idCardNo},</if>
            <if test="cardNo != null">#{cardNo},</if>
            <if test="zhuyuanNo != null">#{zhuyuanNo},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="outTradeNo != null">#{outTradeNo},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="outRefundNo != null">#{outRefundNo},</if>
            <if test="hisTradeStatus != null">#{hisTradeStatus},</if>
            <if test="hisTradeNo != null">#{hisTradeNo},</if>
            <if test="alipayTradeStatus != null">#{alipayTradeStatus},</if>
            <if test="alipayTradeTime != null">#{alipayTradeTime},</if>
            <if test="manualRefundState != null">#{manualRefundState},</if>
        </trim>
    </insert>

    <update id="updateAlipayRefund" parameterType="AlipayRefund">
        update alipay_refund
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="patientId != null">patient_id = #{patientId},</if>
            <if test="jzCardNo != null and jzCardNo != ''">jz_card_no = #{jzCardNo},</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no = #{idCardNo},</if>
            <if test="cardNo != null and cardNo != ''">card_no = #{cardNo},</if>
            <if test="zhuyuanNo != null and zhuyuanNo != ''">zhuyuan_no = #{zhuyuanNo},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="outTradeNo != null and outTradeNo != ''">out_trade_no = #{outTradeNo},</if>
            <if test="tradeNo != null and tradeNo != ''">trade_no = #{tradeNo},</if>
            <if test="outRefundNo != null and outRefundNo != ''">out_refund_no = #{outRefundNo},</if>
            <if test="hisTradeStatus != null">his_trade_status = #{hisTradeStatus},</if>
            <if test="hisTradeNo != null and hisTradeNo != ''">his_trade_no = #{hisTradeNo},</if>
            <if test="alipayTradeStatus != null">alipay_trade_status = #{alipayTradeStatus},</if>
            <if test="alipayTradeTime != null">alipay_trade_time = #{alipayTradeTime},</if>
            <if test="manualRefundState != null">manual_refund_state = #{manualRefundState},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAlipayRefundById" parameterType="Long">
        delete
        from alipay_refund
        where id = #{id}
    </delete>

    <delete id="deleteAlipayRefundByIds" parameterType="String">
        delete from alipay_refund where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="sumAmount" parameterType="AlipayRefund" resultType="Long">
        select sum(amount) as sumAmount
        from alipay_refund
        where create_time between #{params.beginCreateTime} and #{params.endCreateTime}
          and (out_refund_no != '')
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.AlipayPaymentMapper">

    <resultMap type="AlipayPayment" id="AlipayPaymentResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="type" column="type"/>
        <result property="openid" column="openid"/>
        <result property="patientId" column="patient_id"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="jzCardNo" column="jz_card_no"/>
        <result property="cardNo" column="card_no"/>
        <result property="zhuyuanNo" column="zhuyuan_no"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="tradeStatus" column="trade_status"/>
        <result property="hisReceiptNo" column="his_receipt_no"/>
        <result property="hisTradeStatus" column="his_trade_status"/>
        <result property="hisSuccessTime" column="his_success_time"/>
        <result property="exrefund" column="exrefund"/>
        <result property="unrefund" column="unrefund"/>
        <result property="manualRechargeState" column="manual_recharge_state"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectAlipayPaymentVo">
        select id,
               create_time,
               type,
               openid,
               patient_id,
               id_card_no,
               jz_card_no,
               card_no,
               zhuyuan_no,
               out_trade_no,
               total_amount,
               trade_no,
               trade_status,
               his_receipt_no,
               his_trade_status,
               his_success_time,
               exrefund,
               unrefund,
               manual_recharge_state,
               remark
        from alipay_payment
    </sql>

    <select id="selectAlipayPaymentList" parameterType="AlipayPayment" resultMap="AlipayPaymentResult">
        <include refid="selectAlipayPaymentVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="type != null ">and type = #{type}</if>
            <if test="patientId != null  and patientId != ''">and patient_id = #{patientId}</if>
            <if test="jzCardNo != null  and jzCardNo != ''">and jz_card_no = #{jzCardNo}</if>
            <if test="cardNo != null  and cardNo != ''">and card_no = #{cardNo}</if>
            <if test="zhuyuanNo != null  and zhuyuanNo != ''">and zhuyuan_no = #{zhuyuanNo}</if>
            <if test="outTradeNo != null  and outTradeNo != ''">and out_trade_no = #{outTradeNo}</if>
            <if test="tradeNo != null  and tradeNo != ''">and trade_no = #{tradeNo}</if>
            <if test="tradeStatus != null  and tradeStatus != ''">and trade_status = #{tradeStatus}</if>
            <if test="hisTradeStatus != null  and hisTradeStatus != ''">and his_trade_status = #{hisTradeStatus}</if>
            <if test="hisReceiptNo != null  and hisReceiptNo != ''">and his_receipt_no = #{hisReceiptNo}</if>
            <if test="manualRechargeState != null ">and manual_recharge_state = #{manualRechargeState}</if>
            <if test="params.filterErrorOrder != null and params.filterErrorOrder != ''">
                and ((trade_status = 'TRADE_SUCCESS' or trade_status = '') and his_trade_status != '充值成功')
            </if>
        </where>
    </select>

    <select id="selectAlipayPaymentById" parameterType="Long" resultMap="AlipayPaymentResult">
        <include refid="selectAlipayPaymentVo"/>
        where id = #{id}
    </select>

    <select id="selectAlipayPaymentByOutTradeNo" parameterType="String" resultMap="AlipayPaymentResult">
        <include refid="selectAlipayPaymentVo"/>
        where out_trade_no = #{outTradeNo} limit 1
    </select>

    <insert id="insertAlipayPayment" parameterType="AlipayPayment" useGeneratedKeys="true" keyProperty="id">
        insert into alipay_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="type != null">type,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="patientId != null">patient_id,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="jzCardNo != null and jzCardNo != ''">jz_card_no,</if>
            <if test="cardNo != null and cardNo != ''">card_no,</if>
            <if test="zhuyuanNo != null">zhuyuan_no,</if>
            <if test="outTradeNo != null and outTradeNo != ''">out_trade_no,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="tradeStatus != null">trade_status,</if>
            <if test="hisReceiptNo != null">his_receipt_no,</if>
            <if test="hisTradeStatus != null">his_trade_status,</if>
            <if test="hisSuccessTime != null">his_success_time,</if>
            <if test="exrefund != null">exrefund,</if>
            <if test="unrefund != null">unrefund,</if>
            <if test="manualRechargeState != null">manual_recharge_state,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="type != null">#{type},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="patientId != null">#{patientId},</if>
            <if test="idCardNo != null">#{idCardNo},</if>
            <if test="jzCardNo != null and jzCardNo != ''">#{jzCardNo},</if>
            <if test="cardNo != null and cardNo != ''">#{cardNo},</if>
            <if test="zhuyuanNo != null">#{zhuyuanNo},</if>
            <if test="outTradeNo != null and outTradeNo != ''">#{outTradeNo},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="tradeStatus != null">#{tradeStatus},</if>
            <if test="hisReceiptNo != null">#{hisReceiptNo},</if>
            <if test="hisTradeStatus != null">#{hisTradeStatus},</if>
            <if test="hisSuccessTime != null">#{hisSuccessTime},</if>
            <if test="exrefund != null">#{exrefund},</if>
            <if test="unrefund != null">#{unrefund},</if>
            <if test="manualRechargeState != null">#{manualRechargeState},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateAlipayPayment" parameterType="AlipayPayment">
        update alipay_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="patientId != null">patient_id = #{patientId},</if>
            <if test="idCardNo != null">id_card_no = #{idCardNo},</if>
            <if test="jzCardNo != null and jzCardNo != ''">jz_card_no = #{jzCardNo},</if>
            <if test="cardNo != null and cardNo != ''">card_no = #{cardNo},</if>
            <if test="zhuyuanNo != null">zhuyuan_no = #{zhuyuanNo},</if>
            <if test="outTradeNo != null and outTradeNo != ''">out_trade_no = #{outTradeNo},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="tradeStatus != null">trade_status = #{tradeStatus},</if>
            <if test="hisReceiptNo != null">his_receipt_no = #{hisReceiptNo},</if>
            <if test="hisTradeStatus != null">his_trade_status = #{hisTradeStatus},</if>
            <if test="hisSuccessTime != null">his_success_time = #{hisSuccessTime},</if>
            <if test="exrefund != null">exrefund = #{exrefund},</if>
            <if test="unrefund != null">unrefund = #{unrefund},</if>
            <if test="manualRechargeState != null">manual_recharge_state = #{manualRechargeState},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAlipayPaymentById" parameterType="Long">
        delete
        from alipay_payment
        where id = #{id}
    </delete>

    <delete id="deleteAlipayPaymentByIds" parameterType="String">
        delete from alipay_payment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="sumAmount" parameterType="AlipayPayment" resultType="Long">
        select sum(total_amount) as sumAmount
        from alipay_payment
        where create_time between #{params.beginCreateTime} and #{params.endCreateTime}
          and (trade_no is not null and trade_no != '')
    </select>
</mapper>

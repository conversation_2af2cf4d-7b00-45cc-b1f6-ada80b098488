<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.DepartmentMapper">

    <resultMap type="space.lzhq.ph.domain.Department" id="DepartmentResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="parentId" column="parent_id"/>
        <result property="parentName" column="parent_name"/>
        <result property="intro" column="intro"/>
        <result property="branch" column="branch"/>
        <result property="logo" column="logo"/>
        <result property="telephone" column="telephone"/>
        <result property="keyword" column="keyword"/>
        <result property="address" column="address"/>
        <result property="sortNo" column="sort_no"/>
        <result property="simplePinyin" column="simple_pinyin"/>
        <result property="fullPinyin" column="full_pinyin"/>
    </resultMap>

    <sql id="selectDepartmentVo">
        select id,
        name,
        type,
        parent_id,
        parent_name,
        intro,
        branch,
        logo,
        telephone,
        keyword,
        address,
        sort_no,
        simple_pinyin,
        full_pinyin
        from ph_department
    </sql>

    <select id="selectDepartmentAll" resultMap="DepartmentResult">
        <include refid="selectDepartmentVo"/>
    </select>

    <select id="selectDepartmentList" parameterType="Department" resultMap="DepartmentResult">
        <include refid="selectDepartmentVo"/>
        <where>
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="branch != null and branch != ''">and branch = #{branch}</if>
            <if test="simplePinyin != null and simplePinyin != ''">and simple_pinyin like concat('%',
                lower(#{simplePinyin}),
                '%')
            </if>
            <if test="fullPinyin != null and fullPinyin != ''">and full_pinyin like concat('%', lower(#{fullPinyin}),
                '%')
            </if>
        </where>
    </select>

    <select id="selectDepartmentListByKeyword" parameterType="String" resultMap="DepartmentResult">
        <include refid="selectDepartmentVo"/>
        <where>
            <if test="keyword != null and keyword != ''">and (
                name like #{keyword}
                or simple_pinyin like #{keyword}
                or full_pinyin like #{keyword}
                )
            </if>
        </where>
    </select>

    <select id="selectDepartmentById" parameterType="String" resultMap="DepartmentResult">
        <include refid="selectDepartmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertDepartment" parameterType="Department">
        insert into ph_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="branch != null and branch != ''">branch,</if>
            <if test="parentId != null and parentId != ''">parent_id,</if>
            <if test="parentName != null and parentName != ''">parent_name,</if>
            <if test="intro != null and intro != ''">intro,</if>
            <if test="logo != null and logo != ''">logo,</if>
            <if test="telephone != null and telephone != ''">telephone,</if>
            <if test="keyword != null and keyword != ''">keyword,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="sortNo != null ">sort_no,</if>
            <if test="simplePinyin != null and simplePinyin != ''">simple_pinyin,</if>
            <if test="fullPinyin != null and fullPinyin != ''">full_pinyin,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="branch != null and branch != ''">#{branch},</if>
            <if test="parentId != null and parentId != ''">#{parentId},</if>
            <if test="parentName != null and parentName != ''">#{parentName},</if>
            <if test="intro != null and intro != ''">#{intro},</if>
            <if test="logo != null and logo != ''">#{logo},</if>
            <if test="telephone != null and telephone != ''">#{telephone},</if>
            <if test="keyword != null and keyword != ''">#{keyword},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="sortNo != null ">#{sortNo},</if>
            <if test="simplePinyin != null and simplePinyin != ''">#{simplePinyin},</if>
            <if test="fullPinyin != null and fullPinyin != ''">#{fullPinyin},</if>
        </trim>
    </insert>
    <insert id="insertDepartments" parameterType="java.util.List">
        insert into ph_department (id, name, type, parent_id, parent_name, intro, branch, logo, telephone, keyword,
        address,
        sort_no, simple_pinyin, full_pinyin)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.name},#{item.type},#{item.parentId},#{item.parentName},#{item.intro},#{item.branch},#{item.logo},#{item.telephone},#{item.keyword},#{item.address},#{item.sortNo},#{item.simplePinyin},#{item.fullPinyin})
        </foreach>
    </insert>

    <update id="updateDepartment" parameterType="Department">
        update ph_department
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="parentId != null and parentId != ''">parent_id = #{parentId},</if>
            <if test="parentName != null and parentName != ''">parent_name = #{parentName},</if>
            <if test="intro != null">intro = #{intro},</if>
            <if test="branch != null">branch = #{branch},</if>
            <if test="logo != null and logo != ''">logo = #{logo},</if>
            <if test="telephone != null and telephone != ''">telephone = #{telephone},</if>
            <if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
            <if test="address != null">address = #{address},</if>
            <if test="sortNo != null ">sort_no = #{sortNo},</if>
            <if test="simplePinyin != null and simplePinyin != ''">simple_pinyin = #{simplePinyin},</if>
            <if test="fullPinyin != null and fullPinyin != ''">full_pinyin = #{fullPinyin},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateDepartments" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update ph_department
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.name != null">name = #{item.name},</if>
                <if test="item.type != null">type = #{type},</if>
                <if test="item.parentId != null and item.parentId != ''">parent_id = #{item.parentId},</if>
                <if test="item.parentName != null and item.parentName != ''">parent_name = #{item.parentName},</if>
                <if test="item.intro != null">intro = #{item.intro},</if>
                <if test="item.branch != null">branch = #{item.branch},</if>
                <if test="item.logo != null and item.logo != ''">logo = #{item.logo},</if>
                <if test="item.telephone != null and item.telephone != ''">telephone = #{item.telephone},</if>
                <if test="item.keyword != null and item.keyword != ''">keyword = #{item.keyword},</if>
                <if test="item.address != null">address = #{item.address},</if>
                <if test="item.sortNo != null">sort_no = #{item.sortNo},</if>
                <if test="item.simplePinyin != null">simple_pinyin = #{item.simplePinyin},</if>
                <if test="item.fullPinyin != null">full_pinyin = #{item.fullPinyin},</if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <update id="truncateTable">
        truncate table ph_department
    </update>

    <delete id="deleteDepartmentById" parameterType="String">
        delete
        from ph_department
        where id = #{id}
    </delete>

    <delete id="deleteDepartmentByIds" parameterType="String">
        delete from ph_department where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByType" parameterType="String">
        delete
        from ph_department
        where type = #{type}
    </delete>
</mapper>

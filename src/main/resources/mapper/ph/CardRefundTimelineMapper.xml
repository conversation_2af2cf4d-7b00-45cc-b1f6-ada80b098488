<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.CardRefundTimelineMapper">

    <resultMap type="space.lzhq.ph.domain.CardRefundTimeline" id="TimelineResult">
        <result property="id" column="id"/>
        <result property="registrationId" column="registration_id"/>
        <result property="eventType" column="event_type"/>
        <result property="eventDescription" column="event_description"/>
        <result property="operatorType" column="operator_type"/>
        <result property="operatorName" column="operator_name"/>
        <result property="oldStatus" column="old_status"/>
        <result property="newStatus" column="new_status"/>
        <result property="eventTime" column="event_time"/>
        <result property="additionalData" column="additional_data"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectTimelineVo">
        SELECT id, registration_id, event_type, event_description, operator_type, operator_name,
        old_status, new_status, event_time, additional_data, create_time
        FROM card_refund_timeline
    </sql>

    <select id="selectTimelineByRegistrationId" parameterType="Map" resultMap="TimelineResult">
        <include refid="selectTimelineVo"/>
        WHERE registration_id = #{registrationId}
        <if test="includeDetails != null and includeDetails == false">
            AND event_type IN ('REGISTRATION_CREATED', 'REGISTRATION_SUBMITTED', 'REGISTRATION_APPROVED',
            'REGISTRATION_REJECTED', 'REGISTRATION_RESUBMITTED')
        </if>
        ORDER BY event_time DESC
    </select>

    <select id="selectLatestByRegistrationIdAndEventType" parameterType="Map" resultMap="TimelineResult">
        <include refid="selectTimelineVo"/>
        WHERE registration_id = #{registrationId}
        AND event_type = #{eventType}
        ORDER BY event_time DESC
        LIMIT 1
    </select>

    <insert id="insertBatch" parameterType="List">
        INSERT INTO card_refund_timeline
        (id, registration_id, event_type, event_description, operator_type, operator_name,
        old_status, new_status, event_time, additional_data, create_time)
        VALUES
        <foreach collection="timelines" item="timeline" separator=",">
            (#{timeline.id}, #{timeline.registrationId}, #{timeline.eventType}, #{timeline.eventDescription},
            #{timeline.operatorType}, #{timeline.operatorName}, #{timeline.oldStatus}, #{timeline.newStatus},
            #{timeline.eventTime}, #{timeline.additionalData}, #{timeline.createTime})
        </foreach>
    </insert>

</mapper> 
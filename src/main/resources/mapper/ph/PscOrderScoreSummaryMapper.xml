<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.PscOrderScoreSummaryMapper">
    <select id="summary" resultType="space.lzhq.ph.domain.PscOrderScoreSummary">
        select carer_employee_no        as carerEmployee<PERSON><PERSON>,
               carer_name               as carer<PERSON><PERSON>,
               carer_mobile             as carerMobile,
               sum(if(score = 1, 1, 0)) as score1,
               sum(if(score = 2, 1, 0)) as score2,
               sum(if(score = 3, 1, 0)) as score3,
               sum(if(score = 4, 1, 0)) as score4,
               sum(if(score = 5, 1, 0)) as score5,
               count(*)                 as total
        from psc_order
        where create_time between #{minCreateTime} and #{maxCreateTime}
          and status = 99
        group by carer_employee_no
    </select>
</mapper>

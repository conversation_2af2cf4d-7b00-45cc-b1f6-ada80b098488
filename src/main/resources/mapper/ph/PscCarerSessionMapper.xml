<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.PscCarerSessionMapper">

    <resultMap type="PscCarerSession" id="PscCarerSessionResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="employeeNo" column="employee_no"/>
        <result property="mobile" column="mobile"/>
        <result property="clientType" column="client_type"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
    </resultMap>

    <sql id="selectPscCarerSessionVo">
        select id, name, employee_no, mobile, client_type, open_id, union_id
        from psc_carer_session
    </sql>

    <select id="selectPscCarerSessionList" parameterType="PscCarerSession" resultMap="PscCarerSessionResult">
        <include refid="selectPscCarerSessionVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="employeeNo != null  and employeeNo != ''">and employee_no = #{employeeNo}</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="clientType != null ">and client_type = #{clientType}</if>
            <if test="openId != null  and openId != ''">and open_id = #{openId}</if>
            <if test="unionId != null  and unionId != ''">and union_id = #{unionId}</if>
        </where>
    </select>

    <select id="selectPscCarerSessionById" parameterType="Long" resultMap="PscCarerSessionResult">
        <include refid="selectPscCarerSessionVo"/>
        where id = #{id}
    </select>

    <select id="selectPscCarerSessionByEmployeeNoAndClientType" resultMap="PscCarerSessionResult">
        <include refid="selectPscCarerSessionVo"/>
        where employee_no = #{employeeNo}
        and client_type = #{clientType}
        limit 1
    </select>

    <select id="selectPscCarerSessionByOpenId" resultMap="PscCarerSessionResult">
        <include refid="selectPscCarerSessionVo"/>
        where open_id = #{openId}
        limit 1
    </select>

    <select id="existsByEmployeeNoAndClientType" resultType="Boolean">
        select count(*) > 0
        from psc_carer_session
        where employee_no = #{employeeNo}
          and client_type = #{clientType}
    </select>

    <insert id="insertPscCarerSession" parameterType="PscCarerSession" useGeneratedKeys="true" keyProperty="id">
        insert into psc_carer_session
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="employeeNo != null and employeeNo != ''">employee_no,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="clientType != null">client_type,</if>
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="employeeNo != null and employeeNo != ''">#{employeeNo},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="clientType != null">#{clientType},</if>
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
        </trim>
    </insert>

    <update id="updatePscCarerSession" parameterType="PscCarerSession">
        update psc_carer_session
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="employeeNo != null and employeeNo != ''">employee_no = #{employeeNo},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="clientType != null">client_type = #{clientType},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePscCarerSessionById" parameterType="Long">
        delete
        from psc_carer_session
        where id = #{id}
    </delete>

    <delete id="deletePscCarerSessionByIds" parameterType="String">
        delete from psc_carer_session where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletePscCarerSessionByOpenId">
        delete
        from psc_carer_session
        where open_id = #{openId}
    </delete>

</mapper>
<!--
 * bootstrap-star-rating v4.1.3
 * http://plugins.krajee.com/star-rating
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2013 - 2021, <PERSON><PERSON><PERSON>, Krajee.com
 *
 * Licensed under the BSD 3-Clause
 * https://github.com/kartik-v/bootstrap-star-rating/blob/master/LICENSE.md
-->
<!DOCTYPE html>
<!--suppress CssUnusedSymbol, JSUnresolvedLibraryURL -->
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>K<PERSON><PERSON> JQuery Plugins - &copy; Kartik</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/star-rating.css" media="all" type="text/css"/>
    <link rel="stylesheet" href="../themes/krajee-fa/theme.css" media="all" type="text/css"/>
    <link rel="stylesheet" href="../themes/krajee-svg/theme.css" media="all" type="text/css"/>
    <link rel="stylesheet" href="../themes/krajee-uni/theme.css" media="all" type="text/css"/>
    <script src="http://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
    <script src="../js/star-rating.js" type="text/javascript"></script>
    <script src="../themes/krajee-fa/theme.js" type="text/javascript"></script>
    <script src="../themes/krajee-svg/theme.js" type="text/javascript"></script>
    <script src="../themes/krajee-gly/theme.js" type="text/javascript"></script>
    <script src="../themes/krajee-uni/theme.js" type="text/javascript"></script>
<body>
<div class="container">
    <div class="page-header">
        <h2>Bootstrap Star Rating Examples
            <small>&copy; Kartik Visweswaran, Krajee.com</small>
        </h2>
    </div>
    <form>
        <div class="page-header">
            <h3>Glyphicon Star LTR</h3>
        </div>
        <input type="text" class="rating rating-loading" value="3.75" data-size="xl" data-theme="krajee-gly" title="">
        <br>
        <input type="text" class="rating rating-loading" value="2.5" data-size="lg" data-theme="krajee-gly" title="">
        <br>
        <input type="text" class="rating rating-loading" value="1.75" data-size="md" data-theme="krajee-gly" title="">
        <br>
        <input type="text" class="rating rating-loading" value="4" data-size="sm" data-theme="krajee-gly" title="">
        <br>
        <input type="text" class="rating rating-loading" value="2" data-size="xs" data-theme="krajee-gly" title="">
        <br>

        <div class="page-header">
            <h3>Glyphicon Star RTL</h3>
        </div>
        <input type="text" class="kv-gly-star rating-loading" value="3.75" dir="rtl" data-size="xl" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-gly-star rating-loading" value="2.5" dir="rtl" data-size="lg" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-gly-star rating-loading" value="1.75" dir="rtl" data-size="md" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-gly-star rating-loading" value="4" dir="rtl" data-size="sm" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-gly-star rating-loading" value="2" dir="rtl" data-size="xs" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>

        <div class="page-header">
            <h3>Glyphicon Heart LTR</h3>
        </div>
        <input type="text" class="kv-gly-heart rating-loading" value="3.75" data-size="xl" data-theme="krajee-gly" title="">
        <br>
        <input type="text" class="kv-gly-heart rating-loading" value="2.5" data-size="lg" data-theme="krajee-gly" title="">
        <br>
        <input type="text" class="kv-gly-heart rating-loading" value="1.75" data-size="md" data-theme="krajee-gly" title="">
        <br>
        <input type="text" class="kv-gly-heart rating-loading" value="4" data-size="sm" data-theme="krajee-gly" title="">
        <br>
        <input type="text" class="kv-gly-heart rating-loading" value="2" data-size="xs" data-theme="krajee-gly" title="">
        <br>

        <div class="page-header">
            <h3>Glyphicon Heart RTL</h3>
        </div>
        <input type="text" class="kv-gly-heart rating-loading" value="3.75" dir="rtl" data-size="xl" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-gly-heart rating-loading" value="2.5" dir="rtl" data-size="lg" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-gly-heart rating-loading" value="1.75" dir="rtl" data-size="md" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-gly-heart rating-loading" value="4" dir="rtl" data-size="sm" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-gly-heart rating-loading" value="2" dir="rtl" data-size="xs" data-theme="krajee-gly" title="">
        <div class="clearfix"></div>
        <br>

        <div class="page-header">
            <h3>Unicode Star LTR</h3>
        </div>
        <input type="text" class="kv-uni-star rating-loading" value="3.75" data-size="xl" title="">
        <br>
        <input type="text" class="kv-uni-star rating-loading" value="2.5" data-size="lg" title="">
        <br>
        <input type="text" class="kv-uni-star rating-loading" value="1.75" data-size="md" title="">
        <br>
        <input type="text" class="kv-uni-star rating-loading" value="4" data-size="sm" title="">
        <br>
        <input type="text" class="kv-uni-star rating-loading" value="2" data-size="xs" title="">
        <br>

        <div class="page-header">
            <h3>Unicode Star RTL</h3>
        </div>
        <input type="text" class="kv-uni-star rating-loading" value="3.75" dir="rtl" data-size="xl" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-uni-star rating-loading" value="2.5" dir="rtl" data-size="lg" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-uni-star rating-loading" value="1.75" dir="rtl" data-size="md" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-uni-star rating-loading" value="4" dir="rtl" data-size="sm" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-uni-star rating-loading" value="2" dir="rtl" data-size="xs" title="">
        <div class="clearfix"></div>
        <br>

        <div class="page-header">
            <h3>Unicode Rook LTR</h3>
        </div>
        <input type="text" class="kv-uni-rook rating-loading" value="3.75" data-size="xl" title="">
        <br>
        <input type="text" class="kv-uni-rook rating-loading" value="2.5" data-size="lg" title="">
        <br>
        <input type="text" class="kv-uni-rook rating-loading" value="1.75" data-size="md" title="">
        <br>
        <input type="text" class="kv-uni-rook rating-loading" value="4" data-size="sm" title="">
        <br>
        <input type="text" class="kv-uni-rook rating-loading" value="2" data-size="xs" title="">
        <br>

        <div class="page-header">
            <h3>Unicode Rook RTL</h3>
        </div>
        <input type="text" class="kv-uni-rook rating-loading" value="3.75" dir="rtl" data-size="xl" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-uni-rook rating-loading" value="2.5" dir="rtl" data-size="lg" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-uni-rook rating-loading" value="1.75" dir="rtl" data-size="md" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-uni-rook rating-loading" value="4" dir="rtl" data-size="sm" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-uni-rook rating-loading" value="2" dir="rtl" data-size="xs" title="">
        <div class="clearfix"></div>
        <br>

        <div class="page-header">
            <h3>Font Awesome Star LTR</h3>
        </div>
        <input type="text" class="kv-fa rating-loading" value="3.75" data-size="xl" title="">
        <br>
        <input type="text" class="kv-fa rating-loading" value="2.5" data-size="lg" title="">
        <br>
        <input type="text" class="kv-fa rating-loading" value="1.75" data-size="md" title="">
        <br>
        <input type="text" class="kv-fa rating-loading" value="4" data-size="sm" title="">
        <br>
        <input type="text" class="kv-fa rating-loading" value="2" data-size="xs" title="">
        <br>

        <div class="page-header">
            <h3>Font Awesome Star RTL</h3>
        </div>
        <input type="text" class="kv-fa rating-loading" value="3.75" dir="rtl" data-size="xl" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-fa rating-loading" value="2.5" dir="rtl" data-size="lg" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-fa rating-loading" value="1.75" dir="rtl" data-size="md" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-fa rating-loading" value="4" dir="rtl" data-size="sm" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-fa rating-loading" value="2" dir="rtl" data-size="xs" title="">
        <div class="clearfix"></div>
        <br>

        <div class="page-header">
            <h3>Font Awesome Heart LTR</h3>
        </div>
        <input type="text" class="kv-fa-heart rating-loading" value="3.75" data-size="xl"
               title="">
        <br>
        <input type="text" class="kv-fa-heart rating-loading" value="2.5" data-size="lg" title="">
        <br>
        <input type="text" class="kv-fa-heart rating-loading" value="1.75" data-size="md" title="">
        <br>
        <input type="text" class="kv-fa-heart rating-loading" value="4" data-size="sm" title="">
        <br>
        <input type="text" class="kv-fa-heart rating-loading" value="2" data-size="xs" title="">
        <br>

        <div class="page-header">
            <h3>Font Awesome Heart RTL</h3>
        </div>
        <input type="text" class="kv-fa-heart rating-loading" value="3.75" dir="rtl" data-size="xl" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-fa-heart rating-loading" value="2.5" dir="rtl" data-size="lg" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-fa-heart rating-loading" value="1.75" dir="rtl" data-size="md" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-fa-heart rating-loading" value="4" dir="rtl" data-size="sm" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-fa-heart rating-loading" value="2" dir="rtl" data-size="xs" title="">
        <div class="clearfix"></div>
        <br>

        <div class="page-header">
            <h3>SVG LTR Star</h3>
        </div>
        <input type="text" class="kv-svg rating-loading" value="3.75" data-size="xl" title="">
        <br>
        <input type="text" class="kv-svg rating-loading" value="2.5" data-size="lg" title="">
        <br>
        <input type="text" class="kv-svg rating-loading" value="1.75" data-size="md" title="">
        <br>
        <input type="text" class="kv-svg rating-loading" value="4" data-size="sm" title="">
        <br>
        <input type="text" class="kv-svg rating-loading" value="2" data-size="xs" title="">
        <br>

        <div class="page-header">
            <h3>SVG RTL Star</h3>
        </div>
        <input type="text" class="kv-svg rating-loading" value="3.75" dir="rtl" data-size="xl" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-svg rating-loading" value="2.5" dir="rtl" data-size="lg" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-svg rating-loading" value="1.75" dir="rtl" data-size="md" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-svg rating-loading" value="4" dir="rtl" data-size="sm" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-svg rating-loading" value="2" dir="rtl" data-size="xs" title="">
        <div class="clearfix"></div>
        <br>

        <div class="page-header">
            <h3>SVG LTR Heart</h3>
        </div>
        <input type="text" class="kv-svg-heart rating-loading" value="3.75" data-size="xl" title="">
        <br>
        <input type="text" class="kv-svg-heart rating-loading" value="2.5" data-size="lg" title="">
        <br>
        <input type="text" class="kv-svg-heart rating-loading" value="1.75" data-size="md" title="">
        <br>
        <input type="text" class="kv-svg-heart rating-loading" value="4" data-size="sm" title="">
        <br>
        <input type="text" class="kv-svg-heart rating-loading" value="2" data-size="xs" title="">
        <br>

        <div class="page-header">
            <h3>SVG RTL Heart</h3>
        </div>
        <input type="text" class="kv-svg-heart rating-loading" value="3.75" dir="rtl" data-size="xl" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-svg-heart rating-loading" value="2.5" dir="rtl" data-size="lg" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-svg-heart rating-loading" value="1.75" dir="rtl" data-size="md" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-svg-heart rating-loading" value="4" dir="rtl" data-size="sm" title="">
        <div class="clearfix"></div>
        <br>
        <input type="text" class="kv-svg-heart rating-loading" value="2" dir="rtl" data-size="xs" title="">
        <div class="clearfix"></div>
        <br>
    </form>
</div>
</body>
<script>
    $(document).on('ready', function () {
        $('.kv-gly-star').rating({
            containerClass: 'is-star'
        });
        $('.kv-gly-heart').rating({
            containerClass: 'is-heart',
            defaultCaption: '{rating} hearts',
            starCaptions: function (rating) {
                return rating == 1 ? 'One heart' : rating + ' hearts';
            },
            filledStar: '<i class="glyphicon glyphicon-heart"></i>',
            emptyStar: '<i class="glyphicon glyphicon-heart-empty"></i>'
        });
        $('.kv-fa').rating({
            theme: 'krajee-fa',
            filledStar: '<i class="fa fa-star"></i>',
            emptyStar: '<i class="fa fa-star-o"></i>'
        });
        $('.kv-fa-heart').rating({
            defaultCaption: '{rating} hearts',
            starCaptions: function (rating) {
                return rating == 1 ? 'One heart' : rating + ' hearts';
            },
            theme: 'krajee-fa',
            filledStar: '<i class="fa fa-heart"></i>',
            emptyStar: '<i class="fa fa-heart-o"></i>'
        });
        $('.kv-uni-star').rating({
            theme: 'krajee-uni',
            filledStar: '&#x2605;',
            emptyStar: '&#x2606;'
        });
        $('.kv-uni-rook').rating({
            theme: 'krajee-uni',
            defaultCaption: '{rating} rooks',
            starCaptions: function (rating) {
                return rating == 1 ? 'One rook' : rating + ' rooks';
            },
            filledStar: '&#9820;',
            emptyStar: '&#9814;'
        });
        $('.kv-svg').rating({
            theme: 'krajee-svg',
            filledStar: '<span class="krajee-icon krajee-icon-star"></span>',
            emptyStar: '<span class="krajee-icon krajee-icon-star"></span>'
        });
        $('.kv-svg-heart').rating({
            theme: 'krajee-svg',
            filledStar: '<span class="krajee-icon krajee-icon-heart"></span>',
            emptyStar: '<span class="krajee-icon krajee-icon-heart"></span>',
            defaultCaption: '{rating} hearts',
            starCaptions: function (rating) {
                return rating == 1 ? 'One heart' : rating + ' hearts';
            },
            containerClass: 'is-heart'
        });

        $('.rating,.kv-gly-star,.kv-gly-heart,.kv-uni-star,.kv-uni-rook,.kv-fa,.kv-fa-heart,.kv-svg,.kv-svg-heart').on(
                'change', function () {
                    console.log('Rating selected: ' + $(this).val());
                });
    });
</script>
</html>



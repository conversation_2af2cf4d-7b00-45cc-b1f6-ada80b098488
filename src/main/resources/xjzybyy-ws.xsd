<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="https://xjzybyy.xjyqtl.cn/ws/hospitalized"
           targetNamespace="https://xjzybyy.xjyqtl.cn/ws/hospitalized" elementFormDefault="qualified">

    <xs:element name="PushHospitalizedDayExpenseRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="data_type" type="tns:data_type"/>
                <xs:element name="data" type="tns:day_expense_data"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="PushHospitalizedDayExpenseResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="tns:result"/>
                <xs:element name="message" type="tns:message"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="PushHospitalizedArrearsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="data_type" type="tns:data_type"/>
                <xs:element name="data" type="tns:arrears_data"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="PushHospitalizedArrearsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="tns:result"/>
                <xs:element name="message" type="tns:message"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:complexType name="arrears_data">
        <xs:sequence>
            <xs:element name="hospital_id" type="tns:hospital_id"/>
            <xs:element name="hospital_area_id" type="xs:string"/>
            <xs:element name="patient_card" type="tns:patient_card"/>
            <xs:element name="id_card" type="tns:id_card"/>
            <xs:element name="real_name" type="tns:real_name"/>
            <xs:element name="serial_number" type="tns:serial_number"/>
            <xs:element name="total_amount" type="tns:total_amount"/>
            <xs:element name="used_amount" type="tns:used_amount"/>
            <xs:element name="remain_amount" type="tns:remain_amount"/>
            <xs:element name="arrear_amount" type="tns:arrear_amount"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="day_expense_data">
        <xs:sequence>
            <xs:element name="hospital_id" type="tns:hospital_id"/>
            <xs:element name="hospital_area_id" type="xs:string"/>
            <xs:element name="patient_card" type="tns:patient_card"/>
            <xs:element name="id_card" type="tns:id_card"/>
            <xs:element name="real_name" type="tns:real_name"/>
            <xs:element name="serial_number" type="tns:serial_number"/>
            <xs:element name="fee_type" type="tns:fee_type"/>
            <xs:element name="fee_date" type="tns:fee_date"/>
            <xs:element name="total_amount" type="tns:total_amount"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="result">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="message">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="data_type">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="hospital_id">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="hospital_area_id">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="patient_card">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="id_card">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="real_name">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="serial_number">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="fee_type">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="fee_date">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="total_amount">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="used_amount">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="remain_amount">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="arrear_amount">
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
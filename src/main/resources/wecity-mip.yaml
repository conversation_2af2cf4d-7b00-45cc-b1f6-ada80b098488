maMip:
  # 环境标识，test=测试环境，prod=正式环境
  env: prod
  # 合作方ID，由腾讯工程师分配
  partnerId: 50006837
  # 合作方密钥，由腾讯工程师分配
  # 测试环境
  # partnerKey: 430d3e1945207095ab8a9186edcc2c9a
  # 正式环境
  partnerKey: "4d283424b8f492dcc842c8651bc7a54a"
  # 渠道号，由腾讯工程师分配
  channelNo: AAG0KpOooRohKo5YZL2S9Bwo
  # 城市编码，参见《对接移动医疗平台接口文档_国家局》（https://docs.qq.com/doc/DV3lxV3hSbXFudVBE）
  cityId: 650100
  # 定点医药机构编码，参见《测试环境反馈单》
  orgCode: H65010300035
  # 医保支付密钥
  mipKey: "4588c286a1a0a4d74a97b501d35a26cd"
  # 定点医药机构小程序/H5应用ID
  # 测试环境
  # orgAppId: 1I4R327SV1JG4460C80A0000DFC97D7B
  # 正式环境
  orgAppId: "1IQNGAH9H08I8C430B0A0000DF12A4A6"
  # 机构渠道编码，参见国家医保局医保移动支付反馈单
  orgChannelCode: "BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxvhx5UDJoOagngtwI/IKRwp"
  # 公众号或小程序的appId
  appId: wx61dc15cd278adfcb

mpMip:
  # 环境标识，test=测试环境，prod=正式环境
  env: prod
  # 合作方ID，由腾讯工程师分配
  partnerId: 50001684
  # 合作方密钥，由腾讯工程师分配
  partnerKey: "7c12863bdbcf492e116056e887452bfb"
  # 渠道号，由腾讯工程师分配
  channelNo: AAGtHoCjOYgP0yqX0U4KE8Ql
  # 城市编码，参见《对接移动医疗平台接口文档_国家局》（https://docs.qq.com/doc/DV3lxV3hSbXFudVBE）
  cityId: 650100
  # 定点医药机构编码，参见《测试环境反馈单》
  orgCode: H65010300035
  # 医保支付密钥
  mipKey: "5a3189a81e060c717e6b04b244641246"
  # 定点医药机构小程序/H5应用ID
  orgAppId: "1GHJ6V6P50BIE1470B0A0000C4ABAE0D"
  # 机构渠道编码，参见国家医保局医保移动支付反馈单
  orgChannelCode: "BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxvhx5UDJoOagngtwI/IKRwp"
  # 公众号或小程序的appId
  appId: wxebe51b3e144b2173
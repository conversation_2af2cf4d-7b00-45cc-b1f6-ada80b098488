package space.lzhq.ph.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 验证CustomErrorController是否能正确捕获400错误
 */
@SpringBootTest
@AutoConfigureWebMvc
public class CustomErrorControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Test
    public void testCustomErrorControllerCatches400Error() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 模拟一个会产生400错误的请求
        String longToken = "RaGpmsZ-LBNb8lOHiyF1DjTP4_7qmvijTwCuIVHk2fBHsLQryBmsNXaVyc5EMclnQVcOvrtqWXijqaygnunj7aE1RKJ0RqQ3Gd2ejwTj5Wkia2ZuXrpkAAWAEiQt-Tn8M1qbdm0Os13Jw7Fsa7P-9syWmaB3QTW5WliwoGAGXRegD0ZUmxgn8mKPC1RAaVHbiyOFkdY-U9wkzmYQVOAHttcUr3JSQo_DsECUBEJwnAwmpbVESOdYhMHnJyS8kEmnLkF3rQykIQz0MmMiym-tCLkVEjCK8e_VBzK4FQ4KrFY2PUC9ivlPQnHq1YOY9gF5NtxbR9iZ3YUVSBYBG5mUdWalVnGYIio75ZAsI3_JcpP9akd0pfdwJ9QS01jYHtfybbfgO4UHxTcEHcShaJxdBQUM2ry6sVfV4rxcF690707N3CJ84JpgfRBARJwgsAjupUHFxHRrOBSOf5NWd7inJ-OVwBOE7mcoJjHTFPZnzdT8PpGjsvBH6QBLOzO4IeHJz9G2BQbxkooDyyJY2oaHzELcIoDj2_3GGSVZhAyBMaI_EhPVcplgPAsK6KUVZkArcQk3EyWOkleuUuMG2Ciew_UT4f6VUKhBl0uxSnlJVWZsUMaRR119_Mbc_Nuveyz0qaiZ2h-Iynk_GaocpW27G5LFxEJASOILp5CI7omVftjA8xAD6XMif-x5WL4iIJSDRvOzeIgdjsghAszetHaGY5sfqXVHqWwlLNYjBHt_ed-e-99d-0D2sUQlO-zgGGBkM1qbdm0Os13Jw7Fsa7P-9iqpkc3oaWZ2dAadg-cwzZkaQ3enTTi_PV4tJat76Ok5hQUDSQA7kvNjdEBRuf4xvypKDUOWauK4QMG_GddkvSGgMKzdGIpg5yS6YzMVJ3KJL8UtZcB3Nsx3MrNczAap9ZJlBzVwYzphEBvtTonanNGYF4a0EMvjeYVnezDoQbXHZXMIUwW-SNPhiCLIA1JdX1HKDkuRpfaRYKpf_EeHKCsFB8U8u5Zb3mGh3YHh1DE7DaZWDnORRCxcZLQspRljrfJdXtEv-_IWDe665n82OAhTwlwSPsI0vkkDpUo1r_RMFn_HAHUH1t_t2oU_bOPHrvShipKXIbU2ZA41QYW3JgHMK3xdn9dI_gQxRWeXRkxVOzVKaMVXNGiQP4IvgrMN-Yzoa-PJWtszrT8TnNdVYlT7GmXqRrB0atsR-_pZVj7dXdduvFQnXPjV4pyIroo-7-R9HsGiI9Q8g6PENOHxLvowpv42BQ6WntXdBauqIsA6";
        
        mockMvc.perform(get("/api/report/detailLISV1")
                .param("reportToken", longToken))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.message").value("请求处理失败，服务器捕获到异常。详情请查看服务器日志。"));
    }
    
    @Test 
    public void testDirectErrorEndpoint() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 直接访问/error端点
        mockMvc.perform(get("/error"))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.status").value(500));
    }
}

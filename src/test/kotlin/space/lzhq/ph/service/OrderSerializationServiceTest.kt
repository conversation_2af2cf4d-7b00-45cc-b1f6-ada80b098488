package space.lzhq.ph.service

import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*

/**
 * OrderSerializationService 测试类
 * 验证内存管理和并发控制功能
 */
class OrderSerializationServiceTest {

    private lateinit var orderSerializationService: OrderSerializationService

    @BeforeEach
    fun setUp() {
        orderSerializationService = OrderSerializationService()
    }

    @Test
    fun `测试基本的锁功能`(): Unit = runBlocking {
        val orderNo = "TEST_ORDER_001"
        var executed = false

        val result = orderSerializationService.executeWithOrderLock(orderNo) {
            executed = true
            "success"
        }

        assertTrue(executed)
        assertEquals("success", result)
        assertTrue(orderSerializationService.getMutexCount() > 0)
    }

    @Test
    fun `测试同步版本的锁功能`() {
        val orderNo = "TEST_ORDER_002"
        var executed = false

        val result = orderSerializationService.executeWithOrderLockBlocking(orderNo) {
            executed = true
            "success"
        }

        assertTrue(executed)
        assertEquals("success", result)
        assertTrue(orderSerializationService.getMutexCount() > 0)
    }

    @Test
    fun `测试检查订单处理状态`(): Unit = runBlocking {
        val orderNo = "TEST_ORDER_CHECK"
        
        // 初始状态应该是未处理
        assertFalse(orderSerializationService.isOrderProcessing(orderNo))
        
        // 在一个协程中长时间持有锁
        val job = launch {
            orderSerializationService.executeWithOrderLock(orderNo) {
                delay(200) // 模拟长时间操作
                "processing"
            }
        }
        
        // 短暂延迟确保锁被获取
        delay(50)
        
        // 现在应该检测到正在处理
        assertTrue(orderSerializationService.isOrderProcessing(orderNo))
        
        // 等待操作完成
        job.join()
        
        // 完成后应该不再处理（注意：由于缓存，Mutex可能仍存在但未锁定）
        // 这里主要测试的是锁定状态，而不是Mutex是否存在
    }

    @Test
    fun `测试非阻塞尝试获取锁（协程版本）`(): Unit = runBlocking {
        val orderNo = "TEST_ORDER_TRY"
        var firstExecuted = false
        var secondExecuted = false
        
        // 第一个协程获取锁并长时间持有
        val job1 = launch {
            orderSerializationService.executeWithOrderLock(orderNo) {
                firstExecuted = true
                delay(200)
                "first"
            }
        }
        
        // 短暂延迟确保第一个协程获取了锁
        delay(50)
        
        // 第二个协程尝试获取锁，应该立即失败
        val result = orderSerializationService.tryExecuteWithOrderLock(orderNo) {
            secondExecuted = true
            "second"
        }
        
        // 第二个操作应该返回null（锁被占用）
        assertNull(result)
        assertFalse(secondExecuted)
        
        // 等待第一个协程完成
        job1.join()
        assertTrue(firstExecuted)
        
        // 现在再次尝试应该成功
        val result2 = orderSerializationService.tryExecuteWithOrderLock(orderNo) {
            secondExecuted = true
            "second"
        }
        
        assertEquals("second", result2)
        assertTrue(secondExecuted)
    }

    @Test
    fun `测试非阻塞尝试获取锁（同步版本）`() {
        val orderNo = "TEST_ORDER_TRY_BLOCKING"
        var secondExecuted = false
        
        // 使用一个线程持有锁
        val thread = Thread {
            orderSerializationService.executeWithOrderLockBlocking(orderNo) {
                Thread.sleep(200) // 模拟长时间操作
                "first"
            }
        }
        thread.start()
        
        // 短暂等待确保锁被获取
        Thread.sleep(50)
        
        // 尝试获取锁，应该立即失败
        val result = orderSerializationService.tryExecuteWithOrderLockBlocking(orderNo) {
            secondExecuted = true
            "second"
        }
        
        // 应该返回null（锁被占用）
        assertNull(result)
        assertFalse(secondExecuted)
        
        // 等待第一个线程完成
        thread.join()
        
        // 现在再次尝试应该成功
        val result2 = orderSerializationService.tryExecuteWithOrderLockBlocking(orderNo) {
            secondExecuted = true
            "second"
        }
        
        assertEquals("second", result2)
        assertTrue(secondExecuted)
    }

    @Test
    fun `测试手动清理功能`(): Unit = runBlocking {
        val orderNo = "TEST_ORDER_003"
        
        // 创建一个Mutex
        orderSerializationService.executeWithOrderLock(orderNo) {
            "test"
        }
        
        val countBefore = orderSerializationService.getMutexCount()
        assertTrue(countBefore > 0)
        
        // 手动清理
        orderSerializationService.cleanupOrderMutex(orderNo)
        
        // 注意：Caffeine的估算大小可能不会立即反映变化
        // 所以我们主要验证清理方法不会抛出异常
        assertDoesNotThrow { 
            orderSerializationService.cleanupOrderMutex(orderNo) 
        }
    }

    @Test
    fun `测试缓存统计信息`(): Unit = runBlocking {
        val orderNo = "TEST_ORDER_004"
        
        // 执行操作以产生统计数据
        orderSerializationService.executeWithOrderLock(orderNo) {
            "test"
        }
        
        val stats = orderSerializationService.getCacheStats()
        assertNotNull(stats)
        assertTrue(stats.contains("hitCount"))
        assertTrue(stats.contains("missCount"))
        assertTrue(stats.contains("size"))
    }

    @Test
    fun `测试并发访问相同订单`(): Unit = runBlocking {
        val orderNo = "TEST_ORDER_005"
        val results = mutableListOf<String>()
        
        // 并发执行多个操作
        val job1 = launch {
            orderSerializationService.executeWithOrderLock(orderNo) {
                delay(100) // 模拟耗时操作
                results.add("job1")
            }
        }
        
        val job2 = launch {
            orderSerializationService.executeWithOrderLock(orderNo) {
                delay(50)
                results.add("job2")
            }
        }
        
        // 等待所有任务完成
        job1.join()
        job2.join()
        
        // 验证结果（应该按顺序执行）
        assertEquals(2, results.size)
        assertTrue(results.contains("job1"))
        assertTrue(results.contains("job2"))
    }

    @Test
    fun `测试清理过期缓存`() {
        // 创建一些Mutex
        orderSerializationService.executeWithOrderLockBlocking("ORDER_1") { "test" }
        orderSerializationService.executeWithOrderLockBlocking("ORDER_2") { "test" }
        orderSerializationService.executeWithOrderLockBlocking("ORDER_3") { "test" }
        
        val countBefore = orderSerializationService.getMutexCount()
        assertTrue(countBefore >= 3)
        
        // 触发清理（虽然还没过期，但方法应该正常执行）
        assertDoesNotThrow { 
            orderSerializationService.cleanupExpiredMutexes() 
        }
        
        // 验证缓存仍然工作
        orderSerializationService.executeWithOrderLockBlocking("ORDER_4") { "test" }
        assertTrue(orderSerializationService.getMutexCount() > 0)
    }
} 
# 🏥 诊间缴费优化设计方案 v2.0

## 📋 需求概述

优化诊间缴费流程，优先使用就诊卡余额，只有余额不足时才需要充值差额。通过重新设计API接口，提供更简洁、可靠的支付体验。

## 🎯 设计原则

1. **接口简化**：面向前端提供单一接口，内部智能处理
2. **重复调用友好**：支持重复调用，充值和缴费逻辑分离确保操作安全
3. **状态透明**：每次调用都重新验证状态，确保数据一致性
4. **职责分离**：支付和缴费逻辑完全分离，便于维护

## 🔄 整体设计方案

### 1. 接口设计

#### A. 废弃现有接口
- ~~`POST /api/outpatient/wxpay`~~ （标记为 @Deprecated）
- ~~`POST /api/outpatient/refreshWxPayOrder`~~ （标记为 @Deprecated）

#### B. 新增统一接口
```kotlin
/**
 * 智能门诊缴费接口
 * 支持余额充足时直接缴费，余额不足时返回微信支付参数
 * 支持重复调用，充值和缴费逻辑分离
 */
@PostMapping("/api/outpatient/intelligentPay")
@RequireSession
@RequireActivePatient(shouldBeMenzhenPatient = true)
fun intelligentPay(
    @RequestBody request: IntelligentPayRequest
): AjaxResult

data class IntelligentPayRequest(
    val feeNos: List<String>  // 费用单号集合
)
```

### 2. 核心业务流程

```mermaid
flowchart TD
    A[接收费用单号集合] --> B[通过HIS查询费用详情]
    B --> C{费用验证}
    C -->|失败| D[返回错误信息]
    C -->|成功| E[计算费用总额]
    E --> F[查询就诊卡余额]
    F --> G{余额是否充足}
    G -->|充足| H[直接调用HIS缴费]
    G -->|不足| I[创建微信支付订单]
    H --> J[保存缴费记录]
    I --> K[返回微信支付参数]
    J --> L[返回缴费成功]
    K --> M[前端调用微信支付]
    M --> N[用户完成支付]
    N --> O[前端再次调用本接口]
    O --> P[余额充足，完成缴费]
```

### 3. 数据模型设计

#### A. 增强微信支付订单表
```sql
-- 微信支付订单表增强
ALTER TABLE wx_payment ADD COLUMN fee_details JSON COMMENT '费用详情JSON';
ALTER TABLE wx_payment ADD COLUMN total_fee DECIMAL(10,2) COMMENT '费用总额';
ALTER TABLE wx_payment ADD COLUMN card_balance_at_time DECIMAL(10,2) COMMENT '下单时卡余额';
ALTER TABLE wx_payment ADD COLUMN payment_type TINYINT DEFAULT 2 COMMENT '支付类型：1=旧版充值，2=智能缴费';

-- 费用详情JSON结构示例
{
  "feeNos": ["F001", "F002"],
  "feeItems": [
    {
      "feeNo": "F001",
      "feeName": "挂号费",
      "amount": 5.00,
      "deptName": "内科"
    }
  ],
  "totalAmount": 85.00,
  "calculatedAt": "2024-01-01T10:00:00"
}
```

#### B. 新增缴费记录表
```sql
-- 门诊缴费记录表
CREATE TABLE outpatient_payment_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    patient_id BIGINT NOT NULL COMMENT '患者ID',
    fee_details JSON NOT NULL COMMENT '完整费用信息JSON',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '缴费总额',
    payment_method VARCHAR(20) NOT NULL COMMENT '支付方式：CARD_BALANCE',
    his_trade_no VARCHAR(100) COMMENT 'HIS系统交易号',
    status TINYINT DEFAULT 1 COMMENT '状态：1=成功，2=失败',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_patient_id (patient_id),
    INDEX idx_created_time (created_time)
) COMMENT='门诊缴费记录表';

-- 费用详情JSON结构示例
{
  "feeNos": ["F001", "F002"],
  "feeItems": [
    {
      "feeNo": "F001",
      "feeName": "挂号费",
      "amount": 5.00,
      "deptName": "内科",
      "doctorName": "张医生"
    }
  ],
  "totalAmount": 85.00,
  "paymentTime": "2024-01-01T10:00:00"
}
```

### 4. 技术实现方案

#### A. 服务层设计

```kotlin
/**
 * 智能门诊缴费服务
 */
interface IIntelligentOutpatientPayService {
    
    /**
     * 智能缴费处理
     * @param patient 患者信息
     * @param feeNos 费用单号集合
     * @return 处理结果
     */
    fun processIntelligentPayment(patient: Patient, feeNos: List<String>): IntelligentPayResult
    
    /**
     * 验证并计算费用
     */
    fun validateAndCalculateFees(patient: Patient, feeNos: List<String>): FeeCalculationResult
    
    /**
     * 查询就诊卡余额
     */
    fun queryCardBalance(patient: Patient): BigDecimal
    
    /**
     * 直接从卡余额缴费
     */
    fun payDirectlyFromCard(patient: Patient, feeDetails: FeeCalculationResult): PaymentResult
    
    /**
     * 创建充值订单
     */
    fun createRechargeOrder(patient: Patient, feeDetails: FeeCalculationResult, shortfall: BigDecimal): WxPaymentResult
}

/**
 * 处理结果类型
 */
sealed class IntelligentPayResult {
    data class DirectPaySuccess(val paymentRecord: OutpatientPaymentRecord) : IntelligentPayResult()
    data class NeedRecharge(val wxPayment: WxPayment, val wxPayParams: Map<String, Any>) : IntelligentPayResult()
    data class Error(val message: String) : IntelligentPayResult()
}

/**
 * 费用计算结果
 */
data class FeeCalculationResult(
    val feeNos: List<String>,
    val feeItems: List<FeeItem>,
    val totalAmount: BigDecimal,
    val validPrescriptions: List<QueryOutpatientPayResponse.OutpatientPayItem>
)

data class FeeItem(
    val feeNo: String,
    val feeName: String,
    val amount: BigDecimal,
    val deptName: String,
    val doctorName: String
)
```

#### B. 控制器实现

```kotlin
@RestController
@RequestMapping("/api/outpatient")
class IntelligentOutpatientController : BaseController() {

    @Autowired
    private lateinit var intelligentPayService: IIntelligentOutpatientPayService

    @PostMapping("/intelligentPay")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun intelligentPay(@RequestBody request: IntelligentPayRequest): AjaxResult {
        val patient = request.getCurrentPatient()
        
        return try {
            when (val result = intelligentPayService.processIntelligentPayment(patient, request.feeNos)) {
                is IntelligentPayResult.DirectPaySuccess -> {
                    AjaxResult.success("缴费成功", mapOf(
                        "paymentRecord" to result.paymentRecord,
                        "type" to "DIRECT_PAY"
                    ))
                }
                is IntelligentPayResult.NeedRecharge -> {
                    AjaxResult.success("需要充值", mapOf(
                        "wxPayParams" to result.wxPayParams,
                        "wxPayment" to result.wxPayment,
                        "type" to "NEED_RECHARGE"
                    ))
                }
                is IntelligentPayResult.Error -> {
                    AjaxResult.error(result.message)
                }
            }
        } catch (e: Exception) {
            logger.error("智能缴费处理失败", e)
            AjaxResult.error("系统异常，请稍后重试")
        }
    }
}
```

### 5. 核心业务逻辑实现

```kotlin
@Service
@Transactional
class IntelligentOutpatientPayServiceImpl : IIntelligentOutpatientPayService {

    override fun processIntelligentPayment(patient: Patient, feeNos: List<String>): IntelligentPayResult {
        
        // 1. 验证并计算费用
        val feeResult = validateAndCalculateFees(patient, feeNos)
        if (feeResult.totalAmount <= BigDecimal.ZERO) {
            return IntelligentPayResult.Error("无有效费用项目")
        }
        
        // 2. 查询就诊卡余额
        val cardBalance = queryCardBalance(patient)
        
        // 3. 判断是否可以直接缴费
        if (cardBalance >= feeResult.totalAmount) {
            // 余额充足，直接缴费
            val paymentResult = payDirectlyFromCard(patient, feeResult)
            return if (paymentResult.success) {
                IntelligentPayResult.DirectPaySuccess(paymentResult.paymentRecord!!)
            } else {
                IntelligentPayResult.Error(paymentResult.errorMessage ?: "缴费失败")
            }
        } else {
            // 余额不足，创建充值订单
            val shortfall = feeResult.totalAmount - cardBalance
            val wxPaymentResult = createRechargeOrder(patient, feeResult, shortfall)
            return if (wxPaymentResult.success) {
                IntelligentPayResult.NeedRecharge(wxPaymentResult.wxPayment!!, wxPaymentResult.wxPayParams!!)
            } else {
                IntelligentPayResult.Error(wxPaymentResult.errorMessage ?: "创建支付订单失败")
            }
        }
    }

    override fun validateAndCalculateFees(patient: Patient, feeNos: List<String>): FeeCalculationResult {
        // 1. 调用HIS接口查询可缴费项目（参考 /api/outpatient/payments）
        val availablePayments = queryAvailablePayments(patient)
        
        // 2. 验证费用单号有效性
        val validItems = availablePayments.filter { payment ->
            feeNos.contains(payment.prescriptionNo) && 
            !payment.isCharged() && 
            NumberUtil.parseDouble(payment.orderFee) > 0
        }
        
        if (validItems.isEmpty()) {
            throw BusinessException("无有效的待缴费项目")
        }
        
        // 3. 计算总费用
        val totalAmount = validItems.sumOf { NumberUtil.parseDouble(it.orderFee) }.toBigDecimal()
        
        // 4. 构建费用明细
        val feeItems = validItems.map { item ->
            FeeItem(
                feeNo = item.prescriptionNo,
                feeName = item.itemName ?: "门诊费用",
                amount = NumberUtil.parseDouble(item.orderFee).toBigDecimal(),
                deptName = item.deptName ?: "",
                doctorName = item.doctorName ?: ""
            )
        }
        
        return FeeCalculationResult(feeNos, feeItems, totalAmount, validItems)
    }

    override fun queryCardBalance(patient: Patient): BigDecimal {
        // 参考 /api/outpatient/info 接口
        val resp = WitontekService.me.queryMenzhenPatient(QueryMenzhenPatientRequest().apply {
            this.jzCardNo = patient.jzCardNo
        })
        
        if (!resp.isOk() || !resp.isValid()) {
            throw BusinessException("查询就诊卡信息失败：${resp.message}")
        }
        
        return NumberUtil.parseDouble(resp.balance ?: "0").toBigDecimal()
    }

    override fun payDirectlyFromCard(patient: Patient, feeDetails: FeeCalculationResult): PaymentResult {
        try {
            // 调用HIS门诊缴费接口
            val outpatientPayResult = WitontekService.me.outpatientPay(
                OutpatientPayRequest().apply {
                    this.prescriptionNo = feeDetails.feeNos.joinToString(",")
                    this.realName = patient.name
                    this.patientCard = patient.cardNo
                    this.payType = "eCardPay"
                    this.tradeNo = UnifiedOrderForm.newOrderNo()
                    this.feeType = "门诊缴费"
                    this.orderFee = feeDetails.totalAmount.toPlainString()
                    this.payedTime = LocalDateTime.now()
                })
            
            if (!outpatientPayResult.isOk()) {
                return PaymentResult(false, errorMessage = outpatientPayResult.message)
            }
            
            // 保存缴费记录
            val paymentRecord = savePaymentRecord(patient, feeDetails)
            
            return PaymentResult(true, paymentRecord = paymentRecord)
            
        } catch (e: Exception) {
            logger.error("直接缴费失败", e)
            return PaymentResult(false, errorMessage = "缴费处理异常")
        }
    }

    override fun createRechargeOrder(patient: Patient, feeDetails: FeeCalculationResult, shortfall: BigDecimal): WxPaymentResult {
        // 参考原 /api/outpatient/wxpay 接口逻辑
        val session = getCurrentSession(patient)
        val ip = getCurrentClientIP()
        
        val outTradeNo = UnifiedOrderForm.newOrderNo()
        val form = UnifiedOrderForm(openid = patient.openId)
            .orderNo(outTradeNo)
            .orderType(2)
            .payWayType(if (session.clientType == ClientType.WEIXIN_MP.code) 32 else 35)
            .subject("${outTradeNo}-智能门诊缴费")
            .amount(shortfall)
            .notifyUrl("https://xjzybyy.xjyqtl.cn/open/wx/onDongruanPay")
            .clientIp(ip)
        form.sign()
        
        val result = runBlocking {
            DongruanPayService.me.createUnifiedOrder(form)
        }
        
        if (result.isOk()) {
            // 保存微信支付订单（增强版本）
            val wxPayment = saveEnhancedWxPayment(patient, outTradeNo, shortfall, feeDetails)
            return WxPaymentResult(true, wxPayment = wxPayment, wxPayParams = result.data)
        } else {
            return WxPaymentResult(false, errorMessage = result.message)
        }
    }

    /**
     * 保存增强版微信支付订单
     */
    private fun saveEnhancedWxPayment(
        patient: Patient, 
        outTradeNo: String, 
        amount: BigDecimal, 
        feeDetails: FeeCalculationResult
    ): WxPayment {
        val wxPayment = WxPayment().apply {
            this.openId = patient.openId
            this.zyPayNo = outTradeNo
            this.amount = (amount * BigDecimal(100)).toLong()  // 转为分
            this.serviceType = ServiceType.MZ.code
            this.patientId = patient.id
            this.hisTradeStatus = Constants.PAYMENT_PENDING
            this.prescriptionNos = feeDetails.feeNos.joinToString(",")
            
            // 新增字段
            this.feeDetails = JSON.toJSONString(mapOf(
                "feeNos" to feeDetails.feeNos,
                "feeItems" to feeDetails.feeItems,
                "totalAmount" to feeDetails.totalAmount,
                "calculatedAt" to LocalDateTime.now()
            ))
            this.totalFee = feeDetails.totalAmount
            this.cardBalanceAtTime = queryCardBalance(patient)
            this.paymentType = 2  // 智能缴费
        }
        
        return wxPaymentService.insertWxPayment(wxPayment)
    }

    /**
     * 保存缴费记录
     */
    private fun savePaymentRecord(patient: Patient, feeDetails: FeeCalculationResult): OutpatientPaymentRecord {
        val paymentRecord = OutpatientPaymentRecord().apply {
            this.patientId = patient.id
            this.feeDetails = JSON.toJSONString(mapOf(
                "feeNos" to feeDetails.feeNos,
                "feeItems" to feeDetails.feeItems,
                "totalAmount" to feeDetails.totalAmount,
                "paymentTime" to LocalDateTime.now()
            ))
            this.totalAmount = feeDetails.totalAmount
            this.paymentMethod = "CARD_BALANCE"
            this.status = 1  // 成功
        }
        
        return outpatientPaymentRecordService.insertRecord(paymentRecord)
    }
}
```

### 6. 异常处理和边界情况

#### A. 重复调用处理
由于充值和缴费逻辑完全分离，重复调用该接口是安全的：
- **重复充值**：每次充值都会增加就诊卡余额，不会产生负面影响
- **重复缴费检查**：通过HIS系统查询费用状态，已缴费项目会被自动过滤
- **状态实时验证**：每次调用都重新查询余额和费用状态，确保数据准确性

#### B. 状态一致性保障
```kotlin
/**
 * 状态同步检查
 */
private fun ensureStateConsistency(patient: Patient, feeNos: List<String>) {
    // 1. 检查费用状态是否发生变化（已缴费的会被过滤）
    // 2. 检查患者账户状态
    // 3. 验证就诊卡余额的准确性
}
```

### 7. 监控和日志

#### A. 关键指标监控
- 直接缴费成功率
- 充值转化率  
- 接口响应时间
- 异常情况统计

#### B. 业务日志记录
```kotlin
@Component
class PaymentAuditLogger {
    
    fun logPaymentAttempt(patient: Patient, feeNos: List<String>, result: IntelligentPayResult) {
        // 记录每次支付尝试的详细信息
    }
    
    fun logStateTransition(orderId: String, fromStatus: String, toStatus: String) {
        // 记录状态变更
    }
}
```

### 8. 部署和迁移策略

#### A. 灰度发布
```kotlin
@Value("\${intelligent.pay.enabled:false}")
private var intelligentPayEnabled: Boolean = false

@Value("\${intelligent.pay.whitelist:}")
private var whitelistPatients: List<String> = emptyList()
```

#### B. 兼容性处理
- 原有接口标记为废弃但保持可用
- 新接口逐步替换旧接口
- 数据结构向下兼容

## 📋 实施计划

### Phase 1: 数据模型和基础服务
- [ ] 数据库表结构调整
- [ ] 基础服务类实现
- [ ] 单元测试编写

### Phase 2: 核心业务逻辑
- [ ] 智能缴费服务实现
- [ ] 控制器接口实现
- [ ] 集成测试

### Phase 3: 监控和优化
- [ ] 监控指标接入
- [ ] 性能优化
- [ ] 错误处理完善

### Phase 4: 灰度发布
- [ ] 小范围测试
- [ ] 逐步放量
- [ ] 全量发布

## 🔧 技术债务清理

1. 原有接口的废弃处理
2. 冗余代码清理
3. 数据库索引优化
4. 缓存策略调整

---

> 💡 **总结**：新设计方案通过单一接口提供智能缴费能力，支持重复调用且操作安全。通过充值和缴费的完全分离设计，简化了业务逻辑，提高了系统的可维护性和可靠性。每次调用都实时验证状态，确保用户体验的同时保障数据一致性。 
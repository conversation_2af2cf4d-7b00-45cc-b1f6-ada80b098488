server
{
    listen 80;
	listen 443 ssl http2;
    server_name xjzybyy.xjyqtl.cn;
    index index.html index.htm default.htm default.html;
    root /www/jar;

    #SSL-START SSL相关配置
    #error_page 404/404.html;
    #HTTP_TO_HTTPS_START
    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }
    #HTTP_TO_HTTPS_END
    ssl_certificate    /www/server/panel/vhost/cert/xjzybyy.xjyqtl.cn.cer;
    ssl_certificate_key    /www/server/panel/vhost/cert/xjzybyy.xjyqtl.cn.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    add_header Strict-Transport-Security "max-age=31536000";
    error_page 497  https://$host$request_uri;

    #HTTP_TO_HTTPS_START
    # 设置一个变量来决定是否重定向到HTTPS
    set $redirect_to_https 1;  # 默认开启重定向

    # 如果是HTTPS或者URI以/ws开头，则不重定向
    if ($scheme = https) {
        set $redirect_to_https 0;
    }
    if ($request_uri ~* ^/ws) {
        set $redirect_to_https 0;
    }

    if ($request_uri ~* ^/pay-service) {
        set $redirect_to_https 0;
    }

    # 根据变量值决定是否重定向
    if ($redirect_to_https = 1) {
        return 301 https://$host$request_uri;
    }

    #HTTP_TO_HTTPS_END
    #SSL-END

    #ERROR-PAGE-START  错误页相关配置
    #error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.svn|\.project|LICENSE|README.md|package.json|package-lock.json|\.env) {
        return 404;
    }

    #一键申请SSL证书验证目录相关设置
    location /.well-known/ {
        root /www/wwwroot/java_node_ssl;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }


    # 静态文件上传路径代理配置
    location ^~ /profile/upload/ {
        alias /www/server/tomcat_zyxcx/upload/upload/;
        try_files $uri =404;
        
        # 设置缓存头
        expires 1d;
        add_header Cache-Control "public, immutable";
        
        # 安全设置
        add_header X-Content-Type-Options nosniff;
    }

    # HTTP反向代理相关配置开始 >>>
    location ~ /purge(/.*) {
        proxy_cache_purge cache_one $Host$request_uri$is_args$args;
    }

    location / {
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $Host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        add_header X-Cache $upstream_cache_status;
        proxy_set_header X-Host $host:$server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_connect_timeout 30s;
        proxy_read_timeout 86400s;
        proxy_send_timeout 30s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # HTTP反向代理相关配置结束 <<<

    location = / {
        rewrite ^ /index.html last;
    }

    # 公众号
    location /mp {
        root /www/wwwroot/xjzybyy.xjyqtl.cn;
        index index.html;
        try_files $uri $uri/ @router;
    }

    # 公众号测试
    location /mp-test {
        root /www/wwwroot/xjzybyy.xjyqtl.cn;
        index index.html;
        try_files $uri $uri/ @router;
    }

    location /index.html {
        alias /www/wwwroot/main/index.html;
    }

    # 官网
    location /main {
        root /www/wwwroot;
        index index.html;
    }

    access_log /www/wwwlogs/zyxcx-access.log;
    error_log  /www/wwwlogs/zyxcx-error.log;
}

#!/bin/bash
set -euo pipefail

# 定义路径
DEPLOY_DIR="/www/jar"
BACKUP_DIR="/www/jar/backup"
JAR_NAME="zhangyi-xjzybyy-1.0.0.jar"

echo "=== 掌医小程序 - 版本信息查看工具 ==="

# 检查当前运行的服务
echo "当前运行状态："
echo "----------------------------------------"
PID=$(jps | grep "$JAR_NAME" | awk '{print $1}')
if [ -n "$PID" ]; then
  echo "✓ 服务正在运行 (PID: $PID)"
  
  # 显示当前部署的jar包信息
  CURRENT_JAR="$DEPLOY_DIR/$JAR_NAME"
  if [ -f "$CURRENT_JAR" ]; then
    FILE_SIZE=$(ls -lh "$CURRENT_JAR" | awk '{print $5}')
    FILE_TIME=$(ls -l "$CURRENT_JAR" | awk '{print $6, $7, $8}')
    echo "当前版本: $JAR_NAME"
    echo "文件大小: $FILE_SIZE"
    echo "修改时间: $FILE_TIME"
  else
    echo "⚠ 警告：未找到当前部署的jar文件"
  fi
else
  echo "✗ 服务未运行"
fi

echo
echo "可用备份版本："
echo "----------------------------------------"

# 检查备份目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
  echo "备份目录不存在 ($BACKUP_DIR)"
  exit 0
fi

# 列出所有备份文件
BACKUP_FILES=($(ls -t "$BACKUP_DIR"/zhangyi-xjzybyy-*_backup_*.jar 2>/dev/null))
ROLLBACK_FILES=($(ls -t "$BACKUP_DIR"/zhangyi-xjzybyy-*_rollback_*.jar 2>/dev/null))

if [ ${#BACKUP_FILES[@]} -eq 0 ] && [ ${#ROLLBACK_FILES[@]} -eq 0 ]; then
  echo "未找到任何备份文件"
  exit 0
fi

# 显示部署备份文件
if [ ${#BACKUP_FILES[@]} -gt 0 ]; then
  echo "部署备份文件 (按时间倒序):"
  for i in "${!BACKUP_FILES[@]}"; do
    FILE_NAME=$(basename "${BACKUP_FILES[$i]}")
    FILE_SIZE=$(ls -lh "${BACKUP_FILES[$i]}" | awk '{print $5}')
    FILE_TIME=$(ls -l "${BACKUP_FILES[$i]}" | awk '{print $6, $7, $8}')
    echo "  [$((i+1))] $FILE_NAME (大小: $FILE_SIZE, 时间: $FILE_TIME)"
  done
  echo
fi

# 显示回退备份文件
if [ ${#ROLLBACK_FILES[@]} -gt 0 ]; then
  echo "回退备份文件 (按时间倒序):"
  for i in "${!ROLLBACK_FILES[@]}"; do
    FILE_NAME=$(basename "${ROLLBACK_FILES[$i]}")
    FILE_SIZE=$(ls -lh "${ROLLBACK_FILES[$i]}" | awk '{print $5}')
    FILE_TIME=$(ls -l "${ROLLBACK_FILES[$i]}" | awk '{print $6, $7, $8}')
    echo "  [$((i+1))] $FILE_NAME (大小: $FILE_SIZE, 时间: $FILE_TIME)"
  done
  echo
fi

# 显示磁盘使用情况
echo "备份目录磁盘使用情况："
echo "----------------------------------------"
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" 2>/dev/null | awk '{print $1}')
BACKUP_COUNT=$(find "$BACKUP_DIR" -name "*.jar" 2>/dev/null | wc -l)
echo "备份文件总数: $BACKUP_COUNT"
echo "备份目录大小: $BACKUP_SIZE"

echo
echo "常用操作命令："
echo "----------------------------------------"
echo "部署新版本: ./deploy.sh"
echo "版本回退:   ./rollback.sh"
echo "查看版本:   ./check_versions.sh"
echo "查看日志:   tail -f /www/logs/gc-*.log"
echo "检查服务:   jps | grep $JAR_NAME" 
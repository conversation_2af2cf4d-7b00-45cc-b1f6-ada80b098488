### /**
###  * 查询排班科室，按拼音首字母检索
###  * @param kw 查询关键字，支持按科室的名称、简拼、全拼模糊搜索
###  */
GET {{host}}/open/paiban/indexedDepartments?kw=
Accept: application/json

###
GET {{host}}/open/paiban/indexedDepartments?kw=ZJ
Accept: application/json

###    /**
###     * 查询排班医生
###     * @param departmentCode 科室代码
###     * @param date 排班日期
###     */
GET {{host}}/open/paiban/doctors?departmentCode=159&date=2020-10-25
Accept: application/json

###    /**
###     * 查询排班号源
###     * @param departmentCode 科室代码
###     * @param doctorCode 医生代码
###     * @param date 排班日期
###     * @param ampm 上午下午，参见 period.code ：1=上午，2=下午，3=晚上
###     */
GET {{host}}/open/paiban/tickets?departmentCode=159&doctorCode=0232&date=2020-10-25&ampm=1

### /**
###  * 微信登录
###  * @param code 通过 wx.login() 接口获取的临时登录凭证
###  */
POST {{host}}/open/wx/login?code=123456
Accept: application/json

###    /**
###     * 查询科室分页
###     * @param pageNum 第几页，默认为1
###     * @param pageSize 每页几条，默认为25
###     * @param kw 关键词
###     */
GET {{host}}/open/department/paginate?pageNo=1&pageSize=100&kw=ZY

###    /**
###     * 查询科室详情
###     * @param id 科室ID
###     */
GET {{host}}/open/department/show/89

###    /**
###     * 查询医生列表
###     * @param departmentId 科室ID
###     */
GET {{host}}/open/doctor/list?departmentId=3

###    /**
###     * 查询医生详情
###     * @param id 医生ID
###     */
GET {{host}}/open/doctor/show/0969

### 查询体检套餐
### gender: 0=不限，1=限男性，2=限女性
### maritalStatus: 0=不限，1=限已婚，2=限未婚
GET {{host}}/open/tijian/packages
Accept: application/json

### 查询体检套餐项目
GET {{host}}/open/tijian/packageItems?packageId=000005
Accept: application/json

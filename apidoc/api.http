### /**
###  * 添加身份证
###  * @param name 姓名
###  * @param idCardNo 身份证号
###  * @param mobile 手机号
###  */
POST {{host}}/api/patient/addIdCard
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

idCardNo=513436200005251165&name=魏丹&mobile=15688243654

###    /**
###     * 获取就诊人列表
###     */
GET {{host}}/api/patient/list
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 获取默认就诊人
###     */
GET {{host}}/api/patient/active
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 根据身份证号查询患者信息(仅适用于未绑定就诊卡的情况)
###     * @param idCardNo 身份证号
###     */
GET {{host}}/api/patient/idCard?idCardNo=513436200005251544
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 切换到指定身份证(仅适用于未绑定就诊卡的情况)
###     * @param idCardNo 身份证号
###     */
POST {{host}}/api/patient/switchToIdCard
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

idCardNo=513436200005251544

###    /**
###     * 根据身份证号移除就诊人(仅适用于未绑定就诊卡的情况)
###     * @param idCardNo 身份证号
###     */
POST {{host}}/api/patient/removeIdCard
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

idCardNo=513436200005251165

### /**
###  * 添加就诊卡
###  * @param name 姓名
###  * @param jzCardNo 就诊卡号
###  * @param mobile 手机号
###  */
POST {{host}}/api/patient/addJzCard
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

jzCardNo=00593738&name=袁艺轩&mobile=13669965100

###    /**
###     * 根据就诊卡号查询患者信息
###     * @param jzCardNo 就诊卡号
###     */
GET {{host}}/api/patient/jzCard?jzCardNo=00392665
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 切换到指定就诊卡
###     * @param jzCardNo 就诊卡号
###     */
POST {{host}}/api/patient/switchToJzCard
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

jzCardNo=00593738

###    /**
###     * 根据就诊卡号移除就诊人
###     * @param jzCardNo 就诊卡号
###     */
POST {{host}}/api/patient/removeJzCard
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

jzCardNo=00181074

###    /**
###     * 查询住院历史
###     * @param patientNo 患者索引号
###     * @param zhuyuanNo 住院号，可选，为空时返回指定患者所有住院记录，不为空时返回指定患者特定住院号的住院记录
###     */
GET {{host}}/api/patient/zhuyuanHistory?patientNo=1849840&zhuyuanNo=261
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 切换住院号
###     * @param zhuyuanNo 住院号
###     */
POST {{host}}/api/patient/switchZhuyuanNo
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

zhuyuanNo=261

###    /**
###     * 预约
###     * @param time 就诊时间，按照 /open/paiban/tickets 中的开始时间(beginTime)原样传入
###     * @param ampm 上午下午，参见 period.code ：1=上午，2=下午，3=晚上
###     * @param departmentCode 科室代码
###     * @param departmentName 科室名称
###     * @param doctorCode 医生代码
###     * @param doctorName 医生姓名
###     * @param regFee 挂号费
###     * @param resourceId 号源标识
###     */
POST {{host}}/api/yuyue
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

time=2020-10-25 13:00:00&ampm=1&departmentCode=159&departmentName=眼科诊室&doctorCode=0232&doctorName=刘世情&regFee=14&resourceId=4799

###    /**
###     * 核酸检测预约
###     * @param date 检测日期，格式：yyyy-MM-dd
###     * @param testItem 检测项目：1=新型冠状病毒核酸检测单人单管，2=血清抗体检测(新冠病毒)
###     */
POST {{host}}/api/covid/yuyue
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

date=2020-11-14&testItem=1


###    /**
###     * 核酸检测预约记录
###     */
GET {{host}}/api/covid/history
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 核酸检测取消预约
###     * @param date 检测日期，格式：yyyy-MM-dd
###     * @param testItem 检测项目：1=新型冠状病毒核酸检测单人单管，2=血清抗体检测(新冠病毒)
###     */
POST {{host}}/api/covid/cancel
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

date=2020-11-14&testItem=1

###    /**
###     * 查询核酸检测预约数
###     */
GET {{host}}/api/covid/count
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 获取门诊的费用清单
###     */
GET {{host}}/api/menzhen/fee
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 获取门诊的费用详单
###     * @param invoiceNumber 发票号码
###     */
GET {{host}}/api/menzhen/feeDetail?invoiceNumber=0018766996
Accept: application/json
Authorization: {{sid}}


###    /**
###     * 获取门诊的待缴费清单
###     * @param endDate 查询截止日期，默认为当天
###     * @param days 自截止日期，向前查询多少天，默认为7
###     */
GET {{host}}/api/menzhen/unpaidOrders?endDate=&days=
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 获取门诊的账单（充值或退款记录）
###     */
GET {{host}}/api/menzhen/bill
Accept: application/json
Authorization: {{sid}}

<> api_menzhen_bill.json

###    /**
###     * 获取住院的费用清单
###     * @param date 清单日
###     */
GET {{host}}/api/zhuyuan/fee?date=2020-11-01
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 获取住院的账单
###     */
GET {{host}}/api/zhuyuan/bill
Accept: application/json
Authorization: {{sid}}


###    /**
###     * 获取门诊的LIS报告
###     * @param pageNo 第几页，默认为1
###     * @param pageSize 每页几条，默认为100
###     */
GET {{host}}/api/report/mzlis?pageNo=1&pageSize=100
Accept: application/json
Authorization: {{sid}}

<> api_report_mzlis.json

###    /**
###     * 获取门诊的LIS报告
###     */
GET {{host}}/api/report/mzlis1
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 获取住院的LIS报告
###     * @param pageNo 第几页，默认为1
###     * @param pageSize 每页几条，默认为100
###     */
GET {{host}}/api/report/zylis?endDate=&days=
Accept: application/json
Authorization: {{sid}}

<> api_report_zylis.json

###
GET {{host}}/api/report/lisDetail1?reportId=20211129G0050136
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 获取门诊的PACS报告
###     * @param pageNo 第几页，默认为1
###     * @param pageSize 每页几条，默认为100
###     */
GET {{host}}/api/report/mzpacs?pageNo=1&pageSize=100
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 门诊充值
###     */
POST {{host}}/api/menzhen/ccbpay
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

amount=100

###    /**
###     * 门诊充值
###     */
POST {{host}}/api/menzhen/wxpay
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

amount=100

###    /**
###     * 获取用于退款的账单，最多退三个月内的订单
###     * @param endDate 查询截止日期，默认为当天
###     * @param days 自截止日期，向前查询多少天，默认为90
###     */
GET {{host}}/api/menzhen/billForRefund?endDate=&days=
Accept: application/json
Authorization: {{sid}}

<> api_menzhen_bill.json

###    /**
###     * 退款
###     * @param zyPayNo 掌医充值订单号
###     * @param amount 退款金额，以分为单位
###     */
POST {{host}}/api/menzhen/wxrefund
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

zyPayNo=MZ202006122333064260&amount=100

###    /**
###     * 退款
###     * @param zyPayNo 掌医充值订单号
###     * @param amount 退款金额，以分为单位
###     */
POST {{host}}/api/menzhen/ccbrefund
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

zyPayNo=MZ202010261743524154&amount=30

###    /**
###     * 注册健康卡
###     * @param idCardNo 身份证号
###     * @param name 姓名
###     * @param mobile 手机号
###     * @param nationCode 民族代码，如 01 表示汉族，参考 /open/dict/queryByType?type=minzu
###     */
POST {{host}}/api/erhcCard/getOrRegister
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

idCardNo=210623199505094493&name=苑政育&mobile=18609933710&nationCode=1

###    /**
###     * 查询或注册就诊卡
###     * @param idCardNo 身份证号
###     * @param name 姓名
###     * @param mobile 手机号
###     * @param nation 民族名称，如 汉族，参考 /open/dict/queryByType?type=minzu
###     */
POST {{host}}/api/erhcCard/getOrRegisterJzCard
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

idCardNo=210623199505094493&name=苑政育&mobile=18609933710&nation=汉族

###    /**
###     * 关联健康卡和就诊卡
###     * @param erhcCardNo 健康卡号
###     * @param empi 健康卡主索引
###     * @param patientId 患者ID
###     */
POST {{host}}/api/erhcCard/bind
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

erhcCardNo=CCE2BFED0C3E13C5BEF245D64D273A827DE77F6515EF547E5282454603493C71&empi=790AF9A1D88D383C1D65E9AC555C2A754A63CB13F15BFC1BAF0B51638CD0444C&patientId=890905

###    /**
###     * 获取健康卡二维码
###     * @param patientId 患者ID
###     */
GET {{host}}/api/erhcCard/getQrCode?patientId=890905
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 查询支付宝医保订单
###     */
GET {{host}}/api/alipayMip/queryOrder?payOrderId=ORD650100202210261728510000700
Accept: application/json
Authorization: {{sid}}

###    /**
###     * 查询支付宝医保订单
###     */
GET {{host}}/api/weixinMip/queryOrder?payOrderId=ORD650100202210311949370001410
Accept: application/json
Authorization: {{sid}}
